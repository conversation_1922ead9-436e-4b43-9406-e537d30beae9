
scapegoat.png
size: 280,988
format: RGBA8888
filter: Linear,Linear
repeat: none
b0
  rotate: true
  xy: 97, 114
  size: 86, 69
  orig: 87, 84
  offset: 1, 8
  index: -1
b1
  rotate: false
  xy: 2, 74
  size: 81, 79
  orig: 87, 84
  offset: 3, 0
  index: -1
b2
  rotate: true
  xy: 165, 16
  size: 67, 68
  orig: 87, 84
  offset: 7, 10
  index: -1
b3
  rotate: true
  xy: 168, 85
  size: 73, 77
  orig: 87, 84
  offset: 4, 6
  index: -1
b4
  rotate: true
  xy: 85, 41
  size: 71, 78
  orig: 87, 84
  offset: 6, 6
  index: -1
b5
  rotate: true
  xy: 2, 2
  size: 70, 78
  orig: 87, 84
  offset: 7, 6
  index: -1
b6
  rotate: true
  xy: 126, 270
  size: 69, 54
  orig: 87, 84
  offset: 8, 16
  index: -1
b7
  rotate: true
  xy: 126, 202
  size: 66, 49
  orig: 87, 84
  offset: 10, 20
  index: -1
b8
  rotate: true
  xy: 235, 18
  size: 65, 35
  orig: 87, 84
  offset: 10, 33
  index: -1
ts_1
  rotate: true
  xy: 2, 688
  size: 298, 276
  orig: 300, 285
  offset: 2, 9
  index: -1
ts_10
  rotate: false
  xy: 177, 160
  size: 101, 98
  orig: 103, 98
  offset: 1, 0
  index: -1
ts_2
  rotate: true
  xy: 193, 260
  size: 86, 84
  orig: 86, 92
  offset: 0, 0
  index: -1
ts_3
  rotate: true
  xy: 212, 472
  size: 103, 55
  orig: 103, 56
  offset: 0, 1
  index: -1
ts_4
  rotate: false
  xy: 2, 233
  size: 122, 106
  orig: 125, 108
  offset: 2, 1
  index: -1
ts_5
  rotate: false
  xy: 2, 341
  size: 189, 170
  orig: 190, 185
  offset: 0, 0
  index: -1
ts_6
  rotate: false
  xy: 193, 348
  size: 80, 122
  orig: 81, 122
  offset: 0, 0
  index: -1
ts_7
  rotate: false
  xy: 212, 577
  size: 63, 109
  orig: 65, 109
  offset: 1, 0
  index: -1
ts_8
  rotate: true
  xy: 2, 155
  size: 76, 93
  orig: 77, 94
  offset: 0, 1
  index: -1
ts_9
  rotate: true
  xy: 2, 513
  size: 173, 208
  orig: 173, 229
  offset: 0, 0
  index: -1
