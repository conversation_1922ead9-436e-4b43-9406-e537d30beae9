syntax = "proto3";

package flyingchess.v1;

import "state.proto";

// 玩家信息
message Player {
  // 用户ID
  string id = 1;
  
  // 用户昵称
  string nickname = 2;
  
  // 用户头像URL
  string avatar_url = 3;
  
  // 是否游戏结束
  bool game_over = 4;
  
  // 是否系统托管
  bool hosting = 5;
  
  // 玩家在战局中的索引（0-3，对应四个角落位置）
  uint32 index = 6;
  
  // 组队ID（如果有组队功能）
  string team_id = 7;
  
  // 是否已退出游戏
  bool exited = 8;
  
  // 累计得分
  int32 score = 9;
  
  // 玩家颜色（0:红, 1:黄, 2:蓝, 3:绿）
  int32 color = 10;
  
  // 玩家棋子列表
  repeated Chess chess_list = 11;
  
  // 是否已准备
  bool ready = 12;
  
  // 是否是当前操作玩家
  bool is_current = 13;
  
  // 是否已完成游戏（所有棋子到达终点）
  bool finished = 14;
  
  // 完成游戏的排名（1-4）
  int32 rank = 15;
}

// 棋子信息
message Chess {
  // 棋子ID
  int32 id = 1;
  
  // 所属玩家索引
  uint32 player_index = 2;
  
  // 棋子颜色（0:红, 1:黄, 2:蓝, 3:绿）
  int32 color = 3;
  
  // 棋子当前状态
  ChessState state = 4;
  
  // 棋子当前位置（格子ID）
  int32 position = 5;
  
  // 是否可选择（当前回合）
  bool selectable = 6;
  
  // 是否被选中
  bool selected = 7;
}
