import {
    _decorator,
    Node,
    Label,
    Sprite,
    Sprite<PERSON>rame,
    tween,
    v3,
    color,
    math,
    Color,
} from 'cc'
import { CommonDialog } from '../CommonDialog/CommonDialog'
import { cat } from '@/core/manager'
import { GameEventConstant } from '@/core/business/constant'
import store from '@/core/business/store'
import { sleep } from '@/core/business/util/TimeUtils'

const { ccclass, property } = _decorator

export interface ShowStoryDialogProps {
    closeDelay?: number
    showCloseBtn?: boolean
}

/**
 * 展示说书人故事对话框
 */
@ccclass('ShowStoryDialog')
export class ShowStoryDialog extends CommonDialog<ShowStoryDialogProps> {
    @property({ type: Node, tooltip: '内容容器' })
    content: Node = null!

    @property({ type: Label, tooltip: '故事文本' })
    story_label: Label = null!

    @property({ type: Sprite, tooltip: '背景图片' })
    background: Sprite = null!

    @property({ type: Sprite, tooltip: '关闭图片' })
    close_btn: Sprite = null!

    @property({ type: Label, tooltip: '提示文本' })
    prompt_label: Label = null!

    NO_STORY_TEXT = '（谜语人未输入谜面）'

    override props: ShowStoryDialogProps = {}

    protected override initUI(): void {
        super.initUI()

        this.close_btn.node.active = this.props.showCloseBtn ?? false

        this.initEvents()

        // 设置故事文本
        this.updateStoryText()
        this.autoClose()
    }

    protected initEvents(): void {
        // 监听关闭按钮点击事件
        if (this.props.showCloseBtn) {
            this.node.on(Node.EventType.TOUCH_END, this.close, this)
        }
    }

    /**
     * 更新故事文本
     */
    private updateStoryText(): void {
        if (!this.story_label) return

        // 获取故事文本
        let storyText = ''

        // 如果没有提供故事，尝试从store中获取
        if (store.game.roomData.roundInfo?.currentStory) {
            storyText = store.game.roomData.roundInfo.currentStory
        }

        if (store.game.roomData.roundInfo?.currentStoryFrom === 2) {
            this.prompt_label.node.active = true
        }

        // 如果仍然没有故事，显示默认文本
        if (storyText.length <= 0) {
            storyText = this.NO_STORY_TEXT
            this.story_label.color = this.prompt_label.color
        }

        // 设置故事文本
        this.story_label.string = storyText

        console.log(`[ShowStoryDialog] 显示故事: ${storyText}`)
    }

    autoClose() {
        // 使用props中的closeDelay值
        const delay = this.props.closeDelay
        console.log(`[ShowStoryDialog] 将在 ${delay}ms 后自动关闭`)

        if (!delay) return
        setTimeout(() => {
            this.close()
        }, delay)
    }

    /**
     * 重写关闭方法，添加动画效果
     */
    override close(): void {
        cat.event.dispatchEvent(GameEventConstant.CLOSE_SHOW_STORY_DIALOG)

        if (!this.content) {
            // 如果没有content节点，直接调用父类的close方法
            super.close()
            return
        }

        if (this.close_btn.node.active) {
            super.close()
            return
        }

        // 执行动画：缩小并飞到目标位置
        tween(this.content)
            .to(
                0.8,
                {
                    position: v3(0, 500, 0),
                    scale: v3(0.2, 0.2, 0.2),
                },
                {
                    easing: 'cubicOut',
                }
            )
            .call(() => {
                // 调用父类的close方法关闭对话框
                if (store.game.isCurrentPlayerCursor) {
                    //通知当前说书人显示手牌
                    cat.event.dispatchEvent(
                        GameEventConstant.SHOW_MY_HAND_CARD_BTN
                    )
                }
                super.close(false)
            })
            .start()
    }
}
