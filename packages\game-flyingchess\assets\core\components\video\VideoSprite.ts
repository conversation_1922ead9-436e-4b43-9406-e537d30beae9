import {
    _decorator,
    Component,
    Node,
    Texture2D,
    ImageAsset,
    VideoPlayer,
    Material,
    gfx,
    game,
    VideoClip,
    loader,
    Sprite,
    SpriteFrame,
    Vec2,
    UITransform,
    v2,
    v4,
    Rect,
    assetManager,
} from 'cc';
// import LogUtil from '../../game_util/LogUtil';
const { ccclass, property } = _decorator;

enum VideoState { //视频状态
    ERROR = -1, // 出错状态
    IDLE = 0, // 置空状态
    PREPARING = 1, //准备中
    PREPARED = 2, //准备完成
    PLAYING = 3, //播放中
    PAUSED = 4, //暂停
    STOP = 5,
    COMPLETED = 5, //播放完成
}

enum ReadyState { //准备状态
    HAVE_NOTHING = 0,
    HAVE_METADATA = 1,
    HAVE_CURRENT_DATA = 2,
    HAVE_FUTURE_DATA = 3,
    HAVE_ENOUGH_DATA = 4,
}

@ccclass('VideoSprite')
export class VideoSprite extends Component {
    @property({ type: VideoPlayer })
    videoComp: VideoPlayer = null!;
    @property({ type: Material, tooltip: 'Sprite材质覆盖' })
    spriteMaterial: Material = null!;

    @property({ type: Vec2, tooltip: '尺寸比[原片:alpha通道块]' })
    rate: Vec2 = v2(1, 1);
    @property({ type: Boolean, tooltip: '原片与alpha通道布局是否横向放置?' })
    isHorizontal: Boolean = false;

    @property({ type: Boolean, tooltip: '是否循环播放?' })
    loop: boolean = false;

    private _clip: VideoClip = null!; //视频资源

    // @property(VideoClip)
    // get clip() {
    //   return this._clip;
    // }

    // set clip(value: VideoClip) {
    //   this._clip = value;
    // }

    private _video: HTMLVideoElement;
    private _texture = new Texture2D();
    private _image = new ImageAsset();
    private _time = 0;
    private _gl: any = null;
    private _currentState: VideoState;
    currentTime: number;
    private _seekTime: number;
    private _loaded: boolean = false;

    override start() {
        // let remoteUrl = 'https://yiqiyou-dev-1300033206.cos.ap-nanjing.myqcloud.com/yiqiyoo/touming.mp4';
        // this.loadNetMp4('remoteUrl');
    }

    loadNetMp4(remoteUrl: string) {
        assetManager.loadRemote<VideoClip>(remoteUrl, (err, video: VideoClip) => {
            if (err) {
                this.node.destroy();
                return;
            }

            if (!this.videoComp) this.videoComp = this.getComponent(VideoPlayer)!;
            this.videoComp.clip = video;
            this._clip = this.videoComp.clip;
            // @ts-ignore
            this._video = this.videoComp._impl._video;
            this._texture.image = this._image;

            this._initializeBrowser();
            if (this._video) {
                this._updateVideoSource();
            }
        });
    }

    // 视频会因为 active = false 终止
    override onEnable(): void {
        if (this._loaded && this._currentState == VideoState.PLAYING) {
            this.play();
        }
    }
    /**
     * 处理视频资源
     */
    private _updateVideoSource() {
        let url = '';

        if (this._clip) {
            url = this._clip.nativeUrl;
        }
        if (url && loader.md5Pipe) {
            url = loader.md5Pipe.transformURL(url);
            // LogUtil.log('_updateVideoSource', url);
        }

        this._loaded = false;
        this._video.pause();
        this._video.src = url;

        this.node.emit('preparing', this);
    }

    override update(deltaTime: number) {
        this._time += deltaTime;
        if (this._time < 0.032 || !this._loaded) {
            return;
        } else {
            this._time = 0;
        }
        this.updateTexture();
        // 解决循环卡帧
        if (this.loop && this._video.currentTime > this._video.duration - 0.15) {
            this._onCompleted();
        }
    }
    /**
     * 重置贴图状态
     * @param texture 贴图
     * @param width 宽
     * @param height 高
     */
    private _resetTexture(texture: Texture2D, width: number, height: number, format?: number) {
        texture.setFilters(Texture2D.Filter.LINEAR, Texture2D.Filter.LINEAR);
        texture.setMipFilter(Texture2D.Filter.LINEAR);
        texture.setWrapMode(Texture2D.WrapMode.CLAMP_TO_EDGE, Texture2D.WrapMode.CLAMP_TO_EDGE);

        texture.reset({
            width: width,
            height: height,
            //@ts-ignore
            format: format ? format : gfx.Format.RGB8,
        });
    }

    /**
     * initialize browser player, register video event handler
     */
    private _initializeBrowser(): void {
        // @ts-ignore
        // this._video = this.VideoView._impl._video;
        this._video.style.zIndex = '-1';
        this._video.setAttribute('x5-video-player-type', 'true');
        this._video.setAttribute('playsinline', 'true');
        this._video.crossOrigin = 'anonymous';
        this._video.autoplay = true;
        this._video.loop = false;
        this._video.muted = true;

        this._video.addEventListener('loadedmetadata', () => this._onMetaLoaded());
        this._video.addEventListener('ended', () => this._onCompleted());
        this._loaded = false;
        let onCanPlay = () => {
            if (this._loaded || this._currentState == VideoState.PLAYING) return;
            if (this._video.readyState === ReadyState.HAVE_ENOUGH_DATA || this._video.readyState === ReadyState.HAVE_METADATA) {
                this._video.currentTime = 0;
                this._loaded = true;
                this._onReadyToPlay();
                // setTimeout(() => this._video.play(), 20)
            }
        };
        this._video.addEventListener('canplay', onCanPlay);
        this._video.addEventListener('canplaythrough', onCanPlay);
        this._video.addEventListener('suspend', onCanPlay);
    }
    private _onMetaLoaded() {
        // this._video.loop = this.loop;
        this.node.emit('loaded', this);
    }
    private _onReadyToPlay() {
        // this._updatePixelFormat();
        this._currentState = VideoState.PREPARED;

        if (this._seekTime > 0.1) {
            this.currentTime = this._seekTime;
        }
        this._resetTexture(this._texture, this._video.videoWidth, this._video.videoHeight);

        this.updateTexture();
        this.node.emit('ready', this);
        this._video.play();
        this._currentState = VideoState.PLAYING;
        this.useSprite();
    }

    private useSprite() {
        let useToSprite = this.getComponent(Sprite);
        // this.SpRect =
        if (useToSprite)
            this.scheduleOnce(() => {
                let sf = new SpriteFrame();
                sf.texture = this._texture;

                let sourceWidth = this._texture.width;
                let sourceHeight = this._texture.height;
                // let rate = this.rate.x / this.rate.y;
                let rect = new Rect();
                sf.packable = false;
                if (this.isHorizontal) {
                    rect.set(0, 0, Math.floor((sourceWidth * this.rate.x) / (this.rate.x + this.rate.y) - 1), sourceHeight);
                } else {
                    rect.set(0, 0, sourceWidth, Math.floor((sourceHeight * this.rate.x) / (this.rate.x + this.rate.y) - 1));
                }
                sf.rect = rect;
                useToSprite.spriteFrame = sf;
                useToSprite.customMaterial = this.spriteMaterial;
                useToSprite.getMaterialInstance(0)!.setProperty('TexSize', v2(sourceWidth, sourceHeight));
            });
    }

    private _onCompleted() {
        // LogUtil.log("play _onCompleted")
        if (this.loop) {
            if (this._currentState == VideoState.PLAYING) {
                this.currentTime = 0;
                this._video.currentTime = 0;
                this._video.play();
            }
        } else {
            this._currentState = VideoState.COMPLETED;
            this.node.emit('completed', this);
            this.node.destroy();
            // EventHandler.emitEvents(this.videoPlayerEvent, this, EventType.COMPLETED);
        }
    }

    updateTexture() {
        if (this._isInPlaybackState()) {
            // @ts-ignore
            this._texture.uploadData(this._video);
            // 2.x
            // this._updateGLTexture(this._texture, _video);
            /**
             * 更新材质
             */
            this.spriteMaterial.setProperty('mainTexture', this._texture);
        }
    }

    play() {
        if (this._video) this._video.play();
    }

    _updateGLTexture(texture: Texture2D, video: HTMLVideoElement) {
        // @ts-ignore
        const gl: WebGLRenderingContext = this._gl || texture._gfxDevice._context;
        // @ts-ignore
        const glTexture = texture._gfxTexture.gpuTexture.glTexture;
        gl.bindTexture(gl.TEXTURE_2D, glTexture);
        gl.texImage2D(gl.TEXTURE_2D, 0, gl.RGBA, gl.RGBA, gl.UNSIGNED_BYTE, video);
    }

    private _isInPlaybackState() {
        return !!this._video && this._currentState != VideoState.IDLE && this._currentState != VideoState.PREPARING && this._currentState != VideoState.ERROR;
    }
}
