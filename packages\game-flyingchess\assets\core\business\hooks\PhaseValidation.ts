/**阶段验证 */

import store from '../store'
export function PhaseValidation(phase: any) {
    return function (target: any, key: string, descriptor: PropertyDescriptor) {
        const originalMethod = descriptor.value

        descriptor.value = function (...args: any[]) {
            const { game } = store
            // if (game.state == phase) {
            //     // 执行原始方法
            //     return originalMethod.apply(this, args);
            // } else {
            //     // 当前阶段与操作不匹配
            //     window.ccLog('PhaseValidation 验证不通过')
            // }
        }

        return descriptor
    }
}
