/**
 * @describe 层级界面管理
 * <AUTHOR>
 * @date 2023-08-03 20:02:45
 */

import {
    director,
    instantiate,
    error,
    log,
    warn,
    isValid,
    Widget,
    Tween,
    Prefab,
    Canvas,
    Layers,
    Scene,
} from 'cc'

import { Gui } from './Gui'
import { Manager } from '../index'
import { BaseManager } from '../BaseManager'
import SceneLayer from './scene/SceneLayer'
import UILayer from './layer/UILayer'

type UIComponentType<T> = T extends UILayer<infer U> ? U : void

type SceneComponentType<T> = T extends SceneLayer<infer U> ? U : void

/**预制体路径 */
export enum BasePrefab {
    Root = 'prefabs/core/base/root',
    Toast = 'prefabs/core/base/toast',
    BlackMask = 'prefabs/core/base/black-mask',
    Loading = 'prefabs/core/base/loading',
    Notice = 'prefabs/core/base/notice',
    Reconnection = 'prefabs/core/base/reconnection',
}

// 切换场景界面的时候 需要清除UI层

export class GuiManager extends BaseManager {
    /**常驻顶层UI节点层级 */
    private _gui: Gui

    get gui(): Gui {
        return this._gui
    }

    set gui(v: Gui) {
        this._gui = v
    }

    /**
     * 获取预制体
     * @param type 类型
     * @returns
     */
    private getGuiPrefabByType = (type: BasePrefab) => {
        return new Promise<Prefab>((resolve, reject) => {
            this.cat.res.load<Prefab>(
                type,
                (err: Error | null, prefab: Prefab) => {
                    if (err) {
                        reject(err)
                    } else {
                        resolve(prefab)
                    }
                }
            )
        })
    }

    /**初始化 */
    async init() {
        // 初始化常驻节点
        const root_prefab = await this.getGuiPrefabByType(BasePrefab.Root)
        const root = instantiate(root_prefab)
        this.gui = root.getComponent(Gui)!.init(this.cat)
        director.addPersistRootNode(root)

        // 更新场景
        return this
    }

    // /**初始化通知 */
    // async initNotice() {
    //     // const res = await this.getNotice()
    //     // this.noticeNode = instantiate(res)
    //     // debugger
    //     // director.addPersistRootNode(res);
    // }

    // // private async creatBlackNode() {
    // //     const blockMaskPrefab = await this.getBlackNode();
    // //     this.blockMask = instantiate(blockMaskPrefab);
    // //     this.ui_container.addChild(this.blockMask);
    // //     this.blockMaskSiblingIndexChanged();
    // // }

    // /**清空所有 */
    // clearAll() { }

    // /**清空UI */
    // clearUI() {
    //     this.ui_container.removeAllChildren()
    //     this.ui_container.destroyAllChildren()
    //     this.blockMask = null
    // }

    // /**
    //  * 打开ui层
    //  * @param ui 界面的节点或组件
    //  * @param options 动画参数选项
    //  */

    // async open<T>(ui: Node, options?: IOpenOptions<UIComponentType<T>>, uiType: LayerType = LayerType.UI) {
    //     const { rootNode, component } = this.findUIBaseLayer(ui, true);
    //     const uilayer = rootNode.getComponent(UILayer)
    //     if (uilayer && options) {
    //         for (const key in options) {
    //             uilayer[key] = options[key];
    //         }
    //     }
    //     if (rootNode) {
    //         if (!this.blockMask) {
    //             await this.creatBlackNode()
    //         }
    //         // 将节点挂载到遮罩的tweenNode节点上
    //         this.blockMask.getChildByName('tweenNode').addChild(rootNode);
    //         // 添加层级遮罩
    //         this.add();
    //     }

    //     // 运动结束之后，添加界面到UI层
    //     [this.ui_container, this.loading_container, this.notice_container, this.reconnect_container, this.toast_container][uiType].addChild(rootNode);

    //     if (component) {
    //         // 调用uiNode组件下的showTween方法
    //         await component.showTween(options || {});
    //     }
    //     component.updateProps(options?.props)

    // }
    // /**
    //  * 关闭ui层
    //  * @param ui 界面的节点或组件
    //  * @param options 动画参数选项
    //  */
    // async close<T extends UILayer<any>>(ui: Node | T, options?: ICloseOptions<T>) {
    //     const { rootNode, component } = this.findUIBaseLayer(ui, false);

    //     // if (rootNode) {
    //     //     // 添加界面到UI层
    //     //     this.ui_container.addChild(rootNode)
    //     // }
    //     if (component) {
    //         if (options) {
    //             for (const key in options) {
    //                 component[key] = options[key];
    //             }
    //         }
    //         // 调用uiNode组件下的showTween方法
    //         await component.hideTween<T>(options || {});
    //         // 移除 层级遮罩
    //         this.sub();
    //     }
    // }

    // /**获取顶部ui */
    // private getTopUIBytype(uiType: LayerType = LayerType.UI) {
    //     const currentLayer = [this.ui_container, this.loading_container, this.notice_container, this.reconnect_container, this.toast_container][uiType]
    //     const uiList = currentLayer.children
    //     const lastNode = uiList[uiList.length - 1]
    //     return lastNode
    // }

    // /**替换顶部UI */
    // async openUIWithPop<T>(ui: Node, options?: IOpenOptions<UIComponentType<T>>, uiType: LayerType = LayerType.UI) {
    //     // 获取顶部ui
    //     const lastNode = this.getTopUIBytype(uiType)
    //     window.ccLog('顶部UI:', lastNode)
    //     if (lastNode && lastNode !== this.blockMask) await this.close(lastNode)
    //     this.open(ui, options, uiType)
    // }

    // private findUIBaseLayer<T extends UILayer<any>>(ui: Node | T, isOpen: boolean): { rootNode: Node; component: T } {
    //     let rootNode: Node = null;
    //     let component: T = null;
    //     if (ui instanceof Node) {
    //         // ui为节点
    //         rootNode = ui;
    //         // 获取组件
    //         const extendsBaseLayer = ui.components.filter(item => {
    //             return (item instanceof UILayer);
    //         }) as T[];
    //         if (extendsBaseLayer.length) {
    //             if (extendsBaseLayer.length == 1) {
    //                 component = extendsBaseLayer[0];
    //             } else {
    //                 warn(`${ui.name}节点存在多个继承自BaseLayer的组件`);
    //             }
    //         } else {
    //             error(`${ui.name}节点未找到继承自BaseLayer的组件`);
    //             return { rootNode, component };
    //         }
    //     } else {
    //         // ui为T组件
    //         // 定义一个变量用于保存根节点
    //         rootNode = ui.node;
    //         component = ui;
    //         if (isOpen) {
    //             // 循环向上查找根节点，直到找到没有父节点的节点
    //             while (rootNode.parent) {
    //                 rootNode = rootNode.parent;
    //             }
    //             component.root = rootNode;
    //         } else {
    //             rootNode = ui.root;
    //         }
    //     }
    //     return { rootNode, component };
    // }

    // /**显示loading动画 */
    // async showLoading({ title = '', mask = true, black = true } = {}) {
    //     if (!isValid(this.loading_container)) return
    //     if (this.loading_container.children.length > 0) {
    //         const sub = this.loading_container.getComponentInChildren(ShowLoading)
    //         if (sub) {
    //             sub.updateProps({ title, mask, black })
    //         }
    //         return
    //     }

    //     const res = await this.getLoading()
    //     const node = instantiate(res)
    //     const comp = node.getComponent(ShowLoading)
    //     comp.addToParent(this.loading_container, { props: { title, mask, black } })
    // }

    // /**隐藏loading动画 */
    // hideLoading() {
    //     if (!isValid(this.loading_container)) return
    //     if (this.loading_container.children.length > 0) {
    //         this.loading_container.removeAllChildren()
    //     }
    // }

    // /**loading是否存在 */
    // hasLoading() {
    //     return !!this.loading_container?.children?.length
    // }

    // /**显示toast动画 */
    // async showToast({ title = '', type = ToastType.SLIDE, time = 1 }: { title: string, type?: ToastType, time?: number }) {
    //     if (!isValid(this.toast_container)) return
    //     // 获取toast预制体
    //     let prefab = res.ResLoader.get<Prefab>(BasePrefab.Toast);
    //     if (!prefab) {
    //         await new Promise<void>((resolve, reject) => {
    //             res.ResLoader.load<Prefab>(BasePrefab.Toast, (err: Error, data: Prefab) => {
    //                 if (err) {
    //                     error(err);
    //                     return
    //                 }
    //                 prefab = data;
    //                 resolve();
    //             });
    //         });
    //     }
    //     const toastNode = instantiate(prefab);
    //     const toast = toastNode.getComponent(Toast)
    //     toast.addToParent(this.toast_container, {
    //         props: {
    //             title, type, time
    //         }
    //     });
    //     await toast.toast()
    // }
    // /**隐藏toast动画 */
    // hideToast() {
    //     if (!isValid(this.toast_container)) return
    //     this.toast_container.children.forEach(item => {
    //         Tween.stopAllByTarget(item);
    //     });
    //     this.toast_container.removeAllChildren();
    // }

    // private createContainer(name: string): Node {
    //     // 适配组件
    //     const node = new Node(name);
    //     const widget = node.addComponent(Widget);
    //     widget.isAlignLeft = widget.isAlignRight = widget.isAlignTop = widget.isAlignBottom = true;
    //     widget.top = widget.bottom = widget.left = widget.right = 0;
    //     widget.alignMode = Widget.AlignMode.ON_WINDOW_RESIZE;
    //     return node;
    // }

    // /**获取黑色遮罩 */
    // private async getBlackNode(): Promise<Prefab> {
    //     return new Promise((resolve, reject) => {
    //         if (!this.blockMaskPrefab) {
    //             res.ResLoader.load<Prefab>(BasePrefab.BlackMask, (err: Error, prefab: Prefab) => {
    //                 if (err) {
    //                     reject(err);
    //                 } else {
    //                     this.blockMaskPrefab = prefab;
    //                     resolve(this.blockMaskPrefab);
    //                 }
    //             });
    //         } else {
    //             resolve(this.blockMaskPrefab);
    //         }
    //     });
    // }

    // /**获取loading */
    // private async getLoading() {
    //     return new Promise<Prefab>((resolve, reject) => {
    //         if (this.loadingPrefab) {
    //             resolve(this.loadingPrefab);
    //         } else {
    //             res.ResLoader.load<Prefab>(BasePrefab.Loading, (err: Error, prefab: Prefab) => {
    //                 if (err) {
    //                     reject(err);
    //                 } else {
    //                     this.loadingPrefab = prefab;
    //                     resolve(this.loadingPrefab);
    //                 }
    //             });
    //         }
    //     });
    // }
    // /**增加层级 */
    // add() {
    //     const last = this.ui_container.children[this.ui_container.children.length - 1];
    //     last && this.blockMask?.setSiblingIndex(last.getSiblingIndex());
    //     this.blockMaskSiblingIndexChanged();
    // }
    // /**减少层级 */
    // sub() {
    //     const last = this.ui_container.children[this.ui_container.children.length - 2];
    //     last && this.blockMask?.setSiblingIndex(last.getSiblingIndex());
    //     this.blockMaskSiblingIndexChanged();
    // }

    // blockMaskSiblingIndexChanged() {
    //     if (!this.blockMask) return
    //     this.blockMask.active = this.ui_container.children.length > 1 || this.blockMask.getChildByName('tweenNode').children.length > 1;
    // }

    // /**获取 notice */
    // private async getNotice() {
    //     return new Promise<Prefab>((resolve, reject) => {
    //         if (this.noticePrefab) {
    //             resolve(this.noticePrefab);
    //         } else {
    //             res.ResLoader.load<Prefab>(BasePrefab.Notice, (err: Error, prefab: Prefab) => {
    //                 if (err) {
    //                     reject(err);
    //                 } else {
    //                     this.noticePrefab = prefab;
    //                     resolve(this.noticePrefab);
    //                 }
    //             });
    //         }
    //     });
    // }

    // /**显示notice动画 */
    // async showNotice(props: NoticeProps) {
    //     const res = await this.getNotice()
    //     this.noticeNode = instantiate(res)
    //     this.open<Notice>(this.noticeNode, { props }, LayerType.NOTICE)
    // }

    // /**更新notice */
    // updateNotice(props: NoticeProps) {
    //     if (!this.noticeNode) return console.error('notice不存在 请先调用showNotice')
    //     const notice = this.noticeNode.getComponent(Notice)
    //     notice.updateProps(props)
    // }

    // /**隐藏notice */
    // hideNotice() {
    //     this.close(this.noticeNode)
    // }

    // /**显示断线重连 */
    // async showReconnect(type: ReconnectPrompt) {
    //     if (!isValid(this.reconnect_container)) return
    //     if (this.reconnect_container.children.length > 0) {
    //         this.reconnectionNode.getComponent(Reconnection).updateProps(type)
    //         return
    //     }
    //     // 清理场景
    //     // this.ui_container.active = false
    //     this.loading_container.active = this.notice_container.active = false
    //     const res = await this.getReconnect()
    //     this.reconnectionNode = instantiate(res)
    //     this.open<Reconnection>(this.reconnectionNode, { props: type, isMotion: false }, LayerType.RECONNECTTION)
    // }

    // hasReconnect() {
    //     return this.reconnect_container?.children?.length > 0
    // }

    // /**隐藏断线重连 */
    // hideReconnect() {
    //     if (!isValid(this.reconnectionNode)) return
    //     this.close(this.reconnectionNode)
    // }

    // /**获取 reconnect */
    // private async getReconnect() {
    //     return new Promise<Prefab>((resolve, reject) => {
    //         if (this.reconnectionPrefab) {
    //             resolve(this.reconnectionPrefab);
    //         } else {
    //             res.ResLoader.load<Prefab>(BasePrefab.Reconnection, (err: Error, prefab: Prefab) => {
    //                 if (err) {
    //                     reject(err);
    //                 } else {
    //                     this.reconnectionPrefab = prefab;
    //                     resolve(this.reconnectionPrefab);
    //                 }
    //             });
    //         }
    //     });
    // }
}
