import {
    _decorator,
    Node,
    Label,
    ProgressBar,
    log,
    Prefab,
    Game,
    AudioClip,
    error,
} from 'cc'

import BaseLoading from '@/core/business/loading/BaseLoading'
import { cat } from '@/core/manager'
import EnterGame from '@/core/business/hooks/EnterGame'
import { DEBUG, EDITOR } from 'cc/env'
import { ReconnectPrompt } from '@/core/ui/reconnection/Reconnection'
import {
    GameCloseType,
    JSBridgeClient,
} from '@/core/business/jsbridge/JSBridge'
import store, { Store } from '@/core/business/store'

const { ccclass, property } = _decorator

@ccclass('Loading')
export class Loading extends BaseLoading {
    // @property({ type: Label, tooltip: '进度文本' })
    // progressLabel: Label = null!;

    @property({ type: Node, tooltip: '进度条' })
    progress: Node = null!

    private maxPro: number = 0

    override async init() {
        const url_params = store.global.url_params

        const promises: Promise<any>[] = [this.loadRes()]
        Promise.all(promises).then(
            async () => {
                if (!EDITOR && url_params.params) {
                    await EnterGame()
                    store.game.isParamsFromUrl = true
                    store.game.gameLoadFinished = true
                    cat.gui.hideLoading()
                }

                // 进入游戏场景
                cat.gui.loadScene('game')
            },
            (err) => {
                error(err)
                JSBridgeClient.closeGame(
                    GameCloseType.JoinOverTime,
                    JSON.stringify({ err, info: '加载资源错误' })
                )
            }
        )
    }

    /** 加载初始游戏内容资源 */
    private async loadRes() {
        /** 加载进度事件 */
        const onProgressCallback = (finished: number, total: number) => {
            var progress = finished / total
            if (this.maxPro < progress) {
                this.maxPro = progress
            }
            this.progress.getComponent(ProgressBar)!.progress = this.maxPro
            // this.progressLabel.string = `加载中...${~~(this.maxPro * 100)}%`;
        }
        cat.tracking.loading.loadingProgress(0)
        await new Promise<void>((resolve) => {
            cat.res.loadDir('prefabs/', Prefab, onProgressCallback, () => {
                cat.tracking.loading.loadingProgress(1)
                resolve()
            })
        })
    }
}
