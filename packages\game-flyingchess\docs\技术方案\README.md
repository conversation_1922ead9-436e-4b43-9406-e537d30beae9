# 飞行棋技术方案文档

## 1. 项目概述

### 1.1 项目背景

飞行棋是一款经典的多人棋盘游戏，玩家通过掷骰子决定棋子移动步数，最先将所有棋子移动到终点的玩家获胜。本项目旨在使用 Cocos Creator 开发一款在线多人飞行棋游戏，支持 2-4 名玩家同时游戏，并提供观战功能。

### 1.2 项目目标

-   实现经典飞行棋游戏的核心玩法
-   支持 2-4 名玩家在线对战
-   支持观战模式
-   支持托管和断线重连功能
-   提供流畅的游戏体验和精美的视觉效果
-   机器人陪玩支持

### 1.3 技术选型

-   游戏引擎：Cocos Creator 3.8.5
-   开发语言：TypeScript
-   网络通信：WebSocket
-   状态管理：自定义 Store
-   构建工具：Cocos Creator 内置构建工具

## 2. 系统架构

### 2.1 整体架构

飞行棋游戏采用客户端-服务器架构，客户端负责游戏界面渲染和用户交互，服务器负责游戏逻辑处理和状态同步。

#### 2.1.1 客户端架构

客户端采用模块化设计，主要分为以下几个模块：

-   **Core 模块**：核心功能模块，包含基础组件和工具

    -   Manager：核心管理层，提供音频、事件、GUI、网络请求等基础功能
    -   Business：业务逻辑层，包含 API 调用、状态管理、WebSocket 通信等
    -   Components：Cocos 组件层，提供可跨游戏复用的 Cocos 组件
    -   UI：界面层，负责游戏界面的展示

-   **Scene 模块**：场景管理模块，位于 packages\game-flyingchess\assets\script\scene 目录下
    -   start-scene：游戏启动场景，负责初始化框架和加载资源
    -   loading：加载场景，负责资源预加载和进度展示
    -   game：游戏主场景，包含以下子模块：
        -   components：游戏组件，如棋盘(Desk)、棋子(Chess)、骰子(Dice)等
        -   ui：游戏界面，如玩家信息、游戏状态、对话框等

### 2.2 通信架构

客户端和服务器之间通过 WebSocket 进行实时通信，使用 Protocol Buffers 作为数据序列化格式。主要的通信流程包括：

1. 客户端连接服务器
2. 客户端发送加入游戏请求
3. 所有玩家加入游戏后（或者到达超时时间后），游戏开局，服务端给所有玩家发送战局初始化消息
4. 战局开始后，客户端初始化游戏主界面
5. 客户端接受服务端的消息来更新游戏的状态，同时发送玩家的操作给服务器
6. 游戏结束后，服务器发送游戏结果

#### 2.2.1 消息类型

根据 `message.proto` 定义，系统支持以下主要消息类型：

1. **游戏基础相关消息**

    - `MESSAGE_TYPE_GAME_INFO_BROADCAST`：游戏所有信息广播
    - `MESSAGE_TYPE_STATE_CHANGED_BROADCAST`：游戏状态变更广播
    - `MESSAGE_TYPE_GAME_OVER_BROADCAST`：游戏结束广播

2. **玩家操作相关消息**

    - `MESSAGE_TYPE_ROLL_DICE_REQUEST/RESPONSE`：投掷骰子请求/响应
    - `MESSAGE_TYPE_SELECT_CHESS_REQUEST/RESPONSE`：选择棋子请求/响应

#### 2.2.2 状态同步机制

游戏采用状态同步机制，服务器作为权威数据源，客户端根据服务器的状态更新本地状态：

1. 服务器定期广播游戏状态（`GameInfoBroadcast`）
2. 当游戏状态发生变化时，服务器发送状态变更广播（`StateChangedBroadcast`）
3. 客户端根据接收到的状态更新本地游戏状态
4. 客户端发送操作请求（如投掷骰子、选择棋子等）
5. 服务器处理请求，更新游戏状态，并广播新状态

## 3. 核心功能模块

### 3.1 游戏初始化模块

#### 3.1.1 功能描述

负责游戏的初始化工作，包括资源加载、场景切换、玩家分配等。

#### 3.1.2 实现方案

-   使用 Cocos Creator 的资源加载系统加载游戏资源
-   使用 WebSocket 连接服务器并获取游戏初始化数据
-   根据服务器分配的座位信息初始化玩家位置
-   根据玩家位置调整棋盘视角，确保当前玩家的起点位于左下角

### 3.2 棋盘模块

#### 3.2.1 功能描述

棋盘是游戏的核心组件，包含起点、终点、常规格子、特殊格子等元素。

#### 3.2.2 实现方案

-   使用 2D 精灵和图集实现棋盘，确保视觉效果良好
-   定义格子数据结构，包含位置、类型、效果等信息
-   实现格子之间的连接关系，支持棋子的移动路径计算
-   支持棋盘旋转，确保当前玩家的视角始终一致

### 3.3 棋子模块

#### 3.3.1 功能描述

棋子是玩家操作的对象，每个玩家有四个棋子，需要将所有棋子移动到终点才能获胜。

#### 3.3.2 实现方案

-   使用 2D 精灵实现棋子，不同玩家的棋子使用不同颜色区分
-   定义棋子数据结构，包含所属玩家、当前位置、状态等信息
-   实现棋子的移动动画，包括普通移动、跳跃、飞行等
-   处理棋子之间的交互，如撞子、迭子等

### 3.4 骰子模块

#### 3.4.1 功能描述

骰子用于决定棋子的移动步数，玩家需要点击骰子进行投掷。

#### 3.4.2 实现方案

-   使用 2D 精灵序列帧动画实现骰子，支持投掷动画
-   实现骰子点数的随机生成，根据不同场景调整点数的权重
-   处理投掷结果，根据点数决定可执行的操作
-   支持连投奖励，如投掷到 6 点可以再投一次

### 3.5 游戏逻辑模块

#### 3.5.1 功能描述

游戏逻辑模块负责处理游戏的核心规则，包括回合流转、棋子移动、特殊效果等。

#### 3.5.2 实现方案

-   实现回合系统，确保玩家按照顺序进行操作
-   处理棋子移动规则，包括起飞、行进、跳子、飞棋等
-   实现特殊判定，如迭子、撞子等
-   处理游戏结束条件和排名规则

### 3.6 网络通信模块

#### 3.6.1 功能描述

网络通信模块负责客户端和服务器之间的数据交换，确保游戏状态的同步。

#### 3.6.2 实现方案

-   使用 WebSocket 建立实时通信连接
-   使用 Protocol Buffers 序列化数据，减少传输量
-   实现断线重连机制，确保游戏的连续性
-   处理网络延迟和丢包问题，确保游戏体验

### 3.7 UI 模块

#### 3.7.1 功能描述

UI 模块负责游戏界面的展示和用户交互，包括玩家信息、游戏状态、操作按钮等。

#### 3.7.2 实现方案

## 4. 游戏流程

### 4.1 游戏战局初始化阶段

1. 玩家进入游戏，服务器分配座次
2. 根据座次确定回合流转顺序
3. 根据玩家位置调整棋盘视角
4. 玩家的四个棋子放置在起点

### 4.2 游戏回合流程

1. 当前回合玩家投掷骰子
2. 根据骰子点数执行棋子操作
    - 如果点数为 6 且有棋子在起点，可以选择起飞
    - 如果有棋子在棋盘上，可以选择移动棋子
3. 执行特殊操作
    - 处理格子效果
    - 处理棋子交互（撞子、迭子等）
4. 判断回合是否结束
    - 如果投掷到 6 点，可以再投一次
    - 如果没有可操作的棋子，回合结束
5. 判断是否达成胜利条件
    - 如果达成，游戏结束
    - 如果未达成，进入下一个玩家的回合

### 4.3 游戏结束

1. 判断游戏结束条件：某玩家四个棋子都进入终点
2. 计算其他玩家的排名
    - 到达终点棋子数量
    - 已经起飞的棋子数量
    - 棋子距离终点的格子数
    - 被撞回起点的次数
    - 座位顺序
3. 显示游戏结束界面，展示排名和得分
4. 提供再来一局或返回大厅的选项

## 5. 技术实现细节

### 5.1 棋盘和格子实现

棋盘由多种类型的格子组成，包括：

-   起点格子：每个玩家的起始位置，存放未起飞的棋子
-   起始格子：棋子起飞后的第一个格子
-   常规格子：棋盘外围的普通格子
-   特殊格子：包含特殊效果的格子，如转向格子、飞棋起点等
-   冲刺格子：通向终点的格子
-   终点格子：棋子的最终目标

每个格子都有唯一的 ID 和坐标，以及与其他格子的连接关系。格子之间的连接关系决定了棋子的移动路径。

### 5.2 棋子移动实现

棋子移动是游戏的核心交互，采用服务端权威的架构设计：

#### 5.2.1 服务端处理

服务端负责所有游戏逻辑的计算和判定：

-   **路径计算**：根据骰子点数和棋子当前位置，计算完整的移动路径
-   **规则判定**：处理各种移动规则，包括：
    -   起飞：当投掷到 6 点时，可以将起点的棋子移动到起始格子
    -   普通移动：根据骰子点数，沿着预定路径移动棋子
    -   跳子：当棋子停在与自己颜色相同的格子上时，跳到下一个相同颜色的格子
    -   飞棋：当棋子停在带有飞机标识的格子上时，沿着虚线飞到指定格子
-   **特殊效果判定**：检测和处理特殊效果，如撞子、迭子等
-   **冲突处理**：处理多个棋子之间的交互和冲突

#### 5.2.2 客户端执行

客户端接收服务端返回的移动路径数据，负责动画播放和视觉呈现：

-   **路径解析**：解析服务端返回的移动路径数组
-   **动画播放**：根据路径数据播放棋子移动动画
-   **事件动画**：根据特殊效果类型播放对应的事件动画（撞子、飞棋等）
-   **状态更新**：更新本地棋子和棋盘的显示状态

#### 5.2.3 数据流程

1. 玩家选择棋子后，客户端发送选择请求给服务端
2. 服务端计算移动路径和特殊效果，返回详细的移动数据
3. 客户端接收数据，按照路径数组逐步播放移动动画
4. 动画播放完成后，客户端更新最终状态

### 5.3 特殊判定实现

游戏中有多种特殊判定，采用服务端权威处理：

#### 5.3.1 服务端判定

所有特殊判定逻辑都在服务端处理：

-   **迭子判定**：当多个棋子叠在一起时，形成迭子，服务端处理特殊移动规则
-   **撞子判定**：当棋子移动到有其他玩家棋子的格子时，服务端判定撞子效果，将对方棋子撞回起点
-   **连投判定**：当投掷到 6 点或满足其他条件时，服务端判定是否可以再投一次骰子
-   **跳跃判定**：当棋子停在与自己颜色相同的格子上时，服务端计算跳跃目标位置
-   **飞棋判定**：当棋子停在带有飞机标识的格子上时，服务端计算飞行目标位置

#### 5.3.2 客户端表现

客户端根据服务端返回的特殊效果数据播放相应动画：

-   **撞子动画**：播放棋子碰撞和被撞棋子返回起点的动画
-   **迭子动画**：播放多个棋子叠加移动的动画效果
-   **跳跃动画**：播放棋子跳跃到目标位置的动画
-   **飞棋动画**：播放棋子沿着虚线飞行的动画效果
-   **连投提示**：显示玩家可以再次投掷骰子的提示

## 6. 游戏状态和数据结构

根据 proto 协议文件的定义，游戏状态和数据结构如下：

### 6.1 游戏状态（State）

游戏状态定义了游戏在不同阶段的状态，主要包括以下几类：

#### 6.1.1 游戏基础状态

-   `STATE_UNSPECIFIED`：未指定状态
-   `STATE_GAME_START`：游戏开始
-   `STATE_GAME_OVER`：游戏结束
-   `STATE_GAME_FINISHING`：游戏正在结束（结算中）
-   `STATE_GAME_FINISHED`：游戏已结束（结算完成）

#### 6.1.2 回合状态

-   `STATE_ROUND_START`：回合开始
-   `STATE_PLAYER_ROLL_DICE`：玩家投掷骰子
-   `STATE_PLAYER_ROLLED_DICE`：玩家投掷骰子完成
-   `STATE_PLAYER_SELECT_CHESS`：玩家选择棋子
-   `STATE_PLAYER_SELECTED_CHESS`：玩家选择棋子完成
-   `STATE_CHESS_MOVING`：棋子移动中
-   `STATE_CHESS_MOVED`：棋子移动完成
-   `STATE_SPECIAL_EFFECT`：特殊效果触发
-   `STATE_SPECIAL_EFFECT_FINISHED`：特殊效果完成
-   `STATE_ROUND_END`：回合结束

#### 6.1.3 托管和断线状态

-   `STATE_PLAYER_HOSTING`：玩家托管
-   `STATE_PLAYER_CANCEL_HOSTING`：玩家取消托管
-   `STATE_PLAYER_DISCONNECTED`：玩家断线
-   `STATE_PLAYER_RECONNECTED`：玩家重连

### 6.2 棋子状态（ChessState）

-   `CHESS_STATE_UNSPECIFIED`：未指定状态
-   `CHESS_STATE_IN_START`：在起点
-   `CHESS_STATE_ON_BOARD`：在棋盘上
-   `CHESS_STATE_IN_END`：在终点

### 6.3 格子类型（GridType）

-   `GRID_TYPE_UNSPECIFIED`：未指定类型
-   `GRID_TYPE_START`：起点格子
-   `GRID_TYPE_NORMAL`：普通格子
-   `GRID_TYPE_JUMP`：跳跃格子（与自己颜色相同的格子）
-   `GRID_TYPE_FLY`：飞行格子（飞机标记格子）
-   `GRID_TYPE_SPRINT`：冲刺格子（通向终点的格子）
-   `GRID_TYPE_END`：终点格子

### 6.4 特殊效果类型（SpecialEffectType）

-   `SPECIAL_EFFECT_UNSPECIFIED`：未指定类型
-   `SPECIAL_EFFECT_BUMP`：撞子（将对方棋子撞回起点）
-   `SPECIAL_EFFECT_STACK`：迭子（多个棋子叠在一起）
-   `SPECIAL_EFFECT_JUMP`：跳跃（跳到下一个相同颜色的格子）
-   `SPECIAL_EFFECT_FLY`：飞行（沿着虚线飞到指定格子）
-   `SPECIAL_EFFECT_ROLL_AGAIN`：连投（投掷到 6 点可以再投一次）

### 6.5 核心数据结构

#### 6.5.1 玩家（Player）

玩家数据结构包含以下主要字段：

-   `id`：用户 ID
-   `nickname`：用户昵称
-   `avatar_url`：用户头像 URL
-   `index`：玩家在战局中的索引（0-3，对应四个角落位置）
-   `color`：玩家颜色（0:红, 1:黄, 2:蓝, 3:绿）
-   `chess_list`：玩家棋子列表
-   `hosting`：是否系统托管
-   `ready`：是否已准备
-   `is_current`：是否是当前操作玩家
-   `finished`：是否已完成游戏（所有棋子到达终点）
-   `rank`：完成游戏的排名（1-4）

#### 6.5.2 棋子（Chess）

棋子数据结构包含以下主要字段：

-   `id`：棋子 ID
-   `player_index`：所属玩家索引
-   `color`：棋子颜色（0:红, 1:黄, 2:蓝, 3:绿）
-   `state`：棋子当前状态
-   `position`：棋子当前位置（格子 ID）
-   `selectable`：是否可选择（当前回合）
-   `selected`：是否被选中

#### 6.5.3 棋盘（Board）

棋盘数据结构包含以下主要字段：

-   `grids`：棋盘格子列表
-   `size`：棋盘尺寸

#### 6.5.4 格子（Grid）

格子数据结构包含以下主要字段：

-   `id`：格子 ID
-   `type`：格子类型
-   `color`：格子颜色（0:红, 1:黄, 2:蓝, 3:绿, -1:无色）
-   `position`：格子位置
-   `next_id`：下一个格子 ID
-   `effect_target_id`：特殊效果目标格子 ID（如跳跃、飞行等）
-   `chess_ids`：格子上的棋子列表

#### 6.5.5 骰子（Dice）

骰子数据结构包含以下主要字段：

-   `value`：骰子点数（1-6）
-   `rolling`：是否正在投掷中
-   `animation_duration`：投掷动画持续时间（毫秒）

#### 6.5.6 移动路径（MovePath）： Step[]

移动路径数据是一个 Step 数组，数组的每一项结构 Step 包含以下主要字段：

-   `chess_id`：棋子 ID
-   `start_grid_id`：起始格子 ID
-   `target_grid_id`：目标格子 ID
-   `path_grid_ids`：路径上的格子 ID 列表
-   `effect_type`：特殊效果类型
-   `effect_targets`：特殊效果目标（如被撞的棋子 ID）

**设计原则**：

-   **服务端权威**：棋子的移动路径逻辑完全由服务端处理，包括路径计算、特殊效果判定（撞子、飞棋、跳跃等）
-   **客户端执行**：服务端处理完成后，返回一个详细的移动路径数组给客户端
-   **数组结构**：数组的每一项标识棋子这一步要移动到哪个位置，以及是否触发特殊事件
-   **职责分离**：客户端不需要处理游戏规则判定逻辑，只需根据服务端返回的移动路径数据来执行棋子的行进动画和对应事件动画的播放

### 6.6 消息类型（MessageType）

游戏中的消息类型主要分为以下几类：

#### 6.6.1 游戏基础相关消息

-   `MESSAGE_TYPE_GAME_INFO_BROADCAST`：游戏所有信息广播
-   `MESSAGE_TYPE_STATE_CHANGED_BROADCAST`：游戏状态变更广播
-   `MESSAGE_TYPE_GAME_OVER_BROADCAST`：游戏结束广播

#### 6.6.2 玩家操作相关消息

-   `MESSAGE_TYPE_ROLL_DICE_REQUEST/RESPONSE`：投掷骰子请求/响应
-   `MESSAGE_TYPE_SELECT_CHESS_REQUEST/RESPONSE`：选择棋子请求/响应

## 7. 性能优化

### 7.1 资源加载优化

-   使用资源预加载，减少游戏中的加载时间
-   实现资源的按需加载，减少内存占用
-   优化资源大小，减少下载时间

### 7.2 渲染优化

-   使用图集（Sprite Atlas）技术，优化精灵渲染效率
-   优化材质和贴图，减少渲染开销
-   使用批处理技术，减少 DrawCall

### 7.3 内存优化

-   实现对象池，重用频繁创建和销毁的对象
-   及时释放不需要的资源
-   控制同时加载的资源数量

### 7.4 网络优化

-   减少消息频率，只发送必要的数据
-   压缩消息内容，减少传输量
-   实现预测和插值算法，减少延迟影响

## 8. 总结

飞行棋游戏是一款经典的多人棋盘游戏，本技术方案详细描述了使用 Cocos Creator 开发在线多人飞行棋游戏的技术实现方案。通过模块化设计和清晰的架构，确保游戏的可维护性和扩展性。同时，通过优化资源加载、渲染性能和网络通信，提供流畅的游戏体验。
