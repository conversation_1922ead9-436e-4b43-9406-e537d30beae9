// @generated by protoc-gen-es v2.2.5 with parameter "target=ts,import_extension=none,json_types=true"
// @generated from file task.proto (package protos, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv1";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file task.proto.
 */
export const file_task: GenFile = /*@__PURE__*/
  fileDesc("Cgp0YXNrLnByb3RvEgZwcm90b3MiSAoVVGFza0F3YXJkTm90aWZpY2F0aW9uEhIKCnRhc2tfY2xhc3MYASABKAkSDAoEdGlwcxgCIAEoCRINCgV2YWx1ZRgDIAEoBEIwCiRjb20uc3RudHMuY2xvdWQuc3VpbGV5b28uZWNoby5wcm90b3NaCC4vcHJvdG9zYgZwcm90bzM");

/**
 * 任务奖励通知
 *
 * @generated from message protos.TaskAwardNotification
 */
export type TaskAwardNotification = Message<"protos.TaskAwardNotification"> & {
  /**
   * 任务类型
   *
   * @generated from field: string task_class = 1;
   */
  taskClass: string;

  /**
   * 提示
   *
   * @generated from field: string tips = 2;
   */
  tips: string;

  /**
   * 奖励
   *
   * @generated from field: uint64 value = 3;
   */
  value: bigint;
};

/**
 * 任务奖励通知
 *
 * @generated from message protos.TaskAwardNotification
 */
export type TaskAwardNotificationJson = {
  /**
   * 任务类型
   *
   * @generated from field: string task_class = 1;
   */
  taskClass?: string;

  /**
   * 提示
   *
   * @generated from field: string tips = 2;
   */
  tips?: string;

  /**
   * 奖励
   *
   * @generated from field: uint64 value = 3;
   */
  value?: string;
};

/**
 * Describes the message protos.TaskAwardNotification.
 * Use `create(TaskAwardNotificationSchema)` to create a new message.
 */
export const TaskAwardNotificationSchema: GenMessage<TaskAwardNotification, TaskAwardNotificationJson> = /*@__PURE__*/
  messageDesc(file_task, 0);

