/**
 * @describe 平台接口
 * <AUTHOR>
 * @date 2023-08-02 19:48:12
 */

import { HTML5, WECHAT } from 'cc/env'
import { H5API } from './H5API'
import { WXAPI } from './WXAPI'
import { error, log } from 'cc'
import store from '../store'
import { CustomPlatform } from '../store/global'
import { DaiDaiAPI } from './DaiDaiAPI'
import { Manager } from '@/core/manager'
import { SuiLeYooH5API } from './SuiLeYooH5API'
import { SuiLeYooNativeAPI } from './SuiLeYooNativeAPI'

/**平台相关 */

// 扩展 Manager 接口，添加 Platform 属性
declare module '@/core/manager' {
    interface Manager {
        platform: MinimageAPI
    }
}

export class Platform {
    platform: MinimageAPI
    constructor(cat: Manager) {
        if (store.global.customPlatform == CustomPlatform.DaiDaiH5) {
            this.platform = new DaiDaiAPI(cat)
        } else if (store.global.customPlatform == CustomPlatform.SuiLeYoo) {
            this.platform = HTML5
                ? new SuiLeYooH5API(cat)
                : new SuiLeYooNativeAPI(cat)
        } else if (WECHAT) {
            this.platform = new WXAPI(cat)
        } else {
            error('默认H5平台运行环境')
            this.platform = new H5API(cat)
        }
    }
}
