import {
    _decorator,
    Node,
    Label,
    ProgressBar,
    log,
    Prefab,
    Game,
    AudioClip,
    error,
    Sprite,
    tween,
    Vec3,
    sp,
    UITransform,
} from 'cc'

import BaseLoading from '@/core/business/loading/BaseLoading'
import { cat } from '@/core/manager'
import { CardManager } from '@/core/manager/CardManager.b'
import EnterGame from '@/core/business/hooks/EnterGame'
import { DEBUG, EDITOR } from 'cc/env'
import { ReconnectPrompt } from '@/core/ui/reconnection/Reconnection'
import {
    GameCloseType,
    JSBridgeClient,
} from '@/core/business/jsbridge/JSBridge'
import store, { Store } from '@/core/business/store'
import { reflect } from '@bufbuild/protobuf/reflect'
import { CardInfoListRequestSchema } from '@/pb-generate/server/dixit/v1/handler_pb'
import { create } from '@bufbuild/protobuf'

const { ccclass, property } = _decorator

@ccclass('Loading')
export class Loading extends BaseLoading {
    // @property({ type: Label, tooltip: '进度文本' })
    // progressLabel: Label = null!;

    @property({ type: Node, tooltip: '进度条' })
    progress: Node = null!

    @property({ type: Sprite, tooltip: '加载中--加' })
    sprite_jia: Sprite = null!

    @property({ type: Sprite, tooltip: '加载中--载' })
    sprite_zai: Sprite = null!

    @property({ type: Sprite, tooltip: '加载中--中' })
    sprite_zhong: Sprite = null!

    @property({ type: sp.Skeleton, tooltip: '加载小人动画节点' })
    little_spine: sp.Skeleton = null!

    private maxPro: number = 0

    private little_spine_startX = 0

    override async init() {
        // 初始化加载动画
        this.initLoadingAnimation()
        cat.cardManager = CardManager.getInstance()
        const url_params = store.global.url_params

        const promises: Promise<any>[] = [this.loadRes()]
        Promise.all(promises).then(
            async () => {
                if (!EDITOR && url_params.params) {
                    await EnterGame()
                    store.game.isParamsFromUrl = true
                    store.game.gameLoadFinished = true
                    cat.gui.hideLoading()
                }

                // 进入游戏场景
                cat.gui.loadScene('game')
            },
            (err) => {
                error(err)
                JSBridgeClient.closeGame(
                    GameCloseType.JoinOverTime,
                    JSON.stringify({ err, info: '加载资源错误' })
                )
            }
        )
    }

    /** 加载初始游戏内容资源 */
    private async loadRes() {
        /** 加载进度事件 */
        const onProgressCallback = (finished: number, total: number) => {
            var progress = finished / total
            if (this.maxPro < progress) {
                this.maxPro = progress

                // 更新进度条
                this.progress.getComponent(ProgressBar)!.progress = this.maxPro

                //更新小人的位置
                this.updateLittleSpinePosition()
                // this.progressLabel.string = `加载中...${~~(this.maxPro * 100)}%`;
            }
        }
        this.little_spine_startX = this.little_spine.node.position.x

        cat.tracking.loading.loadingProgress(0)
        await new Promise<void>((resolve) => {
            cat.res.loadDir('prefabs/', Prefab, onProgressCallback, () => {
                cat.tracking.loading.loadingProgress(1)
                resolve()
            })
        })
    }

    private updateLittleSpinePosition() {
        // 根据加载进度更新little_spine的位置
        if (this.little_spine && this.little_spine.node) {
            // 获取进度条的宽度
            const progressUITransform = this.progress.getComponent(UITransform)
            if (progressUITransform) {
                const progressBarWidth = progressUITransform.width

                // 计算little_spine应该在的位置
                // 假设进度条是水平的，从左到右增长
                const startX = -progressBarWidth / 2 // 进度条最左侧的位置
                const endX = 391 // 进度条最右侧的位置

                // 根据当前进度计算x坐标
                const newX =
                    this.little_spine_startX + (endX - startX) * this.maxPro

                // console.log(
                //     `[updateLittleSpinePosition] 进度条宽度: ${progressBarWidth}, little_spine_startX: ${this.little_spine_startX}, 最右侧位置: ${endX}, 当前进度: ${this.maxPro}, 新位置: ${newX}`
                // )

                if (newX < this.little_spine_startX) return

                // 保持y和z坐标不变，只更新x坐标
                const currentPos = this.little_spine.node.position
                this.little_spine.node.setPosition(
                    newX,
                    currentPos.y,
                    currentPos.z
                )

                // console.log(
                //     `[updateLittleSpinePosition] 更新little_spine位置: ${newX}, 当前进度: ${this.maxPro}`
                // )
            }
        }
    }

    /**
     * 初始化加载动画，让三个精灵按照从左到右的顺序上下浮动
     */
    initLoadingAnimation() {
        window.ccLog('Loading-->initLoadingAnimation()')
        // 动画参数
        const moveDistance = 10 // 上下移动的距离
        const duration = 0.8 // 单次动画持续时间
        const delayBetweenSprites = 0.2 // 每个精灵开始动画的延迟时间

        // 为每个精灵创建上下浮动的动画
        this.createFloatingAnimation(
            this.sprite_jia.node,
            moveDistance,
            duration,
            0
        )
        this.createFloatingAnimation(
            this.sprite_zai.node,
            moveDistance,
            duration,
            delayBetweenSprites
        )
        this.createFloatingAnimation(
            this.sprite_zhong.node,
            moveDistance,
            duration,
            delayBetweenSprites * 2
        )
    }

    /**
     * 创建上下浮动的动画
     * @param node 要添加动画的节点
     * @param moveDistance 上下移动的距离
     * @param duration 单次动画持续时间
     * @param delay 开始动画的延迟时间
     */
    private createFloatingAnimation(
        node: Node,
        moveDistance: number,
        duration: number,
        delay: number
    ) {
        // 保存初始位置
        const startPos = node.position.clone()
        const upPos = new Vec3(
            startPos.x,
            startPos.y + moveDistance,
            startPos.z
        )
        const downPos = new Vec3(
            startPos.x,
            startPos.y - moveDistance,
            startPos.z
        )

        // 创建无限循环的上下浮动动画
        tween(node)
            .delay(delay) // 设置延迟，使三个精灵按顺序开始动画
            .to(duration / 2, { position: upPos }) // 向上移动
            .to(duration / 2, { position: downPos }) // 向下移动
            .to(duration / 2, { position: startPos }) // 回到原位
            .union()
            .repeatForever() // 无限循环
            .start()
    }
}
