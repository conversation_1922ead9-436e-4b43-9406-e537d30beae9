// @generated by protoc-gen-es v2.4.0 with parameter "target=ts,json_types=true"
// @generated from file dixit/v1/game.proto (package dixit.v1, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage } from "@bufbuild/protobuf/codegenv1";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv1";
import type { PlayerInfoMessage, PlayerInfoMessageJson } from "./player_pb";
import { file_dixit_v1_player } from "./player_pb";
import type { State, StateJson } from "./state_pb";
import { file_dixit_v1_state } from "./state_pb";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file dixit/v1/game.proto.
 */
export const file_dixit_v1_game: GenFile = /*@__PURE__*/
  fileDesc("ChNkaXhpdC92MS9nYW1lLnByb3RvEghkaXhpdC52MSLDBQoRR2FtZUluZm9Ccm9hZGNhc3QSEQoJYmF0dGxlX2lkGAEgASgJEjcKCWRlY2tfaW5mbxgCIAEoCzIkLmRpeGl0LnYxLkdhbWVJbmZvQnJvYWRjYXN0LkRlY2tJbmZvEjkKCnN0YXRlX2luZm8YAyABKAsyJS5kaXhpdC52MS5HYW1lSW5mb0Jyb2FkY2FzdC5TdGF0ZUluZm8SOQoKcm91bmRfaW5mbxgEIAEoCzIlLmRpeGl0LnYxLkdhbWVJbmZvQnJvYWRjYXN0LlJvdW5kSW5mbxowCghEZWNrSW5mbxIQCgh0aGVtZV9pZBgBIAEoBRISCgp0aGVtZV9uYW1lGAIgASgJGnYKCVN0YXRlSW5mbxIeCgVzdGF0ZRgBIAEoDjIPLmRpeGl0LnYxLlN0YXRlEhQKDGNvdW50ZG93bl9tcxgCIAEoAxIYChBzZXJ2ZXJfdGltZXN0YW1wGAMgASgDEhkKEXN0YXJ0ZWRfdGltZXN0YW1wGAQgASgDGsECCglSb3VuZEluZm8SEwoLcm91bmRfY291bnQYASABKAUSEwoLcm91bmRfdG90YWwYAiABKAUSHQoVY3VycmVudF9wbGF5ZXJfY3Vyc29yGAMgASgFEhUKDWN1cnJlbnRfc3RvcnkYBCABKAkSSgoOc2VsZWN0ZWRfY2FyZHMYBSADKAsyMi5kaXhpdC52MS5HYW1lSW5mb0Jyb2FkY2FzdC5Sb3VuZEluZm8uU2VsZWN0ZWRDYXJkGocBCgxTZWxlY3RlZENhcmQSDAoEY2FyZBgBIAEoCRIdChVzZWxlY3RlZF9wbGF5ZXJfaW5kZXgYAiABKAUSHAoUdm90ZWRfcGxheWVyX2luZGljZXMYAyADKAUSDQoFc2NvcmUYBCABKAUSHQoVc2NvcmVkX3BsYXllcl9pbmRpY2VzGAUgAygFIhIKEFJhbmtpbmdCcm9hZGNhc3QiPgoTQ2xpZW50RGF0YUJyb2FkY2FzdBIZChFmcm9tX3BsYXllcl9pbmRleBgBIAEoBRIMCgRkYXRhGAIgASgJIo8BChNTZXJ2ZXJEYXRhQnJvYWRjYXN0EhkKEWZyb21fcGxheWVyX2luZGV4GAEgASgFEjQKBmFjdGlvbhgCIAEoDjIkLmRpeGl0LnYxLlNlcnZlckRhdGFCcm9hZGNhc3QuQWN0aW9uEgwKBGRhdGEYAyABKAwiGQoGQWN0aW9uEg8KC1VOU1BFQ0lGSUVEEAAiIwoSU2VydmVyU2VsZWN0ZWREYXRhEg0KBWNhcmRzGAEgAygJInAKDFN5bmNSZXNwb25zZRIwCgtwbGF5ZXJfaW5mbxgBIAEoCzIbLmRpeGl0LnYxLlBsYXllckluZm9NZXNzYWdlEi4KCWdhbWVfaW5mbxgCIAEoCzIbLmRpeGl0LnYxLkdhbWVJbmZvQnJvYWRjYXN0IhMKEUdhbWVPdmVyQnJvYWRjYXN0YgZwcm90bzM", [file_dixit_v1_player, file_dixit_v1_state]);

/**
 * --- 公共 ---
 * 状态机的广播
 *
 * @generated from message dixit.v1.GameInfoBroadcast
 */
export type GameInfoBroadcast = Message<"dixit.v1.GameInfoBroadcast"> & {
  /**
   * 战局ID
   *
   * @generated from field: string battle_id = 1;
   */
  battleId: string;

  /**
   * Deck 牌的信息
   *
   * @generated from field: dixit.v1.GameInfoBroadcast.DeckInfo deck_info = 2;
   */
  deckInfo?: GameInfoBroadcast_DeckInfo;

  /**
   * 当前状态信息
   *
   * @generated from field: dixit.v1.GameInfoBroadcast.StateInfo state_info = 3;
   */
  stateInfo?: GameInfoBroadcast_StateInfo;

  /**
   * 回合信息
   *
   * @generated from field: dixit.v1.GameInfoBroadcast.RoundInfo round_info = 4;
   */
  roundInfo?: GameInfoBroadcast_RoundInfo;
};

/**
 * --- 公共 ---
 * 状态机的广播
 *
 * @generated from message dixit.v1.GameInfoBroadcast
 */
export type GameInfoBroadcastJson = {
  /**
   * 战局ID
   *
   * @generated from field: string battle_id = 1;
   */
  battleId?: string;

  /**
   * Deck 牌的信息
   *
   * @generated from field: dixit.v1.GameInfoBroadcast.DeckInfo deck_info = 2;
   */
  deckInfo?: GameInfoBroadcast_DeckInfoJson;

  /**
   * 当前状态信息
   *
   * @generated from field: dixit.v1.GameInfoBroadcast.StateInfo state_info = 3;
   */
  stateInfo?: GameInfoBroadcast_StateInfoJson;

  /**
   * 回合信息
   *
   * @generated from field: dixit.v1.GameInfoBroadcast.RoundInfo round_info = 4;
   */
  roundInfo?: GameInfoBroadcast_RoundInfoJson;
};

/**
 * Describes the message dixit.v1.GameInfoBroadcast.
 * Use `create(GameInfoBroadcastSchema)` to create a new message.
 */
export const GameInfoBroadcastSchema: GenMessage<GameInfoBroadcast, GameInfoBroadcastJson> = /*@__PURE__*/
  messageDesc(file_dixit_v1_game, 0);

/**
 * 牌局信息
 *
 * @generated from message dixit.v1.GameInfoBroadcast.DeckInfo
 */
export type GameInfoBroadcast_DeckInfo = Message<"dixit.v1.GameInfoBroadcast.DeckInfo"> & {
  /**
   * 主题 ID
   *
   * @generated from field: int32 theme_id = 1;
   */
  themeId: number;

  /**
   * 主题名称
   *
   * @generated from field: string theme_name = 2;
   */
  themeName: string;
};

/**
 * 牌局信息
 *
 * @generated from message dixit.v1.GameInfoBroadcast.DeckInfo
 */
export type GameInfoBroadcast_DeckInfoJson = {
  /**
   * 主题 ID
   *
   * @generated from field: int32 theme_id = 1;
   */
  themeId?: number;

  /**
   * 主题名称
   *
   * @generated from field: string theme_name = 2;
   */
  themeName?: string;
};

/**
 * Describes the message dixit.v1.GameInfoBroadcast.DeckInfo.
 * Use `create(GameInfoBroadcast_DeckInfoSchema)` to create a new message.
 */
export const GameInfoBroadcast_DeckInfoSchema: GenMessage<GameInfoBroadcast_DeckInfo, GameInfoBroadcast_DeckInfoJson> = /*@__PURE__*/
  messageDesc(file_dixit_v1_game, 0, 0);

/**
 * @generated from message dixit.v1.GameInfoBroadcast.StateInfo
 */
export type GameInfoBroadcast_StateInfo = Message<"dixit.v1.GameInfoBroadcast.StateInfo"> & {
  /**
   * 当前状态
   *
   * @generated from field: dixit.v1.State state = 1;
   */
  state: State;

  /**
   * 倒计时剩余时间（ms)，0表示没有倒计时
   *
   * @generated from field: int64 countdown_ms = 2;
   */
  countdownMs: bigint;

  /**
   * 服务器时间戳（ms）
   *
   * @generated from field: int64 server_timestamp = 3;
   */
  serverTimestamp: bigint;

  /**
   * 游戏开始时间戳（ms）
   *
   * @generated from field: int64 started_timestamp = 4;
   */
  startedTimestamp: bigint;
};

/**
 * @generated from message dixit.v1.GameInfoBroadcast.StateInfo
 */
export type GameInfoBroadcast_StateInfoJson = {
  /**
   * 当前状态
   *
   * @generated from field: dixit.v1.State state = 1;
   */
  state?: StateJson;

  /**
   * 倒计时剩余时间（ms)，0表示没有倒计时
   *
   * @generated from field: int64 countdown_ms = 2;
   */
  countdownMs?: string;

  /**
   * 服务器时间戳（ms）
   *
   * @generated from field: int64 server_timestamp = 3;
   */
  serverTimestamp?: string;

  /**
   * 游戏开始时间戳（ms）
   *
   * @generated from field: int64 started_timestamp = 4;
   */
  startedTimestamp?: string;
};

/**
 * Describes the message dixit.v1.GameInfoBroadcast.StateInfo.
 * Use `create(GameInfoBroadcast_StateInfoSchema)` to create a new message.
 */
export const GameInfoBroadcast_StateInfoSchema: GenMessage<GameInfoBroadcast_StateInfo, GameInfoBroadcast_StateInfoJson> = /*@__PURE__*/
  messageDesc(file_dixit_v1_game, 0, 1);

/**
 * @generated from message dixit.v1.GameInfoBroadcast.RoundInfo
 */
export type GameInfoBroadcast_RoundInfo = Message<"dixit.v1.GameInfoBroadcast.RoundInfo"> & {
  /**
   * 当前回合数
   *
   * @generated from field: int32 round_count = 1;
   */
  roundCount: number;

  /**
   * 总回合数
   *
   * @generated from field: int32 round_total = 2;
   */
  roundTotal: number;

  /**
   * 当前玩家游标
   *
   * @generated from field: int32 current_player_cursor = 3;
   */
  currentPlayerCursor: number;

  /**
   * 本轮故事
   *
   * @generated from field: string current_story = 4;
   */
  currentStory: string;

  /**
   * 本轮出牌列表，仅投票和唱票环节有
   *
   * @generated from field: repeated dixit.v1.GameInfoBroadcast.RoundInfo.SelectedCard selected_cards = 5;
   */
  selectedCards: GameInfoBroadcast_RoundInfo_SelectedCard[];
};

/**
 * @generated from message dixit.v1.GameInfoBroadcast.RoundInfo
 */
export type GameInfoBroadcast_RoundInfoJson = {
  /**
   * 当前回合数
   *
   * @generated from field: int32 round_count = 1;
   */
  roundCount?: number;

  /**
   * 总回合数
   *
   * @generated from field: int32 round_total = 2;
   */
  roundTotal?: number;

  /**
   * 当前玩家游标
   *
   * @generated from field: int32 current_player_cursor = 3;
   */
  currentPlayerCursor?: number;

  /**
   * 本轮故事
   *
   * @generated from field: string current_story = 4;
   */
  currentStory?: string;

  /**
   * 本轮出牌列表，仅投票和唱票环节有
   *
   * @generated from field: repeated dixit.v1.GameInfoBroadcast.RoundInfo.SelectedCard selected_cards = 5;
   */
  selectedCards?: GameInfoBroadcast_RoundInfo_SelectedCardJson[];
};

/**
 * Describes the message dixit.v1.GameInfoBroadcast.RoundInfo.
 * Use `create(GameInfoBroadcast_RoundInfoSchema)` to create a new message.
 */
export const GameInfoBroadcast_RoundInfoSchema: GenMessage<GameInfoBroadcast_RoundInfo, GameInfoBroadcast_RoundInfoJson> = /*@__PURE__*/
  messageDesc(file_dixit_v1_game, 0, 2);

/**
 * @generated from message dixit.v1.GameInfoBroadcast.RoundInfo.SelectedCard
 */
export type GameInfoBroadcast_RoundInfo_SelectedCard = Message<"dixit.v1.GameInfoBroadcast.RoundInfo.SelectedCard"> & {
  /**
   * 选择的牌
   *
   * @generated from field: string card = 1;
   */
  card: string;

  /**
   * 选择该牌的玩家索引，仅唱票环节有
   *
   * @generated from field: int32 selected_player_index = 2;
   */
  selectedPlayerIndex: number;

  /**
   * 投票的玩家索引，仅唱票环节有
   *
   * @generated from field: repeated int32 voted_player_indices = 3;
   */
  votedPlayerIndices: number[];

  /**
   * 该牌得分，仅唱票环节有
   *
   * @generated from field: int32 score = 4;
   */
  score: number;

  /**
   * 得分玩家索引，仅唱票环节有
   *
   * @generated from field: repeated int32 scored_player_indices = 5;
   */
  scoredPlayerIndices: number[];
};

/**
 * @generated from message dixit.v1.GameInfoBroadcast.RoundInfo.SelectedCard
 */
export type GameInfoBroadcast_RoundInfo_SelectedCardJson = {
  /**
   * 选择的牌
   *
   * @generated from field: string card = 1;
   */
  card?: string;

  /**
   * 选择该牌的玩家索引，仅唱票环节有
   *
   * @generated from field: int32 selected_player_index = 2;
   */
  selectedPlayerIndex?: number;

  /**
   * 投票的玩家索引，仅唱票环节有
   *
   * @generated from field: repeated int32 voted_player_indices = 3;
   */
  votedPlayerIndices?: number[];

  /**
   * 该牌得分，仅唱票环节有
   *
   * @generated from field: int32 score = 4;
   */
  score?: number;

  /**
   * 得分玩家索引，仅唱票环节有
   *
   * @generated from field: repeated int32 scored_player_indices = 5;
   */
  scoredPlayerIndices?: number[];
};

/**
 * Describes the message dixit.v1.GameInfoBroadcast.RoundInfo.SelectedCard.
 * Use `create(GameInfoBroadcast_RoundInfo_SelectedCardSchema)` to create a new message.
 */
export const GameInfoBroadcast_RoundInfo_SelectedCardSchema: GenMessage<GameInfoBroadcast_RoundInfo_SelectedCard, GameInfoBroadcast_RoundInfo_SelectedCardJson> = /*@__PURE__*/
  messageDesc(file_dixit_v1_game, 0, 2, 0);

/**
 * @generated from message dixit.v1.RankingBroadcast
 */
export type RankingBroadcast = Message<"dixit.v1.RankingBroadcast"> & {
};

/**
 * @generated from message dixit.v1.RankingBroadcast
 */
export type RankingBroadcastJson = {
};

/**
 * Describes the message dixit.v1.RankingBroadcast.
 * Use `create(RankingBroadcastSchema)` to create a new message.
 */
export const RankingBroadcastSchema: GenMessage<RankingBroadcast, RankingBroadcastJson> = /*@__PURE__*/
  messageDesc(file_dixit_v1_game, 1);

/**
 * @generated from message dixit.v1.ClientDataBroadcast
 */
export type ClientDataBroadcast = Message<"dixit.v1.ClientDataBroadcast"> & {
  /**
   * 发起玩家索引
   *
   * @generated from field: int32 from_player_index = 1;
   */
  fromPlayerIndex: number;

  /**
   * @generated from field: string data = 2;
   */
  data: string;
};

/**
 * @generated from message dixit.v1.ClientDataBroadcast
 */
export type ClientDataBroadcastJson = {
  /**
   * 发起玩家索引
   *
   * @generated from field: int32 from_player_index = 1;
   */
  fromPlayerIndex?: number;

  /**
   * @generated from field: string data = 2;
   */
  data?: string;
};

/**
 * Describes the message dixit.v1.ClientDataBroadcast.
 * Use `create(ClientDataBroadcastSchema)` to create a new message.
 */
export const ClientDataBroadcastSchema: GenMessage<ClientDataBroadcast, ClientDataBroadcastJson> = /*@__PURE__*/
  messageDesc(file_dixit_v1_game, 2);

/**
 * 服务端自定义数据广播
 *
 * @generated from message dixit.v1.ServerDataBroadcast
 */
export type ServerDataBroadcast = Message<"dixit.v1.ServerDataBroadcast"> & {
  /**
   * 发起玩家索引
   *
   * @generated from field: int32 from_player_index = 1;
   */
  fromPlayerIndex: number;

  /**
   * 操作类型
   *
   * @generated from field: dixit.v1.ServerDataBroadcast.Action action = 2;
   */
  action: ServerDataBroadcast_Action;

  /**
   * 数据内容，proto 类型二进制消息，不同 action 对应的 data 类型不一样
   *
   * @generated from field: bytes data = 3;
   */
  data: Uint8Array;
};

/**
 * 服务端自定义数据广播
 *
 * @generated from message dixit.v1.ServerDataBroadcast
 */
export type ServerDataBroadcastJson = {
  /**
   * 发起玩家索引
   *
   * @generated from field: int32 from_player_index = 1;
   */
  fromPlayerIndex?: number;

  /**
   * 操作类型
   *
   * @generated from field: dixit.v1.ServerDataBroadcast.Action action = 2;
   */
  action?: ServerDataBroadcast_ActionJson;

  /**
   * 数据内容，proto 类型二进制消息，不同 action 对应的 data 类型不一样
   *
   * @generated from field: bytes data = 3;
   */
  data?: string;
};

/**
 * Describes the message dixit.v1.ServerDataBroadcast.
 * Use `create(ServerDataBroadcastSchema)` to create a new message.
 */
export const ServerDataBroadcastSchema: GenMessage<ServerDataBroadcast, ServerDataBroadcastJson> = /*@__PURE__*/
  messageDesc(file_dixit_v1_game, 3);

/**
 * @generated from enum dixit.v1.ServerDataBroadcast.Action
 */
export enum ServerDataBroadcast_Action {
  /**
   * @generated from enum value: UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,
}

/**
 * @generated from enum dixit.v1.ServerDataBroadcast.Action
 */
export type ServerDataBroadcast_ActionJson = "UNSPECIFIED";

/**
 * Describes the enum dixit.v1.ServerDataBroadcast.Action.
 */
export const ServerDataBroadcast_ActionSchema: GenEnum<ServerDataBroadcast_Action, ServerDataBroadcast_ActionJson> = /*@__PURE__*/
  enumDesc(file_dixit_v1_game, 3, 0);

/**
 * 服务端选择数据
 *
 * @generated from message dixit.v1.ServerSelectedData
 */
export type ServerSelectedData = Message<"dixit.v1.ServerSelectedData"> & {
  /**
   * 关系到的牌的索引
   *
   * @generated from field: repeated string cards = 1;
   */
  cards: string[];
};

/**
 * 服务端选择数据
 *
 * @generated from message dixit.v1.ServerSelectedData
 */
export type ServerSelectedDataJson = {
  /**
   * 关系到的牌的索引
   *
   * @generated from field: repeated string cards = 1;
   */
  cards?: string[];
};

/**
 * Describes the message dixit.v1.ServerSelectedData.
 * Use `create(ServerSelectedDataSchema)` to create a new message.
 */
export const ServerSelectedDataSchema: GenMessage<ServerSelectedData, ServerSelectedDataJson> = /*@__PURE__*/
  messageDesc(file_dixit_v1_game, 4);

/**
 * 客户端重连时，会调用Sync方法，服务端返回当前玩家和游戏信息
 *
 * @generated from message dixit.v1.SyncResponse
 */
export type SyncResponse = Message<"dixit.v1.SyncResponse"> & {
  /**
   * @generated from field: dixit.v1.PlayerInfoMessage player_info = 1;
   */
  playerInfo?: PlayerInfoMessage;

  /**
   * @generated from field: dixit.v1.GameInfoBroadcast game_info = 2;
   */
  gameInfo?: GameInfoBroadcast;
};

/**
 * 客户端重连时，会调用Sync方法，服务端返回当前玩家和游戏信息
 *
 * @generated from message dixit.v1.SyncResponse
 */
export type SyncResponseJson = {
  /**
   * @generated from field: dixit.v1.PlayerInfoMessage player_info = 1;
   */
  playerInfo?: PlayerInfoMessageJson;

  /**
   * @generated from field: dixit.v1.GameInfoBroadcast game_info = 2;
   */
  gameInfo?: GameInfoBroadcastJson;
};

/**
 * Describes the message dixit.v1.SyncResponse.
 * Use `create(SyncResponseSchema)` to create a new message.
 */
export const SyncResponseSchema: GenMessage<SyncResponse, SyncResponseJson> = /*@__PURE__*/
  messageDesc(file_dixit_v1_game, 5);

/**
 * todo
 *
 * @generated from message dixit.v1.GameOverBroadcast
 */
export type GameOverBroadcast = Message<"dixit.v1.GameOverBroadcast"> & {
};

/**
 * todo
 *
 * @generated from message dixit.v1.GameOverBroadcast
 */
export type GameOverBroadcastJson = {
};

/**
 * Describes the message dixit.v1.GameOverBroadcast.
 * Use `create(GameOverBroadcastSchema)` to create a new message.
 */
export const GameOverBroadcastSchema: GenMessage<GameOverBroadcast, GameOverBroadcastJson> = /*@__PURE__*/
  messageDesc(file_dixit_v1_game, 6);

