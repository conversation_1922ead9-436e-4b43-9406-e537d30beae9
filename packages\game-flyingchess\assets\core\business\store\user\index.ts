/**
 * @describe 用户模块数据
 * <AUTHOR>
 * @date 2023-08-02 20:11:20
 */

import { BattleMode, ClientBootParam, ClientBootParamSchema } from 'sgc'
import store, { Store } from '../index'
import { log } from 'cc'
import { BaseStore } from '../BaseStore'
import {
    computed,
    makeAutoObservable,
    makeObservable,
    observable,
} from 'mobx-tsuki'
import { create } from '@bufbuild/protobuf'

export enum GameMode {
    NONE,
    SOLO,
    TEAM,
}

export default class UserStore extends BaseStore {
    /**启动参数 */
    auth: ClientBootParam = create(ClientBootParamSchema)

    constructor(rootStore: Store) {
        super(rootStore)
        makeObservable(this, {
            auth: observable,
            assets: observable,
            pointD10: observable,
            userGameData: observable,
            profile: observable,
            isAudience: computed,
        })
    }

    /**资产 云贝 */
    assets: number

    /**小鱼干 */
    pointD10: number

    get isAudience() {
        const nonce = !this.auth.token
        window.ccLog('isAudience', nonce, this.auth)
        return nonce
    }

    /**获取游戏模式(单双人) */
    get gameMode() {
        return this.auth.mode == BattleMode.PVP_COOPERATION
            ? GameMode.TEAM
            : this.auth.mode == BattleMode.PVP_SOLO
            ? GameMode.SOLO
            : GameMode.NONE
    }

    /**用户的teamId */
    get userTeamId() {
        return this.rootStore.game.getUserPlayer?.teamId ?? ''
    }

    /**玩家索引 */
    public get userIndex(): number {
        return this.rootStore.game.getUserPlayer?.relBattleOffset ?? -1
    }

    /**用户游戏数据 */
    userGameData: IBridge.UserGameDataRes = {
        playTime: 0,
        playCount: 0,
        winCount: 0,
        winRatio: '0%',
        userId: 0,
        gameLevel: 1,
        exp: 1,
        seasonTier: null,
        seasonExp: 0,
    }

    /**用户游戏数据 */
    profile: IBridge.Profile = {
        /**显示昵称 */
        displayName: '',
        /**头像 */
        avatar: '',
        /**头像框 */
        avatarFrame: '',
        /**性别 */
        gender: 0,
        /**简介 */
        intro: '',
        /**铭牌地址 */
        nameplate: '',
        /**徽章 */
        badge: '',
        /**名片卡 */
        bizCard: '',
        /**归属地区 */
        region: '',

        id: undefined,

        charms: 0,
    }
}
