import {
    Asset,
    ImageAsset,
    Rect,
    Size,
    SpriteAtlas,
    SpriteFrame,
    Texture2D,
    Vec2,
} from 'cc'

// 定义 Plist 数据结构接口
interface PlistFrameData {
    frame: string
    sourceSize: string
    offset: string
    rotated: boolean
}

interface PlistAsset {
    frames: Record<string, PlistFrameData>
}

// 定义 XML 解析后的数据结构
interface XmlNode {
    name: string
    attributes: Record<string, string>
    children: XmlNode[]
    text?: string
}

// 定义 Plist XML 解析后的数据结构
interface PlistXmlData {
    plist: {
        dict: {
            frames: Record<
                string,
                {
                    aliases?: any[]
                    spriteOffset: string
                    spriteSize: string
                    spriteSourceSize: string
                    textureRect: string
                    textureRotated: boolean
                }
            >
            metadata?: {
                format: number
                pixelFormat: string
                premultiplyAlpha: boolean
                realTextureFileName: string
                size: string
                smartupdate: string
                textureFileName: string
            }
        }
    }
}

export class PlistParse {
    static parse(
        atlasFrame: ImageAsset,
        plist: Asset | PlistAsset
    ): SpriteAtlas {
        let altas = new SpriteAtlas()
        const texture = new Texture2D()
        texture.image = atlasFrame
        if ((plist as any)['spriteFrames']) {
            return plist as SpriteAtlas
        }
        if (!(plist as any)['_file']) {
            // 获取plist的文本内容
            const plistText = (plist as any).text || ''
            if (plistText) {
                ;(plist as any)['_file'] = PlistParse.parseXml(plistText)
            }
        }
        // 使用类型断言处理 plist 的 _file 属性
        const asset = (plist as any)['_file'] as PlistAsset
        console.log(' plist._file', asset)
        const _frames = altas.spriteFrames
        for (const key in asset.frames) {
            _frames[key.slice(0, -4)] = PlistParse.getSpriteFrame(
                texture,
                asset,
                key
            )
        }
        return altas
    }

    static parse_frame(atlasFrame: ImageAsset, plist: PlistAsset): SpriteAtlas {
        let altas = new SpriteAtlas()
        const texture = new Texture2D()
        texture.image = atlasFrame
        const asset = plist
        console.log(' parse_frame=======', asset)
        const _frames = altas.spriteFrames
        for (const key in asset.frames) {
            _frames[key.slice(0, -4)] = PlistParse.getSpriteFrame(
                texture,
                asset,
                key
            )
        }
        return altas
    }

    static getSpriteFrame(
        texture: Texture2D,
        atlasPlist: PlistAsset,
        imageStr: string
    ): SpriteFrame {
        // 获取.plist文件中关于此图的图片信息
        const frameDataObj = atlasPlist.frames[imageStr]
        // 图片矩形信息
        const rect = PlistParse.GetFrameData(frameDataObj.frame)
        // 图片的原始大小
        const size = PlistParse.GetSizeData(frameDataObj.sourceSize)
        // 图片合图时的裁剪偏移
        const offset = PlistParse.GetOffsetData(frameDataObj.offset)
        // 创建此图的精灵帧
        const sf = new SpriteFrame()
        sf.reset({
            texture,
            rect: rect || undefined,
            offset: offset || undefined,
            originalSize: size || undefined,
            isRotate: frameDataObj.rotated,
        })
        return sf
    }

    public static GetFrameData(str: string): Rect | null {
        // 13是这个rect结构至少要有的字符串长度，例如{{1000,389},{1022,768}}
        if (str.length < 13) {
            console.log('---解析plist的frame rect，数据错误-----')
            return null
        }
        let newStr: string = str
        newStr = newStr.slice(2)
        newStr = newStr.slice(0, newStr.length - 2)
        let newList_0: string[] = newStr.split('},{')
        let newList_1: string[] = newList_0[0].split(',')
        let newList_2: string[] = newList_0[1].split(',')
        if (newList_1.length < 2 || newList_2.length < 2) {
            // console.log('---解析plist的frame rect，字符串数据错误-----');
            return null
        }

        return new Rect(
            parseInt(newList_1[0]),
            parseInt(newList_1[1]),
            parseInt(newList_2[0]),
            parseInt(newList_2[1])
        )
    }

    public static GetSizeData(str: string): Size | null {
        // 5是这个size结构至少要有的字符串长度，例如{64,60}

        if (str.length < 5) {
            // console.log('---解析plist的size，数据错误-----');
            return null
        }

        let newStr: string = str
        newStr = newStr.slice(1)
        newStr = newStr.slice(0, newStr.length - 1)
        let newList_0: string[] = newStr.split(',')
        if (newList_0.length < 2) {
            // console.log('---解析plist的size，字符串数据错误-----');
            return null
        }

        return new Size(parseInt(newList_0[0]), parseInt(newList_0[1]))
    }

    public static GetOffsetData(str: string): Vec2 | null {
        // 5是这个offset结构至少要有的字符串长度，例如{22,-24}

        if (str.length < 5) {
            // console.log('---解析plist的offset，数据错误-----');
            return null
        }
        let newStr: string = str
        newStr = newStr.slice(1)
        newStr = newStr.slice(0, newStr.length - 1)
        let newList_0: string[] = newStr.split(',')
        if (newList_0.length < 2) {
            // console.log('---解析plist的offset，字符串数据错误-----');
            return null
        }

        return new Vec2(parseInt(newList_0[0]), parseInt(newList_0[1]))
    }

    /**
     * 解析XML字符串为结构化数据
     * @param xmlString XML格式的字符串
     * @returns 解析后的PlistAsset对象
     */
    public static parseXml(xmlString: string): PlistAsset {
        // 解析XML字符串为DOM结构
        const parser = new DOMParser()
        const xmlDoc = parser.parseFromString(xmlString, 'text/xml')

        // 创建结果对象
        const result: PlistAsset = {
            frames: {},
        }

        // 获取frames字典
        // 首先找到plist元素
        const plistElement = xmlDoc.getElementsByTagName('plist')[0]
        if (!plistElement) {
            console.error('解析XML失败：找不到plist元素')
            return result
        }

        // 找到dict元素
        const dictElements = plistElement.getElementsByTagName('dict')
        if (dictElements.length === 0) {
            console.error('解析XML失败：找不到dict元素')
            return result
        }

        // 在dict中找到key为frames的元素
        const rootDict = dictElements[0]
        let framesDict: Element | null = null
        let foundFramesKey = false

        // 遍历rootDict的子元素
        for (let i = 0; i < rootDict.childNodes.length; i++) {
            const node = rootDict.childNodes[i]
            if (node.nodeType === Node.ELEMENT_NODE) {
                const element = node as Element

                // 如果找到了key为frames的元素
                if (foundFramesKey && element.tagName === 'dict') {
                    framesDict = element
                    break
                }

                // 检查是否是key元素且内容为frames
                if (
                    element.tagName === 'key' &&
                    element.textContent === 'frames'
                ) {
                    foundFramesKey = true
                } else {
                    foundFramesKey = false
                }
            }
        }

        if (!framesDict) {
            console.error('解析XML失败：找不到frames字典')
            return result
        }

        // 遍历frames字典中的所有key-dict对
        let currentKey: string | null = null
        let currentNode = framesDict.firstChild

        while (currentNode) {
            if (currentNode.nodeType === Node.ELEMENT_NODE) {
                const element = currentNode as Element

                // 如果是key元素，记录当前key
                if (element.tagName === 'key') {
                    currentKey = element.textContent
                }
                // 如果是dict元素且有当前key，处理帧数据
                else if (element.tagName === 'dict' && currentKey) {
                    const frameData: PlistFrameData = {
                        frame: '',
                        sourceSize: '',
                        offset: '',
                        rotated: false,
                    }

                    // 遍历dict中的所有key-value对
                    let dictKey: string | null = null
                    let dictNode = element.firstChild

                    while (dictNode) {
                        if (dictNode.nodeType === Node.ELEMENT_NODE) {
                            const dictElement = dictNode as Element

                            // 如果是key元素，记录当前dict key
                            if (dictElement.tagName === 'key') {
                                dictKey = dictElement.textContent
                            }
                            // 处理value元素
                            else if (dictKey) {
                                if (dictKey === 'textureRect') {
                                    frameData.frame =
                                        dictElement.textContent || ''
                                } else if (
                                    dictKey === 'spriteSize' ||
                                    dictKey === 'spriteSourceSize'
                                ) {
                                    frameData.sourceSize =
                                        dictElement.textContent || ''
                                } else if (dictKey === 'spriteOffset') {
                                    frameData.offset =
                                        dictElement.textContent || ''
                                } else if (dictKey === 'textureRotated') {
                                    // 检查是否为true元素或者内容为true的元素
                                    frameData.rotated =
                                        dictElement.tagName === 'true' ||
                                        dictElement.textContent === 'true'
                                }

                                dictKey = null // 重置dictKey
                            }
                        }

                        dictNode = dictNode.nextSibling
                    }

                    // 将帧数据添加到结果中
                    result.frames[currentKey] = frameData
                    currentKey = null // 重置currentKey
                }
            }

            currentNode = currentNode.nextSibling
        }

        return result
    }
}
