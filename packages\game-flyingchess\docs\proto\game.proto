syntax = "proto3";

package flyingchess.v1;

import "state.proto";
import "player.proto";
import "chess.proto";

// --- 游戏广播消息 ---

// 游戏信息广播
message GameInfoBroadcast {
  // 战局ID
  string battle_id = 1;
  
  // 棋盘信息
  Board board = 2;
  
  // 当前状态信息
  StateInfo state_info = 3;
  
  // 玩家列表
  repeated Player players = 4;
  
  // 回合信息
  RoundInfo round_info = 5;
  
  // 游戏配置
  GameConfig config = 6;
  
  // 游戏结果
  GameResult result = 7;
  
  // 状态信息
  message StateInfo {
    // 当前游戏状态
    State state = 1;
    
    // 状态开始时间（毫秒时间戳）
    int64 start_time = 2;
    
    // 状态持续时间（毫秒）
    int32 duration = 3;
    
    // 下一个状态
    State next_state = 4;
  }
  
  // 回合信息
  message RoundInfo {
    // 当前回合数
    int32 round = 1;
    
    // 当前操作玩家索引
    uint32 current_player_index = 2;
    
    // 骰子信息
    Dice dice = 3;
    
    // 选中的棋子ID
    int32 selected_chess_id = 4;
    
    // 移动路径
    MovePath move_path = 5;
    
    // 特殊效果
    SpecialEffect special_effect = 6;
    
    // 回合开始时间（毫秒时间戳）
    int64 start_time = 7;
    
    // 回合超时时间（毫秒）
    int32 timeout = 8;
  }
  
  // 特殊效果
  message SpecialEffect {
    // 效果类型
    SpecialEffectType type = 1;
    
    // 效果源（触发效果的棋子ID）
    int32 source_chess_id = 2;
    
    // 效果目标（受到效果影响的棋子ID列表）
    repeated int32 target_chess_ids = 3;
    
    // 效果开始时间（毫秒时间戳）
    int64 start_time = 4;
    
    // 效果持续时间（毫秒）
    int32 duration = 5;
  }
}

// 游戏配置
message GameConfig {
  // 游戏模式（0:经典模式, 1:快速模式, 2:团队模式）
  int32 mode = 1;
  
  // 玩家数量（2-4）
  int32 player_count = 2;
  
  // 每个玩家的棋子数量（默认4）
  int32 chess_per_player = 3;
  
  // 回合超时时间（秒）
  int32 round_timeout = 4;
  
  // 是否允许托管
  bool allow_hosting = 5;
  
  // 是否允许观战
  bool allow_spectating = 6;
  
  // 是否启用连投奖励（投掷到6点可以再投一次）
  bool enable_roll_again = 7;
}

// 游戏结果
message GameResult {
  // 游戏是否已结束
  bool game_over = 1;
  
  // 获胜玩家索引
  uint32 winner_index = 2;
  
  // 玩家排名列表
  repeated PlayerRank player_ranks = 3;
  
  // 游戏结束时间（毫秒时间戳）
  int64 end_time = 4;
  
  // 玩家排名
  message PlayerRank {
    // 玩家索引
    uint32 player_index = 1;
    
    // 排名（1-4）
    int32 rank = 2;
    
    // 得分
    int32 score = 3;
    
    // 到达终点的棋子数量
    int32 finished_chess_count = 4;
  }
}

// 状态变更广播
message StateChangedBroadcast {
  // 战局ID
  string battle_id = 1;
  
  // 当前状态
  State current_state = 2;
  
  // 上一个状态
  State previous_state = 3;
  
  // 状态变更时间（毫秒时间戳）
  int64 change_time = 4;
  
  // 状态数据（根据不同状态包含不同数据）
  oneof state_data {
    // 骰子数据（用于STATE_PLAYER_ROLLED_DICE）
    Dice dice_data = 5;
    
    // 棋子选择数据（用于STATE_PLAYER_SELECTED_CHESS）
    ChessSelectData chess_select_data = 6;
    
    // 棋子移动数据（用于STATE_CHESS_MOVED）
    ChessMoveData chess_move_data = 7;
    
    // 特殊效果数据（用于STATE_SPECIAL_EFFECT）
    SpecialEffectData special_effect_data = 8;
  }
  
  // 棋子选择数据
  message ChessSelectData {
    // 选中的棋子ID
    int32 chess_id = 1;
    
    // 玩家索引
    uint32 player_index = 2;
  }
  
  // 棋子移动数据
  message ChessMoveData {
    // 移动路径
    MovePath move_path = 1;
  }
  
  // 特殊效果数据
  message SpecialEffectData {
    // 效果类型
    SpecialEffectType type = 1;
    
    // 效果源（触发效果的棋子ID）
    int32 source_chess_id = 2;
    
    // 效果目标（受到效果影响的棋子ID列表）
    repeated int32 target_chess_ids = 3;
  }
}

// 游戏结束广播
message GameOverBroadcast {
  // 战局ID
  string battle_id = 1;
  
  // 游戏结果
  GameResult result = 2;
}

// --- 客户端请求消息 ---

// 投掷骰子请求
message RollDiceRequest {
  // 战局ID
  string battle_id = 1;
  
  // 玩家ID
  string player_id = 2;
}

// 投掷骰子响应
message RollDiceResponse {
  // 骰子点数
  int32 value = 1;
  
  // 可选择的棋子ID列表
  repeated int32 selectable_chess_ids = 2;
}

// 选择棋子请求
message SelectChessRequest {
  // 战局ID
  string battle_id = 1;
  
  // 玩家ID
  string player_id = 2;
  
  // 选中的棋子ID
  int32 chess_id = 3;
}

// 选择棋子响应
message SelectChessResponse {
  // 是否选择成功
  bool success = 1;
  
  // 移动路径
  MovePath move_path = 2;
}

// 托管请求
message HostingRequest {
  // 战局ID
  string battle_id = 1;
  
  // 玩家ID
  string player_id = 2;
  
  // 是否托管（true:托管, false:取消托管）
  bool hosting = 3;
}

// 托管响应
message HostingResponse {
  // 是否成功
  bool success = 1;
}

// 准备请求
message ReadyRequest {
  // 战局ID
  string battle_id = 1;
  
  // 玩家ID
  string player_id = 2;
  
  // 是否准备
  bool ready = 3;
}

// 准备响应
message ReadyResponse {
  // 是否成功
  bool success = 1;
  
  // 已准备的玩家数量
  int32 ready_count = 2;
  
  // 总玩家数量
  int32 total_count = 3;
}
