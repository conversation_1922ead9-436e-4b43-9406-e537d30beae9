/**
* @describe 登录模块数据
* <AUTHOR>
* @date 2023-08-02 20:11:20
*/

import { envConfig } from "../../environment";
import { BaseStore } from "../BaseStore";


export default class Login extends BaseStore {
    /**用于获取token */
    code: string = '';
    /**标识 */
    token: string = "";
    /**websocket 连接地址 TODO*/
    ws_url: string = '';
    /**登录标识 */
    isLogin: boolean = true

    /**随乐游游戏状态信息 */
    gameStateInfo: IBridge.GameStateInfoRes = null
}

