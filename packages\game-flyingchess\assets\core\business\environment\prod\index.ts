/**
 * @describe 生产环境配置
 * <AUTHOR>
 * @date 2023-08-02 19:46:13
 */

import { queryStringToObject } from '../util'
const queryParams = queryStringToObject(location.search.slice(1))

const ENV_TYPE: RuntineEnv = {
    static_base_url: '',
    http_base_url: 'https://draw-something-test.yiqiyoo.com',
    event_tracking_url: 'https://dssp.stnts.com/?opt=put&type=json',
    event_tracking_key: 'alibabatutudodo@',
    bucket: '',
    region: '',
    secret: '',
    player_ws_url:
        queryParams.player_ws_url || 'wss://game-ws.mityoo.com/ws/connector',
    audience_ws_url:
        queryParams.audience_ws_url || 'wss://game-ws.mityoo.com/ws/audience',
    stop_service_url: '',
}
export default ENV_TYPE
