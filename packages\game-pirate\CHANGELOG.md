# 海盗桶 - 更新日志

本项目的所有重要更改都将记录在此文件中。


### 0.1.1 (2025-05-28)


### ♻️ 代码重构

* 重构plist相关组件，解决类型报错 ([310e5d1](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/310e5d1d0be5ca66f14cafbad2592c74617e84ca))


### 🔧 其他更改

* 海盗桶仓库改名 ([d5263d8](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/d5263d8b9308b203ed35e2400698c9d618fdb758))
* standard-version ([a3c864b](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/a3c864bb8cab6ff6be3e855c57fca72fbc811a0f))


### ✨ 新功能

* 背景音音量调整 ([48d6585](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/48d6585aa3df1b6fd8b5b279d5033f408d71f36c))
* 补充jsbridge文档 ([1c9aa92](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/1c9aa92ba055b29a59bd924a25a48ec7f971d129))
* 场景背景调整 ([bc8aec7](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/bc8aec789e055715a29ff6907f99814873a5c82e))
* 处理玩家信息变更 ([96e4808](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/96e48086da78d27bf17bedc8491e849a13a75244))
* 刀孔 to 缝隙 ([93d0c4d](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/93d0c4dacea2dfa3fafa1e5acc2f9f9eb6bb3c31))
* 刀孔点击时检测处于桶子的正面还是背面 ([a9d87ad](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/a9d87adfd6efc96e1c95c811eb5e5ac9b77da780))
* 攻击按钮替换 ([76c3e16](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/76c3e16775c57aeba7879afe37e328bf176b6a0f))
* 攻击和复活相关调整 ([4a70e65](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/4a70e650315e411060360ded27077107c11a78f7))
* 攻击和复活相关调整 ([222ef69](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/222ef691b64fd8836bcbaed99bc5d7275f483153))
* 骨骼动画png压缩 ([eaa7c2b](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/eaa7c2bedd68f5d9b5eb1fc3274796d82c230e3d))
* 光源 ([db637f0](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/db637f0499c8752267ff67c3c8d1de83ac1b77f0))
* 海盗桶-背景动效 ([cf25f70](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/cf25f7007cf1140d281cf5e9b6daae5ed49ce6f1))
* 海盗桶不再自动旋转回正 ([b65b355](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/b65b3557ce1272b44456a4f4a6ebbd9292b43803))
* 海盗桶游戏关闭阴影渲染 ([231a1e2](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/231a1e248d87dbbb2e47078432a7a8c233890654))
* 卡牌替换 & 击中弱点动画调整 ([ec7ab47](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/ec7ab47e2f23feae7e59f345bcdbfed376afbeb7))
* 开启纹理压缩 ([de56617](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/de566179f98d0368a13776ce1657207f0894075e))
* 轮到你了层级优化 ([0b8b653](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/0b8b6534965d08d6851e3f4e8a6dca9c5b3b085a))
* 配置每个包独立的版本管理，限制changelog只包含当前包的提交 ([8251041](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/8251041e22fcda32bef901f4349e28a0572351bc))
* 强化后剩余刀孔不足谈提示 ([802df2b](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/802df2bfc36eebc0f70f8ef48ccc456e8eec97a4))
* 所有游戏同步性能优化改动 ([e089d40](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/e089d40cef2c2f315043995446637fdedb5d99ff))
* 同步框架 ([5d5cbdd](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/5d5cbdd4fdb5138143b9bbdd77569d4a0784be6c))
* 同步装扮框架代码 & 海盗桶对接装扮 ([81eae5a](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/81eae5ab8bfb35c7a2f146f2862e02480904fd34))
* 同步装扮框架代码 & 海盗桶对接装扮 ([04fa121](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/04fa1215385933a41f67db2f495ef5245808e2a3))
* 同步装扮框架代码 & 海盗桶对接装扮 ([cfd5e61](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/cfd5e614be3413f118703e5c8e89eae2e2f5e21e))
* 同步装扮框架代码 & 海盗桶对接装扮 ([29df5e6](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/29df5e6ffb972bb0173724ba5147629d3911b90c))
* 同步装扮框架代码 & 海盗桶对接装扮 ([4b4fee6](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/4b4fee63e83fa5204a0984b27d6066fac3e157a4))
* 投票和玩家标签相关调整 ([e80a463](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/e80a463bae376fc6933cc7d97dc548682e6c4eb2))
* 透视 ([5e3b7bc](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/5e3b7bcceb85330fae4519f3e0eae842d9b7f1d2))
* 透视流程调整-角度旋转优化 ([92c3855](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/92c3855ad11b5d2d342cb4666ba0a98ac455dd87))
* 修复观战路由 ([8003f3a](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/8003f3a54ddd8d518065db57a7e25c65cae23333))
* 修复mockSharkRotate ([3c02b1f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/3c02b1f827b9064b9f9bc85ade404976912f86cb))
* 移除无用的节点和贴图 ([b0a684f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/b0a684f2a8313d0cfb6467ab863e8fae596fc8b1))
* 增加击中非弱点的2d动画 ([a1dd59f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/a1dd59f3715ff8da9c72e589f8ae369578a51f2d))
* 增加新的jsbridge方法 ([1b32549](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/1b32549ee4f8572bd9f24679cfaf879d1c0d9f67))
* 支持更多的强化次数 ([ba5f809](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/ba5f80995c6581bfd979a1b746686f8f01ee9f7c))
* plistImage 支持添加 .plist 文件资源 ([8cec5b3](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/8cec5b327446582a4ed307e1060e17f35903ce9b))
* plistImage 组件支持播放本地plist资源，支持设置播放完成回调 ([146b25f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/146b25f86afc630d1a189bd7f352ac5ef04c890c))
* WIP: 透视流程调整 ([df05ff3](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/df05ff3bcf87e9e739cf1a96976f78a317b145e4))
* WIP: 透视流程调整 ([13c7806](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/13c7806e59f9fabafc85505907ab2a82a7809b45))
* WIP: 透视流程调整 ([75b704e](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/75b704e1b039b9493ce6150f4f0b7608fbf5beb7))


### 🐛 Bug修复

* 被动出牌后也收起之前选中的手牌 ([c4859d0](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/c4859d02bc8757f7b2db81ed502780866958593b))
* 修复版本管理配置中的路径重复问题 ([8e1fe4e](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/8e1fe4e88c4e2721fbc51741958ed636f6c092c8))
* 用户信息变更和观战 ([6e9db5e](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/6e9db5e07dec43cfc5fc2b352889b331571c2bd2))
* avatar ([f7db817](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/f7db817d34bbf80f65eda4673111daeda7378292))
* avatar ([fa217d9](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/fa217d9488c7b7cd6372137d9446491c05abfa40))
* avatar ([46318b1](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/46318b1e4168282d807ac0ddbf4a0f0f2cf382a7))
* debug环境判断去掉 local ([e69591f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/e69591fc4859e4c97d0857007efb94155675aa5f))

# 海盗桶 - 更新日志

本项目的所有重要更改都将记录在此文件中。

本文件格式基于 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/)，
并且本项目遵循 [语义化版本](https://semver.org/lang/zh-CN/)。

## [未发布]

### 新增
- 初始化版本管理系统
- 集成 standard-version 进行自动化版本管理

### 变更
- 无

### 修复
- 无

### 移除
- 无

---

*注意：此 CHANGELOG 将在首次运行 standard-version 后自动更新*
