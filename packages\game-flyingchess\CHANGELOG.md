# 飞行棋 - 更新日志

本项目的所有重要更改都将记录在此文件中。


### 0.1.1 (2025-05-28)


### 🔧 其他更改

* 初始化飞行棋项目 ([414046e](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/414046ee5f432fd0d76e07deec6fee17ddfa5e71))


### 📝 文档更新

* 技术方案文档更新 ([7048180](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/70481801db96fc8b006f3e6aeabfff7e39b14d0f))
* 技术方案文档更新 ([10f1fc4](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/10f1fc4c7190af15a0e506885acb04e393794bf0))
* 技术方案文档更新 ([60c7f01](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/60c7f01e9390e183d17dd5532bdeb97020859b49))
* docs ([cddaba6](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/cddaba6f69bcdf9c1042bbdbd544d06cdeec9527))
* docs ([2c52e1a](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/2c52e1a8fdbfd088eae964dcba418742f8f12632))
* docs ([499679e](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/499679e3aff4b5302d1259638172d9fd0049d729))
* docs ([a17c4cb](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/a17c4cb02469e6115f4b106aa08e1f09527bee36))
* docs ([5e9d5b2](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/5e9d5b2ff71521ecc3a2861cb4730ef7f20d5dfb))
* docs ([f5dc7a3](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/f5dc7a3f874837dcfc97fa47b78d32c281409348))


### ✨ 新功能

* 配置每个包独立的版本管理，限制changelog只包含当前包的提交 ([8251041](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/8251041e22fcda32bef901f4349e28a0572351bc))
* WIP: 场景搭建 ([6867c1a](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/6867c1a29e8c7a4ebdc55db4ab6d09d8cde66c0d))
* WIP: 场景搭建 ([7cc41ef](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/7cc41efcbd42f48d666012fc9d2356c8a38f2215))
* WIP: 场景搭建 ([260574b](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/260574b8cb7a83936986ea49ea36d7e743a53924))
* WIP: 场景搭建 ([37fec84](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/37fec847c1b380e6d9439fa9e2bf9ba9c85c39a2))
* WIP: 场景搭建 ([95d7c7f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/95d7c7ff5c9234a57514d202d24de5abe185778f))
* WIP: 场景搭建 ([d9ad53a](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/d9ad53abf74e3c71e208328313c8cfeef58b127b))
* WIP: 场景搭建 ([0ca401d](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/0ca401d1e7aa2f7f8fce161c3a3b71df1bd192e4))
* WIP: 场景搭建 ([3ef7065](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/3ef7065e7fea40c92ab2c92e950fdcff6ffab424))


### 🐛 Bug修复

* 修复版本管理配置中的路径重复问题 ([8e1fe4e](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/8e1fe4e88c4e2721fbc51741958ed636f6c092c8))
