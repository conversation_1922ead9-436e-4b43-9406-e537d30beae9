syntax = "proto3";

package flyingchess.v1;

import "state.proto";

// 棋盘信息
message Board {
  // 棋盘格子列表
  repeated Grid grids = 1;
  
  // 棋盘尺寸
  BoardSize size = 2;
}

// 棋盘尺寸
message BoardSize {
  // 宽度
  int32 width = 1;
  
  // 高度
  int32 height = 2;
}

// 格子信息
message Grid {
  // 格子ID
  int32 id = 1;
  
  // 格子类型
  GridType type = 2;
  
  // 格子颜色（0:红, 1:黄, 2:蓝, 3:绿, -1:无色）
  int32 color = 3;
  
  // 格子位置
  Position position = 4;
  
  // 下一个格子ID
  int32 next_id = 5;
  
  // 特殊效果目标格子ID（如跳跃、飞行等）
  int32 effect_target_id = 6;
  
  // 格子上的棋子列表
  repeated int32 chess_ids = 7;
}

// 位置信息
message Position {
  // X坐标
  float x = 1;
  
  // Y坐标
  float y = 2;
}

// 骰子信息
message Dice {
  // 骰子点数（1-6）
  int32 value = 1;
  
  // 是否正在投掷中
  bool rolling = 2;
  
  // 投掷动画持续时间（毫秒）
  int32 animation_duration = 3;
}

// 移动路径
message MovePath {
  // 棋子ID
  int32 chess_id = 1;
  
  // 起始格子ID
  int32 start_grid_id = 2;
  
  // 目标格子ID
  int32 target_grid_id = 3;
  
  // 路径上的格子ID列表
  repeated int32 path_grid_ids = 4;
  
  // 移动步数
  int32 steps = 5;
  
  // 特殊效果类型
  SpecialEffectType effect_type = 6;
  
  // 特殊效果目标（如被撞的棋子ID）
  repeated int32 effect_targets = 7;
}
