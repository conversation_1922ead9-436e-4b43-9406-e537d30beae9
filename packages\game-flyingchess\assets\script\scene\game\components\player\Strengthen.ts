import {
    _decorator,
    Component,
    instantiate,
    isValid,
    Label,
    log,
    Node,
    Prefab,
    Animation,
    Skeleton,
    sp,
    Sprite,
    SpriteFrame,
    tween,
    Vec3,
} from 'cc'
import { BaseComponent } from '@/core/manager/gui/base/BaseComponent'
import { AudioEffectConstant } from '@/core/business/constant'
import { cat } from '@/core/manager'
import { create } from '@bufbuild/protobuf'
import store from '@/core/business/store'
import { Player, PlayerSchema } from '@/pb-generate/server/pirate/v1/player_pb'

import { PlayerItem } from './PlayerItem'

import { State as DeskState } from '@/pb-generate/server/pirate/v1/state_pb'

const { ccclass, property } = _decorator

@ccclass('Strengthen')
export class Strengthen extends BaseComponent {
    @property({ type: Node, tooltip: '强化特效动画节点' })
    spine_slot: Node

    @property({ type: Node, tooltip: '强化次数节点' })
    count_strengthen_node: Node

    @property({ type: Sprite, tooltip: '强化次数' })
    strengthen_count: Sprite = null!
    @property({ type: [SpriteFrame], tooltip: '强化次数精灵图集' })
    strengthen_count_spriteFrame: SpriteFrame[] = []

    @property({ type: sp.Skeleton })
    spine_strengthen: sp.Skeleton = null!

    override props = {
        player: create(PlayerSchema),
    }

    protected override initUI(): void {
        // this.spine_strengthen.node.active = false
    }

    protected override onLoad(): void {}

    protected override onAutoObserver(): void {
        this.addAutorun([
            () => {
                if (this.props.player.drawableCount > 0) {
                    this.updateStrengthenCount(this.props.player.drawableCount)
                } else {
                    this.node.active = false
                }
            },
        ])
    }

    override onDestroy(): void {}

    /**更新强化次数 */
    updateStrengthenCount(count: number = 0) {
        const node = this.count_strengthen_node.parent!
        this.strengthen_count.spriteFrame =
            this.strengthen_count_spriteFrame[count - 1]
        node.active = true
        tween(node)
            .to(0.2, { scale: new Vec3(1.2, 1.2, 0) })
            .to(0.2, { scale: Vec3.ONE })
            .start()
    }

    hide() {
        this.node.active = false
    }
}
