// @generated by protoc-gen-es v2.2.5 with parameter "target=ts,json_types=true"
// @generated from file pirate/v1/game.proto (package pirate.v1, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage } from "@bufbuild/protobuf/codegenv1";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv1";
import type { PlayerInfoMessage, PlayerInfoMessageJson } from "./player_pb";
import { file_pirate_v1_player } from "./player_pb";
import type { Card, CardJson, CardState, CardStateJson } from "./card_pb";
import { file_pirate_v1_card } from "./card_pb";
import type { State, StateJson } from "./state_pb";
import { file_pirate_v1_state } from "./state_pb";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file pirate/v1/game.proto.
 */
export const file_pirate_v1_game: GenFile = /*@__PURE__*/
  fileDesc("ChRwaXJhdGUvdjEvZ2FtZS5wcm90bxIJcGlyYXRlLnYxIvQEChFHYW1lSW5mb0Jyb2FkY2FzdBIRCgliYXR0bGVfaWQYASABKAkSOAoJZGVja19pbmZvGAIgASgLMiUucGlyYXRlLnYxLkdhbWVJbmZvQnJvYWRjYXN0LkRlY2tJbmZvEjoKCnN0YXRlX2luZm8YAyABKAsyJi5waXJhdGUudjEuR2FtZUluZm9Ccm9hZGNhc3QuU3RhdGVJbmZvEjoKCnJvdW5kX2luZm8YBCABKAsyJi5waXJhdGUudjEuR2FtZUluZm9Ccm9hZGNhc3QuUm91bmRJbmZvGswBCghEZWNrSW5mbxIXCg9kZWNrX2NhcmRfdG90YWwYASABKAUSIQoZZGVja19yZW1haW5pbmdfY2FyZF9jb3VudBgCIAEoBRInCh9kZWNrX3JlbWFpbmluZ19kZWF0aF9jYXJkX2NvdW50GAMgASgFEi4KEGRlY2tfY2FyZF9zdGF0ZXMYBCADKA4yFC5waXJhdGUudjEuQ2FyZFN0YXRlEisKEmRlY2tfZGlzY2FyZF9jYXJkcxgFIAMoDjIPLnBpcmF0ZS52MS5DYXJkGncKCVN0YXRlSW5mbxIfCgVzdGF0ZRgBIAEoDjIQLnBpcmF0ZS52MS5TdGF0ZRIUCgxjb3VudGRvd25fbXMYAiABKAMSGAoQc2VydmVyX3RpbWVzdGFtcBgDIAEoAxIZChFzdGFydGVkX3RpbWVzdGFtcBgEIAEoAxpSCglSb3VuZEluZm8SEwoLcm91bmRfY291bnQYASABKAUSHQoVY3VycmVudF9wbGF5ZXJfY3Vyc29yGAIgASgFEhEKCWNsb2Nrd2lzZRgDIAEoCCISChBSYW5raW5nQnJvYWRjYXN0Ij4KE0NsaWVudERhdGFCcm9hZGNhc3QSGQoRZnJvbV9wbGF5ZXJfaW5kZXgYASABKAUSDAoEZGF0YRgCIAEoCSLKAQoTU2VydmVyRGF0YUJyb2FkY2FzdBIZChFmcm9tX3BsYXllcl9pbmRleBgBIAEoBRI1CgZhY3Rpb24YAiABKA4yJS5waXJhdGUudjEuU2VydmVyRGF0YUJyb2FkY2FzdC5BY3Rpb24SDAoEZGF0YRgDIAEoDCJTCgZBY3Rpb24SDwoLVU5TUEVDSUZJRUQQABIRCg1EUkFXX1NFTEVDVEVEEAESEgoOU1RFQUxfU0VMRUNURUQQAhIRCg1QRUVLX1NFTEVDVEVEEAMiKgoSU2VydmVyU2VsZWN0ZWREYXRhEhQKDGNhcmRfaW5kaWNlcxgBIAMoBSJyCgxTeW5jUmVzcG9uc2USMQoLcGxheWVyX2luZm8YASABKAsyHC5waXJhdGUudjEuUGxheWVySW5mb01lc3NhZ2USLwoJZ2FtZV9pbmZvGAIgASgLMhwucGlyYXRlLnYxLkdhbWVJbmZvQnJvYWRjYXN0IhMKEUdhbWVPdmVyQnJvYWRjYXN0YgZwcm90bzM", [file_pirate_v1_player, file_pirate_v1_card, file_pirate_v1_state]);

/**
 * --- 公共 ---
 * 状态机的广播
 *
 * @generated from message pirate.v1.GameInfoBroadcast
 */
export type GameInfoBroadcast = Message<"pirate.v1.GameInfoBroadcast"> & {
  /**
   * 战局ID
   *
   * @generated from field: string battle_id = 1;
   */
  battleId: string;

  /**
   * Deck 牌的信息
   *
   * @generated from field: pirate.v1.GameInfoBroadcast.DeckInfo deck_info = 2;
   */
  deckInfo?: GameInfoBroadcast_DeckInfo;

  /**
   * 当前状态信息
   *
   * @generated from field: pirate.v1.GameInfoBroadcast.StateInfo state_info = 3;
   */
  stateInfo?: GameInfoBroadcast_StateInfo;

  /**
   * 回合信息
   *
   * @generated from field: pirate.v1.GameInfoBroadcast.RoundInfo round_info = 4;
   */
  roundInfo?: GameInfoBroadcast_RoundInfo;
};

/**
 * --- 公共 ---
 * 状态机的广播
 *
 * @generated from message pirate.v1.GameInfoBroadcast
 */
export type GameInfoBroadcastJson = {
  /**
   * 战局ID
   *
   * @generated from field: string battle_id = 1;
   */
  battleId?: string;

  /**
   * Deck 牌的信息
   *
   * @generated from field: pirate.v1.GameInfoBroadcast.DeckInfo deck_info = 2;
   */
  deckInfo?: GameInfoBroadcast_DeckInfoJson;

  /**
   * 当前状态信息
   *
   * @generated from field: pirate.v1.GameInfoBroadcast.StateInfo state_info = 3;
   */
  stateInfo?: GameInfoBroadcast_StateInfoJson;

  /**
   * 回合信息
   *
   * @generated from field: pirate.v1.GameInfoBroadcast.RoundInfo round_info = 4;
   */
  roundInfo?: GameInfoBroadcast_RoundInfoJson;
};

/**
 * Describes the message pirate.v1.GameInfoBroadcast.
 * Use `create(GameInfoBroadcastSchema)` to create a new message.
 */
export const GameInfoBroadcastSchema: GenMessage<GameInfoBroadcast, GameInfoBroadcastJson> = /*@__PURE__*/
  messageDesc(file_pirate_v1_game, 0);

/**
 * @generated from message pirate.v1.GameInfoBroadcast.DeckInfo
 */
export type GameInfoBroadcast_DeckInfo = Message<"pirate.v1.GameInfoBroadcast.DeckInfo"> & {
  /**
   * 桌面卡牌总数
   *
   * @generated from field: int32 deck_card_total = 1;
   */
  deckCardTotal: number;

  /**
   * 桌面剩余牌数量
   *
   * @generated from field: int32 deck_remaining_card_count = 2;
   */
  deckRemainingCardCount: number;

  /**
   * 桌面死亡牌（弱点）剩余数量
   *
   * @generated from field: int32 deck_remaining_death_card_count = 3;
   */
  deckRemainingDeathCardCount: number;

  /**
   * 桌面卡牌信息
   *
   * @generated from field: repeated pirate.v1.CardState deck_card_states = 4;
   */
  deckCardStates: CardState[];

  /**
   * 桌面已经出的牌的信息
   *
   * @generated from field: repeated pirate.v1.Card deck_discard_cards = 5;
   */
  deckDiscardCards: Card[];
};

/**
 * @generated from message pirate.v1.GameInfoBroadcast.DeckInfo
 */
export type GameInfoBroadcast_DeckInfoJson = {
  /**
   * 桌面卡牌总数
   *
   * @generated from field: int32 deck_card_total = 1;
   */
  deckCardTotal?: number;

  /**
   * 桌面剩余牌数量
   *
   * @generated from field: int32 deck_remaining_card_count = 2;
   */
  deckRemainingCardCount?: number;

  /**
   * 桌面死亡牌（弱点）剩余数量
   *
   * @generated from field: int32 deck_remaining_death_card_count = 3;
   */
  deckRemainingDeathCardCount?: number;

  /**
   * 桌面卡牌信息
   *
   * @generated from field: repeated pirate.v1.CardState deck_card_states = 4;
   */
  deckCardStates?: CardStateJson[];

  /**
   * 桌面已经出的牌的信息
   *
   * @generated from field: repeated pirate.v1.Card deck_discard_cards = 5;
   */
  deckDiscardCards?: CardJson[];
};

/**
 * Describes the message pirate.v1.GameInfoBroadcast.DeckInfo.
 * Use `create(GameInfoBroadcast_DeckInfoSchema)` to create a new message.
 */
export const GameInfoBroadcast_DeckInfoSchema: GenMessage<GameInfoBroadcast_DeckInfo, GameInfoBroadcast_DeckInfoJson> = /*@__PURE__*/
  messageDesc(file_pirate_v1_game, 0, 0);

/**
 * @generated from message pirate.v1.GameInfoBroadcast.StateInfo
 */
export type GameInfoBroadcast_StateInfo = Message<"pirate.v1.GameInfoBroadcast.StateInfo"> & {
  /**
   * 当前状态
   *
   * @generated from field: pirate.v1.State state = 1;
   */
  state: State;

  /**
   * 倒计时剩余时间（ms)，0表示没有倒计时
   *
   * @generated from field: int64 countdown_ms = 2;
   */
  countdownMs: bigint;

  /**
   * 服务器时间戳（ms）
   *
   * @generated from field: int64 server_timestamp = 3;
   */
  serverTimestamp: bigint;

  /**
   * 游戏开始时间戳（ms）
   *
   * @generated from field: int64 started_timestamp = 4;
   */
  startedTimestamp: bigint;
};

/**
 * @generated from message pirate.v1.GameInfoBroadcast.StateInfo
 */
export type GameInfoBroadcast_StateInfoJson = {
  /**
   * 当前状态
   *
   * @generated from field: pirate.v1.State state = 1;
   */
  state?: StateJson;

  /**
   * 倒计时剩余时间（ms)，0表示没有倒计时
   *
   * @generated from field: int64 countdown_ms = 2;
   */
  countdownMs?: string;

  /**
   * 服务器时间戳（ms）
   *
   * @generated from field: int64 server_timestamp = 3;
   */
  serverTimestamp?: string;

  /**
   * 游戏开始时间戳（ms）
   *
   * @generated from field: int64 started_timestamp = 4;
   */
  startedTimestamp?: string;
};

/**
 * Describes the message pirate.v1.GameInfoBroadcast.StateInfo.
 * Use `create(GameInfoBroadcast_StateInfoSchema)` to create a new message.
 */
export const GameInfoBroadcast_StateInfoSchema: GenMessage<GameInfoBroadcast_StateInfo, GameInfoBroadcast_StateInfoJson> = /*@__PURE__*/
  messageDesc(file_pirate_v1_game, 0, 1);

/**
 * @generated from message pirate.v1.GameInfoBroadcast.RoundInfo
 */
export type GameInfoBroadcast_RoundInfo = Message<"pirate.v1.GameInfoBroadcast.RoundInfo"> & {
  /**
   * 回合数
   *
   * @generated from field: int32 round_count = 1;
   */
  roundCount: number;

  /**
   * 当前玩家游标
   *
   * @generated from field: int32 current_player_cursor = 2;
   */
  currentPlayerCursor: number;

  /**
   * 游戏方向：顺时针（true）或逆时针（false）
   *
   * @generated from field: bool clockwise = 3;
   */
  clockwise: boolean;
};

/**
 * @generated from message pirate.v1.GameInfoBroadcast.RoundInfo
 */
export type GameInfoBroadcast_RoundInfoJson = {
  /**
   * 回合数
   *
   * @generated from field: int32 round_count = 1;
   */
  roundCount?: number;

  /**
   * 当前玩家游标
   *
   * @generated from field: int32 current_player_cursor = 2;
   */
  currentPlayerCursor?: number;

  /**
   * 游戏方向：顺时针（true）或逆时针（false）
   *
   * @generated from field: bool clockwise = 3;
   */
  clockwise?: boolean;
};

/**
 * Describes the message pirate.v1.GameInfoBroadcast.RoundInfo.
 * Use `create(GameInfoBroadcast_RoundInfoSchema)` to create a new message.
 */
export const GameInfoBroadcast_RoundInfoSchema: GenMessage<GameInfoBroadcast_RoundInfo, GameInfoBroadcast_RoundInfoJson> = /*@__PURE__*/
  messageDesc(file_pirate_v1_game, 0, 2);

/**
 * @generated from message pirate.v1.RankingBroadcast
 */
export type RankingBroadcast = Message<"pirate.v1.RankingBroadcast"> & {
};

/**
 * @generated from message pirate.v1.RankingBroadcast
 */
export type RankingBroadcastJson = {
};

/**
 * Describes the message pirate.v1.RankingBroadcast.
 * Use `create(RankingBroadcastSchema)` to create a new message.
 */
export const RankingBroadcastSchema: GenMessage<RankingBroadcast, RankingBroadcastJson> = /*@__PURE__*/
  messageDesc(file_pirate_v1_game, 1);

/**
 * @generated from message pirate.v1.ClientDataBroadcast
 */
export type ClientDataBroadcast = Message<"pirate.v1.ClientDataBroadcast"> & {
  /**
   * 发起玩家索引
   *
   * @generated from field: int32 from_player_index = 1;
   */
  fromPlayerIndex: number;

  /**
   * @generated from field: string data = 2;
   */
  data: string;
};

/**
 * @generated from message pirate.v1.ClientDataBroadcast
 */
export type ClientDataBroadcastJson = {
  /**
   * 发起玩家索引
   *
   * @generated from field: int32 from_player_index = 1;
   */
  fromPlayerIndex?: number;

  /**
   * @generated from field: string data = 2;
   */
  data?: string;
};

/**
 * Describes the message pirate.v1.ClientDataBroadcast.
 * Use `create(ClientDataBroadcastSchema)` to create a new message.
 */
export const ClientDataBroadcastSchema: GenMessage<ClientDataBroadcast, ClientDataBroadcastJson> = /*@__PURE__*/
  messageDesc(file_pirate_v1_game, 2);

/**
 * 服务端自定义数据广播
 *
 * @generated from message pirate.v1.ServerDataBroadcast
 */
export type ServerDataBroadcast = Message<"pirate.v1.ServerDataBroadcast"> & {
  /**
   * 发起玩家索引
   *
   * @generated from field: int32 from_player_index = 1;
   */
  fromPlayerIndex: number;

  /**
   * 操作类型
   *
   * @generated from field: pirate.v1.ServerDataBroadcast.Action action = 2;
   */
  action: ServerDataBroadcast_Action;

  /**
   * 数据内容，proto 类型二进制消息，不同 action 对应的 data 类型不一样
   *
   * @generated from field: bytes data = 3;
   */
  data: Uint8Array;
};

/**
 * 服务端自定义数据广播
 *
 * @generated from message pirate.v1.ServerDataBroadcast
 */
export type ServerDataBroadcastJson = {
  /**
   * 发起玩家索引
   *
   * @generated from field: int32 from_player_index = 1;
   */
  fromPlayerIndex?: number;

  /**
   * 操作类型
   *
   * @generated from field: pirate.v1.ServerDataBroadcast.Action action = 2;
   */
  action?: ServerDataBroadcast_ActionJson;

  /**
   * 数据内容，proto 类型二进制消息，不同 action 对应的 data 类型不一样
   *
   * @generated from field: bytes data = 3;
   */
  data?: string;
};

/**
 * Describes the message pirate.v1.ServerDataBroadcast.
 * Use `create(ServerDataBroadcastSchema)` to create a new message.
 */
export const ServerDataBroadcastSchema: GenMessage<ServerDataBroadcast, ServerDataBroadcastJson> = /*@__PURE__*/
  messageDesc(file_pirate_v1_game, 3);

/**
 * @generated from enum pirate.v1.ServerDataBroadcast.Action
 */
export enum ServerDataBroadcast_Action {
  /**
   * @generated from enum value: UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * @generated from enum value: DRAW_SELECTED = 1;
   */
  DRAW_SELECTED = 1,

  /**
   * @generated from enum value: STEAL_SELECTED = 2;
   */
  STEAL_SELECTED = 2,

  /**
   * @generated from enum value: PEEK_SELECTED = 3;
   */
  PEEK_SELECTED = 3,
}

/**
 * @generated from enum pirate.v1.ServerDataBroadcast.Action
 */
export type ServerDataBroadcast_ActionJson = "UNSPECIFIED" | "DRAW_SELECTED" | "STEAL_SELECTED" | "PEEK_SELECTED";

/**
 * Describes the enum pirate.v1.ServerDataBroadcast.Action.
 */
export const ServerDataBroadcast_ActionSchema: GenEnum<ServerDataBroadcast_Action, ServerDataBroadcast_ActionJson> = /*@__PURE__*/
  enumDesc(file_pirate_v1_game, 3, 0);

/**
 * 服务端选择数据
 *
 * @generated from message pirate.v1.ServerSelectedData
 */
export type ServerSelectedData = Message<"pirate.v1.ServerSelectedData"> & {
  /**
   * 关系到的牌的索引
   *
   * @generated from field: repeated int32 card_indices = 1;
   */
  cardIndices: number[];
};

/**
 * 服务端选择数据
 *
 * @generated from message pirate.v1.ServerSelectedData
 */
export type ServerSelectedDataJson = {
  /**
   * 关系到的牌的索引
   *
   * @generated from field: repeated int32 card_indices = 1;
   */
  cardIndices?: number[];
};

/**
 * Describes the message pirate.v1.ServerSelectedData.
 * Use `create(ServerSelectedDataSchema)` to create a new message.
 */
export const ServerSelectedDataSchema: GenMessage<ServerSelectedData, ServerSelectedDataJson> = /*@__PURE__*/
  messageDesc(file_pirate_v1_game, 4);

/**
 * 客户端重连时，会调用Sync方法，服务端返回当前玩家和游戏信息
 *
 * @generated from message pirate.v1.SyncResponse
 */
export type SyncResponse = Message<"pirate.v1.SyncResponse"> & {
  /**
   * @generated from field: pirate.v1.PlayerInfoMessage player_info = 1;
   */
  playerInfo?: PlayerInfoMessage;

  /**
   * @generated from field: pirate.v1.GameInfoBroadcast game_info = 2;
   */
  gameInfo?: GameInfoBroadcast;
};

/**
 * 客户端重连时，会调用Sync方法，服务端返回当前玩家和游戏信息
 *
 * @generated from message pirate.v1.SyncResponse
 */
export type SyncResponseJson = {
  /**
   * @generated from field: pirate.v1.PlayerInfoMessage player_info = 1;
   */
  playerInfo?: PlayerInfoMessageJson;

  /**
   * @generated from field: pirate.v1.GameInfoBroadcast game_info = 2;
   */
  gameInfo?: GameInfoBroadcastJson;
};

/**
 * Describes the message pirate.v1.SyncResponse.
 * Use `create(SyncResponseSchema)` to create a new message.
 */
export const SyncResponseSchema: GenMessage<SyncResponse, SyncResponseJson> = /*@__PURE__*/
  messageDesc(file_pirate_v1_game, 5);

/**
 * todo
 *
 * @generated from message pirate.v1.GameOverBroadcast
 */
export type GameOverBroadcast = Message<"pirate.v1.GameOverBroadcast"> & {
};

/**
 * todo
 *
 * @generated from message pirate.v1.GameOverBroadcast
 */
export type GameOverBroadcastJson = {
};

/**
 * Describes the message pirate.v1.GameOverBroadcast.
 * Use `create(GameOverBroadcastSchema)` to create a new message.
 */
export const GameOverBroadcastSchema: GenMessage<GameOverBroadcast, GameOverBroadcastJson> = /*@__PURE__*/
  messageDesc(file_pirate_v1_game, 6);

