import { Store } from "../index";
import { BaseStore } from "../BaseStore";
import { makeAutoObservable } from "mobx-tsuki";

export default class Lobby extends BaseStore {

    constructor(rootStore: Store) {
        super(rootStore)
        makeAutoObservable(this)
    }

    profiles: Map<number, IBridge.Profile> = new Map()

    roomList: IBridge.RoomListItem[] = []

    levelRank: IBridge.LevelRankRes = {
        /**榜单数据 */
        rankList: [],
        /**用户排名 */
        userRank: null
    }

    tierRank: IBridge.TierRankRes = {
        /**赛季列表 */
        gameSeasons: [],
        /**榜单数据 */
        rankList: [],
        /**用户个人段位信息 */
        userRank: null
    }


    /**匹配大厅 */
    matchRoom: IBridge.MatchRoomRes = {
        ddRid: '',
        roomSerial: '',
        imUsername: '',
        impassword: '',
        swToken: '',
        userId: 0,
        partyMode: 1,
    }


}