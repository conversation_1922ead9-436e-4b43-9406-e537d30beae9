import { Prefab, Node, Component, _decorator, CCClass } from 'cc'

import { BaseComponent } from '../base/BaseComponent'
import { GameEventConstant } from '@/core/business/constant'
import { GlobalEventConstant } from '../../constant'
import { cat } from '../../index'
import { LayerType } from '../Gui'

/**遮罩管理 TODO (每个层级都有一个遮罩 但是同时只能出现1个) */
const { ccclass, property } = _decorator

@ccclass('BlackMask')
export class BlackMask extends BaseComponent {
    @property({ type: Node, tooltip: '动画节点' })
    tween: Node
}
