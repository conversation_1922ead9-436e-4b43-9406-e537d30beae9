/**
 * @describe
 * <AUTHOR>
 * @date 2024-09-12 11:35:02
 */

import { error, log, native } from 'cc'
import { ANDROID, IOS } from 'cc/env'
import { Bridge } from './Bridge'
import { cat } from '@/core/manager'

type MethodNameMap = {
    [Bridge.MethodName.CocosBridge.LOAD_API_REQUEST]:
        | Bridge.EventName.APIName
        | Bridge.EventName.APIV2Name
    [Bridge.MethodName.CocosBridge
        .LOAD_BRIDGE_REQUEST]: Bridge.EventName.BridgeName
    [Bridge.MethodName.CocosBridge
        .LOAD_COMMON_REQUEST]: Bridge.EventName.CommonName
    [Bridge.MethodName.CocosBridge
        .LOAD_CHATROOM_REQUEST]: Bridge.EventName.ChatRoomName
    // 其他桥接类型和对应的枚举类型...
}

export class CocosAPI {
    protected Aandroid_BRIDGE_CLASS_NAME = 'com.stnts.cocos.CocosApiManager'

    protected IOS_BRIDGE_CLASS_NAME = 'CocosManager'

    private iosCallStaticMethod<T extends Bridge.MethodName.CocosBridge, K>(
        bridgeType: T,
        methodName: MethodNameMap[T],
        params?: K
    ) {
        native.reflection.callStaticMethod(
            this.IOS_BRIDGE_CLASS_NAME,
            `${bridgeType}:`,
            JSON.stringify({ name: methodName, para: params ?? '' })
        )
    }

    private androidCallStaticMethod<T extends Bridge.MethodName.CocosBridge, K>(
        bridgeType: T,
        methodName: MethodNameMap[T],
        params?: K
    ) {
        window.ccLog(
            'androidCallStaticMethod',
            bridgeType,
            methodName,
            JSON.stringify(params)
        )
        let result = native.reflection.callStaticMethod(
            this.Aandroid_BRIDGE_CLASS_NAME,
            bridgeType,
            '(Ljava/lang/String;)Ljava/lang/String;',
            JSON.stringify({ name: methodName, para: params ?? '' })
        )

        if (bridgeType == Bridge.MethodName.CocosBridge.LOAD_BRIDGE_REQUEST) {
            if (result == 'failure') {
                if (methodName != Bridge.EventName.BridgeName.CLOSE_DIALOG) {
                    cat.gui.showToast({ title: '功能未开放，敬请期待!' })
                }
            }
        }
    }

    callStaticMethod<T extends Bridge.MethodName.CocosBridge, K>(
        bridgeType: T,
        methodName: MethodNameMap[T],
        params?: K
    ) {
        if (IOS) {
            // iOS 平台方法调用逻辑
            this.iosCallStaticMethod(bridgeType, methodName, params)
        } else if (ANDROID) {
            // Android 平台方法调用逻辑
            this.androidCallStaticMethod(bridgeType, methodName, params)
        } else {
            error('非NATIVE平台,无法使用bridge')
        }
        return this
    }
}
