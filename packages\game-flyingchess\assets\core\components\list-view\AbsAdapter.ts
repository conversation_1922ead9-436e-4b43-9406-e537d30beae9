/**数据绑定的辅助适配器 */

import { Node } from 'cc';

export abstract class AbsAdapter<T> {
    private onItemClickListener: (adapter: AbsAdapter<T>, posIndex: number) => void;

    private dataSet: T[] = [];

    public setDataSet<K extends T>(data: K[]) {
        this.dataSet = data || [];
    }

    public getCount(): number {
        return this.dataSet ? this.dataSet.length : 0;
    }

    public getItem(posIndex: number): any {
        return this.dataSet[posIndex];
    }

    public _getView(item: Node, posIndex: number): Node {
        this.updateView(item, posIndex, this.dataSet[posIndex]);
        // if (this.onItemClickListener) {
        //     Common.setClickListenerAnim(item, true, this.onItemClicked, this, posIndex);
        // }
        return item;
    }

    public setOnItemClickListener(l: (adapter: AbsAdapter<T>, posIndex: number) => void, target: any) {
        this.onItemClickListener = target ? l.bind(target) : l;
    }

    private onItemClicked(event: Event, posIndex: number) {
        if (this.onItemClickListener) {
            this.onItemClickListener(this, posIndex);
        }
    }

    public abstract updateView(item: Node, posIndex: number, data?: T): void
}