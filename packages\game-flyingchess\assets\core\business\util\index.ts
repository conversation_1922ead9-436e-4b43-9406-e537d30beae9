/**
 * @describe 工具类
 * <AUTHOR>
 * @date 2023-08-02 20:16:42
 */

import * as ArrayUtils from './ArrayUtils'
import * as BlobUtils from './BlobUtils'
import * as EncryptUtil from './EncryptUtil'
import * as StringUtil from './StringUtil'
import * as TimeUtils from './TimeUtils'
import * as NodeUtils from './NodeUtils'
import * as Commontils from './Commontils'
import { BaseManager } from '@/core/manager/BaseManager'

export class Util extends BaseManager {
    arrayUtils = ArrayUtils
    blobUtils = BlobUtils
    encryptUtil = EncryptUtil
    stringUtil = StringUtil
    timeUtils = TimeUtils
    nodeUtils = NodeUtils
    commontils = Commontils
}
