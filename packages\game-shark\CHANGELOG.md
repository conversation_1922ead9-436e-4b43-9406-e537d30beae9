# 小心鲨手 - 更新日志

本项目的所有重要更改都将记录在此文件中。


### 0.1.1 (2025-05-28)


### 📦 构建系统

* pb 生成 ([9b28777](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/9b2877763bb7dfdb96efe87d8407cf70411afd2e))


### ♻️ 代码重构

* 补充 debugout.ts 类型 ([471dcae](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/471dcae52ba983fabc3e39ab9a8f13eee5d03c80))
* 代码简化 ([c09c9e0](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/c09c9e079ea87838a50313119e695b1bed873f73))
* 简化玩家头像及播放状态动效相关的机制 ([037405d](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/037405d21d76c1348060d408ca4f3364c8f65705))
* 模块引用路径优化 ([456ac21](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/456ac21c581eefe9e6856c882fcf570a5a38e02e))
* 模块引用路径优化 ([f7d9963](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/f7d9963340b9cf3bda919defead8e38fc7c31b52))
* 模块引用路径优化 ([4007e97](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/4007e973bf8ce234f82a00d4af85a68926051c22))
* 模块引用路径优化 ([f262d70](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/f262d70dbbf04190bfb0a2cde5f4ea964a7639ab))
* 模块引用路径优化 ([c3f4b2e](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/c3f4b2e5b04af625d6b950a31a11237f14a20a8e))
* 项目结构和依赖调整 ([5620df2](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/5620df27cb98faa5598e0a425a5619e11ebb999d))
* 重构装扮 ([a10dd4e](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/a10dd4ebf01d9f82a307d90285e39d1d44bfd24d))
* 重构plist相关组件，解决类型报错 ([310e5d1](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/310e5d1d0be5ca66f14cafbad2592c74617e84ca))
* 重命名组件 & 重新挂载 ([8a9ad51](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/8a9ad5192912699f5e01bab9c21bb479efb5ce68))
* CardAnimationManager ([721e34b](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/721e34b6f306a172f83335f5f85e279de5068ad3))
* start scene 和对应的挂载脚本迁移目录 ([fbddecb](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/fbddecb00cb118ef95f87b24d4e57cd46bb49028))


### ✨ 新功能

* 3D摄像机位置、鲨鱼放大位置基准点、玩家头像布局调整 ([18d99dd](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/18d99dd908f2bc2d2291483332d230226f47009e))
* 3D摄像机位置、鲨鱼放大位置基准点、玩家头像布局调整 ([d0959b3](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/d0959b3ff28586f23b56aa47c0a118119c1756b7))
* 按牙/诊断组件在特定时机增加 disableTouch 桥接调用 ([dcc1ce4](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/dcc1ce40c62ea2449399245903c52bf3d397ec47))
* 按牙按钮根据次数不同显示不同资源 ([3bd28a0](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/3bd28a073cd2dd51e24643a563cd03674e26b8fe))
* 按牙结束鲨鱼收起时收起选中的手牌 ([2413d3d](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/2413d3d47d0f37e9117f850de25f9dca67f764ba))
* 按牙界面鲨鱼模型拉近放大 ([1afd2b7](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/1afd2b70577f7faebc9b6ad59be6bb78eb243008))
* 按牙界面UI布局调整 ([666a69f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/666a69fb868980f630e8ff27b7491a644ca06a07))
* 背景音音量调整 ([48d6585](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/48d6585aa3df1b6fd8b5b279d5033f408d71f36c))
* 本地环境不开启 debugout ([ec16115](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/ec16115a0d67857d931e168fc33453d773187a0c))
* 补充jsbridge文档 ([1c9aa92](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/1c9aa92ba055b29a59bd924a25a48ec7f971d129))
* 不再显示小结算信息 ([327debf](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/327debf072e7ccfab909008dcc2b27960ba277a6))
* 操作鲨鱼模型时，下方游戏页面中的“按牙”和“出牌”的按键隐藏 ([a35fbc7](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/a35fbc701e7a2b57991e2dd17fad302bec684fef))
* 测试本地加载 ([1596d4c](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/1596d4c87b25f807364e9e83dd75925e24b61041))
* 测试服调整 ([5369c05](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/5369c05583b278c44bd04a418f4e446eed6fc5da))
* 测试服调整 ([27fa11f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/27fa11fc1b157f0eb070c795f864689860d1ac44))
* 测试环境 ([cb053cb](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/cb053cbee35b76cb922923f81ffbb635096c9413))
* 带云朵的背景图位置下移 ([e7e666f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/e7e666f54a6cb709a821c68058bbc7bb467a8db3))
* 当url传了 params 参数时，就还是走旧的流程 ([f484ca7](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/f484ca7a71a548df1b925871bcf028ea8dac6667))
* 导出日志提取到GUI中，在任何场景下都可以点击 ([759e570](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/759e570e90434d98a8680504977c1a9a1d37db11))
* 导出日志提取到GUI中，在任何场景下都可以点击 ([948faba](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/948fabadca0e9e0097778ac806ad6fbdb54f916d))
* 导出日志提取到GUI中，在任何场景下都可以点击 ([d426fad](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/d426fad224333312a6818fa6f25c8ee5a74e3a68))
* 导出日志提取到GUI中，在任何场景下都可以点击 ([0541da2](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/0541da27eb7adafa0038be8889193147cbd3bc97))
* 倒计时效果2 ([517078a](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/517078afe5ed3cc8782cfe176f44150b2fe15673))
* 点按牙齿碰撞检测由射线检测改为球体投射检测，扩大检测范围 ([3b884cc](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/3b884cc6e09d98946c901a70249e4b9889ebee2b))
* 点击按牙流程优化 ([e262de7](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/e262de720f10b513fe9abedf6ce1bda453bab5c3))
* 点击摸牌区弹出按牙界面相关调整 ([2b08021](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/2b08021731b87544eec519206c8e496a45af5e78))
* 调用closeGame增加参数 ([f4a5a3a](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/f4a5a3aa9ba3437ad8bce2b4b2a0ef0641b9ead8))
* 调整UI遮罩层不透明度 ([f1e2c41](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/f1e2c41bcf4d42585e42026f856d87c81a2c2c85))
* 放大后的鲨鱼模型位置改为动态计算，更好的适配不同分辨率屏幕 ([cd1ac03](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/cd1ac0375a03eea55a4f4cb0f7bce4f8d255d790))
* 攻击流程优化 ([c5ed67e](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/c5ed67ec4d51feff471fc9560c74ce581bda335d))
* 构建开启debug模式 ([2901534](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/2901534bb8af73250ca8a1be2933e92ac9749e1f))
* 关闭多点触控 ([d92eda3](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/d92eda3f573c19d71625533ad43aefee243de204))
* 关闭网络错误提示后，不销毁ws，确保还能继续重连 ([a04197a](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/a04197aea56afb693857b46dee3c15548fd75b28))
* 关闭阴影 ([4a5d3f4](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/4a5d3f4a14919be45b7ef0ba821a9323973539f6))
* 关闭阴影 & 锁30帧 ([31e4929](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/31e4929833dfd15e78d45cb972225f05a3ce677c))
* 关闭debugout ([a7449b2](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/a7449b29456c12bf8a53b4d5242e9c3e94ec200c))
* 光照 & 阴影 ([5f51706](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/5f51706dd4bb7d6d35f4d1b658448c8a5161559f))
* 光照效果调整 ([66d5644](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/66d564472b970c82d4128a72565ee9992b11fdf6))
* 合图 ([8d8e4c2](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/8d8e4c2e6162de6d819fbe055fdd7cb360f010aa))
* 合图调整 ([ef27b9b](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/ef27b9b8dd4731c26193766a7951a3766d180fa3))
* 恢复disableTouch桥接 ([73db4e3](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/73db4e388ab8a6ad028beba71c9125d6b03d8155))
* 进入摸牌出牌阶段时清空手牌选择状态 ([e8a7d53](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/e8a7d53fd59aa0b953bba8ce7ee52de738d1d7d5))
* 卡牌素材替换 ([2cd33cf](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/2cd33cfd402c2cf7c3ad9362bfdd1c222d09f035))
* 客户端加载zip包调试 ([7582eba](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/7582ebaf2e67c5b55dc9aa8c5237d3965279e0ae))
* 联调环境支持url动态传入服务地址 ([08bf255](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/08bf2555bf021ecc64520e3b468b3ba3b0059c11))
* 联调环境支持url动态传入服务地址 ([71eecf4](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/71eecf4853d3cf00ec5793a2332d348432cf6764))
* 联调环境debugMode ([4bdff05](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/4bdff05cf5cec1de24f568bd8e65aa48bbb2079e))
* 联调环境debugMode ([c4d3aef](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/c4d3aefa68b3322682886270bcd009ec0e7842bd))
* 联调环境debugMode ([2a06145](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/2a061453b6a3ee77ede898af272f18109a372ef0))
* 麦位表情 ([72b6995](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/72b699576c41c44944b1bbda658daf90187336d9))
* 麦位表情 ([98edc08](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/98edc082c76436447e673452b1c86f0101e65744))
* 麦位表情 ([b42f3b4](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/b42f3b41483311264f0694a14462c85f4bd360ad))
* 摸牌时的交互动画优化 ([3406962](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/3406962b95c4a84af8a4ca70e6d56ada728647bb))
* 摸牌时鲨鱼模型距离拉远 ([a71bbc5](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/a71bbc569fc3d2d7d56c576cdb1d8d48b4d23028))
* 配合iOS新webview调试背景音播放 ([0d9a829](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/0d9a8296aec1d69cd9b234804d47c0293bb5bf49))
* 配置每个包独立的版本管理，限制changelog只包含当前包的提交 ([8251041](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/8251041e22fcda32bef901f4349e28a0572351bc))
* 屏蔽麦位表情 ([f9a4760](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/f9a4760e24ab0730b08c01cb3d71e1fe8fd6d743))
* 钳子按钮调整 ([2f38247](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/2f38247d38b8fbf7b93703dd4741efb380e5abf7))
* 切后台播放背景音测试 ([5b8eb3c](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/5b8eb3cbae0e45f7c1d4637102e5a49718ee36a2))
* 切后台不断连 ([bf7ac49](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/bf7ac4953ea769ba8ef140ca06bd7803f08d84fd))
* 去掉 loading 场景 ([afffce2](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/afffce2f1a77ad31868d931532656d7801681241))
* 去掉天空盒 ([0fbd1f6](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/0fbd1f608701bfdf824cc912047b03cf5064c435))
* 去掉牙齿阴影，减少drawcall ([8e6ea71](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/8e6ea710d0be4a8267c4f7e4d6be2636940637ea))
* 去掉牙齿组件 instance ([f85e3f9](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/f85e3f91b07816bdfb49cf7cca48e59c31e1181f))
* 日志优化 ([f4a68e4](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/f4a68e47e6605a458f1f3376aa9cf45fe61b8b3b))
* 鲨鱼触摸旋转优化 ([fb112a3](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/fb112a30f297d02c343af845cf20309b7ab90251))
* 鲨鱼触摸旋转优化 ([9c7ecd3](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/9c7ecd30356a3c6a24b230302f094ab7033181eb))
* 鲨鱼触摸旋转优化 ([dc11c93](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/dc11c93b404c77fc625f79d88da34e69dfd85141))
* 鲨鱼位置调整 ([903022b](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/903022b0e567f82c57ba7f2915491440e9590d55))
* 鲨鱼旋转优化 ([5ca378f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/5ca378f63acf21bea7414da1e6e3bf115d2534cd))
* 上报错误优化 ([e85b75a](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/e85b75a96ea4b08e19058d978fc46800df42a3f9))
* 上报错误优化 ([ba4ed1f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/ba4ed1faadf7357f3a3200757ec955ab01adb0d8))
* 剩余牙齿数量不足的场景下诅咒按牙的处理 ([db25f41](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/db25f4161dc338ef093b037a508d80cf774cc8ae))
* 实现测试和联调环境日志导出功能 ([b0aa464](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/b0aa4641b7953dadec337180d311da3b844d8057))
* 实现测试和联调环境日志导出功能 ([c1b93a3](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/c1b93a3a8ff83fbf3cab57bf2232b668a353a441))
* 索取按钮替换 ([9f6aeb1](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/9f6aeb173f703d072a3949a88816705399305dd3))
* 索取动画缩小 ([f74f0ae](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/f74f0ae8e4822ea6be4c08f6ebcd0e0a43e98ee3))
* 索取和休想动画层级调整 ([128ef2c](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/128ef2c187f9a87f78156a8d8b8ffbb91edbf8b5))
* 索取时增加动效 ([5878396](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/58783963bc4bd33ed873e5fdeff3f6035659502a))
* 锁定20帧率 ([a0f517c](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/a0f517c4fe7512dbb5c551d23658bd49a784cb20))
* 逃生界面的布局调整 ([5c1bdb7](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/5c1bdb7893b3306b3713f381b871a44b8351fa5e))
* 逃生界面的布局调整 ([8be3d28](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/8be3d28d793bbafd1763594421ce1e1bf2381e29))
* 通过url传 params的模式在进入游戏后直接加入战斗 ([f890595](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/f890595dca9d9227964177987e5a90c20be2dd0e))
* 同步 shark-suileyoo 分支代码 ([4f5ac9c](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/4f5ac9cd0b9dc7506c8e88c31436e3c60b2fd00c))
* 同步框架 ([5d5cbdd](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/5d5cbdd4fdb5138143b9bbdd77569d4a0784be6c))
* 同步装扮框架代码 & 海盗桶对接装扮 ([81eae5a](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/81eae5ab8bfb35c7a2f146f2862e02480904fd34))
* 同步装扮框架代码 & 海盗桶对接装扮 ([04fa121](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/04fa1215385933a41f67db2f495ef5245808e2a3))
* 同步装扮框架代码 & 海盗桶对接装扮 ([29df5e6](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/29df5e6ffb972bb0173724ba5147629d3911b90c))
* 同步shared 到小心鲨手 ([463205b](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/463205b87ca31004da4907609dc785705432eac4))
* 线上服务配置 ([0a706e8](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/0a706e8c467fd6c319c61f22172551d58a603a2e))
* 线上环境支持 debug=1 开启导出日志模式 ([498aee1](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/498aee1327984ea271e9bdbd11adc0849709c9f4))
* 休想动画 ([6d5f08f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/6d5f08fdf546594ba6f02a42bd04da153cc241ae))
* 修复观战路由 ([8003f3a](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/8003f3a54ddd8d518065db57a7e25c65cae23333))
* 选人调整 ([c2c31df](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/c2c31df081285b983d549f7c3f35ea9aa434f69a))
* 选中卡牌后的提示组件位置调整 ([e485779](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/e485779d3e8c7ae40bce8de3a6a16cb173548c8b))
* 牙齿点击增加防抖 ([dddd3e4](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/dddd3e4a2584ddf16755bb8879290fc31063865f))
* 牙齿点击增加防抖 ([ddecd67](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/ddecd675ea557dc754d5259cb93d1c9f71d21961))
* 已经按下去的牙销毁碰撞检测组件 ([f3ab9be](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/f3ab9bec44e4efac334e302e0db02cea01f8ca03))
* 用户标号改为颜色标签 & 抽取预制体 ([33e6745](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/33e6745ecf4e5faf223c9632f4c8a6f55f5bca5b))
* 用户头像装扮 ([998167c](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/998167c44179e615d6ec98fb22c5ca88f047dad0))
* 用户头像装扮 ([17cd0c0](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/17cd0c0411e7547bb70486279937c1deb50568cd))
* 用户头像装扮 ([036f82a](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/036f82a05857207f5c45b8cf39a4e9c9925f9f17))
* 用户头像装扮 ([4fcf99b](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/4fcf99be461e48e8162eb1c3f7b2a9f741b673b9))
* 用户头像装扮 ([1c2cfc4](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/1c2cfc45bc1773c8aece6030ce012cf914001117))
* 用户头像装扮 ([259a067](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/259a06766c50592530846ff0cae43fc8741b6622))
* 用户头像装扮 ([a50c40f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/a50c40f41e0132ab4b25acff044ada41a68c359c))
* 用户头像装扮 ([309de3d](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/309de3d1b0f4cbe8283a305ef2cf3cafa2db1f34))
* 用户头像装扮 ([ac23aa5](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/ac23aa5bc082866c8d3e2dcb57402215649ab135))
* 用户头像装扮 ([8a429b4](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/8a429b4f75c92ecb70049fbd1aa727fa8d500cae))
* 优化牙齿碰撞体 ([b69437f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/b69437f2ab8a6e2c76b3a846d9925c985b65fcd7))
* 游戏背景 ([8f841ae](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/8f841ae57f2812e28d45bdbb7e0ad5518dfd5ade))
* 游戏的开始暂停 ([4cb142d](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/4cb142d2ba49c58c299b3a14eec803ce2550ae5e))
* 增加 LogMethod 装饰器，方便调试 ([e7689ea](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/e7689eaabc9cce119e7b8d89e06b77a70227608a))
* 增加桥接方法同步到各个项目 ([42600b4](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/42600b42075b1cb4e71442fb06d389f8c45df2d6))
* 增加鲨鱼咬人动画 ([4153a17](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/4153a17dbe7a982bdd3365e27f5f5e547990afd4))
* 增加新的牌桌状态 DESK_STATE_UNSPECIFIED ([2098265](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/2098265db5dc35f4fde91e50e9379ac73329b3f0))
* 针对channel=web，对局结束后返回上个页面 ([b37e6ca](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/b37e6cac0e8ddadc519f68f0cf76fa2f3a30e21a))
* 针对channel=web，对局结束后返回上个页面 ([ffe4fe1](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/ffe4fe136b7366fb112131f323a65956dd5af3a7))
* 支持根据url传参区分env ([edcbc1c](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/edcbc1c74025763e60dd8406b69bc4b1c925b80e))
* 中诅咒时玩家头像状态调整 ([bb0f806](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/bb0f806aca5afdd16a29f855bc2457bd8e57f6f4))
* 中诅咒时玩家头像状态调整 ([87deb4d](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/87deb4d84d41f4fd827686d0b157a4f96d3e3671))
* 注释调试代码 ([2a7b45d](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/2a7b45da79c09c64d5857619474523039a83bcc3))
* 祝福 to 祈祷 ([7d52026](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/7d520269b85e26eb72df2ead7135f50b2d4653cf))
* 祝福交互优化 ([b7a03bc](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/b7a03bc86dbea4a8c4aa7896e1bdc7dd733755c3))
* 祝福交互优化 ([f2414db](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/f2414db2335d71b8f2474bdaa01b7882d4aac50c))
* 祝福钳子对接美术资源 ([0430d7d](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/0430d7ddb70127b9a98caf73045031ba4b08f279))
* 诅咒动画对接调整 ([4c321a4](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/4c321a4ffc6db7e3d1eda4d911bce19d41ed6dcc))
* 诅咒和休想音效 ([a855c56](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/a855c569129e59b29eca3ebbb63e4907133bd19a))
* 诅咒摸多张牌时，不收起摸牌鲨鱼界面 ([32816f5](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/32816f55171637544b265043700a34b2945268a2))
* 诅咒状态下按牙选中牙齿数量不足提示文案优化 ([1a51fb1](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/1a51fb1f8ef6209ee97e09d793e8fa314618eea5))
* 组件的处理 ([fda3e27](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/fda3e2711185a7497ef6011bd342458443833995))
* 组件的处理 ([9947473](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/99474735e961fd5009ff6188f2c73f35502fec5b))
* 组件的处理 ([f63ee97](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/f63ee97dc3cddab60092a89e7bb6ba01ab28a393))
* add log ([be0fa68](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/be0fa68e4301ff41d16885db9dfd5eec729459f3))
* add log ([832819e](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/832819e4d18624c38b327c40d31b1361de349e29))
* AI 模拟玩家索取选牌 ([b18fec5](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/b18fec590547b8a373115f2f0d775a3459803bde))
* AI 模拟玩家旋转鲨鱼选牙齿 ([a7cd000](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/a7cd000442b125ce133376807217420a566744ec))
* AI 模拟玩家旋转鲨鱼选牙齿 ([3f35911](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/3f3591136bd8b142e2ba3e31a2c832c8256f5707))
* AI 模拟玩家诊断时旋转鲨鱼选牙齿 ([22bab65](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/22bab656208e0b0cb03b9d0326b9981650fdb693))
* audio 相关调整 ([cd1b714](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/cd1b7144b258f1ff82dcfca443ac613edbd90831))
* debugMode 也通过参数控制 ([d7cd8ef](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/d7cd8efb0a45a278edf4467afa0dd1d9dc0d7483))
* enableWebDebugger ([8ab3933](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/8ab3933965d194037659d49ab59a6a14a9f83a24))
* enableWebDebugger false ([7dfdb4a](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/7dfdb4a816753c8b213f479fe0ddbb325eee1241))
* EVENT_RANKING 的处理 ([c34b0fc](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/c34b0fcd4142e4b0960e178cf91659dadc1619cf))
* flutter 平台使用 dsbridge 实现桥接层 ([97c9a23](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/97c9a230cd7652c9afa5669b3dcfd43314b736c4))
* JSON.safeStringify ([66b937b](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/66b937b951e50bd23a73adb6171a7cea4c208ae3))
* log 上传机制 ([48d736c](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/48d736c87e83bdfdfea8866715d6749d483c0cb6))
* log 上传机制 ([782f26e](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/782f26e054dd0ecfd7ad6c8f8a56940d0fa4b67e))
* log 上传机制 ([3f9bfe3](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/3f9bfe3f7d5fbfc3ade4a31582a04e8f2c269b27))
* main_title 层级问题 ([5d14315](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/5d14315bd4fd176476a4d7df3a4fd04a30d48950))
* merge json ([e1f55a7](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/e1f55a749d85b43207ba829fad94b1d158be93c5))
* merge json ([4ce71a7](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/4ce71a7438d1e0a04c8dac7bf7d69a880db9aa32))
* platform 为 flutter 时不显示游戏内小结算界面 ([0138fcd](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/0138fcd6e98f2d4e27a22d399b1d19ef6a39857c))
* plistImage 支持添加 .plist 文件资源 ([8cec5b3](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/8cec5b327446582a4ed307e1060e17f35903ce9b))
* plistImage 组件支持播放本地plist资源，支持设置播放完成回调 ([146b25f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/146b25f86afc630d1a189bd7f352ac5ef04c890c))
* shark touchmove 还原 ([42e8ff1](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/42e8ff16eaff69b84d1894342147f2e64fa5cba6))
* WIP: 搭建核心玩法 ([b02ebcc](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/b02ebcc8bfce317efc307b7e4c8e90cfd3bc0b74))
* WIP: 动效-冻结 ([23c1553](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/23c1553406bdec1de8deff1eb326f503cb72365b))
* WIP: 动效-冻结 ([4116483](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/4116483650486dd21b614dab6bd53a976014271b))
* WIP: 防御 ([70ab796](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/70ab79602e41f7dd84f39aeb257ea13f63197246))
* WIP: 攻击到弱点 ([7ccb78d](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/7ccb78d22d246eb901c4b8b58fcb0cd37f5292bc))
* WIP: 新增卡牌祝福、钳子 ([b9097ba](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/b9097ba39beb779ff1c4619b7364d1573850c41d))
* WIP: 新增卡牌祝福、钳子 ([43bd8ed](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/43bd8ed68dfc60bb6a52a17ee1a7d1d854de8b20))
* WIP: 新增卡牌祝福、钳子 ([9f10259](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/9f102595ac0b65e92b944c49c61ddce0cb593287))
* WIP: 祝福 & 钳子 ([8736d33](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/8736d33a9c2e4eaadcfc58905e64f4bdce4b7fc1))
* WIP: 祝福 & 钳子 ([307c188](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/307c1880fbeaed606db47f8b82386725a5665e3d))
* WIP: 祝福 & 钳子 ([cf37c0b](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/cf37c0bcf6fd02daae73842f02d2dc514ae48d2b))
* wkwebview ([76fce49](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/76fce49636a2ee3da3be0465c2329b6f32f3326a))
* wkwebview ([d12efaf](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/d12efaf71256cdbbef20916cda9ac03065980e27))


### 🐛 Bug修复

* 避免重复发牌 ([0cca9af](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/0cca9afdccaeaa048b23fe04aa1df9d6ecddfedd))
* 尝试修复对局开始没有手牌 ([f847485](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/f847485a0aa3b64dd74e681b7a000fd1607c1e96))
* 尝试修复对局开始没有手牌 ([fa23f08](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/fa23f0866095f770deff98e5c2dd9298cdbccf38))
* 尝试修复重开手牌为空的问题 ([b20bfc6](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/b20bfc6f263e9abd4c3a893497ac57120117fbb5))
* 点选卡牌设置dobounce时间 ([fdf23a9](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/fdf23a90d7a1fc0d1cf1ef090487795ae2c1d72f))
* 对局结束时处于后台的特殊流程 ([3691bd1](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/3691bd109cffa92db578d554b1eb4614294757df))
* 对局结束时处于后台的特殊流程 ([04a9b7a](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/04a9b7a9666ae0a889117fc516ebba81a327bf0c))
* 对局结束时清除store ([8a8723f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/8a8723fb5397d12035e18b2f6f9858a246fa7253))
* 发牌问题 ([9ea19e5](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/9ea19e535c25eba240296fe417730d9b63ee785f))
* 防止战局初始化执行多次导致的牙齿问题 ([efc8dd4](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/efc8dd48198eaf3ac759eac53f789ed4e0458eea))
* 开局时处于后台的处理流程 ([abde9af](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/abde9af842f3dec55be90cfe414b12e746698113))
* 上下牙齿分布 ([2c34da8](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/2c34da8aa84bfe4aa714294ee710bf9590189ccb))
* 上下牙齿分布 ([71728db](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/71728dbad0696a79cc10aebf6de970a4a33e71f9))
* 逃生界面调整 ([30b4b22](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/30b4b22eaecee030b438fde9e96ce05fb5beb26a))
* 修复按牙问题 ([b444597](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/b4445970e33dc531b6b4ce431ec8587ade979af9))
* 修复版本管理配置中的路径重复问题 ([8e1fe4e](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/8e1fe4e88c4e2721fbc51741958ed636f6c092c8))
* 修复互中诅咒的场景，轮到第二个人时按牙卡住 ([5915e25](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/5915e25f1981cbddc90c6e8d7f1aa63e8a698cb2))
* 修复交换到一张交换牌的场景 ([7e41649](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/7e41649e0f574ffec57b005706c4daa2e4befd18))
* 修复同时中诅咒和多个锅的状态 ([86d19b8](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/86d19b86ef941f1d05f98b571240bf83704f098d))
* 修复只剩最后一人交换时动效卡住 ([14d3a83](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/14d3a8325d3aca2c7aa6619ee510babe81ce2bd9))
* 修复指定人界面诅咒数显示延迟 ([b6c915b](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/b6c915b9468296d84ae991b8e5f4d637a991b9ab))
* 修复中了2重甩锅后摸到坏牙的场景 ([3b1ef6f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/3b1ef6ffc75fa5c149d9cf13dab9f32b7dfa1e26))
* 支持url传参进入游戏流程 ([0649c11](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/0649c11b0c7e25f35f484031aaf7580dc6d39108))
* 诅咒选人列表去除自己 ([3f5bb0e](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/3f5bb0e686545df91ec9cc2bf2262f34096a6a99))
* 坐下第二颗牙无法点击 ([0055909](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/0055909f6a3e6067cf1db28f8bd36142c105e893))
* add core-js ([e4c44c4](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/e4c44c412a1a835bfed9a3bf27f4143cd5d35060))
* Circular Reference ([4740509](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/4740509ae1b1200e74468dbf8111d1b1031582cc))
* Circular Reference ([ce62a90](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/ce62a909650b048a028715463e53c4efea2f8f75))
* debug环境判断去掉 local ([e69591f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/e69591fc4859e4c97d0857007efb94155675aa5f))
* gui层调整，修复后台切前台后的遮罩层问题 ([93d14fc](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/93d14fc30106730711022fc0cb6a499376517a0c))
* loading ([5902c77](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/5902c77739c17c11587b1273cf4fdace7e014ead))
* log ([bf558de](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/bf558de1344e08dbf68e91911885f3e92a27de6e))
* memory leak ([916c8fc](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/916c8fc26a3168ade88fa72429a92d10e164475f))
* stringify ([7cf2ff5](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/7cf2ff5e3575b38158307cf9243bd3f96346ab57))


### 🔧 其他更改

* 恢复裁剪过度的功能。。 ([7dcfc9a](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/7dcfc9a1e25d46dfcc259e64c5b6649e6e31564f))
* 恢复裁剪过度的功能。。 ([23ed41e](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/23ed41e89c80c8043d81939459f036d489387546))
* 恢复裁剪过度的功能。。 ([a88f943](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/a88f9434d80d0e4828aeec80632b7b18a71c8d4f))
* 恢复裁剪过度的功能。。 ([d26d171](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/d26d171e2698cfb67bee35b77890398eb1594456))
* 升级proto依赖 ([ab5c95e](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/ab5c95e60f262647dc686be06160e7a9621b82f7))
* 升级proto依赖 ([6778f12](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/6778f12830112c605d41f9a405cfed47013c1b1e))
* 项目设置优化 ([8d7432d](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/8d7432dd78d8248af673174d62dedc152b04a3a3))
* 小心鲨手升级cocos ([52bc6b9](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/52bc6b9dac891edbfc6867b83ed426ce5bfa393c))
* 引擎功能裁剪 ([bbad1c3](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/bbad1c30427fe88686718effe4b01773ecf9313b))
* 引擎功能进一步裁剪 ([1fd363b](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/1fd363b259c464bf05b717494eb27a42c31407ba))
* **game-shark:** release 0.1.1 ([d9f4046](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/d9f4046a19a38aed20a4d6db5281aee636b949c4))
* **game-shark:** release 0.1.2 ([f60ba02](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/f60ba0240e64bf8235e290f0ead4c3c3bc35f5c2))
* standard-version ([10be362](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/10be362bbb30a5ba631b914dc33162d28bbe6a7e))
* standard-version ([25f21a2](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/25f21a25e0d92485aabce0acc21e5d37238a7f1c))
* standard-version ([a3c864b](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/a3c864bb8cab6ff6be3e855c57fca72fbc811a0f))

# 小心鲨手 - 更新日志

本项目的所有重要更改都将记录在此文件中。

本文件格式基于 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/)，
并且本项目遵循 [语义化版本](https://semver.org/lang/zh-CN/)。

## [未发布]

### 新增

-   初始化版本管理系统
-   集成 standard-version 进行自动化版本管理

### 变更

-   无

### 修复

-   无

### 移除

-   无

---

_注意：此 CHANGELOG 将在首次运行 standard-version 后自动更新_
