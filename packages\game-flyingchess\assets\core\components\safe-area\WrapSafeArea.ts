/**
 * @describe 兼容不兼容SafeArea组件的平台
 * <AUTHOR>
 * @date 2023-09-04 17:43:27
 */

import {
    _decorator,
    CCFloat,
    Component,
    log,
    Node,
    SafeArea,
    screen,
    Widget,
} from 'cc'

import store from '@/core/business/store'
import { TAOBAO_MINIGAME } from 'cc/env'

import { CustomPlatform } from '@/core/business/store/global'
import { cat } from '@/core/manager'
const { ccclass, property, requireComponent } = _decorator

@ccclass('WrapSafeArea')
@requireComponent(Widget)
export class WrapSafeArea extends Component {
    /**安全区域顶部 */
    safeTop: number = 0
    /**安全区域底部 */
    safeBottom: number = 0

    override onLoad() {
        // if (store.global.customPlatform == CustomPlatform.DaiDaiH5) {
        // 通过URL获取安全区域
        const url_params = cat.util.stringUtil.getURLParameters(
            decodeURIComponent(window.location.href)
        )

        this.safeTop = Number(url_params.safe_top || 0)
        this.safeBottom = Number(url_params.safe_bottom || 0)
        // 设备分辨率
        // const devicePixelRatio = screen.devicePixelRatio

        // const width = screen.windowSize.width / devicePixelRatio
        // const height = screen.windowSize.height / devicePixelRatio

        // // 安全高度
        // const safe_height = screen.windowSize
        // 安全宽度
        let widget = this.node.getComponent(Widget)!
        widget.top = this.safeTop
        widget.bottom = this.safeBottom
        window.ccLog('WrapSafeArea--------', widget.top, widget.bottom)
        // widget.updateAlignment()
        // }
    }

    override start() {}

    override update(deltaTime: number) {}
}
