// @generated by protoc-gen-es v2.2.5 with parameter "target=ts,import_extension=none,json_types=true"
// @generated from file authorization.proto (package protos, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage } from "@bufbuild/protobuf/codegenv1";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv1";
import type { Any, AnyJson } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_any } from "@bufbuild/protobuf/wkt";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file authorization.proto.
 */
export const file_authorization: GenFile = /*@__PURE__*/
  fileDesc("ChNhdXRob3JpemF0aW9uLnByb3RvEgZwcm90b3Mi3QEKGUF1dGhvcml6YXRpb25Ob3RpZmljYXRpb24SOAoGc3RhdHVzGAEgASgOMigucHJvdG9zLkF1dGhvcml6YXRpb25Ob3RpZmljYXRpb24uU3RhdHVzEiIKBGRhdGEYAiABKAsyFC5nb29nbGUucHJvdG9idWYuQW55ImIKBlN0YXR1cxILCgdSRVNFUlZFEAASCwoHU1VDQ0VTUxABEgsKB0VYUElSRUQQAhIMCghLSUNLX09VVBADEgsKB0lOVkFMSUQQBBINCglERVNUUk9ZRUQQBRIHCgNCQU4QByKhAQoUQXV0aG9yaXphdGlvbkJhbkRhdGESDgoGc3RhdHVzGAEgASgFEhAKCHRlbXBsYXRlGAIgASgJEjgKBnZhbHVlcxgDIAMoCzIoLnByb3Rvcy5BdXRob3JpemF0aW9uQmFuRGF0YS5WYWx1ZXNFbnRyeRotCgtWYWx1ZXNFbnRyeRILCgNrZXkYASABKAkSDQoFdmFsdWUYAiABKAk6AjgBQjAKJGNvbS5zdG50cy5jbG91ZC5zdWlsZXlvby5lY2hvLnByb3Rvc1oILi9wcm90b3NiBnByb3RvMw", [file_google_protobuf_any]);

/**
 * 认证状态通知
 *
 * @generated from message protos.AuthorizationNotification
 */
export type AuthorizationNotification = Message<"protos.AuthorizationNotification"> & {
  /**
   * 认证状态
   *
   * @generated from field: protos.AuthorizationNotification.Status status = 1;
   */
  status: AuthorizationNotification_Status;

  /**
   * 携带的数据，冻结|AuthorizationBanData
   *
   * @generated from field: google.protobuf.Any data = 2;
   */
  data?: Any;
};

/**
 * 认证状态通知
 *
 * @generated from message protos.AuthorizationNotification
 */
export type AuthorizationNotificationJson = {
  /**
   * 认证状态
   *
   * @generated from field: protos.AuthorizationNotification.Status status = 1;
   */
  status?: AuthorizationNotification_StatusJson;

  /**
   * 携带的数据，冻结|AuthorizationBanData
   *
   * @generated from field: google.protobuf.Any data = 2;
   */
  data?: AnyJson;
};

/**
 * Describes the message protos.AuthorizationNotification.
 * Use `create(AuthorizationNotificationSchema)` to create a new message.
 */
export const AuthorizationNotificationSchema: GenMessage<AuthorizationNotification, AuthorizationNotificationJson> = /*@__PURE__*/
  messageDesc(file_authorization, 0);

/**
 * @generated from enum protos.AuthorizationNotification.Status
 */
export enum AuthorizationNotification_Status {
  /**
   * 保留
   *
   * @generated from enum value: RESERVE = 0;
   */
  RESERVE = 0,

  /**
   * 成功
   *
   * @generated from enum value: SUCCESS = 1;
   */
  SUCCESS = 1,

  /**
   * 认证过期
   *
   * @generated from enum value: EXPIRED = 2;
   */
  EXPIRED = 2,

  /**
   * 被踢出
   *
   * @generated from enum value: KICK_OUT = 3;
   */
  KICK_OUT = 3,

  /**
   * 无效的
   *
   * @generated from enum value: INVALID = 4;
   */
  INVALID = 4,

  /**
   * 账户注销
   *
   * @generated from enum value: DESTROYED = 5;
   */
  DESTROYED = 5,

  /**
   * 账户封禁
   *
   * @generated from enum value: BAN = 7;
   */
  BAN = 7,
}

/**
 * @generated from enum protos.AuthorizationNotification.Status
 */
export type AuthorizationNotification_StatusJson = "RESERVE" | "SUCCESS" | "EXPIRED" | "KICK_OUT" | "INVALID" | "DESTROYED" | "BAN";

/**
 * Describes the enum protos.AuthorizationNotification.Status.
 */
export const AuthorizationNotification_StatusSchema: GenEnum<AuthorizationNotification_Status, AuthorizationNotification_StatusJson> = /*@__PURE__*/
  enumDesc(file_authorization, 0, 0);

/**
 * @generated from message protos.AuthorizationBanData
 */
export type AuthorizationBanData = Message<"protos.AuthorizationBanData"> & {
  /**
   * 类型，0|封号，1|禁言
   *
   * @generated from field: int32 status = 1;
   */
  status: number;

  /**
   * 通知模板
   *
   * @generated from field: string template = 2;
   */
  template: string;

  /**
   * 通知变量
   *
   * @generated from field: map<string, string> values = 3;
   */
  values: { [key: string]: string };
};

/**
 * @generated from message protos.AuthorizationBanData
 */
export type AuthorizationBanDataJson = {
  /**
   * 类型，0|封号，1|禁言
   *
   * @generated from field: int32 status = 1;
   */
  status?: number;

  /**
   * 通知模板
   *
   * @generated from field: string template = 2;
   */
  template?: string;

  /**
   * 通知变量
   *
   * @generated from field: map<string, string> values = 3;
   */
  values?: { [key: string]: string };
};

/**
 * Describes the message protos.AuthorizationBanData.
 * Use `create(AuthorizationBanDataSchema)` to create a new message.
 */
export const AuthorizationBanDataSchema: GenMessage<AuthorizationBanData, AuthorizationBanDataJson> = /*@__PURE__*/
  messageDesc(file_authorization, 1);

