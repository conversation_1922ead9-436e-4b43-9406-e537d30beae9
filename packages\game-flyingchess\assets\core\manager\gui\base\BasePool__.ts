import { NodePool, instantiate, Node, Prefab, Vec3 } from 'cc'

export class BasePool extends NodePool {
    // 统计字段
    private _getCount: number = 0
    private _putCount: number = 0
    private _autoExpandTimes: number = 0

    // 配置参数
    private readonly _prefab: Prefab
    private readonly expandSize: number

    constructor(
        prefab: Prefab,
        initialSize: number = 10,
        expandSize: number = 5
    ) {
        super()
        this._prefab = prefab
        this.expandSize = expandSize

        // 预初始化对象池
        this.preinit(initialSize)
    }

    /**
     * 预初始化对象池
     * @param count 预生成数量
     */
    preinit(count: number): void {
        for (let i = 0; i < count; i++) {
            this.put(instantiate(this._prefab))
        }
    }

    override get(): Node {
        window.ccLog('获取对象池对象')

        let node = super.get()!
        // 自动扩容逻辑
        if (this.size() === 0 || !node) {
            this.preinit(this.expandSize)
            this._autoExpandTimes++
            node = super.get()!
        }

        node?.setScale(Vec3.ONE)
        this._getCount++

        // node.active = true
        // 触发节点激活事件
        node.emit('onEnable')
        return node
    }

    override put(node: Node): void {
        window.ccLog('回收对象池对象')
        // 触发节点回收事件
        node.emit('onDisable')
        super.put(node)
        this._putCount++
    }

    /**
     * 清空对象池（注意会真正销毁节点）
     */
    override clear(): void {
        super.clear()
        this._getCount = 0
        this._putCount = 0
        this._autoExpandTimes = 0
    }

    get totalCreated(): number {
        return this._getCount - this._putCount + this.size()
    }

    get usageStatistics() {
        return {
            currentAvailable: this.size(),
            totalCapacity: this.totalCreated,
            getOperations: this._getCount,
            putOperations: this._putCount,
            autoExpandTimes: this._autoExpandTimes,
        }
    }
}
