import { AudioManager } from './audio'
import { StorageManager } from './storage'
import { GuiManager } from './gui'
import { Gui } from './gui/Gui'
import { http } from './request/http'
import { ResLoader } from './res-loader'
import { MessageManager } from './event'
import { WatchSystem } from '@/core/business/watch'
import { Util } from '@/core/business/util'
import { log, game } from 'cc'
import Debugout from '@/core/business/util/Debugout'

if (
    // location.host.includes('dev') ||
    // location.host.includes('testing') ||
    location.href.includes('debug=1')
) {
    window.__DEBUGOUT_INSTENCE__ = new Debugout()
    console.log = function () {
        window.__DEBUGOUT_INSTENCE__.log.apply(
            window.__DEBUGOUT_INSTENCE__,
            arguments
        )
        for (let arg of arguments) {
            arg = null
        }
    }
    console.error = function () {
        window.__DEBUGOUT_INSTENCE__.error.apply(
            window.__DEBUGOUT_INSTENCE__,
            arguments
        )
    }
    console.warn = function () {
        window.__DEBUGOUT_INSTENCE__.warn.apply(
            window.__DEBUGOUT_INSTENCE__,
            arguments
        )
    }
    console.info = function () {
        window.__DEBUGOUT_INSTENCE__.info.apply(
            window.__DEBUGOUT_INSTENCE__,
            arguments
        )
    }
}
window.ccLog = window.__DEBUGOUT_INSTENCE__ ? console.log : log

// 定义初始的扩展接口
export interface ManagerExtensions {
    [key: string]: any
}

/**
 * @describe 管理类
 * <AUTHOR>
 * @date 2024-09-12 11:49:44
 */
export class Manager {
    static #ins: Manager

    static get instance(): Manager {
        if (!this.#ins) {
            this.#ins = new Manager()
        }
        return this.#ins
    }

    /**TODO */
    mount: () => {}

    /**音频 */
    audio: AudioManager
    /**事件 */
    event: MessageManager
    /**gui */
    gui: Gui
    /**本地存储 */
    storage: StorageManager
    /**资源 */
    res: ResLoader
    /**请求 */
    http: typeof http = http
    /**观战 */
    watch: WatchSystem
    /**工具类 */
    util: Util

    /**启动 */
    async boot() {
        //  在游戏代码中设置合理的帧率
        game.setFrameRate(30)
        this.res = new ResLoader()
        this.storage = new StorageManager(this)
        this.event = new MessageManager()
        this.audio = new AudioManager(this)
        this.util = new Util(this)
        this.gui = (await new GuiManager(this).init()).gui
        this.watch = new WatchSystem(this)
    }
}

export const cat = Manager.instance
