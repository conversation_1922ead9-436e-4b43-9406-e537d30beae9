/**停服通知 */

import { JsonAsset, error, log } from 'cc'
import store from '../../store'
import { cat } from '@/core/manager'

type Notice = {
    /**内容 */
    notice: string
}

/**获取停服公告 */
export default function StopServiceNotice() {
    const { social_game_feature, social_game_id } = store.global
    cat.res.loadRemote(
        `${
            cat.env.stop_service_url
        }/social_games/stop_service/admin/${social_game_feature}-${social_game_id}.json?${Date.now()}`,
        { ext: '.json' },
        (err: Error | null, res: JsonAsset) => {
            if (err) return error(err)
            if (res.json) {
                const data = res.json as Notice
                cat.gui.showNotice({
                    text: data.notice,
                    confrim: () => {
                        window.ccLog('获取停服公告退出')
                        cat.platform.back()
                    },
                })
            }
        }
    )
}
