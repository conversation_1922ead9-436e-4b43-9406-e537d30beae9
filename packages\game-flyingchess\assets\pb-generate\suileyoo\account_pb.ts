// @generated by protoc-gen-es v2.2.5 with parameter "target=ts,import_extension=none,json_types=true"
// @generated from file account.proto (package protos, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv1";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file account.proto.
 */
export const file_account: GenFile = /*@__PURE__*/
  fileDesc("Cg1hY2NvdW50LnByb3RvEgZwcm90b3MimwEKE0JhbGFuY2VOb3RpZmljYXRpb24SDwoHYmFsYW5jZRgBIAEoCRINCgVwb2ludBgCIAEoBBINCgVsZXZlbBgDIAEoBBIUCgxQbGF5VGltZVBhaWQYBCABKAQSFAoMUGxheVRpbWVGcmVlGAUgASgEEhoKEkZhc3RFbnRlckV4cGlyZXNJbhgGIAEoBBINCgVZb29kbxgHIAEoCUIwCiRjb20uc3RudHMuY2xvdWQuc3VpbGV5b28uZWNoby5wcm90b3NaCC4vcHJvdG9zYgZwcm90bzM");

/**
 * 账户资产变动通知
 *
 * @generated from message protos.BalanceNotification
 */
export type BalanceNotification = Message<"protos.BalanceNotification"> & {
  /**
   * 云币余额
   *
   * @generated from field: string balance = 1;
   */
  balance: string;

  /**
   * 积分
   *
   * @generated from field: uint64 point = 2;
   */
  point: bigint;

  /**
   * 用户等级
   *
   * @generated from field: uint64 level = 3;
   */
  level: bigint;

  /**
   * 付费时长，单位：分钟
   *
   * @generated from field: uint64 PlayTimePaid = 4;
   */
  PlayTimePaid: bigint;

  /**
   * 免费体验时长，单位：分钟
   *
   * @generated from field: uint64 PlayTimeFree = 5;
   */
  PlayTimeFree: bigint;

  /**
   * 速进有效期，单位：秒
   *
   * @generated from field: uint64 FastEnterExpiresIn = 6;
   */
  FastEnterExpiresIn: bigint;

  /**
   * 油豆余额
   *
   * @generated from field: string Yoodo = 7;
   */
  Yoodo: string;
};

/**
 * 账户资产变动通知
 *
 * @generated from message protos.BalanceNotification
 */
export type BalanceNotificationJson = {
  /**
   * 云币余额
   *
   * @generated from field: string balance = 1;
   */
  balance?: string;

  /**
   * 积分
   *
   * @generated from field: uint64 point = 2;
   */
  point?: string;

  /**
   * 用户等级
   *
   * @generated from field: uint64 level = 3;
   */
  level?: string;

  /**
   * 付费时长，单位：分钟
   *
   * @generated from field: uint64 PlayTimePaid = 4;
   */
  PlayTimePaid?: string;

  /**
   * 免费体验时长，单位：分钟
   *
   * @generated from field: uint64 PlayTimeFree = 5;
   */
  PlayTimeFree?: string;

  /**
   * 速进有效期，单位：秒
   *
   * @generated from field: uint64 FastEnterExpiresIn = 6;
   */
  FastEnterExpiresIn?: string;

  /**
   * 油豆余额
   *
   * @generated from field: string Yoodo = 7;
   */
  Yoodo?: string;
};

/**
 * Describes the message protos.BalanceNotification.
 * Use `create(BalanceNotificationSchema)` to create a new message.
 */
export const BalanceNotificationSchema: GenMessage<BalanceNotification, BalanceNotificationJson> = /*@__PURE__*/
  messageDesc(file_account, 0);

