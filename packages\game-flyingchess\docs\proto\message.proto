syntax = "proto3";

package flyingchess.v1;

// 消息类型枚举
enum MessageType {
  // 未指定类型
  MESSAGE_TYPE_UNSPECIFIED = 0;

  // ----- 游戏基础相关消息 -----
  // 游戏所有信息广播
  MESSAGE_TYPE_GAME_INFO_BROADCAST = 1;
  // 游戏状态变更广播
  MESSAGE_TYPE_STATE_CHANGED_BROADCAST = 2;
  // 游戏结束广播
  MESSAGE_TYPE_GAME_OVER_BROADCAST = 3;

  // ----- 玩家操作相关消息 -----
  // 投掷骰子请求
  MESSAGE_TYPE_ROLL_DICE_REQUEST = 10;
  // 投掷骰子响应
  MESSAGE_TYPE_ROLL_DICE_RESPONSE = 11;
  // 选择棋子请求
  MESSAGE_TYPE_SELECT_CHESS_REQUEST = 12;
  // 选择棋子响应
  MESSAGE_TYPE_SELECT_CHESS_RESPONSE = 13;
  // 托管请求
  MESSAGE_TYPE_HOSTING_REQUEST = 14;
  // 托管响应
  MESSAGE_TYPE_HOSTING_RESPONSE = 15;
  // 准备请求
  MESSAGE_TYPE_READY_REQUEST = 16;
  // 准备响应
  MESSAGE_TYPE_READY_RESPONSE = 17;

  // ----- 系统相关消息 -----
  // 错误消息
  MESSAGE_TYPE_ERROR = 100;
  // 心跳消息
  MESSAGE_TYPE_HEARTBEAT = 101;
  // 断线重连消息
  MESSAGE_TYPE_RECONNECT = 102;
}

// 消息包装器
message Message {
  // 消息类型
  MessageType type = 1;
  
  // 消息ID
  string message_id = 2;
  
  // 消息时间戳（毫秒）
  int64 timestamp = 3;
  
  // 消息内容（二进制数据，根据type解析为不同的消息）
  bytes content = 4;
  
  // 错误信息（当消息处理出错时）
  ErrorInfo error = 5;
}

// 错误信息
message ErrorInfo {
  // 错误码
  int32 code = 1;
  
  // 错误消息
  string message = 2;
  
  // 错误详情
  string details = 3;
}

// 心跳消息
message Heartbeat {
  // 客户端时间戳（毫秒）
  int64 client_time = 1;
  
  // 服务器时间戳（毫秒，服务器回复时填充）
  int64 server_time = 2;
}

// 断线重连请求
message ReconnectRequest {
  // 战局ID
  string battle_id = 1;
  
  // 玩家ID
  string player_id = 2;
  
  // 会话ID
  string session_id = 3;
}

// 断线重连响应
message ReconnectResponse {
  // 是否重连成功
  bool success = 1;
  
  // 错误信息（当重连失败时）
  string error_message = 2;
  
  // 游戏信息（当重连成功时）
  bytes game_info = 3;
}
