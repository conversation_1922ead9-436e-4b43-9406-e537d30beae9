// @generated by protoc-gen-es v2.4.0 with parameter "target=ts,json_types=true"
// @generated from file dixit/v1/message.proto (package dixit.v1, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile } from "@bufbuild/protobuf/codegenv1";
import { enumDesc, fileDesc } from "@bufbuild/protobuf/codegenv1";

/**
 * Describes the file dixit/v1/message.proto.
 */
export const file_dixit_v1_message: GenFile = /*@__PURE__*/
  fileDesc("ChZkaXhpdC92MS9tZXNzYWdlLnByb3RvEghkaXhpdC52MSr5AQoLTWVzc2FnZVR5cGUSEgoOTVRfVU5TUEVDSUZJRUQQABIaChZNVF9HQU1FX0lORk9fQlJPQURDQVNUEAESHgoaTVRfU1RBVEVfQ0hBTkdFRF9CUk9BRENBU1QQAhIcChhNVF9DTElFTlRfREFUQV9CUk9BRENBU1QQBBIaChZNVF9HQU1FX09WRVJfQlJPQURDQVNUEAUSHAoYTVRfU0VSVkVSX0RBVEFfQlJPQURDQVNUEAYSGgoWTVRfUExBWUVSX0lORk9fTUVTU0FHRRAKEiYKIk1UX1BMQVlFUl9TVEFUVVNfQ0hBTkdFRF9CUk9BRENBU1QQC2IGcHJvdG8z");

/**
 * - Broadcast 表示所有玩家都是同样的消息
 * - Message 表示每个玩家的消息有部分数据不一样
 *
 * @generated from enum dixit.v1.MessageType
 */
export enum MessageType {
  /**
   * buf:lint:ignore ENUM_VALUE_UPPER_SNAKE_CASE
   * buf:lint:ignore ENUM_VALUE_PREFIX
   * 占位 要默认加个0，否则前端有问题
   *
   * @generated from enum value: MT_UNSPECIFIED = 0;
   */
  MT_UNSPECIFIED = 0,

  /**
   * ----- 游戏基础相关消息 -----
   * 游戏所有信息，广播：GameInfoBroadcast
   *
   * @generated from enum value: MT_GAME_INFO_BROADCAST = 1;
   */
  MT_GAME_INFO_BROADCAST = 1,

  /**
   * 游戏状态机改变，广播：GameInfoBroadcast
   *
   * @generated from enum value: MT_STATE_CHANGED_BROADCAST = 2;
   */
  MT_STATE_CHANGED_BROADCAST = 2,

  /**
   * 客户端自定义的数据广播，广播：ClientDataBroadcast
   *
   * @generated from enum value: MT_CLIENT_DATA_BROADCAST = 4;
   */
  MT_CLIENT_DATA_BROADCAST = 4,

  /**
   * 游戏结束，广播：GameOverBroadcast
   *
   * @generated from enum value: MT_GAME_OVER_BROADCAST = 5;
   */
  MT_GAME_OVER_BROADCAST = 5,

  /**
   * 服务端自定义的数据广播，广播：ServerDataBroadcast
   *
   * @generated from enum value: MT_SERVER_DATA_BROADCAST = 6;
   */
  MT_SERVER_DATA_BROADCAST = 6,

  /**
   * ----- 玩家状态相关消息 -----
   * 玩家信息，消息：PlayerInfoMessage
   *
   * @generated from enum value: MT_PLAYER_INFO_MESSAGE = 10;
   */
  MT_PLAYER_INFO_MESSAGE = 10,

  /**
   * 玩家状态变更，广播：Player
   *
   * @generated from enum value: MT_PLAYER_STATUS_CHANGED_BROADCAST = 11;
   */
  MT_PLAYER_STATUS_CHANGED_BROADCAST = 11,
}

/**
 * - Broadcast 表示所有玩家都是同样的消息
 * - Message 表示每个玩家的消息有部分数据不一样
 *
 * @generated from enum dixit.v1.MessageType
 */
export type MessageTypeJson = "MT_UNSPECIFIED" | "MT_GAME_INFO_BROADCAST" | "MT_STATE_CHANGED_BROADCAST" | "MT_CLIENT_DATA_BROADCAST" | "MT_GAME_OVER_BROADCAST" | "MT_SERVER_DATA_BROADCAST" | "MT_PLAYER_INFO_MESSAGE" | "MT_PLAYER_STATUS_CHANGED_BROADCAST";

/**
 * Describes the enum dixit.v1.MessageType.
 */
export const MessageTypeSchema: GenEnum<MessageType, MessageTypeJson> = /*@__PURE__*/
  enumDesc(file_dixit_v1_message, 0);

