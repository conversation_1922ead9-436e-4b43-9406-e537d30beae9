/**加入房间 */

import { ClientBootParam, ClientBootParamSchema } from 'sgc'
import store from '../store'
import { log } from 'cc'
import { cat } from '@/core/manager'
import { from<PERSON>son, fromJsonString } from '@bufbuild/protobuf'

/**链接ws */
const connectWebsocket = () => {
    const { user } = store

    if (!user.auth.battleId) return cat.gui.showToast({ title: '缺少battleId' })

    const { battleId, token = '', serverId = '' } = user.auth
    return cat.ws
        .create(
            {
                battle_id: battleId,
                serverId,
                token,
            },
            {
                url: user.isAudience
                    ? cat.env.audience_ws_url
                    : cat.env.player_ws_url,
                reconnectMaxAttempts: Infinity,
            }
        )
        .connectRequest(user.isAudience)
}

export default async function EnterGame(
    gameEnterInfo?: IBridge.EnterParam | string
) {
    try {
        if (gameEnterInfo) {
            const authParams =
                typeof gameEnterInfo === 'string'
                    ? fromJsonString(ClientBootParamSchema, gameEnterInfo, {
                          ignoreUnknownFields: true,
                      })
                    : from<PERSON>son(ClientBootParamSchema, gameEnterInfo, {
                          ignoreUnknownFields: true,
                      })
            store.user.auth = authParams
        } else {
            await cat.platform.serverLogin()
        }
        window.ccLog('EnterGame authParams', JSON.stringify(store.user.auth))
    } catch (err) {
        cat.gui.showToast({ title: '登录参数错误' })
        throw new Error('登录参数错误')
    }
    // 连接ws
    await connectWebsocket()
}
