import { BaseTracking } from "../BaseTracking"

/**游戏事件属性 */
export enum TrackingGameEvent {
    /**等待房间：等待玩家加入时上报 */
    ROOM_WAIT = 'room_wait',
    /**进入房间：玩家全部到齐，进入房间（发牌）时上报 */
    ROOM_ENTER = 'room_enter',
    /**完成发牌：发牌完成上报 */
    CARDS_FINISH = 'cards_finish',
    /**更换手牌：点击更换手牌时上报 */
    CARDS_CHANGE = 'cards_change',
    /**出局：出局时上报 */
    GAME_OUT = 'game_out',
    /**观战：进入观战状态时上报（点击出局弹窗的观战按钮） */
    BATTLE_WATCH = 'battle_watch',
    /**托管：进入托管状态时上报 */
    GAME_ENTRUST = 'game_entrust',
    /**游戏结束：游戏结束时上报 */
    GAME_OVER = 'game_over'
}


export class TrackingGame extends BaseTracking {

    /**等待房间：等待玩家加入时上报 */
    roomWait = () => {
        this.tracking.upload({
            event_type: TrackingGameEvent.ROOM_WAIT,
        })
    }

    /**进入房间：玩家全部到齐，进入房间（发牌）时上报 */
    roomEnter = () => {
        this.tracking.upload({
            event_type: TrackingGameEvent.ROOM_ENTER,
        })
    }

    /**完成发牌：发牌完成上报 */
    cardsFinish = (nonce: string[]) => {
        this.tracking.upload({
            event_type: TrackingGameEvent.CARDS_FINISH,
            value: nonce
        })
    }

    /**更换手牌：点击更换手牌时上报 */
    cardsChange = (nonce: string[]) => {
        this.tracking.upload({
            event_type: TrackingGameEvent.CARDS_CHANGE,
            value: nonce
        })
    }

    /**出局：出局时上报 */
    gameOut = () => {
        this.tracking.upload({
            event_type: TrackingGameEvent.GAME_OUT,
        })
    }

    /**观战：进入观战状态时上报（点击出局弹窗的观战按钮） */
    battleWatch = () => {
        this.tracking.upload({
            event_type: TrackingGameEvent.BATTLE_WATCH,
        })
    }

    /**托管：进入托管状态时上报 */
    gameEntrust = (entrust: 'on' | 'off') => {
        this.tracking.upload({
            event_type: TrackingGameEvent.GAME_ENTRUST,
            value: { entrust }
        })
    }

    /**游戏结束：游戏结束时上报 */
    gameOver = (ranking: number, score: number) => {
        this.tracking.upload({
            event_type: TrackingGameEvent.GAME_OVER,
            value: { ranking, score }
        })
    }
}
