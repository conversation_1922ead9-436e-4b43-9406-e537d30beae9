import { _decorator, Component, Node, Button, instantiate, Prefab } from 'cc'
import { UploadLog } from '@/core/ui/uploadLog/UploadLog'
import store from '@/core/business/store'
import { cat } from '@/core/manager'
import { BaseComponent } from './base/BaseComponent'

const { ccclass, property } = _decorator

@ccclass('exportLog')
export class exportLog extends BaseComponent {
    @property({ type: Node, tooltip: '导出日志' })
    export_log: Node

    @property({ type: Prefab, tooltip: '上传日志预制体' })
    uploadLogPrefab: Prefab

    protected override onLoad(): void {
        if (store.global.isDebugOrTestEnv) {
            this.export_log.active = true
            this.export_log.on(
                Button.EventType.CLICK,
                this.onExportLogClick,
                this
            )
        }
    }

    private onExportLogClick() {
        const ui_node = instantiate(this.uploadLogPrefab)
        cat.gui.openUI<UploadLog>(ui_node, {
            props: {
                confrim: (value) => {
                    if (window.__DEBUGOUT_INSTENCE__) {
                        window.__DEBUGOUT_INSTENCE__.report(value)
                    }
                    cat.gui.closeUI(ui_node)
                },
                cancel: () => {
                    cat.gui.closeUI(ui_node)
                },
            },
        })
    }
}
