import { _decorator, CCInteger, Component, error, instantiate, Node, NodePool, Prefab, Rect, rect, RigidBody, ScrollView, UIOpacity, UITransform, v2, Vec2, warn } from 'cc';
import { AbsAdapter } from './AbsAdapter';



const { ccclass, property } = _decorator;
/**
 * 通用 ListView 组件.
 * 能够显示垂直/横向ListView. 具体用法见Demo
 */
@ccclass
export default class ListView<T> extends Component {
    @property({ type: Prefab, tooltip: '预制体项模板' })
    private itemTemplate: Prefab = null;

    @property(Vec2)
    private spacing: Vec2 = v2(0, 0);

    @property(Rect)
    private readonly margin: Rect = rect(0, 0, 0, 0);

    // 比可见元素多缓存2个, 缓存越多,快速滑动越流畅,但同时初始化越慢.
    @property
    private spawnCount: number = 2;

    // 横向布局的item 数量. 默认为1,即每行一个元素.
    @property
    private column: number = 1;

    @property(ScrollView)
    private scrollView: ScrollView = null;

    @property(Node)
    emptyView: Node = null;

    private content: Node = null;

    private adapter: AbsAdapter<T> = null;

    private readonly _items: NodePool = new NodePool();

    // 记录当前填充在树上的索引. 用来快速查找哪些位置缺少item了.
    private _filledIds: { [key: number]: Node } = {};

    private horizontal: boolean = false;

    // 初始时即计算item的高度.因为布局时要用到.
    private _itemHeight: number = 1;

    private _itemWidth: number = 1;

    private _itemsVisible: number = 1;

    private dataChanged: boolean = false;

    private _isInited: boolean = false;

    // 当前屏幕可见元素索引值.
    private readonly visibleRange: number[] = [-1, -1];


    public comp: { new(): Component } = null;

    public onLoad() {
        this.init();

        // @ts-ignore
        /**
         *  如果出现列表显示异常,如边界留白,item 错位等问题,可能是所在节点树 存在缩放行为.
         *  具体bug参考: https://forum.cocos.com/t/v2-1-0-scrollview/71260/5
         *  打开以下代码即可解决布局异常问题.
         */
        if (this.scrollView) {
            // this.scheduleOnce(() => {
            //     // @ts-ignore
            //     this.scrollView._calculateBoundary();
            // }, 0.1);
        }
    }

    public async setAdapter(adapter: AbsAdapter<T>) {
        if (this.adapter === adapter) {
            this.notifyUpdate();
            return;
        }
        this.adapter = adapter;
        if (this.adapter == null) {
            warn('adapter 为空.');
            return;
        }
        if (this.itemTemplate == null) {
            error('Listview 未设置待显示的Item模板.');
            return;
        }
        this.visibleRange[0] = this.visibleRange[1] = -1;
        this.recycleAll();
        this.notifyUpdate();
    }

    public getAdapter(): AbsAdapter<T> {
        return this.adapter;
    }

    public getScrollView(): ScrollView {
        return this.scrollView;
    }

    public getAllItems() {
        return this._items;
    }

    /**
     * 滚动API
     * @param pageIndex 滚动到哪一页.
     * @param pageCount 如果>0 则以count数量的item 为一页.否则以当前可见数量为一页.
     * @param timeSecond
     * @return true = 滚动到最后一页了.
     */
    public scrollToPage(pageIndex: number, pageCount?: number, timeSecond?: number): boolean {
        if (!this.adapter || !this.scrollView) {
            return false;
        }
        const count = this.adapter.getCount() || 1;
        const contentUITransform = this.content.getComponent(UITransform)
        const parentUITransform = this.content.parent.getComponent(UITransform)
        //this.column = this.column || 1;
        if (this.horizontal) {
            let pageWidth = 0;
            const maxWidth = contentUITransform.width;
            const columnWidth = this.getColumnWH();
            if (!pageCount) {
                // 可见区域的总宽度. 还需要进一步缩减为整数个item的区域.
                let pW = parentUITransform.width;
                pageWidth = Math.floor(pW / columnWidth) * columnWidth;
            } else {
                pageWidth = columnWidth * pageCount;
            }
            this.scrollView.scrollToOffset(v2(pageWidth * pageIndex, 0), timeSecond);
            return pageWidth * (pageIndex + 1) >= maxWidth;
        } else {
            const maxHeight = contentUITransform.height;
            const rowHeight = this.getColumnWH();
            let pageHeight = 0;
            if (!pageCount) {
                // maskView 的高度.
                let pH = parentUITransform.height;
                pageHeight = Math.floor(pH / rowHeight) * rowHeight;
            } else {
                pageHeight = rowHeight * pageCount;
            }
            this.scrollView.scrollToOffset(v2(0, pageHeight * pageIndex), timeSecond);
            return pageHeight * (pageIndex + 1) >= maxHeight;
        }
    }

    // 获取可见区域的最大元素个数。不包含遮挡一半的元素。
    public getVisibleElements(): number {
        let visibleCount = 0;
        const parentUITransform = this.content.parent.getComponent(UITransform)
        // const count = this.adapter ? (this.adapter.getCount() || 1) : 1;
        if (this.horizontal) {
            // 可见区域的总宽度. 还需要进一步缩减为整数个item的区域.
            let pW = parentUITransform.width;
            visibleCount = Math.floor(pW / this.getColumnWH());
        } else {
            // maskView 的高度.
            let pH = parentUITransform.height;
            visibleCount = Math.floor(pH / this.getColumnWH());
        }
        return visibleCount * this.column;
    }

    private getColumnWH(): number {
        if (this.horizontal) {
            return this._itemWidth + this.spacing.x;
        } else {
            return this._itemHeight + this.spacing.y;
        }
    }

    // 数据变更了需要进行更新UI显示, 可只更新某一条.
    public notifyUpdate() {
        if (this.adapter == null) {
            return;
        }
        if (!this._isInited) {
            this.init();
        }
        if (!this.scrollView || !this.content) {
            return;
        }
        if (this.emptyView) {
            this.emptyView.active = this.adapter.getCount() <= 0;
        }
        this.visibleRange[0] = this.visibleRange[1] = -1;
        const contentUITransform = this.content.getComponent(UITransform)
        if (this.horizontal) {
            contentUITransform.width =
                Math.ceil(this.adapter.getCount() / this.column) * (this._itemWidth + this.spacing.x) - this.spacing.x + this.margin.x + this.margin.width;
        } else {
            contentUITransform.height =
                Math.ceil(this.adapter.getCount() / this.column) * (this._itemHeight + this.spacing.y) - this.spacing.y + this.margin.y + this.margin.height;
        }
        this.dataChanged = true;
    }

    protected lateUpdate() {
        const range = this.getVisibleRange();
        if (!this.checkNeedUpdate(range)) {
            return;
        }
        this.recycleDirty(range);
        this.updateView(range);
    }

    // 向某位置添加一个item.
    private _layoutVertical(child: Node, posIndex: number) {
        this.content.addChild(child);
        // 当columns 大于1时,从左到右依次排列, 否则进行居中排列.
        const column = posIndex % (this.column || 1);
        const row = Math.floor(posIndex / (this.column || 1));
        const childUITransform = child.getComponent(UITransform)
        const contentUITransform = this.content.getComponent(UITransform)
        child.setPosition(
            this.column > 1
                ? this.margin.x + childUITransform.width * childUITransform.anchorX + (childUITransform.width + this.spacing.x) * column - contentUITransform.width * contentUITransform.anchorX
                : 0,
            -this.margin.y - childUITransform.height * (childUITransform.anchorY + row) - this.spacing.y * row
        );
    }

    // 向某位置添加一个item.
    private _layoutHorizontal(child: Node, posIndex: number) {
        this.content.addChild(child);
        const row = posIndex % (this.column || 1);
        const column = Math.floor(posIndex / (this.column || 1));
        const direction = -1; // -1 由上到下排列, 1= 由下往上排
        const childUITransform = child.getComponent(UITransform)
        const contentUITransform = this.content.getComponent(UITransform)
        child.setPosition(
            childUITransform.width * (childUITransform.anchorX + column) + this.spacing.x * column + this.margin.x,
            this.column > 1
                ? direction *
                (this.margin.y + childUITransform.height * childUITransform.anchorY + (childUITransform.height + this.spacing.y) * row - contentUITransform.height * contentUITransform.anchorY)
                : 0
        );
    }

    private recycleAll() {
        for (const child in this._filledIds) {
            if (this._filledIds.hasOwnProperty(child)) {
                this._items.put(this._filledIds[child]);
            }
        }
        this._filledIds = {};
    }

    private recycleDirty(visibleRange: number[]) {
        if (!visibleRange || visibleRange.length < 2) {
            return;
        }
        for (let i = this.visibleRange[0]; i < visibleRange[0]; i++) {
            if (i < 0 || !this._filledIds[i]) {
                continue;
            }
            this._items.put(this._filledIds[i]);
            this._filledIds[i] = null;
        }
        for (let j = this.visibleRange[1]; j > visibleRange[1]; j--) {
            if (j < 0 || !this._filledIds[j]) {
                continue;
            }
            this._items.put(this._filledIds[j]);
            this._filledIds[j] = null;
        }
        this.visibleRange[0] = visibleRange[0];
        this.visibleRange[1] = visibleRange[1];
    }

    private checkNeedUpdate(visibleRange: number[]): boolean {
        return visibleRange && this.visibleRange && (this.visibleRange[0] != visibleRange[0] || this.visibleRange[1] != visibleRange[1]);
    }

    // 填充View.
    private updateView(visibleRange: number[]) {
        for (let i = visibleRange[0]; i <= visibleRange[1]; i++) {
            if (!this.dataChanged) {
                if (this._filledIds[i]) {
                    continue;
                }
            }
            let child = this._filledIds[i] || this._items.get() || instantiate(this.itemTemplate);
            if (this.comp && !(child.getComponent(Component) instanceof this.comp)) {
                child.getComponent(Component).destroy()
                child.addComponent(this.comp);
            }
            child.removeFromParent();
            this.horizontal ? this._layoutHorizontal(child, i) : this._layoutVertical(child, i);
            this._filledIds[i] = this.adapter._getView(child, i);
        }
        this.dataChanged = false;
    }

    // 获取当前屏幕可见元素索引.
    private getVisibleRange(): number[] {
        if (this.adapter == null) {
            return null;
        }
        let scrollOffset = this.scrollView.getScrollOffset();
        let startIndex = 0;

        if (this.horizontal) {
            startIndex = Math.floor(-scrollOffset.x / (this._itemWidth + this.spacing.x));
        } else {
            startIndex = Math.floor(scrollOffset.y / (this._itemHeight + this.spacing.y));
        }
        if (startIndex < 0) {
            startIndex = 0;
        }
        let visible = this.column * (startIndex + this._itemsVisible + this.spawnCount);
        if (visible >= this.adapter.getCount()) {
            visible = this.adapter.getCount() - 1;
        }
        return [startIndex * this.column, visible];
    }

    private init() {
        if (this._isInited) {
            return;
        }
        this._isInited = true;
        if (this.scrollView) {
            this.content = this.scrollView.content;
            this.horizontal = this.scrollView.horizontal;
            const contentUITransform = this.content.getComponent(UITransform)
            const contentParentUITransform = this.content.parent.getComponent(UITransform)
            if (this.horizontal) {
                this.scrollView.vertical = false;
                contentUITransform.anchorX = 0;
                contentUITransform.anchorY = contentParentUITransform.anchorY;
                this.content.setPosition(0 - contentParentUITransform.width * contentParentUITransform.anchorX, 0)
            } else {
                this.scrollView.vertical = true;
                contentUITransform.anchorX = contentParentUITransform.anchorX;
                contentUITransform.anchorY = 1;
                this.content.setPosition(0, contentParentUITransform.height * contentParentUITransform.anchorY)
            }
        } else {
            error('ListView need a scrollView for showing.');
        }

        let itemOne = this._items.get() || instantiate(this.itemTemplate);
        this._items.put(itemOne);
        const itemOneUITransform = itemOne.getComponent(UITransform)
        const contentParent = this.content.parent.getComponent(UITransform)
        this._itemHeight = itemOneUITransform.height || 10;
        this._itemWidth = itemOneUITransform.width || 10;

        if (this.horizontal) {
            this._itemsVisible = Math.ceil((contentParent.width - this.margin.x - this.margin.width) / (this._itemWidth + this.spacing.x));
        } else {
            this._itemsVisible = Math.ceil((contentParent.height - this.margin.y - this.margin.height) / (this._itemHeight + this.spacing.y));
        }
    }
}



