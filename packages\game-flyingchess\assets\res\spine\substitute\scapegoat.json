{"skeleton": {"hash": "4Ws1G9eQGmtQCR7YsKMiTSJRrCM", "spine": "3.8.85", "x": -1266.33, "y": -267.57, "width": 1531.97, "height": 1237.11, "images": "./images/", "audio": "E:/XM/商业策划部/海盗桶/14-替身"}, "bones": [{"name": "root", "x": -13.12, "y": -82}, {"name": "00", "parent": "root", "x": -42.45, "y": 113.29}, {"name": "ts_9", "parent": "00", "length": 66.67, "rotation": 77.2, "x": -31.61, "y": -55.43}, {"name": "ts_10", "parent": "ts_9", "length": 77.13, "rotation": -18.08, "x": 66.67}, {"name": "ts_1", "parent": "ts_10", "length": 251.89, "rotation": 4.86, "x": 74.44, "y": -3.68}, {"name": "ts_3", "parent": "ts_1", "length": 42.29, "rotation": -97, "x": 94.5, "y": -124.86}, {"name": "ts_4", "parent": "ts_3", "length": 32.18, "rotation": -12.72, "x": 42.29}, {"name": "ts_5", "parent": "ts_4", "length": 26.76, "rotation": -1.05, "x": 32.18}, {"name": "ts_6", "parent": "ts_1", "length": 35.58, "rotation": -68.74, "x": 109.24, "y": -132.13}, {"name": "ts_7", "parent": "ts_6", "length": 30.87, "rotation": -0.73, "x": 35.58}, {"name": "ts_8", "parent": "ts_7", "length": 33.29, "rotation": -0.62, "x": 30.87}, {"name": "ts_11", "parent": "ts_10", "length": 48.88, "rotation": -73.82, "x": 51, "y": -19.76}, {"name": "ts_13", "parent": "ts_11", "length": 59.3, "rotation": -45.41, "x": 49.03, "y": -0.57}, {"name": "ts_14", "parent": "ts_13", "length": 56.07, "rotation": -11.45, "x": 59.3}, {"name": "ts_12", "parent": "ts_10", "length": 21.42, "rotation": 92.91, "x": 43.94, "y": 7.01}, {"name": "ts_16", "parent": "ts_12", "length": 58.51, "rotation": 72.98, "x": 21.76, "y": -3.16}, {"name": "ts_17", "parent": "ts_16", "length": 56.86, "rotation": -1.68, "x": 58.51}, {"name": "ts_18", "parent": "ts_9", "length": 56.3, "rotation": -162.98, "x": -12.09, "y": -51.84}, {"name": "ts_19", "parent": "ts_18", "length": 42.61, "rotation": 19.51, "x": 56.3}, {"name": "ts_20", "parent": "ts_9", "length": 50.29, "rotation": -169.89, "x": -6.91, "y": 16.01}, {"name": "ts_21", "parent": "ts_20", "length": 45.46, "rotation": 18.54, "x": 50.29}, {"name": "ts_15", "parent": "root", "rotation": 49.18, "x": -213.54, "y": 773.54}, {"name": "ts_22", "parent": "root", "length": 144.14, "rotation": 143.91, "x": -721.81, "y": 621.34, "scaleX": 4.6056, "scaleY": 4.6056}, {"name": "b0", "parent": "root", "x": -34.5, "y": 113.28, "scaleX": 7.2012, "scaleY": 7.2012, "color": "ff0000ff"}], "slots": [{"name": "ts_10", "bone": "ts_16"}, {"name": "ts_9", "bone": "ts_9"}, {"name": "ts_8", "bone": "ts_18"}, {"name": "ts_7", "bone": "ts_20"}, {"name": "ts_6", "bone": "ts_13"}, {"name": "ts_5", "bone": "ts_9"}, {"name": "ts_4", "bone": "ts_15"}, {"name": "ts_11", "bone": "ts_22", "attachment": "ts_4"}, {"name": "ts_3", "bone": "ts_6"}, {"name": "ts_2", "bone": "ts_3"}, {"name": "ts_1", "bone": "ts_1"}, {"name": "b0", "bone": "b0", "attachment": "b0"}], "skins": [{"name": "default", "attachments": {"b0": {"b0": {"y": 0.5, "width": 87, "height": 84}, "b1": {"y": 0.5, "width": 87, "height": 84}, "b2": {"y": 0.5, "width": 87, "height": 84}, "b3": {"y": 0.5, "width": 87, "height": 84}, "b4": {"y": 0.5, "width": 87, "height": 84}, "b5": {"y": 0.5, "width": 87, "height": 84}, "b6": {"y": 0.5, "width": 87, "height": 84}, "b7": {"y": 0.5, "width": 87, "height": 84}, "b8": {"y": 0.5, "width": 87, "height": 84}}, "ts_1": {"ts_1": {"type": "mesh", "uvs": [0.51753, 1e-05, 0.68072, 0.04598, 0.8141, 0.13391, 0.9239, 0.26003, 0.98384, 0.37922, 1, 0.50312, 0.98849, 0.60585, 0.94117, 0.72912, 0.86485, 0.83717, 0.74766, 0.93313, 0.59658, 0.9657, 0.44399, 0.94372, 0.31284, 0.88775, 0.18835, 0.79087, 0.10837, 0.69394, 0.0454, 0.58398, 0.00771, 0.45885, 0.02799, 0.34637, 0.08526, 0.22424, 0.16454, 0.13145, 0.26738, 0.04188, 0.38397, 0.00206], "triangles": [15, 17, 18, 16, 17, 15, 4, 5, 3, 14, 18, 19, 15, 18, 14, 5, 7, 3, 6, 7, 5, 21, 13, 19, 14, 19, 13, 3, 12, 2, 3, 11, 12, 20, 21, 19, 0, 12, 13, 0, 13, 21, 1, 12, 0, 12, 1, 2, 11, 3, 7, 7, 8, 11, 8, 10, 11, 9, 10, 8], "vertices": [243.75, 52.96, 253.46, 3.22, 248.5, -43.73, 230.65, -89.1, 208.02, -120.17, 178.41, -140.02, 150.58, -149.76, 112.79, -152.42, 75.07, -145.35, 35.08, -125.76, 6.84, -89.1, -7.61, -45.22, -10.54, -2.86, -2.12, 42.81, 12.18, 76.49, 32.05, 107.22, 59.14, 133.03, 90.61, 141.62, 129.43, 141.46, 163.63, 131.68, 200.11, 115.16, 225.65, 88.71], "hull": 22, "edges": [0, 42, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 18, 20, 28, 30, 20, 22, 22, 24, 24, 26, 26, 28], "width": 300, "height": 285}}, "ts_10": {"ts_10": {"type": "mesh", "uvs": [0.98078, 0.02006, 0.98069, 0.24111, 0.89119, 0.37655, 0.79149, 0.52741, 0.65855, 0.68758, 0.49115, 0.88926, 0.28596, 1, 0.2328, 1, 0.02053, 0.92585, 0.0173, 0.71633, 0.20172, 0.5221, 0.44913, 0.34308, 0.68153, 0.17492, 0.84734, 0.05495, 0.96555, 0], "triangles": [10, 11, 4, 5, 10, 4, 7, 9, 10, 7, 8, 9, 7, 10, 6, 5, 6, 10, 0, 13, 14, 1, 13, 0, 1, 12, 13, 2, 12, 1, 3, 12, 2, 11, 12, 3, 4, 11, 3], "vertices": [1, 15, -4.71, 3.3, 1, 1, 15, 10.61, 18.62, 1, 2, 15, 26.52, 21.48, 0.99974, 16, -32.61, 20.53, 0.00026, 2, 15, 44.23, 24.68, 0.88641, 16, -14.99, 24.25, 0.11359, 2, 15, 65.01, 26.09, 0.26435, 16, 5.74, 26.27, 0.73565, 2, 15, 91.18, 27.88, 0.00037, 16, 31.84, 28.83, 0.99963, 1, 16, 54.66, 22.22, 1, 1, 16, 58.65, 18.47, 1, 1, 16, 69.57, -1.82, 1, 1, 16, 55.73, -16.99, 1, 1, 16, 28.85, -17.81, 1, 2, 15, 56.39, -13.03, 0.45358, 16, -1.73, -13.09, 0.54642, 1, 15, 27.81, -7.76, 1, 1, 15, 7.42, -4, 1, 1, 15, -4.99, 0.81, 1], "hull": 15, "edges": [0, 28, 0, 2, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 26, 28, 24, 26, 20, 22, 22, 24, 6, 8, 8, 10, 2, 4, 4, 6], "width": 103, "height": 98}}, "ts_11": {"ts_4": {"x": 65.95, "y": -0.7, "rotation": -143.91, "width": 125, "height": 108}}, "ts_2": {"ts_2": {"type": "mesh", "uvs": [0.4123, 0.14634, 0.64316, 0.196, 0.83437, 0.28497, 0.94456, 0.4056, 0.99999, 0.55737, 0.98521, 0.72093, 0.93419, 0.85269, 0.80339, 0.99752, 0.73652, 0.98842, 0.57623, 0.89426, 0.43085, 0.79176, 0.29269, 0.68647, 0.17648, 0.56078, 0.06753, 0.41487, 0.01101, 0.2748, 0.01124, 0.10672, 0.05919, 0.0972, 0.19022, 0.11298], "triangles": [17, 14, 16, 14, 15, 16, 17, 13, 14, 0, 12, 13, 0, 13, 17, 1, 11, 12, 1, 12, 0, 4, 10, 3, 10, 11, 1, 10, 1, 2, 10, 2, 3, 5, 10, 4, 5, 9, 10, 6, 9, 5, 8, 9, 6, 7, 8, 6], "vertices": [29.73, 20.13, 48.86, 27.12, 67.11, 29.22, 81.11, 25.08, 92.71, 15.97, 99.85, 2.66, 102.77, -9.9, 100.61, -27.2, 95.33, -29.63, 79.05, -29.88, 63.43, -28.79, 48.19, -27.14, 33.5, -22.89, 18.33, -16.74, 7.23, -8.59, -1.18, 4.39, 1.8, 7.37, 12.04, 12.29], "hull": 18, "edges": [4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 0, 0, 2, 2, 4, 22, 24, 20, 22, 16, 18, 18, 20], "width": 86, "height": 92}}, "ts_3": {"ts_3": {"type": "mesh", "uvs": [0.52617, 0.03782, 0.65689, 0.11432, 0.81434, 0.26004, 0.92526, 0.40575, 0.9972, 0.52714, 1, 0.63044, 0.89771, 0.76263, 0.74418, 0.90756, 0.53457, 0.97943, 0.35056, 0.97686, 0.1881, 0.91008, 0.06562, 0.79615, 0, 0.6874, 0, 0.49682, 0.07163, 0.28918, 0.17858, 0.10522, 0.3291, 0, 0.42219, 0], "triangles": [11, 12, 13, 6, 2, 3, 6, 3, 4, 6, 4, 5, 14, 11, 13, 7, 1, 2, 7, 2, 6, 9, 10, 15, 14, 15, 10, 11, 14, 10, 9, 16, 17, 9, 17, 0, 9, 15, 16, 8, 9, 0, 1, 8, 0, 7, 8, 1], "vertices": [49.79, 24.19, 63.57, 21.04, 80.41, 14.26, 92.47, 7.07, 100.42, 0.91, 101.19, -4.83, 91.3, -13.08, 76.22, -22.48, 55.04, -28.28, 36.13, -29.71, 19.15, -27.38, 6.05, -22.07, -1.19, -16.56, -2.08, -5.92, 4.31, 6.28, 14.43, 17.46, 29.39, 24.62, 38.94, 25.41], "hull": 18, "edges": [24, 22, 22, 20, 20, 18, 16, 18, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 0, 0, 34, 32, 34, 32, 30, 30, 28, 24, 26, 28, 26], "width": 103, "height": 56}}, "ts_4": {"ts_4": {"x": 3.51, "y": -8.85, "rotation": -49.18, "width": 125, "height": 108}}, "ts_5": {"ts_5": {"type": "mesh", "uvs": [0.45409, 0.08374, 0.50072, 0.08674, 0.51993, 0.0961, 0.56349, 0.10847, 0.71082, 0.15029, 0.73778, 0.14926, 0.95637, 0.28706, 0.9895, 0.32967, 0.98981, 0.38182, 0.97592, 0.39394, 0.91432, 0.35758, 0.81713, 0.45281, 0.76637, 0.54999, 0.7637, 0.64121, 0.83686, 0.75366, 0.82109, 0.93016, 0.81429, 0.97869, 0.80416, 0.99721, 0.78232, 0.99452, 0.74783, 0.94112, 0.67228, 0.91113, 0.55556, 0.87132, 0.44911, 0.8491, 0.3273, 0.82289, 0.2529, 0.80413, 0.17683, 0.77746, 0.10933, 0.75925, 0.07725, 0.7576, 0.04998, 0.79335, 0.00312, 0.77888, 0.00794, 0.72617, 0.04006, 0.58767, 0.10285, 0.42514, 0.14933, 0.3048, 0.21555, 0.20889, 0.28794, 0.16826, 0.34961, 0.13365, 0.41424, 0.09738, 0.49578, 0.16683, 0.44446, 0.24905, 0.38391, 0.36921, 0.32541, 0.49675, 0.29052, 0.63905, 0.35031, 0.21118, 0.28615, 0.33033, 0.22902, 0.46483, 0.17716, 0.62822, 0.69049, 0.19787, 0.62855, 0.29735, 0.57336, 0.41997, 0.53957, 0.53911, 0.49001, 0.67561], "triangles": [13, 50, 12, 50, 49, 12, 12, 49, 11, 11, 49, 48, 11, 48, 47, 10, 11, 47, 47, 4, 5, 10, 47, 5, 49, 39, 48, 8, 9, 7, 9, 10, 7, 10, 6, 7, 10, 5, 6, 48, 3, 47, 45, 33, 44, 44, 43, 40, 40, 43, 39, 33, 34, 44, 43, 34, 35, 43, 44, 34, 39, 38, 48, 43, 37, 39, 38, 39, 37, 35, 36, 43, 43, 36, 37, 41, 40, 50, 50, 40, 49, 41, 44, 40, 40, 39, 49, 38, 3, 48, 38, 37, 0, 47, 3, 4, 38, 2, 3, 38, 1, 2, 38, 0, 1, 17, 18, 16, 18, 19, 16, 16, 19, 15, 15, 19, 14, 14, 19, 20, 14, 20, 13, 22, 51, 21, 20, 21, 13, 21, 51, 13, 22, 23, 51, 24, 42, 23, 23, 42, 51, 24, 25, 42, 27, 28, 30, 28, 29, 30, 25, 46, 42, 25, 26, 46, 26, 27, 46, 46, 27, 31, 27, 30, 31, 42, 41, 51, 51, 50, 13, 51, 41, 50, 46, 45, 42, 42, 45, 41, 31, 32, 46, 46, 32, 45, 45, 44, 41, 32, 33, 45], "vertices": [2, 3, 72.43, 22.65, 0.55343, 14, 14.18, -29.24, 0.44657, 2, 3, 76.5, 14.76, 0.76247, 14, 6.09, -32.9, 0.23753, 2, 3, 76.88, 10.74, 0.84998, 14, 2.06, -33.09, 0.15002, 2, 3, 79.17, 2.46, 0.98838, 14, -6.32, -34.95, 0.01162, 2, 3, 86.9, -25.53, 0.30214, 11, 15.55, 32.87, 0.69786, 2, 3, 89.69, -29.83, 0.22561, 11, 20.45, 34.35, 0.77439, 1, 11, 67.09, 20.24, 1, 1, 11, 75.18, 14.21, 1, 1, 11, 77.69, 4.9, 1, 1, 11, 75.71, 2.06, 1, 1, 11, 62.68, 5.59, 1, 3, 2, 91.25, -83.35, 0.04608, 3, 49.24, -71.6, 0.00113, 11, 49.29, -16.14, 0.95279, 3, 2, 71.58, -77.93, 0.25453, 3, 28.86, -72.55, 0.02244, 11, 44.53, -35.97, 0.72303, 3, 2, 55.02, -81.17, 0.4779, 3, 14.12, -80.78, 0.01756, 11, 48.32, -52.42, 0.50455, 3, 2, 37.81, -99.34, 0.60918, 3, 3.4, -103.39, 0.00099, 11, 67.05, -69.02, 0.38982, 2, 2, 5.31, -103.65, 0.65363, 11, 72.44, -101.36, 0.34637, 2, 2, -3.74, -104.38, 0.6557, 11, 73.47, -110.37, 0.3443, 2, 2, -7.5, -103.26, 0.65588, 11, 72.48, -114.18, 0.34412, 2, 2, -7.94, -99.11, 0.65592, 11, 68.34, -114.75, 0.34407, 2, 2, 0.24, -90.53, 0.65744, 11, 59.49, -106.86, 0.34256, 2, 2, 2.47, -75.3, 0.67689, 11, 44.2, -105.14, 0.32311, 3, 2, 4.74, -52.04, 0.76121, 3, -42.71, -68.69, 0.00019, 11, 20.88, -103.64, 0.23859, 2, 2, 4.27, -31.41, 0.88283, 11, 0.27, -104.8, 0.11717, 2, 2, 3.86, -7.77, 0.98913, 11, -23.34, -105.99, 0.01087, 1, 2, 4.12, 6.79, 1, 1, 2, 5.72, 21.97, 1, 2, 2, 6.17, 35.23, 0.99966, 14, 13.4, 111.86, 0.00034, 2, 2, 5.11, 41.24, 0.99962, 14, 18.92, 114.45, 0.00038, 1, 2, -2.48, 44.83, 1, 1, 2, -1.85, 54.1, 1, 2, 2, 7.86, 55.37, 0.99906, 14, 33.28, 115.49, 0.00094, 3, 2, 34.2, 55.1, 0.95552, 3, -47.96, 42.3, 0.01794, 14, 39.91, 90, 0.02654, 3, 2, 66.17, 50.13, 0.62441, 3, -16.04, 47.5, 0.18413, 14, 43.48, 57.85, 0.19146, 3, 2, 89.83, 46.45, 0.24612, 3, 7.6, 51.35, 0.26627, 14, 46.13, 34.04, 0.48761, 3, 2, 109.92, 38.11, 0.05766, 3, 29.29, 49.66, 0.1342, 14, 43.34, 12.47, 0.80814, 3, 2, 120.3, 26.37, 0.00783, 3, 42.8, 41.71, 0.01722, 14, 34.72, -0.62, 0.97495, 2, 3, 54.31, 34.94, 0.04062, 14, 27.38, -11.77, 0.95938, 2, 3, 66.37, 27.85, 0.34377, 14, 19.68, -23.46, 0.65623, 2, 3, 63.3, 7.96, 0.7913, 14, -0.03, -19.38, 0.2087, 1, 14, 1.45, -1.37, 1, 2, 3, 20.26, 6.98, 0.88594, 14, 1.18, 23.66, 0.11406, 3, 2, 62.62, 5.96, 0.84929, 3, -5.7, 4.4, 0.13568, 14, -0.08, 49.71, 0.01503, 2, 2, 35.48, 6.59, 0.99886, 14, -6.57, 76.07, 0.00114, 3, 2, 115.19, 13.05, 0.00024, 3, 42.07, 27.47, 0.00432, 14, 20.53, 0.83, 0.99545, 3, 2, 90.99, 20.05, 0.09073, 3, 16.89, 26.61, 0.42459, 14, 20.95, 26.02, 0.48468, 3, 2, 64.32, 25.12, 0.63717, 3, -10.03, 23.15, 0.23718, 14, 18.87, 53.09, 0.12565, 3, 2, 32.66, 28.03, 0.98054, 3, -41.03, 16.09, 0.00602, 14, 13.39, 84.4, 0.01343, 2, 3, 77.36, -26.74, 0.28586, 11, 14.04, 23.37, 0.71414, 2, 3, 55.53, -26.08, 0.02012, 11, 7.33, 2.58, 0.97988, 3, 2, 86.91, -36.83, 0.07802, 3, 30.68, -28.73, 0.28671, 11, 2.95, -22.02, 0.63526, 3, 2, 64, -35.46, 0.38019, 3, 8.47, -34.54, 0.26232, 11, 2.34, -44.97, 0.35749, 3, 2, 37.28, -31.87, 0.77519, 3, -18.04, -39.42, 0.05004, 11, -0.36, -71.78, 0.17478], "hull": 38, "edges": [8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 66, 68, 74, 0, 4, 2, 0, 2, 72, 74, 68, 70, 70, 72, 62, 64, 64, 66, 4, 6, 6, 8, 6, 76, 76, 78, 78, 80, 80, 82, 82, 84, 46, 48, 84, 48, 48, 50, 50, 52, 86, 88, 88, 90, 90, 92, 94, 96, 96, 98, 98, 100, 100, 102, 102, 44], "width": 190, "height": 185}}, "ts_6": {"ts_6": {"type": "mesh", "uvs": [0.43285, 0.00803, 0.45988, 0.02435, 0.54116, 0.09611, 0.67161, 0.22594, 0.85088, 0.46638, 0.96188, 0.69682, 0.97891, 0.89051, 0.9117, 0.97752, 0.83194, 0.99189, 0.66736, 0.99212, 0.51911, 0.94313, 0.2839, 0.78707, 0.09869, 0.61498, 0.04589, 0.54492, 0.01199, 0.47494, 0.01216, 0.31895, 0.05473, 0.22145, 0.12331, 0.13098, 0.27546, 0.03003, 0.35694, 0.00791], "triangles": [12, 4, 11, 5, 10, 11, 5, 11, 4, 9, 10, 5, 9, 5, 6, 7, 8, 6, 8, 9, 6, 12, 13, 15, 14, 15, 13, 16, 19, 15, 19, 12, 15, 19, 17, 18, 17, 19, 16, 19, 1, 12, 0, 1, 19, 2, 12, 1, 3, 12, 2, 4, 12, 3], "vertices": [1, 12, -2.96, 20.28, 1, 1, 12, -0.14, 21.18, 1, 1, 12, 10.73, 22.53, 1, 2, 12, 29.73, 23.8, 0.99995, 13, -33.71, 17.45, 5e-05, 2, 12, 62.39, 21.77, 0.45832, 13, -1.29, 21.95, 0.54168, 2, 12, 91.25, 15.56, 3e-05, 13, 28.22, 21.59, 0.99997, 1, 13, 51.07, 15.43, 1, 1, 13, 59.42, 6.9, 1, 1, 13, 59.04, 0.22, 1, 1, 13, 54.86, -12.43, 1, 2, 12, 99.43, -30.51, 0.00681, 13, 45.39, -21.94, 0.99319, 2, 12, 73.43, -37.54, 0.23132, 13, 21.3, -33.99, 0.76868, 2, 12, 47.75, -40.09, 0.7324, 13, -3.36, -41.58, 0.2676, 2, 12, 38.21, -39.54, 0.85733, 13, -12.82, -42.94, 0.14267, 2, 12, 29.44, -37.66, 0.92983, 13, -21.79, -42.84, 0.07017, 2, 12, 12.95, -28.17, 0.99617, 13, -39.84, -36.81, 0.00383, 1, 12, 4.35, -19.25, 1, 1, 12, -2.45, -8.94, 1, 1, 12, -6.99, 7.89, 1, 1, 12, -6.04, 14.95, 1], "hull": 20, "edges": [0, 38, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38], "width": 81, "height": 122}}, "ts_7": {"ts_7": {"type": "mesh", "uvs": [0.11579, 0.00889, 0.23124, 0.00909, 0.41275, 0.09027, 0.59531, 0.1982, 0.84112, 0.36922, 0.95214, 0.47208, 0.96976, 0.59817, 0.96949, 0.87145, 0.92312, 0.92679, 0.84224, 0.97499, 0.73662, 0.99191, 0.62709, 0.98959, 0.48411, 0.93294, 0.29577, 0.80473, 0.14165, 0.65266, 0.01825, 0.38319, 0.04568, 0.09993, 0.0458, 0.03649], "triangles": [6, 14, 4, 6, 4, 5, 13, 14, 6, 6, 12, 13, 6, 7, 12, 8, 12, 7, 12, 10, 11, 12, 8, 10, 9, 10, 8, 17, 1, 2, 1, 17, 0, 2, 15, 16, 4, 15, 3, 2, 16, 17, 15, 2, 3, 4, 14, 15], "vertices": [1, 19, -2.22, -27.23, 1, 1, 19, -2.56, -19.73, 1, 1, 19, 5.73, -7.53, 1, 1, 19, 16.92, 4.88, 1, 2, 19, 34.79, 21.71, 0.74983, 20, -7.79, 25.51, 0.25017, 2, 19, 45.65, 29.45, 0.42188, 20, 4.97, 29.4, 0.57812, 2, 19, 59.33, 31.24, 0.14929, 20, 18.5, 26.75, 0.85071, 1, 20, 47.15, 18.6, 1, 1, 20, 52.13, 14.05, 1, 1, 20, 55.75, 7.56, 1, 1, 20, 55.65, 0.45, 1, 1, 20, 53.46, -6.33, 1, 1, 20, 44.99, -13.59, 1, 2, 19, 83.88, -11.46, 0.00143, 20, 28.2, -21.55, 0.99857, 2, 19, 67.79, -22.25, 0.17394, 20, 9.52, -26.66, 0.82606, 2, 19, 38.83, -31.64, 0.9373, 20, -20.93, -26.36, 0.0627, 1, 19, 7.9, -31.31, 1, 1, 19, 0.99, -31.63, 1], "hull": 18, "edges": [0, 34, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34], "width": 65, "height": 109}}, "ts_8": {"ts_8": {"type": "mesh", "uvs": [0.34005, 0.00719, 0.52398, 0.01959, 0.62453, 0.0361, 0.76703, 0.09626, 0.88266, 0.19068, 0.94549, 0.25942, 0.97418, 0.307, 0.97433, 0.43075, 0.90477, 0.80024, 0.88351, 0.8515, 0.76691, 0.94634, 0.64987, 0.97976, 0.50523, 0.97856, 0.38879, 0.93072, 0.26843, 0.85677, 0.14975, 0.71074, 0.0824, 0.5745, 0.02717, 0.40784, 0.00836, 0.23746, 0.02759, 0.17484, 0.05838, 0.13285, 0.14405, 0.06261, 0.23096, 0.02614], "triangles": [15, 4, 7, 15, 7, 8, 8, 14, 15, 8, 13, 14, 13, 8, 11, 9, 10, 8, 11, 12, 13, 8, 10, 11, 21, 17, 18, 20, 18, 19, 18, 20, 21, 22, 16, 17, 21, 22, 17, 1, 15, 16, 5, 7, 4, 6, 7, 5, 1, 16, 0, 0, 16, 22, 1, 3, 15, 2, 3, 1, 4, 15, 3], "vertices": [1, 17, 6.91, -2.96, 1, 2, 17, 9.12, 11.08, 0.99479, 18, -40.77, 26.2, 0.00521, 2, 17, 11.23, 18.69, 0.97715, 18, -36.24, 32.66, 0.02285, 2, 17, 17.68, 29.21, 0.91443, 18, -26.65, 40.43, 0.08557, 2, 17, 27.18, 37.44, 0.79948, 18, -14.94, 45.01, 0.20052, 2, 17, 33.98, 41.79, 0.71098, 18, -7.08, 46.84, 0.28902, 2, 17, 38.61, 43.66, 0.65909, 18, -2.1, 47.07, 0.34091, 2, 17, 50.21, 42.82, 0.49917, 18, 8.56, 42.4, 0.50083, 2, 17, 84.45, 34.93, 0.02167, 18, 38.2, 23.52, 0.97833, 2, 17, 89.14, 32.94, 0.0087, 18, 41.95, 20.08, 0.9913, 1, 18, 46.51, 8.28, 1, 1, 18, 45.76, -1.24, 1, 1, 18, 41.17, -11.39, 1, 1, 18, 33.45, -17.79, 1, 2, 17, 86.15, -14.33, 0.00185, 18, 23.36, -23.48, 0.99815, 2, 17, 71.79, -22.43, 0.1471, 18, 7.11, -26.32, 0.8529, 2, 17, 58.64, -26.66, 0.53395, 18, -6.7, -25.92, 0.46605, 2, 17, 42.7, -29.76, 0.90933, 18, -22.75, -23.51, 0.09067, 2, 17, 26.62, -30.02, 0.99737, 18, -38, -18.39, 0.00263, 2, 17, 20.86, -28.11, 0.99997, 18, -42.79, -14.67, 3e-05, 1, 17, 17.1, -25.46, 1, 1, 17, 11, -18.4, 1, 1, 17, 8.07, -11.47, 1], "hull": 23, "edges": [0, 44, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44], "width": 77, "height": 94}}, "ts_9": {"ts_9": {"type": "mesh", "uvs": [0.48656, 0.09596, 0.64993, 0.09598, 0.86552, 0.17998, 0.98851, 0.25769, 0.99999, 0.31302, 0.98673, 0.37946, 0.9538, 0.46576, 0.90621, 0.54418, 0.85592, 0.60035, 0.85573, 0.69893, 0.85556, 0.79138, 0.85524, 0.9568, 0.68433, 0.99917, 0.43703, 0.98475, 0.14368, 0.88274, 0.00615, 0.777, 0.00431, 0.65154, 0.04772, 0.5371, 0.1036, 0.38977, 0.2516, 0.20439, 0.37911, 0.13154, 0.49891, 0.17511, 0.39514, 0.27473, 0.27624, 0.43152, 0.20922, 0.55727, 0.16815, 0.67159, 0.14653, 0.79082, 0.62862, 0.2094, 0.53566, 0.31883, 0.43622, 0.47235, 0.38649, 0.5981, 0.34542, 0.71242, 0.30867, 0.83818, 0.834, 0.2633, 0.73887, 0.37925, 0.65456, 0.51481, 0.61565, 0.64873, 0.58971, 0.74835, 0.56376, 0.86921], "triangles": [27, 1, 2, 33, 27, 2, 33, 2, 3, 34, 27, 33, 28, 27, 34, 4, 5, 33, 4, 33, 3, 34, 33, 5, 6, 34, 5, 35, 28, 34, 7, 35, 34, 6, 7, 34, 8, 35, 7, 20, 0, 21, 22, 20, 21, 19, 20, 22, 22, 23, 19, 23, 18, 19, 23, 22, 29, 29, 28, 35, 24, 17, 18, 23, 24, 18, 30, 23, 29, 24, 23, 30, 35, 30, 29, 36, 35, 8, 36, 30, 35, 25, 17, 24, 25, 24, 30, 16, 17, 25, 9, 36, 8, 31, 25, 30, 31, 30, 36, 37, 31, 36, 37, 36, 9, 15, 16, 25, 26, 15, 25, 26, 25, 31, 10, 37, 9, 32, 26, 31, 38, 31, 37, 38, 37, 10, 32, 31, 38, 14, 15, 26, 14, 26, 32, 11, 38, 10, 13, 32, 38, 14, 32, 13, 12, 38, 11, 13, 38, 12, 21, 0, 1, 27, 21, 1, 28, 21, 27, 22, 21, 28, 29, 22, 28], "vertices": [2, 3, 71.27, 19.75, 0.61344, 14, 11.34, -27.94, 0.38656, 2, 3, 85.77, -4.51, 0.90449, 11, -4.96, 37.64, 0.09551, 2, 3, 88.41, -46.39, 0.06967, 11, 36, 28.51, 0.93033, 2, 3, 84.06, -73.79, 0.00018, 11, 61.1, 16.7, 0.99982, 2, 2, 111.76, -100.98, 0.00146, 11, 66.24, 4.95, 0.99854, 2, 2, 96.41, -102.12, 0.02454, 11, 67.88, -10.35, 0.97546, 3, 3, 40.09, -93.1, 0.00513, 2, 75.88, -100.94, 0.11703, 11, 67.39, -30.91, 0.87784, 3, 3, 20.45, -95.25, 0.01811, 2, 56.54, -96.89, 0.26579, 11, 63.99, -50.38, 0.7161, 3, 3, 4.95, -94.39, 0.02404, 2, 42.07, -91.26, 0.44246, 11, 58.84, -65.02, 0.5335, 3, 3, -14.44, -105.95, 0.01164, 2, 20.05, -96.23, 0.73019, 11, 64.54, -86.87, 0.25818, 1, 2, -0.6, -100.9, 1, 1, 2, -37.55, -109.24, 1, 1, 2, -53.56, -82.55, 1, 1, 2, -59.83, -40.1, 1, 1, 2, -48.29, 14.56, 1, 1, 2, -29.95, 43.13, 1, 1, 2, -2.01, 49.81, 1, 1, 2, 25.21, 48.29, 1, 3, 3, -20.48, 42.06, 0.19578, 2, 60.25, 46.34, 0.71075, 14, 38.28, 62.56, 0.09347, 3, 3, 29.09, 41.88, 0.1831, 2, 107.32, 30.78, 0.01876, 14, 35.59, 13.06, 0.79814, 2, 3, 54.73, 31.52, 0.0589, 14, 23.93, -12.02, 0.9411, 2, 3, 56.81, 8.61, 0.5962, 14, 0.95, -12.93, 0.4038, 2, 3, 28.02, 12.3, 0.52421, 14, 6.1, 15.64, 0.47579, 3, 3, -13.35, 11.52, 0.11861, 2, 57.55, 15.1, 0.84741, 14, 7.42, 57, 0.03398, 3, 3, -44.02, 6.69, 0.001, 2, 26.9, 20.02, 0.99751, 14, 4.15, 87.87, 0.00149, 1, 2, -0.21, 21.15, 1, 1, 2, -27.66, 18.75, 1, 2, 3, 61.59, -14.68, 0.43466, 11, -1.93, 11.58, 0.56534, 3, 3, 31.83, -13.74, 0.66866, 2, 92.66, -22.95, 0.0054, 11, -11.12, -16.74, 0.32594, 3, 3, -7.17, -17.03, 0.22738, 2, 54.57, -13.96, 0.70112, 11, -18.84, -55.11, 0.0715, 3, 3, -36.3, -24.43, 0.01061, 2, 24.58, -11.96, 0.96316, 11, -19.84, -85.15, 0.02623, 1, 2, -2.53, -10.83, 1, 1, 2, -32.02, -11.01, 1, 2, 3, 69.24, -51.51, 0.01133, 11, 35.57, 8.67, 0.98867, 3, 3, 38, -51.02, 0.046, 2, 86.96, -60.3, 0.06659, 11, 26.4, -21.19, 0.88742, 3, 3, 3.88, -54.44, 0.13163, 2, 53.46, -52.95, 0.39629, 11, 20.17, -54.92, 0.47208, 3, 3, -25.9, -64.4, 0.03625, 2, 22.06, -53.18, 0.72333, 11, 21.44, -86.3, 0.24042, 1, 2, -1.18, -53.86, 1, 1, 2, -29.17, -55.62, 1], "hull": 21, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 14, 16, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 36, 38, 38, 40, 40, 0, 20, 22, 10, 12, 12, 14, 32, 34, 34, 36, 16, 18, 18, 20], "width": 173, "height": 229}}}}], "animations": {"scapegoat_admission": {"slots": {"b0": {"attachment": [{"name": null}, {"time": 0.5, "name": "b0"}, {"time": 0.5333, "name": "b1"}, {"time": 0.5667, "name": "b2"}, {"time": 0.6, "name": "b3"}, {"time": 0.6333, "name": "b4"}, {"time": 0.6667, "name": "b5"}, {"time": 0.7, "name": "b6"}, {"time": 0.7333, "name": "b7"}, {"time": 0.7667, "name": "b8"}, {"time": 0.8, "name": null}]}, "ts_1": {"attachment": [{"time": 0.5667, "name": "ts_1"}]}, "ts_2": {"attachment": [{"time": 0.5667, "name": "ts_2"}]}, "ts_3": {"attachment": [{"time": 0.5667, "name": "ts_3"}]}, "ts_5": {"attachment": [{"time": 0.5667, "name": "ts_5"}]}, "ts_6": {"attachment": [{"time": 0.5667, "name": "ts_6"}]}, "ts_7": {"attachment": [{"time": 0.5667, "name": "ts_7"}]}, "ts_8": {"attachment": [{"time": 0.5667, "name": "ts_8"}]}, "ts_9": {"attachment": [{"time": 0.5667, "name": "ts_9"}]}, "ts_10": {"attachment": [{"time": 0.5667, "name": "ts_10"}]}, "ts_11": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "color": "ffffffff"}], "attachment": [{"time": 0.5, "name": null}]}}, "bones": {"ts_22": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -6.86, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 3.44, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": "stepped"}, {"time": 1.2}], "translate": [{"x": 32.78, "y": 38.74, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": 770.45, "y": -548.06, "curve": "stepped"}, {"time": 1.2, "x": 770.45, "y": -548.06}], "scale": [{"curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.0333, "x": 1.149, "y": 0.669, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1, "x": 0.441, "y": 0.441, "curve": "stepped"}, {"time": 1.2, "x": 0.441, "y": 0.441}], "shear": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 1.2}]}, "ts_16": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -27.8, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 4.45, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -4.89, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": 1.09, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "curve": "stepped"}, {"time": 1.2}], "translate": [{"curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.1667, "curve": "stepped"}, {"time": 1.2}], "scale": [{"curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.1667, "curve": "stepped"}, {"time": 1.2}], "shear": [{"curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.1667, "curve": "stepped"}, {"time": 1.2}]}, "ts_9": {"rotate": [{"curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.2}], "translate": [{"curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.2}], "scale": [{"curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.2}], "shear": [{"curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.2}]}, "ts_18": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -41.89, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.7667, "angle": -5.68, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.8333, "angle": 2.51, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": -3.58, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": 2, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "curve": "stepped"}, {"time": 1.2}], "translate": [{"curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.1667, "curve": "stepped"}, {"time": 1.2}], "scale": [{"curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.1667, "curve": "stepped"}, {"time": 1.2}], "shear": [{"curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.1667, "curve": "stepped"}, {"time": 1.2}]}, "ts_20": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -41.89, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 0.6667, "angle": -11.16, "curve": 0.335, "c2": 0.34, "c3": 0.672, "c4": 0.68}, {"time": 0.8333, "angle": 2.51, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": -3.58, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": 2, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "curve": "stepped"}, {"time": 1.2}], "translate": [{"curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.1667, "curve": "stepped"}, {"time": 1.2}], "scale": [{"curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.1667, "curve": "stepped"}, {"time": 1.2}], "shear": [{"curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.1667, "curve": "stepped"}, {"time": 1.2}]}, "ts_13": {"rotate": [{"curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.2}], "translate": [{"curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.2}], "scale": [{"curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.2}], "shear": [{"curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.2}]}, "ts_6": {"rotate": [{"curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.2}], "translate": [{"curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.2}], "scale": [{"curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.2}], "shear": [{"curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.2}]}, "ts_3": {"rotate": [{"curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.2}], "translate": [{"curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.2}], "scale": [{"curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.2}], "shear": [{"curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.2}]}, "ts_1": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 23.08, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -2.97, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 3.32, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -1.54, "curve": 0.25, "c3": 0.75}, {"time": 1.2}], "translate": [{"curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.1333, "curve": "stepped"}, {"time": 1.2}], "scale": [{"curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.1333, "curve": "stepped"}, {"time": 1.2}], "shear": [{"curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.1333, "curve": "stepped"}, {"time": 1.2}]}, "00": {"rotate": [{"curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.2}], "translate": [{"curve": "stepped"}, {"time": 0.5667, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -3.8, "y": -35.15, "curve": "stepped"}, {"time": 0.9667, "x": -3.8, "y": -35.15, "curve": "stepped"}, {"time": 1.1, "x": -3.8, "y": -35.15, "curve": "stepped"}, {"time": 1.2, "x": -3.8, "y": -35.15}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": 0.865, "y": 0.865, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 0.622, "y": 0.622, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "x": 0.648, "y": 0.648, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "x": 0.622, "y": 0.622, "curve": "stepped"}, {"time": 1.2, "x": 0.622, "y": 0.622}], "shear": [{"curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.2}]}, "ts_21": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -41.89, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 0.6667, "angle": -11.16, "curve": 0.335, "c2": 0.34, "c3": 0.672, "c4": 0.68}, {"time": 0.8333, "angle": 2.51, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": -3.58, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": 2, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "curve": "stepped"}, {"time": 1.2}], "translate": [{"curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.1667, "curve": "stepped"}, {"time": 1.2}], "scale": [{"curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.1667, "curve": "stepped"}, {"time": 1.2}], "shear": [{"curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.1667, "curve": "stepped"}, {"time": 1.2}]}, "ts_19": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -41.89, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.7667, "angle": -5.68, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.8333, "angle": 2.51, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": -3.58, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": 2, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "curve": "stepped"}, {"time": 1.2}], "translate": [{"curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.1667, "curve": "stepped"}, {"time": 1.2}], "scale": [{"curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.1667, "curve": "stepped"}, {"time": 1.2}], "shear": [{"curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.1667, "curve": "stepped"}, {"time": 1.2}]}, "ts_17": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -27.8, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 4.45, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -4.89, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": 3.08, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "curve": "stepped"}, {"time": 1.2}], "translate": [{"curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.1667, "curve": "stepped"}, {"time": 1.2}], "scale": [{"curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.1667, "curve": "stepped"}, {"time": 1.2}], "shear": [{"curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.1667, "curve": "stepped"}, {"time": 1.2}]}, "ts_12": {"rotate": [{"curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.2}], "translate": [{"curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.2}], "scale": [{"curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.2}], "shear": [{"curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.2}]}, "ts_14": {"rotate": [{"curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.2}], "translate": [{"curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.2}], "scale": [{"curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.2}], "shear": [{"curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.2}]}, "ts_11": {"rotate": [{"curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.2}], "translate": [{"curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.2}], "scale": [{"curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.2}], "shear": [{"curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.2}]}, "ts_8": {"rotate": [{"curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.2}], "translate": [{"curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.2}], "scale": [{"curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.2}], "shear": [{"curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.2}]}, "ts_7": {"rotate": [{"curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.2}], "translate": [{"curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.2}], "scale": [{"curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.2}], "shear": [{"curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.2}]}, "ts_5": {"rotate": [{"curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.2}], "translate": [{"curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.2}], "scale": [{"curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.2}], "shear": [{"curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.2}]}, "ts_4": {"rotate": [{"curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.2}], "translate": [{"curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.2}], "scale": [{"curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.2}], "shear": [{"curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.2}]}, "ts_10": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 16.4, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -0.97, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": 1, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "curve": "stepped"}, {"time": 1.2}], "translate": [{"curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.2}], "scale": [{"curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.2}], "shear": [{"curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.2}]}, "b0": {"rotate": [{}], "translate": [{"y": 45.11, "curve": "stepped"}, {"time": 0.5, "y": 45.11, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": 51.55, "y": 17.18, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 0.8, "x": 51.55, "y": -4.42}], "scale": [{"x": 0.76, "y": 0.76, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 0.655, "y": 0.655, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5333, "x": 0.798, "y": 0.798, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5667, "x": 1.092, "y": 1.092}], "shear": [{}]}}}, "scapegoat_idle": {"slots": {"b0": {"attachment": [{"name": null}, {"time": 0.2, "name": null}, {"time": 0.3333, "name": null}, {"time": 0.4333, "name": null}]}, "ts_1": {"attachment": [{"name": "ts_1"}, {"time": 0.2, "name": "ts_1"}, {"time": 0.3333, "name": "ts_1"}, {"time": 0.4333, "name": "ts_1"}]}, "ts_2": {"attachment": [{"name": "ts_2"}, {"time": 0.2, "name": "ts_2"}, {"time": 0.3333, "name": "ts_2"}, {"time": 0.4333, "name": "ts_2"}]}, "ts_3": {"attachment": [{"name": "ts_3"}, {"time": 0.2, "name": "ts_3"}, {"time": 0.3333, "name": "ts_3"}, {"time": 0.4333, "name": "ts_3"}]}, "ts_4": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.1, "color": "ffffffff"}], "attachment": [{"name": "ts_4"}]}, "ts_5": {"attachment": [{"name": "ts_5"}, {"time": 0.2, "name": "ts_5"}, {"time": 0.3333, "name": "ts_5"}, {"time": 0.4333, "name": "ts_5"}]}, "ts_6": {"attachment": [{"name": "ts_6"}, {"time": 0.2, "name": "ts_6"}, {"time": 0.3333, "name": "ts_6"}, {"time": 0.4333, "name": "ts_6"}]}, "ts_7": {"attachment": [{"name": "ts_7"}, {"time": 0.2, "name": "ts_7"}, {"time": 0.3333, "name": "ts_7"}, {"time": 0.4333, "name": "ts_7"}]}, "ts_8": {"attachment": [{"name": "ts_8"}, {"time": 0.2, "name": "ts_8"}, {"time": 0.3333, "name": "ts_8"}, {"time": 0.4333, "name": "ts_8"}]}, "ts_9": {"attachment": [{"name": "ts_9"}, {"time": 0.2, "name": "ts_9"}, {"time": 0.3333, "name": "ts_9"}, {"time": 0.4333, "name": "ts_9"}]}, "ts_10": {"attachment": [{"name": "ts_10"}, {"time": 0.2, "name": "ts_10"}, {"time": 0.3333, "name": "ts_10"}, {"time": 0.4333, "name": "ts_10"}]}, "ts_11": {"attachment": [{"name": null}]}}, "bones": {"ts_22": {"rotate": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}], "translate": [{"x": 32.78, "y": 38.74, "curve": "stepped"}, {"time": 0.2, "x": 32.78, "y": 38.74, "curve": "stepped"}, {"time": 0.3333, "x": 32.78, "y": 38.74, "curve": "stepped"}, {"time": 0.4333, "x": 32.78, "y": 38.74}], "scale": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}], "shear": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}]}, "ts_16": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 7.56, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -1.36, "curve": 0.25, "c3": 0.75}, {"time": 0.4667}], "translate": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.4667}], "scale": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}], "shear": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}]}, "ts_9": {"rotate": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}], "translate": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}], "scale": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}], "shear": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}]}, "ts_18": {"rotate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1, "angle": 4.04, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2, "angle": -9.41, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 1.89, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}], "translate": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}], "scale": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}], "shear": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}]}, "ts_20": {"rotate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1, "angle": 4.04, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2, "angle": -9.41, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 1.89, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}], "translate": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}], "scale": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}], "shear": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}]}, "ts_13": {"rotate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1, "angle": -7.23, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2, "angle": 4.67, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -5.04, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}], "translate": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}], "scale": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}], "shear": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}]}, "ts_6": {"rotate": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}], "translate": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}], "scale": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}], "shear": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}]}, "ts_3": {"rotate": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}], "translate": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}], "scale": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}], "shear": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}]}, "ts_1": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 1.24, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -1.11, "curve": 0.25, "c3": 0.75}, {"time": 0.5}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.5}], "scale": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}], "shear": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}]}, "00": {"rotate": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}], "translate": [{"x": 37.05, "y": -13.39, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 47.07, "y": -23.41}], "scale": [{"x": 0.622, "y": 0.622, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 0.412, "y": 0.412, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 0.422, "y": 0.422, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": 0.412, "y": 0.412}], "shear": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}]}, "ts_21": {"rotate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1, "angle": 4.04, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2, "angle": -9.41, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 1.89, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}], "translate": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}], "scale": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}], "shear": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}]}, "ts_19": {"rotate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1, "angle": 4.04, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2, "angle": -9.41, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 1.89, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}], "translate": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}], "scale": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}], "shear": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}]}, "ts_17": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 7.56, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -1.6, "curve": 0.25, "c3": 0.75}, {"time": 0.5}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.5}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.5}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.5}]}, "ts_12": {"rotate": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}], "translate": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}], "scale": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}], "shear": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}]}, "ts_14": {"rotate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1, "angle": -7.23, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2, "angle": 4.67, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -5.04, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}], "translate": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}], "scale": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}], "shear": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}]}, "ts_11": {"rotate": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}], "translate": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}], "scale": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}], "shear": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}]}, "ts_8": {"rotate": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}], "translate": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}], "scale": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}], "shear": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}]}, "ts_7": {"rotate": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}], "translate": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}], "scale": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}], "shear": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}]}, "ts_5": {"rotate": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}], "translate": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}], "scale": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}], "shear": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}]}, "ts_4": {"rotate": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}], "translate": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}], "scale": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}], "shear": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}]}, "ts_10": {"rotate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1, "angle": 0.95, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2, "angle": 2.64, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -1.6, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}], "translate": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}], "scale": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}], "shear": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}]}, "b0": {"rotate": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}], "translate": [{"y": 45.11, "curve": "stepped"}, {"time": 0.2, "y": 45.11, "curve": "stepped"}, {"time": 0.3333, "y": 45.11, "curve": "stepped"}, {"time": 0.4333, "y": 45.11}], "scale": [{"x": 0.76, "y": 0.76, "curve": "stepped"}, {"time": 0.2, "x": 0.76, "y": 0.76, "curve": "stepped"}, {"time": 0.3333, "x": 0.76, "y": 0.76, "curve": "stepped"}, {"time": 0.4333, "x": 0.76, "y": 0.76}], "shear": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}]}, "ts_15": {"rotate": [{"curve": "stepped"}, {"time": 0.2}], "translate": [{"x": -80.78, "y": -449.22, "curve": 0.182, "c2": 0.69, "c3": 0.259}, {"time": 0.2, "x": 160.73, "y": -639.36}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 0.769, "y": 0.769}], "shear": [{"curve": "stepped"}, {"time": 0.2}]}, "root": {"rotate": [{}], "translate": [{}], "scale": [{}], "shear": [{}]}}}, "scapegoat_move": {"slots": {"b0": {"attachment": [{"name": null}]}, "ts_1": {"attachment": [{"name": "ts_1"}]}, "ts_2": {"attachment": [{"name": "ts_2"}]}, "ts_3": {"attachment": [{"name": "ts_3"}]}, "ts_4": {"attachment": [{"name": null}]}, "ts_5": {"attachment": [{"name": "ts_5"}]}, "ts_6": {"attachment": [{"name": "ts_6"}]}, "ts_7": {"attachment": [{"name": "ts_7"}]}, "ts_8": {"attachment": [{"name": "ts_8"}]}, "ts_9": {"attachment": [{"name": "ts_9"}]}, "ts_10": {"attachment": [{"name": "ts_10"}]}, "ts_11": {"attachment": [{"name": null}]}}, "bones": {"ts_22": {"rotate": [{"curve": "stepped"}, {"time": 0.6}], "translate": [{"x": 32.78, "y": 38.74, "curve": "stepped"}, {"time": 0.6, "x": 32.78, "y": 38.74}], "scale": [{"curve": "stepped"}, {"time": 0.6}], "shear": [{"curve": "stepped"}, {"time": 0.6}]}, "ts_16": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 17.23, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -3.02, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 1.67, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.6}], "translate": [{"curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6}], "scale": [{"curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6}], "shear": [{"curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6}]}, "ts_9": {"rotate": [{"curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6}], "translate": [{"curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6}], "scale": [{"curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6}], "shear": [{"curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6}]}, "ts_18": {"rotate": [{"curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 0.2333, "angle": 9.68, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.3667, "angle": -3.62, "curve": 0.25, "c3": 0.75}, {"time": 0.6}], "translate": [{"curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6}], "scale": [{"curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6}], "shear": [{"curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6}]}, "ts_20": {"rotate": [{"curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 0.2333, "angle": 9.68, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.3667, "angle": -3.62, "curve": 0.25, "c3": 0.75}, {"time": 0.6}], "translate": [{"curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6}], "scale": [{"curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6}], "shear": [{"curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6}]}, "ts_13": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 6.64, "curve": 0.25, "c3": 0.75}, {"time": 0.6}], "translate": [{"curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6}], "scale": [{"curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6}], "shear": [{"curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6}]}, "ts_6": {"rotate": [{"curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6}], "translate": [{"curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6}], "scale": [{"curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6}], "shear": [{"curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6}]}, "ts_3": {"rotate": [{"curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6}], "translate": [{"curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6}], "scale": [{"curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6}], "shear": [{"curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6}]}, "ts_1": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -4.68, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 2.62, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -1.08, "curve": 0.25, "c3": 0.75}, {"time": 0.6}], "translate": [{"curve": "stepped"}, {"time": 0.6}], "scale": [{"curve": "stepped"}, {"time": 0.6}], "shear": [{"curve": "stepped"}, {"time": 0.6}]}, "00": {"rotate": [{"curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6}], "translate": [{"x": 37.05, "y": -13.39}], "scale": [{"x": 0.622, "y": 0.622, "curve": "stepped"}, {"time": 0.3667, "x": 0.622, "y": 0.622, "curve": "stepped"}, {"time": 0.6, "x": 0.622, "y": 0.622}], "shear": [{"curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6}]}, "ts_21": {"rotate": [{"curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 0.2333, "angle": 9.68, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.3667, "angle": -3.62, "curve": 0.25, "c3": 0.75}, {"time": 0.6}], "translate": [{"curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6}], "scale": [{"curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6}], "shear": [{"curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6}]}, "ts_19": {"rotate": [{"curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 0.2333, "angle": 9.68, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.3667, "angle": -3.62, "curve": 0.25, "c3": 0.75}, {"time": 0.6}], "translate": [{"curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6}], "scale": [{"curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6}], "shear": [{"curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6}]}, "ts_17": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 17.23, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -7.51, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 3.06, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.6}], "translate": [{"curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6}], "scale": [{"curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6}], "shear": [{"curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6}]}, "ts_12": {"rotate": [{"curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6}], "translate": [{"curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6}], "scale": [{"curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6}], "shear": [{"curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6}]}, "ts_14": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 6.64, "curve": 0.25, "c3": 0.75}, {"time": 0.6}], "translate": [{"curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6}], "scale": [{"curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6}], "shear": [{"curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6}]}, "ts_11": {"rotate": [{"curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6}], "translate": [{"curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6}], "scale": [{"curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6}], "shear": [{"curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6}]}, "ts_8": {"rotate": [{"curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6}], "translate": [{"curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6}], "scale": [{"curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6}], "shear": [{"curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6}]}, "ts_7": {"rotate": [{"curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6}], "translate": [{"curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6}], "scale": [{"curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6}], "shear": [{"curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6}]}, "ts_5": {"rotate": [{"curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6}], "translate": [{"curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6}], "scale": [{"curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6}], "shear": [{"curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6}]}, "ts_4": {"rotate": [{"curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6}], "translate": [{"curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6}], "scale": [{"curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6}], "shear": [{"curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6}]}, "ts_10": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -3.89, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 2.92, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "curve": "stepped"}, {"time": 0.6}], "translate": [{"curve": "stepped"}, {"time": 0.6}], "scale": [{"curve": "stepped"}, {"time": 0.6}], "shear": [{"curve": "stepped"}, {"time": 0.6}]}, "b0": {"rotate": [{"curve": "stepped"}, {"time": 0.6}], "translate": [{"y": 45.11, "curve": "stepped"}, {"time": 0.6, "y": 45.11}], "scale": [{"x": 0.76, "y": 0.76, "curve": "stepped"}, {"time": 0.6, "x": 0.76, "y": 0.76}], "shear": [{"curve": "stepped"}, {"time": 0.6}]}, "ts_15": {"rotate": [{"curve": "stepped"}, {"time": 0.6}], "translate": [{"x": -80.78, "y": -449.22}], "scale": [{"curve": "stepped"}, {"time": 0.6}], "shear": [{"curve": "stepped"}, {"time": 0.6}]}, "root": {"rotate": [{"curve": "stepped"}, {"time": 0.6}], "translate": [{"curve": "stepped"}, {"time": 0.6}], "scale": [{"curve": "stepped"}, {"time": 0.6}], "shear": [{"curve": "stepped"}, {"time": 0.6}]}}}}}