// @generated by protoc-gen-es v2.2.5 with parameter "target=ts,json_types=true"
// @generated from file pirate/v1/card.proto (package pirate.v1, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage } from "@bufbuild/protobuf/codegenv1";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv1";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file pirate/v1/card.proto.
 */
export const file_pirate_v1_card: GenFile = /*@__PURE__*/
  fileDesc("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");

/**
 * @generated from message pirate.v1.CardBroadcast
 */
export type CardBroadcast = Message<"pirate.v1.CardBroadcast"> & {
  /**
   * 玩家索引
   *
   * @generated from field: int32 player_index = 1;
   */
  playerIndex: number;

  /**
   * 手牌索引
   *
   * @generated from field: int32 hand_card_index = 2;
   */
  handCardIndex: number;

  /**
   * 牌
   *
   * @generated from field: pirate.v1.Card card = 3;
   */
  card: Card;
};

/**
 * @generated from message pirate.v1.CardBroadcast
 */
export type CardBroadcastJson = {
  /**
   * 玩家索引
   *
   * @generated from field: int32 player_index = 1;
   */
  playerIndex?: number;

  /**
   * 手牌索引
   *
   * @generated from field: int32 hand_card_index = 2;
   */
  handCardIndex?: number;

  /**
   * 牌
   *
   * @generated from field: pirate.v1.Card card = 3;
   */
  card?: CardJson;
};

/**
 * Describes the message pirate.v1.CardBroadcast.
 * Use `create(CardBroadcastSchema)` to create a new message.
 */
export const CardBroadcastSchema: GenMessage<CardBroadcast, CardBroadcastJson> = /*@__PURE__*/
  messageDesc(file_pirate_v1_card, 0);

/**
 * @generated from message pirate.v1.CardAfterPeekMessage
 */
export type CardAfterPeekMessage = Message<"pirate.v1.CardAfterPeekMessage"> & {
  /**
   * 玩家索引
   *
   * @generated from field: int32 from_player_index = 1;
   */
  fromPlayerIndex: number;

  /**
   * 牌堆中牌的索引
   *
   * @generated from field: repeated int32 deck_card_indices = 2;
   */
  deckCardIndices: number[];

  /**
   * 牌堆中牌的状态（只有自己有数据）
   *
   * @generated from field: repeated pirate.v1.CardState peeked_card_states = 3;
   */
  peekedCardStates: CardState[];
};

/**
 * @generated from message pirate.v1.CardAfterPeekMessage
 */
export type CardAfterPeekMessageJson = {
  /**
   * 玩家索引
   *
   * @generated from field: int32 from_player_index = 1;
   */
  fromPlayerIndex?: number;

  /**
   * 牌堆中牌的索引
   *
   * @generated from field: repeated int32 deck_card_indices = 2;
   */
  deckCardIndices?: number[];

  /**
   * 牌堆中牌的状态（只有自己有数据）
   *
   * @generated from field: repeated pirate.v1.CardState peeked_card_states = 3;
   */
  peekedCardStates?: CardStateJson[];
};

/**
 * Describes the message pirate.v1.CardAfterPeekMessage.
 * Use `create(CardAfterPeekMessageSchema)` to create a new message.
 */
export const CardAfterPeekMessageSchema: GenMessage<CardAfterPeekMessage, CardAfterPeekMessageJson> = /*@__PURE__*/
  messageDesc(file_pirate_v1_card, 1);

/**
 * @generated from message pirate.v1.CardAfterFreezeBroadcast
 */
export type CardAfterFreezeBroadcast = Message<"pirate.v1.CardAfterFreezeBroadcast"> & {
  /**
   * 玩家索引
   *
   * @generated from field: int32 from_player_index = 1;
   */
  fromPlayerIndex: number;

  /**
   * 被【冻结】的玩家索引
   *
   * @generated from field: int32 target_player_index = 2;
   */
  targetPlayerIndex: number;
};

/**
 * @generated from message pirate.v1.CardAfterFreezeBroadcast
 */
export type CardAfterFreezeBroadcastJson = {
  /**
   * 玩家索引
   *
   * @generated from field: int32 from_player_index = 1;
   */
  fromPlayerIndex?: number;

  /**
   * 被【冻结】的玩家索引
   *
   * @generated from field: int32 target_player_index = 2;
   */
  targetPlayerIndex?: number;
};

/**
 * Describes the message pirate.v1.CardAfterFreezeBroadcast.
 * Use `create(CardAfterFreezeBroadcastSchema)` to create a new message.
 */
export const CardAfterFreezeBroadcastSchema: GenMessage<CardAfterFreezeBroadcast, CardAfterFreezeBroadcastJson> = /*@__PURE__*/
  messageDesc(file_pirate_v1_card, 2);

/**
 * @generated from message pirate.v1.CardAfterStealMessage
 */
export type CardAfterStealMessage = Message<"pirate.v1.CardAfterStealMessage"> & {
  /**
   * 玩家索引
   *
   * @generated from field: int32 from_player_index = 1;
   */
  fromPlayerIndex: number;

  /**
   * 玩家选择的目标索引
   *
   * @generated from field: int32 target_player_index = 2;
   */
  targetPlayerIndex: number;

  /**
   * 玩家选择的目标的手牌索引
   *
   * @generated from field: repeated int32 target_player_hand_card_indices = 3;
   */
  targetPlayerHandCardIndices: number[];

  /**
   * 玩家选择的目标牌（只有自己有数据）
   *
   * @generated from field: repeated pirate.v1.Card target_player_cards = 4;
   */
  targetPlayerCards: Card[];
};

/**
 * @generated from message pirate.v1.CardAfterStealMessage
 */
export type CardAfterStealMessageJson = {
  /**
   * 玩家索引
   *
   * @generated from field: int32 from_player_index = 1;
   */
  fromPlayerIndex?: number;

  /**
   * 玩家选择的目标索引
   *
   * @generated from field: int32 target_player_index = 2;
   */
  targetPlayerIndex?: number;

  /**
   * 玩家选择的目标的手牌索引
   *
   * @generated from field: repeated int32 target_player_hand_card_indices = 3;
   */
  targetPlayerHandCardIndices?: number[];

  /**
   * 玩家选择的目标牌（只有自己有数据）
   *
   * @generated from field: repeated pirate.v1.Card target_player_cards = 4;
   */
  targetPlayerCards?: CardJson[];
};

/**
 * Describes the message pirate.v1.CardAfterStealMessage.
 * Use `create(CardAfterStealMessageSchema)` to create a new message.
 */
export const CardAfterStealMessageSchema: GenMessage<CardAfterStealMessage, CardAfterStealMessageJson> = /*@__PURE__*/
  messageDesc(file_pirate_v1_card, 3);

/**
 * @generated from message pirate.v1.CardReviveBroadcast
 */
export type CardReviveBroadcast = Message<"pirate.v1.CardReviveBroadcast"> & {
  /**
   * 摸牌玩家索引
   *
   * @generated from field: int32 drawn_player_index = 1;
   */
  drawnPlayerIndex: number;

  /**
   * 哪个玩家打出【绷带】
   *
   * @generated from field: int32 revived_player_index = 2;
   */
  revivedPlayerIndex: number;

  /**
   * 打出【绷带】的玩家中，【绷带】的手牌索引
   *
   * @generated from field: int32 revived_player_hand_card_index = 3;
   */
  revivedPlayerHandCardIndex: number;
};

/**
 * @generated from message pirate.v1.CardReviveBroadcast
 */
export type CardReviveBroadcastJson = {
  /**
   * 摸牌玩家索引
   *
   * @generated from field: int32 drawn_player_index = 1;
   */
  drawnPlayerIndex?: number;

  /**
   * 哪个玩家打出【绷带】
   *
   * @generated from field: int32 revived_player_index = 2;
   */
  revivedPlayerIndex?: number;

  /**
   * 打出【绷带】的玩家中，【绷带】的手牌索引
   *
   * @generated from field: int32 revived_player_hand_card_index = 3;
   */
  revivedPlayerHandCardIndex?: number;
};

/**
 * Describes the message pirate.v1.CardReviveBroadcast.
 * Use `create(CardReviveBroadcastSchema)` to create a new message.
 */
export const CardReviveBroadcastSchema: GenMessage<CardReviveBroadcast, CardReviveBroadcastJson> = /*@__PURE__*/
  messageDesc(file_pirate_v1_card, 4);

/**
 * @generated from message pirate.v1.CardActiveDefenseBroadcast
 */
export type CardActiveDefenseBroadcast = Message<"pirate.v1.CardActiveDefenseBroadcast"> & {
  /**
   * 出牌玩家索引
   *
   * @generated from field: int32 posted_player_index = 1;
   */
  postedPlayerIndex: number;

  /**
   * 哪个玩家激活了【防御】了
   *
   * @generated from field: int32 activated_player_index = 2;
   */
  activatedPlayerIndex: number;

  /**
   * 激活了【防御】的牌的索引
   *
   * @generated from field: int32 activated_player_hand_card_index = 3;
   */
  activatedPlayerHandCardIndex: number;
};

/**
 * @generated from message pirate.v1.CardActiveDefenseBroadcast
 */
export type CardActiveDefenseBroadcastJson = {
  /**
   * 出牌玩家索引
   *
   * @generated from field: int32 posted_player_index = 1;
   */
  postedPlayerIndex?: number;

  /**
   * 哪个玩家激活了【防御】了
   *
   * @generated from field: int32 activated_player_index = 2;
   */
  activatedPlayerIndex?: number;

  /**
   * 激活了【防御】的牌的索引
   *
   * @generated from field: int32 activated_player_hand_card_index = 3;
   */
  activatedPlayerHandCardIndex?: number;
};

/**
 * Describes the message pirate.v1.CardActiveDefenseBroadcast.
 * Use `create(CardActiveDefenseBroadcastSchema)` to create a new message.
 */
export const CardActiveDefenseBroadcastSchema: GenMessage<CardActiveDefenseBroadcast, CardActiveDefenseBroadcastJson> = /*@__PURE__*/
  messageDesc(file_pirate_v1_card, 5);

/**
 * @generated from message pirate.v1.CardActiveSubstituteBroadcast
 */
export type CardActiveSubstituteBroadcast = Message<"pirate.v1.CardActiveSubstituteBroadcast"> & {
  /**
   * 出牌玩家索引
   *
   * @generated from field: int32 posted_player_index = 1;
   */
  postedPlayerIndex: number;

  /**
   * 哪个玩家激活了【替身】了
   *
   * @generated from field: int32 activated_player_index = 2;
   */
  activatedPlayerIndex: number;

  /**
   * 是否有可选玩家
   *
   * @generated from field: bool hasSelectablePlayer = 3;
   */
  hasSelectablePlayer: boolean;
};

/**
 * @generated from message pirate.v1.CardActiveSubstituteBroadcast
 */
export type CardActiveSubstituteBroadcastJson = {
  /**
   * 出牌玩家索引
   *
   * @generated from field: int32 posted_player_index = 1;
   */
  postedPlayerIndex?: number;

  /**
   * 哪个玩家激活了【替身】了
   *
   * @generated from field: int32 activated_player_index = 2;
   */
  activatedPlayerIndex?: number;

  /**
   * 是否有可选玩家
   *
   * @generated from field: bool hasSelectablePlayer = 3;
   */
  hasSelectablePlayer?: boolean;
};

/**
 * Describes the message pirate.v1.CardActiveSubstituteBroadcast.
 * Use `create(CardActiveSubstituteBroadcastSchema)` to create a new message.
 */
export const CardActiveSubstituteBroadcastSchema: GenMessage<CardActiveSubstituteBroadcast, CardActiveSubstituteBroadcastJson> = /*@__PURE__*/
  messageDesc(file_pirate_v1_card, 6);

/**
 * @generated from message pirate.v1.CardStrengthenInvalidBroadcast
 */
export type CardStrengthenInvalidBroadcast = Message<"pirate.v1.CardStrengthenInvalidBroadcast"> & {
  /**
   * 出牌玩家索引
   *
   * @generated from field: int32 posted_player_index = 1;
   */
  postedPlayerIndex: number;

  /**
   * 哪个玩家激活了【虚弱】，导致【强化】无效
   *
   * @generated from field: int32 weaken_player_index = 2;
   */
  weakenPlayerIndex: number;
};

/**
 * @generated from message pirate.v1.CardStrengthenInvalidBroadcast
 */
export type CardStrengthenInvalidBroadcastJson = {
  /**
   * 出牌玩家索引
   *
   * @generated from field: int32 posted_player_index = 1;
   */
  postedPlayerIndex?: number;

  /**
   * 哪个玩家激活了【虚弱】，导致【强化】无效
   *
   * @generated from field: int32 weaken_player_index = 2;
   */
  weakenPlayerIndex?: number;
};

/**
 * Describes the message pirate.v1.CardStrengthenInvalidBroadcast.
 * Use `create(CardStrengthenInvalidBroadcastSchema)` to create a new message.
 */
export const CardStrengthenInvalidBroadcastSchema: GenMessage<CardStrengthenInvalidBroadcast, CardStrengthenInvalidBroadcastJson> = /*@__PURE__*/
  messageDesc(file_pirate_v1_card, 7);

/**
 * @generated from message pirate.v1.CardStrengthenSuccessBroadcast
 */
export type CardStrengthenSuccessBroadcast = Message<"pirate.v1.CardStrengthenSuccessBroadcast"> & {
  /**
   * 出牌玩家索引
   *
   * @generated from field: int32 posted_player_index = 1;
   */
  postedPlayerIndex: number;
};

/**
 * @generated from message pirate.v1.CardStrengthenSuccessBroadcast
 */
export type CardStrengthenSuccessBroadcastJson = {
  /**
   * 出牌玩家索引
   *
   * @generated from field: int32 posted_player_index = 1;
   */
  postedPlayerIndex?: number;
};

/**
 * Describes the message pirate.v1.CardStrengthenSuccessBroadcast.
 * Use `create(CardStrengthenSuccessBroadcastSchema)` to create a new message.
 */
export const CardStrengthenSuccessBroadcastSchema: GenMessage<CardStrengthenSuccessBroadcast, CardStrengthenSuccessBroadcastJson> = /*@__PURE__*/
  messageDesc(file_pirate_v1_card, 8);

/**
 * @generated from message pirate.v1.CardAfterSwapMessage
 */
export type CardAfterSwapMessage = Message<"pirate.v1.CardAfterSwapMessage"> & {
  /**
   * 请求玩家
   *
   * @generated from field: int32 from_player_index = 1;
   */
  fromPlayerIndex: number;

  /**
   * 目标玩家
   *
   * @generated from field: int32 target_player_index = 2;
   */
  targetPlayerIndex: number;

  /**
   * 交换双方自己的手牌，仅交互双方有
   *
   * @generated from field: repeated pirate.v1.Card player_hand_cards = 3;
   */
  playerHandCards: Card[];

  /**
   * 交换双方自己的手牌数量，仅交互双方有
   *
   * @generated from field: int32 player_hand_card_count = 4;
   */
  playerHandCardCount: number;
};

/**
 * @generated from message pirate.v1.CardAfterSwapMessage
 */
export type CardAfterSwapMessageJson = {
  /**
   * 请求玩家
   *
   * @generated from field: int32 from_player_index = 1;
   */
  fromPlayerIndex?: number;

  /**
   * 目标玩家
   *
   * @generated from field: int32 target_player_index = 2;
   */
  targetPlayerIndex?: number;

  /**
   * 交换双方自己的手牌，仅交互双方有
   *
   * @generated from field: repeated pirate.v1.Card player_hand_cards = 3;
   */
  playerHandCards?: CardJson[];

  /**
   * 交换双方自己的手牌数量，仅交互双方有
   *
   * @generated from field: int32 player_hand_card_count = 4;
   */
  playerHandCardCount?: number;
};

/**
 * Describes the message pirate.v1.CardAfterSwapMessage.
 * Use `create(CardAfterSwapMessageSchema)` to create a new message.
 */
export const CardAfterSwapMessageSchema: GenMessage<CardAfterSwapMessage, CardAfterSwapMessageJson> = /*@__PURE__*/
  messageDesc(file_pirate_v1_card, 9);

/**
 * @generated from message pirate.v1.UiBroadcast
 */
export type UiBroadcast = Message<"pirate.v1.UiBroadcast"> & {
  /**
   * UI 渲染的动作
   *
   * @generated from field: pirate.v1.UiAction action = 1;
   */
  action: UiAction;

  /**
   * 关系到的牌的索引
   *
   * @generated from field: repeated int32 card_indices = 2;
   */
  cardIndices: number[];

  /**
   * 延迟时间（ms）
   *
   * @generated from field: int32 delay_ms = 3;
   */
  delayMs: number;
};

/**
 * @generated from message pirate.v1.UiBroadcast
 */
export type UiBroadcastJson = {
  /**
   * UI 渲染的动作
   *
   * @generated from field: pirate.v1.UiAction action = 1;
   */
  action?: UiActionJson;

  /**
   * 关系到的牌的索引
   *
   * @generated from field: repeated int32 card_indices = 2;
   */
  cardIndices?: number[];

  /**
   * 延迟时间（ms）
   *
   * @generated from field: int32 delay_ms = 3;
   */
  delayMs?: number;
};

/**
 * Describes the message pirate.v1.UiBroadcast.
 * Use `create(UiBroadcastSchema)` to create a new message.
 */
export const UiBroadcastSchema: GenMessage<UiBroadcast, UiBroadcastJson> = /*@__PURE__*/
  messageDesc(file_pirate_v1_card, 10);

/**
 * @generated from enum pirate.v1.Card
 */
export enum Card {
  /**
   * 占位
   *
   * @generated from enum value: CARD_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * 弱点（死亡）
   *
   * @generated from enum value: CARD_DEATH = 1;
   */
  DEATH = 1,

  /**
   * 复活（绷带）
   *
   * @generated from enum value: CARD_REVIVE = 2;
   */
  REVIVE = 2,

  /**
   * 强化
   *
   * @generated from enum value: CARD_STRENGTHEN = 3;
   */
  STRENGTHEN = 3,

  /**
   * 防御
   *
   * @generated from enum value: CARD_DEFENSE = 4;
   */
  DEFENSE = 4,

  /**
   * 虚弱
   *
   * @generated from enum value: CARD_WEAKEN = 5;
   */
  WEAKEN = 5,

  /**
   * 冻结
   *
   * @generated from enum value: CARD_FREEZE = 6;
   */
  FREEZE = 6,

  /**
   * 透视
   *
   * @generated from enum value: CARD_PEEK = 7;
   */
  PEEK = 7,

  /**
   * 索取
   *
   * @generated from enum value: CARD_STEAL = 8;
   */
  STEAL = 8,

  /**
   * 交换
   *
   * @generated from enum value: CARD_SWAP = 9;
   */
  SWAP = 9,

  /**
   * 替身
   *
   * @generated from enum value: CARD_SUBSTITUTE = 10;
   */
  SUBSTITUTE = 10,

  /**
   * 延时特效牌
   *
   * @generated from enum value: CARD_DELAY_EFFECT = 11;
   */
  DELAY_EFFECT = 11,
}

/**
 * @generated from enum pirate.v1.Card
 */
export type CardJson = "CARD_UNSPECIFIED" | "CARD_DEATH" | "CARD_REVIVE" | "CARD_STRENGTHEN" | "CARD_DEFENSE" | "CARD_WEAKEN" | "CARD_FREEZE" | "CARD_PEEK" | "CARD_STEAL" | "CARD_SWAP" | "CARD_SUBSTITUTE" | "CARD_DELAY_EFFECT";

/**
 * Describes the enum pirate.v1.Card.
 */
export const CardSchema: GenEnum<Card, CardJson> = /*@__PURE__*/
  enumDesc(file_pirate_v1_card, 0);

/**
 * @generated from enum pirate.v1.CardState
 */
export enum CardState {
  /**
   * 占位
   *
   * @generated from enum value: CARD_STATE_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * 未使用
   *
   * @generated from enum value: CARD_STATE_UNUSED = 1;
   */
  UNUSED = 1,

  /**
   * 已使用
   *
   * @generated from enum value: CARD_STATE_USED = 2;
   */
  USED = 2,

  /**
   * 坏牌
   *
   * @generated from enum value: CARD_STATE_BAD = 3;
   */
  BAD = 3,

  /**
   * 好牌
   *
   * @generated from enum value: CARD_STATE_GOOD = 4;
   */
  GOOD = 4,
}

/**
 * @generated from enum pirate.v1.CardState
 */
export type CardStateJson = "CARD_STATE_UNSPECIFIED" | "CARD_STATE_UNUSED" | "CARD_STATE_USED" | "CARD_STATE_BAD" | "CARD_STATE_GOOD";

/**
 * Describes the enum pirate.v1.CardState.
 */
export const CardStateSchema: GenEnum<CardState, CardStateJson> = /*@__PURE__*/
  enumDesc(file_pirate_v1_card, 1);

/**
 * @generated from enum pirate.v1.UiAction
 */
export enum UiAction {
  /**
   * @generated from enum value: UI_ACTION_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * @generated from enum value: UI_ACTION_DRAW = 1;
   */
  DRAW = 1,

  /**
   * @generated from enum value: UI_ACTION_STEAL = 2;
   */
  STEAL = 2,

  /**
   * @generated from enum value: UI_ACTION_PEEK = 3;
   */
  PEEK = 3,
}

/**
 * @generated from enum pirate.v1.UiAction
 */
export type UiActionJson = "UI_ACTION_UNSPECIFIED" | "UI_ACTION_DRAW" | "UI_ACTION_STEAL" | "UI_ACTION_PEEK";

/**
 * Describes the enum pirate.v1.UiAction.
 */
export const UiActionSchema: GenEnum<UiAction, UiActionJson> = /*@__PURE__*/
  enumDesc(file_pirate_v1_card, 2);

