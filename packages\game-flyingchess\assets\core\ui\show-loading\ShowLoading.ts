import {
    _decorator,
    BlockInputEvents,
    Component,
    Label,
    Node,
    Prefab,
    Sprite,
} from 'cc'
import { BaseComponent } from '@/core/manager/gui/base/BaseComponent'
import UILayer from '@/core/manager/gui/layer/UILayer'
import RootUILayer from '@/core/manager/gui/layer/RootUILayer'
const { ccclass, property } = _decorator

export type ShowLoadingProps = {
    /**标题 */
    title?: string
    /**阻挡 */
    mask?: boolean
    /**显示黑色(弹窗存在公共的遮罩，这里不需要重复设置) */
    black?: boolean
}

/**
 * @describe 加载框
 * <AUTHOR>
 * @date 2024-09-12 11:49:51
 */

@ccclass('ShowLoading')
export class ShowLoading extends RootUILayer<ShowLoadingProps> {
    @property({ type: Label, tooltip: '标题' })
    title: Label

    @property({ type: Node, tooltip: '动画' })
    loadingTween: Node

    // @property({ type: Node, tooltip: '遮罩' })
    // black: Node

    private loading_rotate: number = 0

    override onLoad() {
        // this.title.node.active = false
        // this.black.active = false
    }

    override props: ShowLoadingProps = {
        title: '',
        mask: true,
        black: true,
    }

    protected override onAutoObserver(): void {
        this.addAutorun([
            () => {
                if (this.props?.title) {
                    this.title.string = `${this.props?.title}`
                    this.title.node.active = true
                } else {
                    this.title.node.active = false
                }
            },
            () => {
                this.getComponent(BlockInputEvents)!.enabled =
                    !!this.props?.mask
            },
        ])
    }

    override update(deltaTime: number) {
        this.loading_rotate += deltaTime * 220
        this.loadingTween.setRotationFromEuler(0, 0, -this.loading_rotate % 360)
        if (this.loading_rotate > 360) {
            this.loading_rotate -= 360
        }
    }
}
