/**
 * @describe
 * <AUTHOR>
 * @date 2024-09-12 11:35:02
 */

import { log } from 'cc'
import { Manager } from '@/core/manager'
import { LoginAPI } from './login'
import { SuileyooAPI } from './suileyoo'
import { UserAPI } from './user'
import 'core-js/es/object/index.js'

declare module '@/core/manager' {
    interface Manager {
        api: API
    }
}

export class API {
    login: LoginAPI
    user: UserAPI
    suileyoo: SuileyooAPI

    constructor(cat: Manager) {
        window.ccLog('api cos')
        this.login = new LoginAPI(cat)
        window.ccLog('api login')
        this.user = new UserAPI(cat)
        window.ccLog('api user')
        this.suileyoo = new SuileyooAPI(cat)
        window.ccLog('api suileyoo')
    }
}
