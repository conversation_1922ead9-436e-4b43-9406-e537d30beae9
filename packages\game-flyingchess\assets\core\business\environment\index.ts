/**
 * @describe 运行环境类
 * <AUTHOR>
 * @date 2023-08-02 19:41:23
 */

import Trial from './trial'
import Dev from './dev'
import { Platform } from '../platform'
import Prod from './prod'
import { log } from 'cc'
import { cat } from '@/core/manager'

// 扩展 Manager 接口，添加 Platform 属性
declare module '@/core/manager' {
    interface Manager {
        env: RuntineEnv
    }
}

export class Environment {
    protected env: RuntineEnv
    async init() {
        // 获取平台
        const res = cat.platform.getEnvironmentAsync()
        const env: string = res instanceof Promise ? await res : res
        this.env = env === 'release' ? Prod : env === 'trial' ? Trial : Dev
        window.ccLog(
            `%c 获取应用环境: ${env}`,
            'background:#35495e; padding: 1px; border-radius: 3px 3px 3px 3px; color: #fff'
        )
        return this.env
    }
}
