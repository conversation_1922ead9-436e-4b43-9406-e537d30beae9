/**
 * @describe
 * <AUTHOR>
 * @date 2024-09-12 11:35:02
 */

import { CocosAPI } from './Cocos'
import { NativeAPI } from './Native'

const cocosAPI: CocosAPI = new CocosAPI()
const nativeAPI: NativeAPI = new NativeAPI()

export default { nativeAPI, cocosAPI }

//将 NativeAPI 注册为全局类，否则无法在 Java 中被调用
window.PlatformToTS = nativeAPI

// 将 NativeAPI 注册为全局类
declare global {
    interface Window {
        PlatformToTS: NativeAPI
        __DEBUGOUT_INSTENCE__?: any
        ccLog: (...args: any[]) => void
    }
}
