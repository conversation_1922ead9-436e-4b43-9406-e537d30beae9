import { WrapperSocialGameClient } from '@/core/manager/request/websocket'
import {
    PostCardRequestJson,
    PostCardResponse,
    PostStoryRequestJson,
    PostStoryResponse,
    SelectCardRequestJson,
    SelectCardResponse,
    VoteRequestJson,
    VoteResponse,
    BreakHostingRequestJson,
    BreakHostingResponse,
    ScoreboardRequestJson,
    ScoreboardResponse,
    CardInfoListRequestJson,
    CardInfoListResponse,
    DataBroadcastRequestJson,
    DataBroadcastResponse,
    StorybookRequestJson,
    StorybookResponseJson,
    GenStoryRequestJson,
    GenStoryResponse,
} from '@/pb-generate/server/dixit/v1/handler_pb'
import type { Message } from '@bufbuild/protobuf'
import { SelectedCard } from '../store/game'

declare module '@/core/manager/request/websocket' {
    interface WrapperSocialGameClient {
        postCard(
            request: PostCardRequestJson,
            options?: RequestOptions
        ): Promise<PostCardResponse>
        postStory(
            request: PostStoryRequestJ<PERSON>,
            options?: RequestOptions
        ): Promise<PostStoryResponse>
        selectCard(
            request: SelectCardRequestJson,
            options?: RequestOptions
        ): Promise<SelectCardResponse>
        vote(
            request: VoteRequestJson,
            options?: RequestOptions
        ): Promise<VoteResponse>
        breakHosting(
            request: BreakHostingRequestJson,
            options?: RequestOptions
        ): Promise<BreakHostingResponse>
        scoreboard(
            request: ScoreboardRequestJson,
            options?: RequestOptions
        ): Promise<ScoreboardResponse>
        cardInfoList(
            request: CardInfoListRequestJson,
            options?: RequestOptions
        ): Promise<CardInfoListResponse>
        dataBroadcast(
            request: DataBroadcastRequestJson,
            options?: RequestOptions
        ): Promise<DataBroadcastResponse>
        storybook(request: StorybookRequestJson): Promise<StorybookResponseJson>
        genStory(
            request: GenStoryRequestJson,
            options?: RequestOptions
        ): Promise<GenStoryResponse>
    }
}
