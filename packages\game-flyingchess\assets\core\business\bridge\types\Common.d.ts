/**
* @describe 
* <AUTHOR>
* @date 2024-09-12 11:35:02
*/

declare namespace IBridge {

    type GameEnv = 'dev' | 'qa' | 'pro' | 'pre'

    /**有哦 0，随乐游 1 */
    type AppType = 0 | 1

    /**资源加载完成响应 */
    type ResCompleteRes = {
        gameId: number,
        gameEnv: GameEnv,
        /**0: 未开始 1: 待开始或已开始 */
        gameState: 0 | 1,
        appType: AppType,
        voiceState: VoiceState,
        giftState?: GiftState,
        musicState: MusicState
        micState: MicState
        bannerSpecial: BannerReq
        invitePlay: boolean
    }

    /** 0 钻石充值 1 云贝充值 */
    type AssetsType = 0 | 1

    /**横幅特效开关 0：不屏蔽 1：屏蔽*/
    type BannerReq = 0 | 1

    /**音效外放状态 0 关闭 1开启*/
    type MusicState = 0 | 1

    /**礼物按钮状态 0 关闭 1开启*/
    type GiftState = 0 | 1

    /**音频状态 0 关闭 1开启*/
    type VoiceState = 0 | 1

    /**麦克风状态 0 关闭 1开启*/
    type MicState = 0 | 1

    /**键盘模式 0不自适应(默认) 1自适应*/
    type InputMode = 0 | 1


    /**声波 */
    type SoundWaveState = {
        /**0 关闭 1开启 */
        state: number,
        userId: number
    }

    type ClickEventValue = '添加好友' | '关注' | '有哦私聊' | '随乐游私聊'

    type Category = '结算页'

    /**事件埋点 */
    type ClickEvent = { category: Category, label: string, value: ClickEventValue }

    /**分享参数 */
    type ShareOption = {
        type: 0,
        path: string,
        userId: number,
        userName: string,
        userAvatar: string,
        praiseCount: number
    } | {
        type: 1,
    }
}
