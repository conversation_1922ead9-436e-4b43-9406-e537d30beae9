{"extends": "./temp/tsconfig.cocos.json", "compilerOptions": {"module": "ESNext", "target": "ESNext", "types": ["minigame-api-typings"], "lib": ["ESNext", "dom"], "allowSyntheticDefaultImports": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "useDefineForClassFields": true, "strictNullChecks": true, "strict": true, "strictPropertyInitialization": false, "noImplicitOverride": true, "moduleResolution": "node", "experimentalDecorators": true, "baseUrl": "./", "paths": {"@/*": ["assets/*"]}}}