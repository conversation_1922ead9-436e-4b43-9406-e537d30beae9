# Cocos 游戏开发技术分享 - 只言片语(Dixit)游戏解析

## 分享概览

-   **主题**: Cocos Creator 游戏开发基础与实战
-   **时长**: 60 分钟
-   **受众**: 非游戏开发人员、产品经理、测试人员
-   **目标**: 了解 Cocos 游戏开发基础概念和只言片语游戏的技术实现

---

## 1. 游戏简介与演示 (5 分钟)

### 1.1 只言片语游戏介绍

-   **游戏类型**: 多人社交卡牌游戏
-   **核心玩法**:
    1. 说书人选择卡牌并讲故事
    2. 其他玩家根据故事选择相似卡牌
    3. 所有玩家投票猜测说书人的卡牌
    4. 根据投票结果计分

### 1.2 游戏流程演示

-   **启动游戏**: 展示加载过程
-   **游戏界面**: 主界面、手牌、投票等
-   **核心功能**: 出牌、讲故事、投票、计分

---

## 2. Cocos 开发基础知识 (10 分钟)

### 2.1 什么是 Cocos Creator？

-   **定义**: 跨平台 2D/3D 游戏开发引擎
-   **类比前端**:
    -   Cocos Creator ≈ React/Vue 框架
    -   场景(Scene) ≈ 页面(Page)
    -   节点(Node) ≈ DOM 元素
    -   组件(Component) ≈ 组件化开发

### 2.2 核心概念对比

| Cocos 概念      | 前端概念    | 说明               |
| --------------- | ----------- | ------------------ |
| Scene(场景)     | Page(页面)  | 游戏的不同界面     |
| Node(节点)      | DOM Element | 游戏对象的基本单位 |
| Component(组件) | Component   | 功能模块化         |
| Prefab(预制体)  | Template    | 可复用的模板       |
| Script(脚本)    | JavaScript  | 业务逻辑代码       |

### 2.3 开发语言

-   **TypeScript**: 主要开发语言，类型安全
-   **装饰器模式**: `@ccclass`, `@property` 等
-   **生命周期**: `onLoad`, `start`, `update`, `onDestroy`

---

## 3. 工程结构解析 (10 分钟)

### 3.1 Monorepo 项目整体结构

我们的项目采用 **Monorepo** 架构，将多个游戏项目统一管理：

```
bomb-duck/                  # 项目根目录
├── packages/               # 游戏包目录
│   ├── game-dixit/         # 只言片语游戏
│   ├── game-flyingchess/   # 飞行棋游戏
│   ├── game-pirate/        # 海盗桶游戏
│   ├── game-shark/         # 小心鲨手游戏
│   ├── shared/             # 共享资源包
│   ├── cli/                # 命令行工具
│   ├── pitayaclient/       # Pitaya 客户端库
│   ├── sgc/                # SGC 通信库
│   └── mobx-tsuki/         # MobX 状态管理库
├── scripts/                # 构建脚本
├── .gitlab-ci.yml          # CI/CD 配置
├── package.json            # 根项目配置
└── yarn.lock               # 依赖锁定文件
```

#### Monorepo 架构优势

-   **代码复用**: 核心框架代码在各游戏间共享
-   **统一管理**: 依赖版本、构建流程、CI/CD 统一配置
-   **独立部署**: 每个游戏可以独立构建和部署
-   **版本控制**: 支持统一版本或独立版本管理

#### 核心包说明

**游戏项目**:

-   **game-dixit**: 只言片语 - 多人社交卡牌游戏
-   **game-flyingchess**: 飞行棋 - 经典棋类游戏
-   **game-pirate**: 海盗游戏 - 3D 冒险游戏
-   **game-shark**: 鲨鱼游戏 - 3D 互动游戏

**基础设施**:

-   **shared**: 共享资源包 - 通用组件和资源
-   **cli**: 命令行工具 - 项目脚手架和管理工具
-   **pitayaclient**: Pitaya 客户端库 - 游戏服务器通信
-   **sgc**: SGC 通信库 - 网络通信协议
-   **mobx-tsuki**: MobX 状态管理库 - 响应式状态管理

#### 相关文档

-   📖 [开发环境配置](docs/开发环境配置.md) - 详细的开发环境搭建指南
-   📖 [版本管理快速开始](docs/版本管理快速开始.md) - 版本发布和管理流程

### 3.2 只言片语游戏项目架构

```
packages/game-dixit/                    # 只言片语游戏项目根目录
├── assets/                             # 游戏资源目录
│   ├── core/                           # │ 核心框架代码
│   │   ├── business/                   # │ ├─ 业务逻辑层
│   │   ├── components/                 # │ ├─ 通用组件库
│   │   ├── manager/                    # │ ├─ 管理器模块
│   │   └── ui/                         # │ └─ UI 基础组件
│   ├── script/                         # │ 游戏逻辑脚本
│   │   └── scene/                      # │ └─ 场景脚本目录
│   ├── pb-generate/                    # │ 协议生成文件
│   │   ├── client/                     # │ ├─ 客户端协议
│   │   ├── server/                     # │ ├─ 服务端协议
│   │   └── suileyoo/                   # │ └─ 第三方协议
│   ├── res/                            # │ 静态资源文件
│   │   ├── plist/                      # │ ├─ 图集文件
│   │   ├── shader/                     # │ ├─ 着色器文件
│   │   ├── spine/                      # │ ├─ Spine 动画
│   │   └── texture/                    # │ └─ 纹理图片
│   ├── resources/                      # │ 动态加载资源
│   │   ├── audio/                      # │ ├─ 音频文件
│   │   └── prefabs/                    # │ └─ 预制体文件
│   └── scene/                          # │ 场景文件目录
│       ├── start.scene                 # │ ├─ 启动场景
│       ├── loading.scene               # │ ├─ 加载场景
│       └── game.scene                  # │ └─ 游戏主场景
├── build/                              # 构建输出目录
│   ├── web-mobile/                     # ├─ Web 移动端构建
│   └── wechatgame/                     # └─ 微信小游戏构建
├── build-templates/                    # 构建模板目录
│   ├── common/                         # ├─ 通用构建模板
│   └── web-mobile/                     # └─ Web 移动端模板
├── client-proto/                       # 客户端协议定义
│   └── game/                           # └─ 游戏协议文件
├── settings/                           # 项目配置目录
├── profiles/                           # 编辑器配置目录
├── library/                            # 编译缓存目录
├── temp/                               # 临时文件目录
├── docs/                               # 项目文档目录
├── buf-*.gen.yaml                      # Buf 协议生成配置
├── buildConfig_*.json                  # 构建配置文件
├── import-map.json                     # 导入映射配置
├── package.json                        # 项目依赖配置
└── tsconfig.json                       # TypeScript 配置
```

### 3.3 核心模块说明

#### 3.3.1 Core 模块 - 框架层

```
core/                                   # 核心框架代码目录
├── manager/                            # │ 管理器模块
│   ├── gui/                            # │ ├─ UI 界面管理
│   │   ├── UIManager.ts                # │ │  ├─ UI 管理器主类
│   │   ├── DialogManager.ts            # │ │  ├─ 弹窗管理器
│   │   └── LayerManager.ts             # │ │  └─ 层级管理器
│   ├── audio/                          # │ ├─ 音频系统管理
│   │   ├── AudioManager.ts             # │ │  ├─ 音频管理器
│   │   └── SoundPool.ts                # │ │  └─ 音效池管理
│   ├── event/                          # │ ├─ 事件系统管理
│   │   ├── EventManager.ts             # │ │  ├─ 事件管理器
│   │   └── GameEventConstant.ts        # │ │  └─ 游戏事件常量
│   ├── request/                        # │ ├─ 网络请求管理
│   │   ├── RequestManager.ts           # │ │  ├─ 请求管理器
│   │   ├── WebSocketManager.ts         # │ │  ├─ WebSocket 管理
│   │   └── ProtocolManager.ts          # │ │  └─ 协议管理器
│   ├── resource/                       # │ ├─ 资源管理
│   │   ├── ResourceManager.ts          # │ │  ├─ 资源管理器
│   │   └── AssetLoader.ts              # │ │  └─ 资源加载器
│   └── scene/                          # │ └─ 场景管理
│       ├── SceneManager.ts             # │    ├─ 场景管理器
│       └── LoadingManager.ts           # │    └─ 加载管理器
├── business/                           # │ 业务逻辑模块
│   ├── store/                          # │ ├─ 状态管理
│   │   ├── GameStore.ts                # │ │  ├─ 游戏状态存储
│   │   ├── PlayerStore.ts              # │ │  ├─ 玩家状态存储
│   │   └── UIStore.ts                  # │ │  └─ UI 状态存储
│   ├── hooks/                          # │ ├─ 业务钩子
│   │   ├── useGameState.ts             # │ │  ├─ 游戏状态钩子
│   │   ├── usePlayer.ts                # │ │  ├─ 玩家钩子
│   │   └── useNetwork.ts               # │ │  └─ 网络钩子
│   ├── util/                           # │ ├─ 工具函数
│   │   ├── GameUtils.ts                # │ │  ├─ 游戏工具类
│   │   ├── MathUtils.ts                # │ │  ├─ 数学工具类
│   │   └── TimeUtils.ts                # │ │  └─ 时间工具类
│   └── config/                         # │ └─ 配置管理
│       ├── GameConfig.ts               # │    ├─ 游戏配置
│       └── Constants.ts                # │    └─ 常量定义
├── components/                         # │ 通用组件库
│   ├── base/                           # │ ├─ 基础组件
│   │   ├── BaseComponent.ts            # │ │  ├─ 组件基类
│   │   ├── UILayer.ts                  # │ │  ├─ UI 层基类
│   │   └── SceneLayer.ts               # │ │  └─ 场景层基类
│   ├── dialog/                         # │ ├─ 弹窗组件
│   │   ├── BaseDialog.ts               # │ │  ├─ 弹窗基类
│   │   ├── ConfirmDialog.ts            # │ │  ├─ 确认弹窗
│   │   └── LoadingDialog.ts            # │ │  └─ 加载弹窗
│   ├── animation/                      # │ ├─ 动画组件
│   │   ├── TweenHelper.ts              # │ │  ├─ 缓动动画助手
│   │   └── SpineHelper.ts              # │ │  └─ Spine 动画助手
│   └── input/                          # │ └─ 输入组件
│       ├── TouchHandler.ts             # │    ├─ 触摸处理器
│       └── KeyboardHandler.ts          # │    └─ 键盘处理器
└── ui/                                 # │ UI 基础组件
    ├── widgets/                        # │ ├─ UI 控件
    │   ├── Button.ts                   # │ │  ├─ 按钮组件
    │   ├── Label.ts                    # │ │  ├─ 文本组件
    │   └── ProgressBar.ts              # │ │  └─ 进度条组件
    ├── layout/                         # │ ├─ 布局组件
    │   ├── GridLayout.ts               # │ │  ├─ 网格布局
    │   └── FlexLayout.ts               # │ │  └─ 弹性布局
    └── effects/                        # │ └─ 特效组件
        ├── ParticleEffect.ts           # │    ├─ 粒子特效
        └── ShaderEffect.ts             # │    └─ 着色器特效
```

#### 3.3.2 Script 模块 - 游戏逻辑层

```
script/scene/
├── start-scene/           # 启动场景
├── loading/               # 加载场景
└── game/                  # 游戏主场景
    ├── ui/                # UI界面
    ├── components/        # 游戏组件
    └── Game.ts            # 游戏主控制器
```

#### 3.3.3 PB-Generate - 协议层

-   **作用**: 自动生成的网络协议文件
-   **技术**: Protocol Buffers (protobuf)
-   **包含**: 游戏状态、玩家信息、消息定义

---

## 4. 场景与节点系统 (8 分钟)

### 4.1 场景管理

-   **场景切换**: `cat.gui.loadScene('game')`
-   **场景生命周期**: 加载 → 初始化 → 运行 → 销毁
-   **常驻节点**: 跨场景保持的 UI 元素

### 4.2 节点层级结构

```
Game Scene
├── UI Layer (UI层)
│   ├── PlayerItems (玩家信息)
│   ├── HandCards (手牌区域)
│   └── Dialogs (弹窗)
├── Game Layer (游戏层)
│   ├── CardItems (卡牌)
│   └── Animations (动画)
└── Background (背景层)
```

### 4.3 组件系统

-   **BaseComponent**: 所有组件的基类
-   **UILayer**: UI 界面基类
-   **SceneLayer**: 场景基类
-   **生命周期管理**: 自动化的初始化和销毁

---

## 5. 消息机制与网络通信 (10 分钟)

### 5.1 Protocol Buffers (PB)

-   **定义**: Google 开发的数据序列化协议
-   **优势**: 高效、跨语言、版本兼容
-   **生成**: 自动从.proto 文件生成 TypeScript 代码

### 5.2 游戏状态管理

```typescript
// 游戏状态枚举
enum State {
    GAME_START = 1, // 游戏开始
    INIT_THEME = 11, // 确定主题
    PLAYER_POST = 14, // 说书人出牌
    PLAYER_TELLING = 16, // 说书人讲故事
    OTHER_PLAYER_SELECT = 18, // 其他玩家选牌
    PLAYER_VOTE = 20, // 玩家投票
    PLAYER_VOTE_COUNT = 21, // 计票阶段
}
```

### 5.3 WebSocket 长连接

-   **连接建立**: 游戏启动时自动连接
-   **消息收发**: 双向实时通信
-   **断线重连**: 自动重连机制
-   **消息队列**: 确保消息顺序

### 5.4 事件驱动架构

```typescript
// 监听游戏状态变化
cat.event.on('MT_GAME_INFO_BROADCAST', (data) => {
    // 根据游戏状态更新UI
    this.updateGameState(data.state)
})
```

---

## 6. 预制体与弹窗系统 (7 分钟)

### 6.1 预制体(Prefab)概念

-   **定义**: 预先制作的游戏对象模板
-   **类比**: 类似前端的组件模板
-   **优势**: 复用性强、统一管理、动态加载

### 6.2 弹窗系统架构

```typescript
// 弹窗基类
class CommonDialog extends UILayer {
    // 自动挂载托管组件
    // 统一的显示/隐藏动画
    // 事件监听管理
}
```

### 6.3 弹窗生命周期

1. **创建**: `instantiate(prefab)` - 实例化预制体
2. **显示**: `cat.gui.openUI()` - 打开 UI 界面
3. **交互**: 用户操作和数据更新
4. **关闭**: `cat.gui.closeUI()` - 关闭并销毁

### 6.4 常见弹窗类型

-   **HandCardDialog**: 手牌选择弹窗
-   **VoteDialog**: 投票界面
-   **StorytellingDialog**: 讲故事界面
-   **ScoreBoardDialog**: 计分板
-   **CardPreviewDialog**: 卡牌预览

---

## 7. 代码演示环节 (5 分钟)

### 7.1 演示内容

1. **场景切换**: 展示场景加载过程
2. **弹窗创建**: 实时创建和销毁弹窗
3. **消息收发**: 展示网络消息的收发
4. **状态同步**: 展示游戏状态的实时同步

### 7.2 关键代码片段

```typescript
// 显示手牌弹窗
showHandCardDialog(type: HandCardDialogShowType) {
    const prefab = cat.res.get<Prefab>('prefabs/scene/game/ui/HandCardDialog')
    const node = instantiate(prefab)
    cat.gui.openUI<HandCardDialog>(node, {
        props: { showType: type }
    })
}
```

---

## 8. AI 在游戏客户端开发中的应用 (8 分钟)

### 8.1 Augment VSCode 插件介绍

-   **定义**: AI 驱动的代码助手，专为开发者设计
-   **核心能力**: 代码生成、重构、调试、文档编写
-   **优势**: 理解项目上下文，提供精准的代码建议

### 8.2 在 Cocos 游戏开发中的实际应用

#### 8.2.1 业务代码生成与补全

#### 8.2.2 文档的生成

-   **API 文档**: 自动生成组件和方法的文档
-   **代码注释**: 智能添加有意义的代码注释
-   **架构说明**: 生成项目架构和模块关系图

#### 8.2.3 游戏逻辑重构

#### 8.2.4 工程化脚本和配置文件编写：如 .gitlab-ci.yml、scripts/ 下的脚本、 packages/cli 下的命令行工具

### 8.3 实际效果统计

-   **开发效率**: 提升约 40-60%的编码效率
-   **代码质量**: 减少常见错误，提高代码可维护性
-   **学习成本**: 降低新技术和复杂项目的学习门槛
-   **创新能力**: 更多时间专注于游戏玩法和用户体验

---

## 9. QA 环节 (5 分钟)

### 常见问题预设

1. **Q**: Cocos 和前端开发的主要区别是什么？
   **A**: 主要在于渲染方式、性能优化和游戏特有的概念如物理引擎、动画系统等

2. **Q**: 游戏的网络同步如何保证一致性？
   **A**: 通过服务器权威、状态同步、消息确认等机制

3. **Q**: 游戏性能如何优化？
   **A**: 资源管理、对象池、渲染优化、内存管理等

---

## 总结

通过本次分享，我们了解了：

-   Cocos Creator 的基础概念和开发模式
-   只言片语游戏的技术架构和实现方式
-   游戏开发中的关键技术点：场景管理、消息机制、UI 系统等
-   游戏开发与前端开发的异同点
-   AI 工具在现代游戏开发中的重要作用和实际应用价值

希望这次分享能帮助大家更好地理解 cocos 游戏开发的技术实现，以及 AI 如何助力提升开发效率！
