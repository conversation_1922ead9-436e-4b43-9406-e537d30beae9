import store from "../../store"
import { BaseTracking } from "../BaseTracking"



/**网络事件属性 */
export enum TrackingNetworkEvent {
    /**断线重连成功：掉线重连回来时上报 */
    NETWORK_CONNECT = 'network_connect',
    /**客户端每隔30s发送一次数据，统计用户时长 */
    DURATION_CHECK = 'duration_check'
}



export class TrackingNetwork extends BaseTracking {
    networkConnect = () => {
        this.tracking.upload({
            event_type: TrackingNetworkEvent.NETWORK_CONNECT,
            value: { type: store.user.isAudience ? "player" : "viewer" }
        })
    }
}