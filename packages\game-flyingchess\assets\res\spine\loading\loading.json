{"skeleton": {"hash": "YSdntAC0sT5vRFY9mFW/cjB8Jro", "spine": "3.8.85", "x": -411.03, "y": 33.41, "width": 885.56, "height": 438.01, "images": "./images/", "audio": "E:/XM/商业策划部/海盗桶"}, "bones": [{"name": "root"}, {"name": "chuan", "parent": "root"}, {"name": "chuann2", "parent": "chuan", "length": 159.49, "x": -41.12, "y": 88.9, "color": "72ff00ff"}, {"name": "chuann17", "parent": "chuann2", "x": 0.04, "y": -0.27, "color": "72ff00ff"}, {"name": "chuann25", "parent": "chuann17", "color": "72ff00ff"}, {"name": "chuann1", "parent": "chuann25", "length": 245.27, "rotation": 81, "x": -0.04, "y": 0.27, "color": "ff00efff"}, {"name": "chuann1a", "parent": "chuann1", "length": 297.43, "rotation": -4.35, "x": 93.42, "y": 18.85, "color": "b300ffff"}, {"name": "chuann3", "parent": "chuann1a", "rotation": 4.35, "x": 252.34, "y": -39.98, "color": "00fc03ff"}, {"name": "chuann_xian2", "parent": "chuann1a", "rotation": 4.35, "x": 248.16, "y": 54.25, "color": "00fc03ff"}, {"name": "chuann_xian3", "parent": "chuann1a", "rotation": 4.35, "x": 177.45, "y": -101.24, "color": "000effff"}, {"name": "chuann4", "parent": "chuann1a", "rotation": 4.35, "x": 171.82, "y": 117.5, "color": "000effff"}, {"name": "chuann_xian5", "parent": "chuann1a", "rotation": 4.35, "x": 33.12, "y": 104.44, "color": "000effff"}, {"name": "chuann_xian4", "parent": "chuann1a", "rotation": 4.35, "x": 35.94, "y": -95.13, "color": "000effff"}, {"name": "chuann6", "parent": "chuann1", "length": 193.75, "rotation": -9.34, "x": 44.01, "y": -74.26, "color": "00ffe7ff"}, {"name": "chuann1a2", "parent": "chuann1a", "x": 266.84, "y": 31.81, "color": "000effff"}, {"name": "chuann1a3", "parent": "chuann1a", "x": 270.37, "y": -23.88, "color": "000effff"}, {"name": "chuann7", "parent": "chuann6", "x": 161.01, "y": -71.33, "color": "00ff3aff"}, {"name": "chuann8", "parent": "chuann6", "x": 138.31, "y": 89.57, "color": "00ff3aff"}, {"name": "chuann9", "parent": "chuann6", "x": 68.57, "y": -69.58, "color": "00ff3aff"}, {"name": "chuann10", "parent": "chuann6", "x": 58.57, "y": 79.63, "color": "00ff3aff"}, {"name": "chuann5", "parent": "chuann1", "x": 49.55, "y": -39.62, "color": "ffae00ff"}, {"name": "chuann11", "parent": "chuann1", "x": 38.17, "y": 207.47, "color": "ffae00ff"}, {"name": "chuann1a18", "parent": "chuann1a", "x": 118.13, "y": 37.24, "color": "b300ffff"}, {"name": "chuann1a4", "parent": "chuann1a18", "x": 25.57, "y": 84.25, "color": "fbc000ff"}, {"name": "chuann1a5", "parent": "chuann1a18", "x": -10.64, "y": 84.81, "color": "fbc000ff"}, {"name": "chuann1a6", "parent": "chuann1a18", "x": -54.58, "y": 77.08, "color": "fbc000ff"}, {"name": "chuann1a7", "parent": "chuann1a18", "x": 31.42, "y": -121.97, "color": "fbc000ff"}, {"name": "chuann1a8", "parent": "chuann1a18", "x": -3.87, "y": -118.49, "color": "fbc000ff"}, {"name": "chuann1a9", "parent": "chuann1a18", "x": -42.54, "y": -121.21, "color": "fbc000ff"}, {"name": "chuann1a10", "parent": "chuann1a18", "x": -1.31, "y": -11.25, "color": "fbc000ff"}, {"name": "chuann1a11", "parent": "chuann1a18", "x": 64.46, "y": 36.13, "color": "b300ffff"}, {"name": "chuann1a12", "parent": "chuann1a18", "x": 66.7, "y": -20.97, "color": "b300ffff"}, {"name": "chuann1a13", "parent": "chuann1a18", "x": 65.34, "y": -81.07, "color": "b300ffff"}, {"name": "chuann1a14", "parent": "chuann1a18", "x": -68.38, "y": 24, "color": "b300ffff"}, {"name": "chuann1a15", "parent": "chuann1a18", "x": -69.31, "y": -22, "color": "b300ffff"}, {"name": "chuann1a16", "parent": "chuann1a18", "x": -73.31, "y": -80.04, "color": "b300ffff"}, {"name": "chuann1a17", "parent": "chuann1a", "x": 278.48, "y": 9.25, "color": "ff0000ff"}, {"name": "chuann12", "parent": "chuann1", "x": 173.77, "y": 342.09, "color": "ff00efff"}, {"name": "chuann13", "parent": "chuann1", "x": 130.54, "y": 218.91, "color": "ff00efff"}, {"name": "chuann15", "parent": "chuann1", "x": 276.36, "y": 95.87, "color": "00ff1bff"}, {"name": "chuann14", "parent": "chuann1", "x": 212.94, "y": 200.88, "color": "00ff1bff"}, {"name": "chuann1a20", "parent": "chuann1a", "x": 221.77, "y": 57.27, "color": "00fc03ff"}, {"name": "chuann1a22", "parent": "chuann1a", "x": 195.75, "y": 49.96, "color": "00fc03ff"}, {"name": "chuann1a24", "parent": "chuann1a", "x": 253.87, "y": 20.33, "color": "00fc03ff"}, {"name": "chuann1a27", "parent": "chuann1a", "x": 227.62, "y": -25.82, "color": "00fc03ff"}, {"name": "chuann1a29", "parent": "chuann1a", "x": 197.9, "y": -30.28, "color": "00fc03ff"}, {"name": "chuann1a31", "parent": "chuann1a", "x": 204.48, "y": 18.08, "color": "00fc03ff"}, {"name": "chuann16", "parent": "chuann6", "x": 148.36, "y": 43.42, "color": "00ffe7ff"}, {"name": "chuann18", "parent": "chuann6", "x": 159.2, "y": -20.7, "color": "00ffe7ff"}, {"name": "chuann19", "parent": "chuann6", "x": 112.23, "y": 93.51, "color": "00ffe7ff"}, {"name": "chuann20", "parent": "chuann6", "x": 80.82, "y": 91.35, "color": "00ffe7ff"}, {"name": "chuann21", "parent": "chuann6", "x": 62.5, "y": 40.57, "color": "00ffe7ff"}, {"name": "chuann22", "parent": "chuann6", "x": 69.57, "y": -12.21, "color": "00ffe7ff"}, {"name": "chuann23", "parent": "chuann6", "x": 133.33, "y": -63.13, "color": "00ffe7ff"}, {"name": "chuann24", "parent": "chuann6", "x": 99.32, "y": -62.68, "color": "00ffe7ff"}, {"name": "tong2", "parent": "chuan", "length": 90.94, "x": 300.88, "y": 89.56, "scaleX": 0.7867, "scaleY": 0.7867}, {"name": "tong", "parent": "tong2", "length": 52.58, "rotation": 65.82}, {"name": "js_2", "parent": "tong", "length": 81.64, "rotation": -3.99, "x": 43.83, "y": 0.85}, {"name": "js_2b", "parent": "js_2", "x": 23.35, "y": 0.62}, {"name": "tong3", "parent": "tong", "x": 1.69, "y": 5.61}, {"name": "tong4", "parent": "tong", "x": -48.41, "y": 0.46}, {"name": "tx/d0", "parent": "chuan", "length": 539.01, "rotation": 0.91, "x": -238.09, "y": 45.06, "scaleY": 1.0435, "color": "ff0093ff"}, {"name": "tx/d1", "parent": "chuan", "length": 539.01, "rotation": 19.62, "x": -253.12, "y": 49.24, "scaleX": 0.5007, "scaleY": 1.2057, "color": "ff0093ff"}, {"name": "tx/d2", "parent": "chuan", "length": 539.01, "rotation": -0.57, "x": 19.68, "y": 59.71, "scaleX": 0.6524, "scaleY": 0.9132, "color": "ff0093ff"}, {"name": "tx/d3", "parent": "chuan", "length": 539.01, "rotation": 0.91, "x": 211.7, "y": 45.46, "scaleX": 0.4763, "scaleY": 0.6766, "color": "ff0093ff"}, {"name": "js_3", "parent": "js_2", "x": 57.41, "y": 0.01}], "slots": [{"name": "tx/d1", "bone": "tx/d1", "attachment": "tx/d9"}, {"name": "tx/d2", "bone": "tx/d2", "attachment": "tx/d9"}, {"name": "js_2", "bone": "js_2", "attachment": "js_2"}, {"name": "tong", "bone": "tong", "attachment": "tong"}, {"name": "chuann2", "bone": "chuann6", "attachment": "chuann2"}, {"name": "chuann_fan3", "bone": "chuann6", "attachment": "chuann_fan3"}, {"name": "chuann1a", "bone": "chuann1a", "attachment": "chuann1a"}, {"name": "chuann_fan2", "bone": "chuann1a", "attachment": "chuann_fan2"}, {"name": "chuann_fan1", "bone": "chuann1a", "attachment": "chuann_fan1"}, {"name": "chuann_xian5", "bone": "chuann_xian5", "attachment": "chuann_xian5"}, {"name": "chuann1b", "bone": "chuann1", "attachment": "chuann1b"}, {"name": "chuann_xian4", "bone": "chuann_xian4", "attachment": "chuann_xian4"}, {"name": "chuann_xian3", "bone": "chuann_xian3", "attachment": "chuann_xian3"}, {"name": "chuann_xian2", "bone": "chuann_xian2", "attachment": "chuann_xian2"}, {"name": "chuann_xian1", "bone": "chuan", "attachment": "chuann_xian1"}, {"name": "js_2b", "bone": "js_2b", "attachment": "js_2b"}, {"name": "js_2a", "bone": "js_2", "attachment": "js_2a"}, {"name": "tx/d3", "bone": "tx/d3", "attachment": "tx/d9"}, {"name": "tx/d0", "bone": "tx/d0", "attachment": "tx/d9"}, {"name": "h1", "bone": "js_3", "attachment": "h1"}, {"name": "h2", "bone": "js_3", "attachment": "h2"}, {"name": "h3", "bone": "js_3", "attachment": "h3"}, {"name": "h4", "bone": "js_3", "attachment": "h4"}, {"name": "h5", "bone": "js_3", "attachment": "h5"}], "skins": [{"name": "default", "attachments": {"chuann1a": {"chuann1a": {"type": "mesh", "uvs": [0.62762, 0, 0.77131, 0.10202, 0.86667, 0.36785, 1, 0.46574, 1, 0.52745, 0.56557, 0.44171, 0.48378, 0.81031, 0.52046, 0.85124, 0.84833, 0.93808, 0.88966, 0.99982, 0.40119, 0.97907, 0.00243, 0.79433, 0.00056, 0.73423, 0.26789, 0.79814, 0.40748, 0.82823, 0.49253, 0.42841, 0.35399, 0.39521, 0.11372, 0.36842, 0.02014, 0.32781, 0.48938, 0], "triangles": [17, 18, 19, 16, 17, 19, 15, 19, 0, 5, 15, 0, 1, 5, 0, 16, 19, 15, 2, 5, 1, 2, 3, 4, 5, 2, 4, 13, 11, 12, 6, 15, 5, 6, 14, 15, 10, 13, 14, 11, 13, 10, 10, 7, 8, 14, 6, 7, 10, 14, 7, 9, 10, 8], "vertices": [295.49, -0.87, 276.43, -49.99, 207.29, -96, 188.68, -141.8, 171.09, -145.97, 165.24, -12.52, 54.45, -13.42, 45.35, -26.97, 43.45, -129.18, 28.73, -145.5, 0.58, -0.57, 25.45, 129.1, 42.45, 133.72, 42.87, 50.84, 44.03, 7.79, 163.94, 9.84, 163.74, 52.8, 154.63, 125.21, 159.68, 155.46, 285.85, 39.75], "hull": 20, "edges": [0, 38, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38], "width": 302, "height": 293}}, "chuann1b": {"chuann1b": {"type": "mesh", "uvs": [0.03404, 1e-05, 0.10041, 0.04186, 0.12362, 0.0741, 0.12655, 0.09389, 0.35047, 0.30656, 0.40824, 0.32358, 0.47519, 0.34407, 0.53799, 0.36838, 0.60695, 0.39904, 0.67312, 0.43301, 0.73967, 0.47289, 0.8827, 0.56051, 0.92531, 0.60192, 0.97012, 0.65484, 0.9981, 0.68787, 0.99966, 0.69874, 0.99552, 0.73079, 0.97957, 0.79443, 0.96166, 0.79437, 0.9564, 0.82364, 0.93512, 0.91811, 0.92289, 0.94119, 0.89351, 0.99666, 0.79946, 0.9967, 0.72579, 0.99674, 0.61611, 0.99678, 0.51323, 0.99683, 0.43611, 0.99479, 0.37746, 0.99323, 0.34348, 0.90459, 0.31794, 0.86335, 0.25168, 0.77565, 0.2169, 0.68775, 0.19662, 0.60638, 0.19184, 0.56609, 0.17358, 0.54727, 0.16023, 0.4404, 0.16596, 0.3997, 0.19677, 0.3379, 0.1888, 0.28181, 0.10363, 0.20127, 0.08892, 0.20455, 0.04803, 0.16582, 0.00688, 0.10915, 1e-05, 0.07217, 0.00272, 0.02279, 0.01756, 1e-05, 0.23168, 0.26078, 0.29962, 0.30645, 0.31435, 0.43858, 0.40602, 0.45326, 0.47805, 0.47936, 0.54026, 0.4973, 0.61884, 0.53482, 0.68841, 0.57071, 0.74777, 0.61219, 0.81244, 0.66113, 0.87219, 0.70843, 0.92212, 0.74758, 0.25776, 0.45502, 0.22253, 0.4803, 0.19945, 0.50797, 0.26502, 0.731, 0.2944, 0.71057, 0.33266, 0.71602, 0.40987, 0.74053, 0.49733, 0.76913, 0.5829, 0.795, 0.67787, 0.82904, 0.75235, 0.87398, 0.83502, 0.92164, 0.89584, 0.94615], "triangles": [4, 47, 3, 45, 0, 44, 0, 43, 44, 42, 0, 1, 46, 0, 45, 42, 43, 0, 1, 3, 41, 3, 1, 2, 41, 42, 1, 3, 40, 41, 47, 4, 48, 39, 40, 3, 47, 39, 3, 38, 39, 47, 49, 48, 4, 50, 4, 5, 50, 5, 6, 49, 4, 50, 59, 47, 48, 59, 48, 49, 38, 47, 59, 51, 6, 7, 50, 6, 51, 60, 38, 59, 37, 38, 60, 36, 37, 60, 52, 7, 8, 51, 7, 52, 61, 36, 60, 53, 8, 9, 52, 8, 53, 35, 36, 61, 34, 35, 61, 54, 9, 10, 53, 9, 54, 60, 33, 34, 56, 55, 10, 54, 10, 55, 11, 56, 10, 61, 60, 34, 59, 32, 33, 57, 56, 11, 57, 11, 12, 63, 59, 49, 59, 33, 60, 63, 32, 59, 64, 49, 50, 63, 49, 64, 16, 13, 14, 16, 14, 15, 62, 32, 63, 65, 50, 51, 64, 50, 65, 58, 57, 12, 58, 12, 13, 18, 58, 13, 66, 51, 52, 65, 51, 66, 31, 32, 62, 16, 18, 13, 17, 18, 16, 67, 52, 53, 66, 52, 67, 19, 58, 18, 54, 67, 53, 68, 54, 55, 68, 67, 54, 30, 63, 64, 31, 62, 63, 30, 31, 63, 69, 55, 56, 68, 55, 69, 29, 64, 65, 30, 64, 29, 20, 58, 19, 70, 56, 57, 71, 70, 57, 57, 58, 71, 69, 56, 70, 58, 20, 71, 20, 21, 71, 27, 28, 29, 27, 65, 66, 27, 29, 65, 22, 70, 71, 22, 71, 21, 23, 69, 70, 23, 70, 22, 24, 68, 69, 24, 69, 23, 25, 67, 68, 25, 68, 24, 26, 66, 67, 26, 67, 25, 27, 66, 26], "vertices": [2, 5, 183.28, 384.18, 0.96, 20, 133.72, 423.79, 0.04, 2, 5, 177.45, 345.49, 0.96, 20, 127.9, 385.1, 0.04, 2, 5, 170.51, 331.18, 0.96, 20, 120.95, 370.8, 0.04, 2, 5, 165.26, 328.68, 0.96, 20, 115.7, 368.3, 0.04, 2, 5, 125.7, 195.01, 0.93047, 20, 76.14, 234.63, 0.06953, 2, 5, 126.03, 162.19, 0.93344, 20, 76.48, 201.81, 0.06656, 2, 5, 126.21, 124.13, 0.93789, 20, 76.65, 163.74, 0.06211, 2, 5, 124.96, 88.2, 0.933, 20, 75.4, 127.81, 0.067, 2, 5, 122.48, 48.56, 0.92858, 20, 72.92, 88.18, 0.07142, 2, 5, 118.83, 10.34, 0.92444, 20, 69.27, 49.95, 0.07556, 2, 5, 113.57, -28.36, 0.92, 20, 64.02, 11.25, 0.08, 2, 5, 101.73, -111.62, 0.92, 20, 52.18, -72, 0.08, 2, 5, 93.94, -137.1, 0.93304, 20, 44.39, -97.48, 0.06696, 2, 5, 83.14, -164.31, 0.95976, 20, 33.59, -124.69, 0.04024, 2, 5, 76.4, -181.29, 0.98042, 20, 26.84, -141.67, 0.01958, 2, 5, 73.51, -182.64, 0.98206, 20, 23.95, -143.02, 0.01794, 2, 5, 64.22, -181.75, 0.98456, 20, 14.66, -142.14, 0.01544, 2, 5, 45.09, -175.71, 0.98835, 20, -4.46, -136.09, 0.01165, 2, 5, 43.53, -165.76, 0.98369, 20, -6.02, -126.15, 0.01631, 2, 5, 34.92, -164.13, 0.98687, 20, -14.64, -124.52, 0.01313, 2, 5, 6.74, -156.48, 0.99756, 20, -42.82, -116.87, 0.00244, 2, 5, -0.77, -150.72, 0.99926, 20, -50.32, -111.1, 0.00074, 1, 5, -18.8, -136.85, 1, 2, 5, -27.08, -84.65, 0.99357, 20, -76.63, -45.03, 0.00643, 2, 5, -33.56, -43.76, 0.9913, 20, -83.11, -4.14, 0.0087, 2, 5, -43.21, 17.12, 0.99531, 20, -92.76, 56.74, 0.00469, 1, 5, -52.26, 74.23, 1, 1, 5, -58.47, 117.13, 1, 2, 5, -63.19, 149.76, 0.9904, 21, -101.36, -57.71, 0.0096, 2, 5, -41.49, 172.52, 0.97701, 21, -79.66, -34.95, 0.02299, 2, 5, -32.24, 188.52, 0.96014, 21, -70.41, -18.95, 0.03986, 2, 5, -13.64, 229.17, 0.92978, 21, -51.81, 21.7, 0.07022, 2, 5, 7.79, 252.35, 0.93181, 21, -30.38, 44.88, 0.06819, 2, 5, 28.67, 267.19, 0.93491, 21, -9.5, 59.72, 0.06509, 2, 5, 39.47, 271.62, 0.94157, 21, 1.3, 64.16, 0.05843, 2, 5, 43.11, 282.59, 0.93675, 21, 4.94, 75.12, 0.06325, 3, 5, 71.7, 294.71, 0.95166, 21, 33.53, 87.25, 0.04543, 20, 22.15, 334.33, 0.00292, 3, 5, 83.54, 293.32, 0.95986, 21, 45.37, 85.86, 0.03066, 20, 33.99, 332.94, 0.00948, 3, 5, 103.46, 278.95, 0.98256, 21, 65.29, 71.48, 0.00237, 20, 53.91, 318.56, 0.01507, 2, 5, 118.38, 285.84, 0.97436, 20, 68.83, 325.46, 0.02564, 2, 5, 133.33, 336.67, 0.96, 20, 83.78, 376.29, 0.04, 2, 5, 131.13, 344.69, 0.96, 20, 81.57, 384.31, 0.04, 2, 5, 138.32, 369.1, 0.96, 20, 88.77, 408.72, 0.04, 2, 5, 150.49, 394.44, 0.96474, 20, 100.93, 434.05, 0.03526, 2, 5, 160.19, 399.88, 0.96568, 20, 110.63, 439.5, 0.03432, 2, 5, 174.18, 400.56, 0.9618, 20, 124.63, 440.17, 0.0382, 2, 5, 181.83, 393.32, 0.96, 20, 132.28, 432.94, 0.04, 2, 5, 128.01, 262.97, 0.96763, 20, 78.46, 302.58, 0.03237, 2, 5, 121.26, 223.24, 0.9473, 20, 71.7, 262.86, 0.0527, 2, 5, 85.75, 209.24, 0.96572, 20, 36.2, 248.86, 0.03428, 2, 5, 89.72, 157.71, 0.95801, 20, 40.16, 197.32, 0.04199, 2, 5, 88.78, 116.57, 0.96, 20, 39.22, 156.19, 0.04, 2, 5, 89.25, 81.25, 0.95497, 20, 39.69, 120.87, 0.04503, 2, 5, 85.7, 35.98, 0.95091, 20, 36.15, 75.6, 0.04909, 2, 5, 81.82, -4.22, 0.94565, 20, 32.26, 35.4, 0.05435, 2, 5, 75.48, -39, 0.94336, 20, 25.93, 0.61, 0.05664, 2, 5, 67.53, -77.05, 0.94051, 20, 17.98, -37.44, 0.05949, 2, 5, 59.61, -112.31, 0.94593, 20, 10.05, -72.69, 0.05407, 2, 5, 53.09, -141.75, 0.96482, 20, 3.54, -102.13, 0.03518, 3, 5, 76.2, 239.93, 0.98825, 21, 38.03, 32.46, 0.00238, 20, 26.64, 279.54, 0.00937, 2, 5, 66.06, 258.37, 0.98007, 21, 27.89, 50.9, 0.01993, 2, 5, 56.33, 269.96, 0.96064, 21, 18.16, 62.49, 0.03936, 2, 5, -0.03, 223.73, 0.94384, 21, -38.2, 16.26, 0.05616, 3, 5, 8.24, 208.32, 0.96419, 21, -29.93, 0.85, 0.03305, 20, -41.32, 247.94, 0.00276, 3, 5, 10.08, 186.84, 0.97512, 21, -28.08, -20.63, 0.01026, 20, -39.47, 226.46, 0.01462, 2, 5, 10.04, 142.9, 0.97027, 20, -39.51, 182.52, 0.02973, 2, 5, 9.76, 93.09, 0.96475, 20, -39.79, 132.71, 0.03525, 2, 5, 10.08, 44.46, 0.96128, 20, -39.48, 84.07, 0.03872, 2, 5, 8.94, -9.76, 0.96033, 20, -40.62, 29.85, 0.03967, 2, 5, 2.97, -53.08, 0.9679, 20, -46.59, -13.47, 0.0321, 2, 5, -3.04, -101.08, 0.983, 20, -52.6, -61.46, 0.017, 2, 5, -4.53, -135.91, 0.99563, 20, -54.08, -96.3, 0.00437], "hull": 47, "edges": [0, 92, 0, 2, 2, 4, 4, 6, 6, 8, 18, 20, 22, 24, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 24, 26, 26, 28, 20, 22, 78, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 36, 98, 118, 118, 120, 120, 122, 122, 70, 62, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 40, 42, 42, 44, 142, 42, 52, 54, 54, 56, 50, 52, 48, 50, 44, 46, 46, 48], "width": 562, "height": 282}}, "chuann2": {"chuann2": {"type": "mesh", "uvs": [0.59396, 0, 0.63813, 0.04826, 0.59076, 0.17826, 0.71351, 0.20719, 1, 0.3048, 1, 0.32693, 0.98854, 0.36956, 0.96906, 0.37082, 0.64532, 0.26021, 0.56992, 0.2411, 0.46868, 0.60052, 0.47017, 0.65051, 0.90706, 0.75385, 0.85604, 0.82231, 0.73006, 0.78661, 0.439, 0.711, 0.4241, 0.77595, 0.38217, 0.94766, 0.31358, 0.99331, 0.28782, 0.9194, 0.34433, 0.68704, 0.29595, 0.67408, 0.04468, 0.61844, 0, 0.58912, 0, 0.58488, 0.01286, 0.5634, 0.13051, 0.57475, 0.3589, 0.61831, 0.38099, 0.53461, 0.48858, 0.21275, 0.28826, 0.16041, 0.26114, 0.11088, 0.50919, 0.14422, 0.52937, 0.08114, 0.56454, 0], "triangles": [33, 34, 0, 1, 33, 0, 30, 31, 32, 2, 33, 1, 32, 33, 2, 29, 30, 32, 9, 29, 32, 2, 9, 32, 8, 2, 3, 9, 2, 8, 4, 7, 3, 4, 5, 7, 5, 6, 7, 8, 3, 7, 23, 24, 25, 9, 10, 28, 9, 28, 29, 27, 28, 10, 22, 25, 26, 23, 25, 22, 27, 10, 11, 21, 26, 27, 22, 26, 21, 20, 21, 27, 15, 20, 27, 11, 15, 27, 16, 20, 15, 14, 11, 12, 15, 11, 14, 13, 14, 12, 19, 20, 16, 17, 19, 16, 18, 19, 17], "vertices": [195.99, 3.86, 190.17, -8.03, 163.28, -6.26, 166.28, -32.94, 167.77, -97.03, 163.74, -98.37, 155.2, -98.61, 153.66, -94.73, 152.02, -22.29, 150.43, -5.82, 78.11, -6.97, 69.1, -10.29, 79.68, -105.28, 63.77, -99.05, 61.79, -71.3, 55.97, -7.61, 43.13, -8.51, 9.02, -10.36, -3.92, 0.81, 7.81, 10.51, 53.97, 13.07, 53.07, 23.68, 46.29, 78.08, 48.63, 88.93, 49.4, 89.18, 54.18, 87.87, 60.04, 63.28, 67.47, 14.26, 84.22, 14.83, 150.12, 12.42, 146.17, 56.27, 153.37, 64.77, 164, 12.37, 176.85, 12.08, 194.01, 9.84], "hull": 35, "edges": [0, 68, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68], "width": 214, "height": 192}}, "chuann_fan1": {"chuann_fan1": {"type": "mesh", "uvs": [0.41893, 0.02584, 0.53349, 0.0564, 0.65026, 0.09252, 0.7505, 0.13836, 0.85515, 0.18837, 0.94437, 0.23282, 1, 0.23282, 1, 0.28681, 0.97718, 0.33543, 0.94083, 0.41878, 0.90338, 0.53686, 0.87144, 0.64382, 0.85491, 0.76329, 0.8439, 0.88137, 0.8472, 1, 0.79855, 1, 0.70711, 0.95765, 0.62119, 0.92292, 0.51655, 0.87708, 0.40419, 0.83123, 0.30689, 0.79901, 0.19099, 0.76502, 0.09396, 0.75709, 0.06791, 0.80354, 0.03395, 0.81034, 0.0149, 0.73216, 0.00323, 0.64107, 1e-05, 0.55655, 0, 0.45946, 0.00302, 0.36183, 0.01904, 0.26312, 0.03771, 0.18244, 0.06252, 0.1042, 0.10564, 0.02489, 0.16224, 0, 0.21795, 0, 0.30987, 0, 0.19415, 0.05409, 0.16617, 0.12251, 0.14668, 0.18878, 0.13142, 0.26682, 0.11447, 0.34378, 0.10514, 0.43464, 0.09667, 0.52444, 0.10006, 0.6014, 0.10599, 0.69654, 0.29756, 0.05409, 0.26535, 0.13961, 0.24501, 0.19947, 0.22466, 0.27858, 0.20601, 0.35768, 0.18567, 0.44961, 0.17719, 0.53085, 0.17126, 0.61102, 0.17889, 0.69761, 0.40291, 0.07133, 0.38087, 0.14616, 0.35544, 0.22206, 0.33425, 0.30437, 0.31476, 0.3792, 0.29611, 0.44547, 0.28594, 0.53954, 0.27915, 0.6304, 0.27576, 0.71913, 0.51395, 0.09592, 0.49022, 0.17289, 0.46479, 0.25199, 0.4419, 0.32682, 0.41987, 0.40699, 0.40546, 0.46681, 0.39528, 0.53415, 0.37663, 0.63891, 0.3707, 0.73298, 0.6233, 0.1365, 0.60804, 0.19316, 0.58516, 0.27547, 0.55379, 0.3674, 0.52921, 0.44864, 0.50972, 0.51586, 0.49785, 0.57134, 0.48174, 0.65365, 0.47496, 0.76482, 0.73007, 0.1746, 0.71312, 0.23553, 0.68854, 0.31036, 0.66226, 0.39114, 0.63852, 0.4649, 0.62327, 0.53332, 0.6097, 0.59852, 0.5919, 0.68404, 0.57919, 0.79735, 0.83481, 0.21746, 0.80823, 0.27481, 0.78048, 0.34936, 0.7597, 0.42084, 0.73944, 0.49274, 0.72101, 0.56905, 0.69763, 0.64505, 0.68256, 0.72867, 0.66468, 0.84436, 0.91386, 0.25998, 0.89078, 0.31567, 0.86027, 0.38674, 0.83599, 0.45932, 0.81195, 0.53012, 0.79117, 0.60839, 0.77528, 0.68525, 0.75833, 0.76943, 0.74477, 0.87894], "triangles": [58, 48, 57, 49, 48, 58, 67, 57, 66, 58, 57, 67, 76, 66, 75, 67, 66, 76, 59, 49, 58, 50, 49, 59, 76, 75, 85, 68, 58, 67, 59, 58, 68, 60, 50, 59, 77, 67, 76, 68, 67, 77, 51, 50, 60, 86, 76, 85, 77, 76, 86, 69, 59, 68, 60, 59, 69, 77, 69, 68, 78, 69, 77, 87, 77, 86, 78, 77, 87, 70, 60, 69, 70, 69, 78, 61, 51, 60, 61, 60, 70, 79, 70, 78, 88, 78, 87, 79, 78, 88, 71, 61, 70, 80, 71, 70, 79, 80, 70, 89, 79, 88, 46, 35, 36, 55, 36, 0, 46, 36, 55, 46, 37, 35, 47, 37, 46, 56, 46, 55, 47, 46, 56, 48, 38, 47, 57, 47, 56, 48, 47, 57, 57, 56, 66, 64, 0, 1, 55, 0, 64, 73, 1, 2, 64, 1, 73, 65, 55, 64, 56, 55, 65, 74, 64, 73, 65, 64, 74, 66, 56, 65, 75, 65, 74, 66, 65, 75, 82, 2, 3, 73, 2, 82, 91, 3, 4, 82, 3, 91, 83, 73, 82, 74, 73, 83, 92, 82, 91, 83, 82, 92, 84, 74, 83, 75, 74, 84, 92, 91, 101, 93, 83, 92, 84, 83, 93, 85, 75, 84, 94, 84, 93, 85, 84, 94, 100, 4, 5, 91, 4, 100, 5, 6, 7, 101, 91, 100, 8, 5, 7, 100, 5, 8, 101, 100, 8, 102, 92, 101, 93, 92, 102, 9, 101, 8, 102, 101, 9, 103, 94, 93, 102, 103, 93, 9, 103, 102, 10, 103, 9, 95, 85, 94, 95, 94, 103, 86, 85, 95, 104, 95, 103, 104, 103, 10, 96, 86, 95, 96, 95, 104, 87, 86, 96, 88, 87, 96, 105, 96, 104, 11, 104, 10, 105, 104, 11, 97, 88, 96, 97, 96, 105, 106, 97, 105, 106, 105, 11, 98, 97, 106, 12, 106, 11, 107, 98, 106, 107, 106, 12, 13, 107, 12, 108, 107, 13, 15, 108, 13, 16, 108, 15, 15, 13, 14, 89, 88, 97, 98, 89, 97, 90, 81, 89, 90, 89, 98, 99, 90, 98, 99, 98, 107, 18, 81, 90, 108, 99, 107, 17, 90, 99, 18, 90, 17, 16, 99, 108, 17, 99, 16, 80, 79, 89, 72, 63, 71, 72, 71, 80, 81, 72, 80, 81, 80, 89, 20, 63, 72, 19, 72, 81, 20, 72, 19, 19, 81, 18, 52, 51, 61, 61, 53, 52, 62, 53, 61, 62, 61, 71, 45, 44, 53, 54, 53, 62, 45, 53, 54, 63, 54, 62, 63, 62, 71, 21, 54, 63, 21, 22, 54, 21, 63, 20, 25, 26, 45, 22, 25, 45, 22, 45, 54, 23, 25, 22, 24, 25, 23, 43, 28, 42, 52, 43, 42, 27, 28, 43, 44, 43, 52, 27, 43, 44, 53, 44, 52, 26, 27, 44, 26, 44, 45, 41, 30, 40, 41, 40, 50, 29, 30, 41, 42, 29, 41, 51, 41, 50, 42, 41, 51, 28, 29, 42, 51, 52, 42, 39, 32, 38, 31, 32, 39, 39, 38, 48, 40, 31, 39, 30, 31, 40, 49, 39, 48, 40, 39, 49, 50, 40, 49, 37, 34, 35, 37, 33, 34, 38, 33, 37, 32, 33, 38, 38, 37, 47], "vertices": [3, 31, 10.12, 28.94, 0.47238, 30, 12.36, -28.15, 0.51478, 29, 78.13, 19.23, 0.01284, 3, 32, 12.22, 59.07, 0.0183, 31, 10.86, -1.03, 0.97305, 30, 13.1, -58.12, 0.00865, 4, 27, 81.19, 65.72, 0.00021, 32, 11.98, 28.3, 0.52555, 31, 10.63, -31.8, 0.46832, 29, 78.63, -41.52, 0.00592, 3, 9, 19.28, 57.29, 0.00138, 32, 8.85, 1.18, 0.98036, 31, 7.5, -58.92, 0.01826, 3, 26, 39.08, 13.67, 0.19813, 9, 13.44, 29.25, 0.29772, 32, 5.16, -27.23, 0.50415, 3, 26, 35.57, -10.64, 0.04257, 9, 8.1, 5.27, 0.90556, 32, 1.65, -51.54, 0.05187, 1, 9, 10.33, -8.79, 1, 2, 26, 28.2, -27.03, 0.00636, 9, -0.5, -10.51, 0.99364, 2, 26, 17.25, -23.62, 0.22305, 9, -11.16, -6.28, 0.77695, 3, 27, 33.93, -21.95, 0.06299, 26, -1.37, -18.47, 0.77492, 9, -29.33, 0.27, 0.16208, 3, 28, 47.06, -15.44, 0.00524, 27, 8.39, -18.15, 0.68727, 26, -26.9, -14.68, 0.3075, 3, 28, 24.04, -12.49, 0.3681, 27, -14.63, -15.21, 0.63106, 26, -49.92, -11.73, 0.00084, 3, 12, 38.79, -5.78, 0.05689, 28, -0.53, -13.98, 0.91403, 27, -39.2, -16.69, 0.02908, 3, 35, 6.27, -57.94, 0.00602, 12, 14.68, -6.74, 0.66783, 28, -24.5, -16.77, 0.32615, 1, 12, -8.98, -11.34, 1, 2, 35, -19.84, -52.2, 0.01326, 12, -10.92, 0.96, 0.98674, 3, 35, -16.88, -27.44, 0.42739, 12, -6.09, 25.42, 0.51749, 28, -47.65, 13.73, 0.05512, 4, 34, -19.1, -62.45, 0.00331, 35, -15.1, -4.41, 0.9151, 12, -2.57, 48.25, 0.07766, 28, -45.87, 36.76, 0.00393, 4, 34, -16.23, -34.23, 0.36709, 35, -12.23, 23.8, 0.63142, 27, -81.67, 62.26, 0.00027, 29, -84.23, -44.98, 0.00122, 3, 33, -14.75, -50.1, 0.0084, 34, -13.82, -4.1, 0.92667, 35, -9.82, 53.94, 0.06493, 3, 33, -14.14, -24.35, 0.47941, 34, -13.2, 21.65, 0.52012, 29, -81.2, 10.9, 0.00047, 4, 25, -28.07, -46.97, 0.0072, 11, -0.47, -37.17, 0.11178, 33, -14.27, 6.11, 0.87757, 34, -13.34, 52.11, 0.00345, 3, 25, -32.23, -22.43, 0.02444, 11, -2.76, -12.38, 0.79442, 33, -18.44, 30.65, 0.18114, 2, 11, -13.12, -7.27, 0.9864, 33, -29.16, 34.96, 0.0136, 1, 11, -15.84, 1.1, 1, 2, 25, -31.98, -1.57, 0.05433, 11, -0.93, 8.4, 0.94567, 3, 25, -14.68, 5.6, 0.65915, 11, 16.87, 14.24, 0.34073, 33, -0.89, 58.69, 0.00012, 3, 24, -42.12, 2.63, 0.06861, 25, 1.82, 10.37, 0.93063, 11, 33.69, 17.74, 0.00076, 4, 24, -22.95, 7.19, 0.5293, 25, 21, 14.92, 0.47041, 33, 34.79, 68, 8e-05, 29, -32.28, 103.25, 0.00021, 3, 23, -39.7, 11.57, 0.01059, 24, -3.48, 11.01, 0.94453, 25, 40.46, 18.74, 0.04488, 2, 23, -19.25, 12.21, 0.48728, 24, 16.96, 11.64, 0.51272, 3, 10, -29.08, 17.59, 0.00858, 23, -2.21, 11.34, 0.9245, 24, 34, 10.78, 0.06692, 2, 10, -12.4, 13.8, 0.41884, 23, 14.71, 8.83, 0.58116, 1, 10, 5.23, 5.42, 1, 2, 10, 12.49, -8.11, 0.81756, 30, 2.3, 36.99, 0.18244, 3, 10, 14.72, -22.19, 0.4745, 23, 44.47, -25.01, 0.00079, 30, 5.59, 23.12, 0.52471, 3, 10, 18.4, -45.43, 0.01134, 31, 8.78, 57.32, 0.0069, 30, 11.02, 0.22, 0.98176, 6, 10, 2.92, -17.89, 0.60336, 23, 32.38, -21.61, 0.03453, 24, 68.6, -22.17, 0.00073, 33, 126.33, 38.64, 0.0002, 30, -6.5, 26.51, 0.35504, 29, 59.27, 73.89, 0.00615, 6, 10, -11.92, -12.99, 0.5037, 23, 17.22, -17.85, 0.2679, 24, 53.43, -18.41, 0.01017, 33, 111.17, 42.41, 0.00175, 30, -21.67, 30.27, 0.19134, 29, 44.1, 77.65, 0.02514, 7, 10, -25.99, -10.16, 0.18018, 23, 2.97, -16.1, 0.58748, 24, 39.19, -16.66, 0.0653, 25, 83.13, -8.93, 0.00017, 33, 96.92, 44.16, 0.00603, 30, -35.91, 32.02, 0.11483, 29, 29.86, 79.4, 0.04601, 7, 10, -42.25, -8.78, 0.01857, 23, -13.34, -15.96, 0.51507, 24, 22.87, -16.52, 0.30821, 25, 66.82, -8.79, 0.00615, 33, 80.61, 44.3, 0.01751, 30, -52.23, 32.17, 0.06541, 29, 13.54, 79.55, 0.06908, 7, 10, -58.36, -6.94, 7e-05, 23, -29.55, -15.34, 0.20394, 24, 6.67, -15.9, 0.61181, 25, 50.61, -8.17, 0.04685, 33, 64.4, 44.91, 0.03526, 30, -68.43, 32.78, 0.0313, 29, -2.66, 80.16, 0.07076, 6, 23, -48.04, -17.28, 0.02564, 24, -11.83, -17.84, 0.54609, 25, 32.11, -10.11, 0.27702, 33, 45.91, 42.98, 0.07507, 30, -86.93, 30.85, 0.01211, 29, -21.16, 78.23, 0.06407, 7, 23, -66.28, -19.38, 0.00201, 24, -30.06, -19.94, 0.22879, 25, 13.88, -12.2, 0.59252, 11, 43.99, -5.68, 0.01036, 33, 27.67, 40.88, 0.12245, 30, -105.17, 28.75, 0.00338, 29, -39.4, 76.13, 0.04049, 7, 23, -81.28, -23.83, 9e-05, 24, -45.07, -24.39, 0.04643, 25, -1.12, -16.66, 0.57852, 11, 28.7, -8.98, 0.14572, 33, 12.67, 36.43, 0.2079, 30, -120.17, 24.3, 0.00079, 29, -54.4, 71.68, 0.02056, 5, 24, -63.51, -30.33, 0.00159, 25, -19.56, -22.59, 0.16669, 11, 9.86, -13.5, 0.52525, 33, -5.77, 30.49, 0.30467, 29, -72.84, 65.74, 0.0018, 5, 10, 7.06, -44.04, 0.00396, 23, 38.49, -47.37, 0.00044, 24, 74.71, -47.93, 3e-05, 33, 132.44, 12.89, 1e-05, 30, -0.39, 0.75, 0.99555, 8, 10, -11.38, -38.61, 0.12469, 23, 19.7, -43.36, 0.10464, 24, 55.92, -43.92, 0.02392, 25, 99.86, -36.18, 0.00019, 33, 113.65, 16.9, 0.00444, 31, -21.42, 61.86, 0.02114, 30, -19.19, 4.77, 0.62474, 29, 46.58, 52.15, 0.09623, 8, 10, -24.2, -35.36, 0.10709, 23, 6.67, -41.09, 0.20835, 24, 42.89, -41.66, 0.06994, 25, 86.83, -33.92, 0.00278, 33, 100.62, 19.16, 0.01425, 31, -34.45, 64.12, 0.01895, 30, -32.21, 7.03, 0.40853, 29, 33.56, 54.41, 0.1701, 8, 10, -40.87, -32.73, 0.03734, 23, -10.15, -39.73, 0.25111, 24, 26.06, -40.3, 0.17945, 25, 70, -32.56, 0.01844, 33, 83.8, 20.52, 0.04225, 31, -51.28, 65.48, 0.00965, 30, -49.04, 8.39, 0.21738, 29, 16.73, 55.77, 0.24437, 9, 10, -57.48, -30.53, 0.00492, 23, -26.88, -38.8, 0.16897, 24, 9.34, -39.36, 0.29558, 25, 53.28, -31.62, 0.06734, 33, 67.07, 21.46, 0.09575, 34, 68, 67.46, 8e-05, 31, -68, 66.42, 0.00192, 30, -65.76, 9.33, 0.10274, 29, 0, 56.71, 0.26272, 7, 23, -46.24, -38.04, 0.05769, 24, -10.02, -38.6, 0.30467, 25, 33.92, -30.87, 0.19095, 33, 47.71, 22.22, 0.19854, 34, 48.64, 68.22, 0.0015, 30, -85.13, 10.09, 0.03523, 29, -19.36, 57.47, 0.21142, 8, 23, -62.79, -39.73, 0.01467, 24, -26.57, -40.3, 0.18033, 25, 17.37, -32.56, 0.28969, 11, 45.93, -26.24, 0.01314, 33, 31.16, 20.52, 0.34307, 34, 32.1, 66.52, 0.00272, 30, -101.67, 8.39, 0.01161, 29, -35.9, 55.77, 0.14477, 8, 23, -78.97, -42.01, 0.00208, 24, -42.76, -42.58, 0.0623, 25, 1.18, -34.84, 0.25302, 11, 29.62, -27.29, 0.08015, 33, 14.98, 18.24, 0.53622, 34, 15.91, 64.24, 0.00065, 30, -117.86, 6.11, 0.00244, 29, -52.09, 53.49, 0.06314, 6, 24, -59.41, -48.53, 0.00513, 25, -15.47, -40.8, 0.06726, 11, 12.56, -31.97, 0.14768, 33, -1.67, 12.28, 0.77573, 30, -134.51, 0.15, 5e-05, 29, -68.74, 47.53, 0.00414, 5, 23, 41.32, -74.42, 0.00157, 24, 77.53, -74.98, 0.0005, 31, 0.19, 30.8, 0.41253, 30, 2.43, -26.3, 0.55074, 29, 68.2, 21.08, 0.03466, 7, 10, -8.07, -68.03, 8e-05, 23, 25.23, -72.44, 0.01756, 24, 61.45, -73, 0.00701, 33, 119.18, -12.18, 0.00065, 31, -15.89, 32.78, 0.30986, 30, -13.65, -24.31, 0.50782, 29, 52.12, 23.07, 0.15702, 8, 10, -24.3, -64, 0.00413, 23, 8.74, -69.66, 0.05055, 24, 44.96, -70.22, 0.02746, 25, 88.9, -62.49, 0.00136, 33, 102.69, -9.4, 0.00678, 31, -32.38, 35.56, 0.18306, 30, -30.15, -21.54, 0.38229, 29, 35.62, 25.84, 0.34438, 8, 10, -41.66, -61.26, 0.00348, 23, -8.77, -68.24, 0.07068, 24, 27.45, -68.8, 0.06178, 25, 71.39, -61.07, 0.00936, 33, 85.18, -7.98, 0.02952, 31, -49.89, 36.98, 0.07636, 30, -47.66, -20.12, 0.21679, 29, 18.11, 27.26, 0.53203, 9, 10, -57.44, -58.7, 0.00069, 23, -24.7, -66.89, 0.06261, 24, 11.51, -67.45, 0.09457, 25, 55.46, -59.72, 0.0282, 33, 69.25, -6.63, 0.08546, 34, 70.18, 39.36, 0.00401, 31, -65.83, 38.33, 0.01762, 30, -63.59, -18.77, 0.10601, 29, 2.18, 28.61, 0.60083, 8, 23, -38.9, -65.35, 0.04245, 24, -2.68, -65.91, 0.11032, 25, 41.26, -58.18, 0.0548, 33, 55.06, -5.1, 0.18121, 34, 55.99, 40.9, 0.02427, 31, -80.02, 39.87, 0.0013, 30, -77.78, -17.23, 0.04847, 29, -12.01, 30.15, 0.53718, 7, 23, -58.08, -67.23, 0.01361, 24, -21.86, -67.79, 0.07506, 25, 22.08, -60.06, 0.06803, 33, 35.87, -6.97, 0.37456, 34, 36.81, 39.03, 0.08471, 30, -96.96, -19.1, 0.0115, 29, -31.19, 28.28, 0.37252, 7, 23, -76.42, -69.8, 0.00225, 24, -40.21, -70.36, 0.02659, 25, 3.73, -62.62, 0.03367, 33, 17.53, -9.54, 0.61899, 34, 18.46, 36.46, 0.14981, 30, -115.31, -21.67, 0.00146, 29, -49.54, 25.71, 0.16724, 6, 23, -94.15, -73.11, 2e-05, 24, -57.93, -73.67, 0.00126, 25, -13.99, -65.94, 0.00032, 33, -0.2, -12.85, 0.75477, 34, 0.73, 33.14, 0.21921, 29, -67.26, 22.39, 0.02442, 2, 31, 1.9, 1.99, 0.98275, 30, 4.14, -55.11, 0.01725, 9, 23, 26.42, -100.93, 0.0015, 24, 62.63, -101.49, 0.00027, 35, 125.3, 63.36, 0.00012, 27, 55.86, 101.82, 0.00367, 26, 20.57, 105.29, 0.0006, 32, -13.35, 64.39, 0.02546, 31, -14.71, 4.29, 0.72756, 30, -12.47, -52.8, 0.0816, 29, 53.3, -5.42, 0.15921, 9, 23, 9.29, -98.3, 0.00551, 24, 45.51, -98.86, 0.00208, 35, 108.17, 65.99, 0.00062, 27, 38.73, 104.44, 0.00806, 26, 3.44, 107.92, 0.00176, 32, -30.48, 67.02, 0.03562, 31, -31.83, 6.92, 0.41654, 30, -29.59, -50.18, 0.10295, 29, 36.17, -2.8, 0.42688, 12, 23, -6.84, -96.11, 0.00718, 24, 29.37, -96.67, 0.00397, 25, 73.32, -88.93, 9e-05, 33, 87.11, -35.85, 0.00026, 35, 92.04, 68.18, 0.00106, 28, 61.27, 109.35, 3e-05, 27, 22.6, 106.64, 0.00754, 26, -12.69, 110.11, 0.00167, 32, -46.61, 69.21, 0.02263, 31, -47.97, 9.11, 0.1843, 30, -45.73, -47.98, 0.06755, 29, 20.04, -0.6, 0.70372, 8, 23, -23.98, -94.37, 0.00163, 24, 12.24, -94.94, 0.00121, 25, 56.18, -87.2, 7e-05, 26, -29.83, 111.84, 4e-05, 32, -63.75, 70.94, 0.00088, 31, -65.1, 10.84, 0.01234, 30, -62.87, -46.25, 0.00809, 29, 2.9, 1.13, 0.97574, 12, 23, -36.65, -93.59, 0.00202, 24, -0.43, -94.15, 0.00816, 25, 43.51, -86.42, 0.00427, 33, 57.3, -33.33, 0.0447, 34, 58.24, 12.67, 0.0585, 35, 62.24, 70.7, 0.00534, 28, 31.47, 111.87, 0.00052, 27, -7.2, 109.16, 0.00319, 26, -42.5, 112.63, 2e-05, 32, -76.41, 71.73, 0.00011, 30, -75.53, -45.46, 0.00152, 29, -9.76, 1.92, 0.87165, 12, 23, -50.55, -94.21, 0.00159, 24, -14.33, -94.77, 0.01149, 25, 29.61, -87.04, 0.00694, 33, 43.4, -33.95, 0.11591, 34, 44.33, 12.04, 0.19827, 35, 48.33, 70.08, 0.01492, 28, 17.56, 111.25, 0.0013, 27, -21.11, 108.53, 0.0076, 26, -56.4, 112.01, 2e-05, 32, -90.32, 71.11, 0.00073, 30, -89.44, -46.09, 0.00055, 29, -23.67, 1.29, 0.64068, 10, 23, -72.34, -94.48, 0.00022, 24, -36.13, -95.04, 0.00613, 25, 7.81, -87.3, 0.0034, 33, 21.61, -34.22, 0.21109, 34, 22.54, 11.78, 0.48357, 35, 26.54, 69.81, 0.01238, 28, -4.23, 110.98, 0.00071, 27, -42.9, 108.27, 0.00488, 32, -112.11, 70.84, 0.00017, 29, -45.46, 1.03, 0.27745, 6, 24, -55.06, -97.97, 0.00045, 33, 2.68, -37.15, 0.16052, 34, 3.61, 8.85, 0.797, 28, -23.16, 108.05, 1e-05, 27, -61.83, 105.34, 0.00041, 29, -64.39, -1.9, 0.04161, 6, 35, 140.35, 31.92, 0.00026, 27, 70.91, 70.37, 0.00385, 26, 35.62, 73.85, 0.00093, 32, 1.7, 32.95, 0.43104, 31, 0.35, -27.15, 0.53977, 29, 68.35, -36.87, 0.02415, 6, 35, 128.26, 33.07, 0.00181, 27, 58.82, 71.52, 0.01774, 26, 23.53, 74.99, 0.00872, 32, -10.39, 34.09, 0.38324, 31, -11.75, -26.01, 0.49319, 29, 56.26, -35.72, 0.0953, 9, 34, 106.65, -23.13, 0.00137, 35, 110.65, 34.91, 0.00851, 28, 79.88, 76.08, 0.00028, 27, 41.21, 73.36, 0.0513, 26, 5.92, 76.84, 0.02642, 32, -28, 35.94, 0.28183, 31, -29.36, -24.16, 0.3549, 30, -27.12, -81.26, 0.00113, 29, 38.65, -33.88, 0.27426, 9, 34, 86.64, -19.62, 0.01365, 35, 90.64, 38.41, 0.02551, 28, 59.87, 79.58, 0.00411, 27, 21.2, 76.87, 0.08276, 26, -14.09, 80.34, 0.02812, 32, -48.01, 39.44, 0.15579, 31, -49.37, -20.66, 0.17822, 30, -47.13, -77.75, 0.00151, 29, 18.64, -30.37, 0.51033, 8, 34, 69.14, -17.31, 0.0586, 35, 73.14, 40.73, 0.0543, 28, 42.37, 81.9, 0.01171, 27, 3.7, 79.18, 0.09067, 26, -31.59, 82.66, 0.01735, 32, -65.51, 41.76, 0.07792, 31, -66.87, -18.35, 0.06038, 29, 1.14, -28.06, 0.62906, 9, 33, 53.78, -61.6, 0.0034, 34, 54.71, -15.6, 0.14973, 35, 58.71, 42.43, 0.08866, 28, 27.94, 83.6, 0.01749, 27, -10.73, 80.89, 0.08083, 26, -46.02, 84.36, 0.00803, 32, -79.94, 43.46, 0.03844, 31, -81.3, -16.64, 0.01495, 29, -13.29, -26.35, 0.59847, 9, 33, 42.12, -61.25, 0.00967, 34, 43.05, -15.25, 0.26308, 35, 47.05, 42.79, 0.12433, 28, 16.28, 83.96, 0.02001, 27, -22.39, 81.24, 0.06803, 26, -57.68, 84.72, 0.00325, 32, -91.6, 43.82, 0.02066, 31, -92.96, -16.28, 0.00303, 29, -24.95, -26, 0.48794, 8, 33, 24.91, -61.09, 0.01078, 34, 25.84, -15.09, 0.47829, 35, 29.84, 42.94, 0.17337, 28, -0.93, 84.11, 0.01543, 27, -39.6, 81.4, 0.04094, 26, -74.89, 84.87, 0.00031, 32, -108.81, 43.97, 0.00711, 29, -42.16, -25.84, 0.27377, 6, 34, 3.48, -18.61, 0.67056, 35, 7.48, 39.42, 0.26781, 28, -23.29, 80.59, 0.00364, 27, -61.96, 77.88, 0.01022, 32, -131.17, 40.45, 0.00092, 29, -64.52, -29.36, 0.04685, 5, 35, 139.14, 3.54, 0.0001, 27, 69.7, 41.99, 0.00142, 32, 0.49, 4.57, 0.95705, 31, -0.87, -55.53, 0.03791, 29, 67.14, -65.25, 0.00351, 8, 34, 122.1, -53.13, 0.00012, 35, 126.1, 4.91, 0.00264, 27, 56.66, 43.36, 0.03092, 26, 21.37, 46.84, 0.06637, 9, -1.7, 63.66, 0.00123, 32, -12.55, 5.94, 0.77634, 31, -13.91, -54.17, 0.07548, 29, 54.1, -63.88, 0.0469, 9, 34, 105.87, -50.51, 0.00269, 35, 109.87, 7.52, 0.01209, 28, 79.1, 48.69, 0.00038, 27, 40.43, 45.98, 0.10592, 26, 5.14, 49.45, 0.13899, 9, -17.69, 67.5, 8e-05, 32, -28.78, 8.55, 0.49994, 31, -30.14, -51.55, 0.10182, 29, 37.87, -61.26, 0.1381, 8, 34, 88.36, -47.75, 0.01432, 35, 92.36, 10.28, 0.03645, 28, 61.59, 51.45, 0.00827, 27, 22.92, 48.74, 0.20424, 26, -12.37, 52.21, 0.12822, 32, -46.29, 11.31, 0.28256, 31, -47.65, -48.79, 0.08234, 29, 20.36, -58.5, 0.24361, 8, 34, 72.39, -45.3, 0.0413, 35, 76.39, 12.74, 0.08094, 28, 45.62, 53.91, 0.03219, 27, 6.95, 51.19, 0.25622, 26, -28.34, 54.67, 0.0748, 32, -62.26, 13.77, 0.15501, 31, -63.62, -46.34, 0.04772, 29, 4.39, -56.05, 0.31183, 8, 34, 57.97, -44.7, 0.08251, 35, 61.98, 13.33, 0.14898, 28, 31.2, 54.5, 0.06852, 27, -7.47, 51.79, 0.25345, 26, -42.76, 55.26, 0.03409, 32, -76.68, 14.36, 0.08337, 31, -78.03, -45.74, 0.02044, 29, -10.02, -55.45, 0.30864, 8, 34, 44.29, -44.38, 0.13157, 35, 48.29, 13.65, 0.24598, 28, 17.52, 54.82, 0.10092, 27, -21.15, 52.11, 0.20223, 26, -56.44, 55.58, 0.01107, 32, -90.36, 14.68, 0.0422, 31, -91.71, -45.42, 0.00642, 29, -23.71, -55.13, 0.2596, 8, 34, 26.35, -43.96, 0.19222, 35, 30.35, 14.08, 0.42994, 28, -0.42, 55.25, 0.09821, 27, -39.09, 52.53, 0.10684, 26, -74.38, 56.01, 0.0009, 32, -108.3, 15.11, 0.01432, 31, -109.66, -44.99, 0.00043, 29, -41.65, -54.71, 0.15714, 6, 34, 3.22, -46.1, 0.1709, 35, 7.22, 11.94, 0.75613, 28, -23.55, 53.1, 0.02263, 27, -62.22, 50.39, 0.01806, 32, -131.43, 12.96, 0.00151, 29, -64.78, -56.85, 0.03077, 6, 35, 136.86, -24.56, 1e-05, 27, 67.42, 13.9, 0.00037, 26, 32.13, 17.37, 0.21277, 9, 6.79, 33.46, 0.23378, 32, -1.79, -23.53, 0.5523, 29, 64.86, -93.34, 0.00077, 8, 34, 119.97, -78.66, 0, 35, 123.97, -20.62, 0.0007, 27, 54.52, 17.83, 0.01549, 26, 19.23, 21.3, 0.34967, 9, -5.77, 38.36, 0.12028, 32, -14.68, -19.6, 0.5019, 31, -16.04, -79.7, 0.00039, 29, 51.97, -89.41, 0.01156, 8, 34, 103.6, -75.24, 0.00094, 35, 107.6, -17.21, 0.00563, 27, 38.16, 21.25, 0.11994, 26, 2.87, 24.72, 0.46959, 9, -21.83, 43.02, 0.01823, 32, -31.05, -16.18, 0.33089, 31, -32.41, -76.28, 0.00906, 29, 35.6, -85.99, 0.04574, 8, 34, 88.25, -73.42, 0.00472, 35, 92.25, -15.38, 0.01786, 28, 61.48, 25.79, 0.0023, 27, 22.81, 23.07, 0.32735, 26, -12.48, 26.55, 0.36551, 32, -46.4, -14.35, 0.18851, 31, -47.75, -74.45, 0.01351, 29, 20.25, -84.17, 0.08025, 8, 34, 72.86, -71.74, 0.01336, 35, 76.86, -13.7, 0.04695, 28, 46.08, 27.47, 0.0338, 27, 7.41, 24.75, 0.52339, 26, -27.88, 28.23, 0.16181, 32, -61.8, -12.67, 0.10347, 31, -63.15, -72.78, 0.0109, 29, 4.86, -82.49, 0.10632, 8, 34, 56.69, -70.72, 0.02602, 35, 60.69, -12.69, 0.10791, 28, 29.92, 28.48, 0.15631, 27, -8.75, 25.77, 0.51422, 26, -44.04, 29.24, 0.03317, 32, -77.96, -11.66, 0.04778, 31, -79.31, -71.76, 0.00493, 29, -11.31, -81.47, 0.10966, 8, 34, 40.3, -68.46, 0.03923, 35, 44.3, -10.43, 0.22885, 28, 13.53, 30.74, 0.30807, 27, -25.14, 28.03, 0.30184, 26, -60.43, 31.5, 0.0033, 32, -94.35, -9.4, 0.02108, 31, -95.71, -69.5, 0.00124, 29, -27.7, -79.21, 0.0964, 8, 34, 22.89, -68.63, 0.03148, 35, 26.89, -10.59, 0.43558, 12, 38.84, 38.9, 0.01493, 28, -3.88, 30.57, 0.34946, 27, -42.55, 27.86, 0.10979, 32, -111.76, -9.57, 0.0063, 31, -113.11, -69.67, 1e-05, 29, -45.11, -79.38, 0.05246, 6, 35, 2.99, -11.56, 0.7687, 12, 14.92, 39.75, 0.10657, 28, -27.78, 29.61, 0.11747, 27, -66.46, 26.89, 0.00417, 32, -135.66, -10.53, 0.00014, 29, -69.01, -80.35, 0.00295, 3, 26, 28.41, -4.31, 0.18197, 9, 1.43, 12.13, 0.69816, 32, -5.51, -45.21, 0.11987, 4, 26, 16.04, -1.17, 0.52255, 9, -10.66, 16.19, 0.38, 32, -17.88, -42.07, 0.09721, 29, 48.78, -111.89, 0.00024, 7, 34, 100.93, -96.87, 1e-05, 35, 104.93, -38.83, 0.00023, 27, 35.49, -0.38, 0.01393, 26, 0.2, 3.1, 0.95682, 32, -33.72, -37.8, 0.0264, 31, -35.07, -97.9, 4e-05, 29, 32.93, -107.62, 0.00258, 7, 34, 85.16, -94.22, 0.00032, 35, 89.16, -36.19, 0.00176, 27, 19.72, 2.27, 0.422, 26, -15.57, 5.74, 0.54075, 32, -49.49, -35.16, 0.02428, 31, -50.85, -95.26, 0.00048, 29, 17.16, -104.97, 0.01041, 7, 34, 69.76, -91.55, 0.00135, 35, 73.76, -33.52, 0.00643, 27, 4.32, 4.94, 0.86004, 26, -30.97, 8.41, 0.10393, 32, -64.89, -32.49, 0.01446, 31, -66.25, -92.59, 0.00062, 29, 1.76, -102.3, 0.01318, 7, 34, 53.07, -90.04, 0.00396, 35, 57.07, -32.01, 0.03014, 28, 26.3, 9.16, 0.26544, 27, -12.37, 6.45, 0.67544, 32, -81.58, -30.98, 0.00578, 31, -82.94, -91.08, 0.00021, 29, -14.93, -100.79, 0.01903, 6, 34, 36.95, -89.69, 0.00417, 35, 40.95, -31.65, 0.06397, 28, 10.18, 9.51, 0.65302, 27, -28.49, 6.8, 0.25997, 32, -97.7, -30.63, 0.00239, 29, -31.05, -100.44, 0.01647, 7, 34, 19.32, -89.41, 0.00107, 35, 23.32, -31.38, 0.16571, 12, 33.7, 18.45, 0.08561, 28, -7.45, 9.79, 0.72165, 27, -46.12, 7.08, 0.0187, 32, -115.33, -30.35, 0.00047, 29, -48.68, -100.16, 0.00678, 4, 35, 0.89, -33.13, 0.28328, 12, 11.2, 18.4, 0.47454, 28, -29.88, 8.04, 0.24211, 29, -71.11, -101.92, 6e-05], "hull": 37, "edges": [68, 70, 68, 66, 66, 64, 64, 62, 58, 60, 60, 62, 56, 58, 54, 56, 52, 54, 52, 50, 50, 48, 48, 46, 46, 44, 44, 42, 42, 40, 40, 38, 38, 36, 36, 34, 34, 32, 32, 30, 28, 30, 28, 26, 26, 24, 24, 22, 22, 20, 20, 18, 18, 16, 16, 14, 70, 72, 72, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 14, 12, 10, 12], "width": 256, "height": 203}}, "chuann_fan2": {"chuann_fan2": {"type": "mesh", "uvs": [0.47401, 0.02593, 0.62952, 0.06575, 0.77167, 0.10761, 0.89903, 0.15614, 1, 0.1915, 1, 0.25586, 0.94559, 0.3188, 0.89404, 0.44324, 0.85246, 0.58793, 0.82528, 0.71705, 0.81429, 0.84128, 0.83605, 0.9461, 0.80028, 0.99999, 0.74761, 1, 0.66831, 0.94253, 0.56522, 0.87959, 0.42546, 0.82953, 0.28867, 0.80235, 0.14978, 0.81801, 0.07754, 0.87292, 0.03292, 0.82398, 0.00682, 0.71283, 1e-05, 0.56227, 0, 0.43023, 0.02188, 0.27146, 0.06351, 0.13272, 0.11505, 0, 0.21803, 0, 0.33999, 0.00966, 0.1877, 0.13746, 0.15842, 0.29205, 0.14222, 0.43991, 0.14319, 0.58692, 0.13875, 0.71731, 0.33324, 0.12488, 0.31073, 0.29309, 0.29218, 0.45555, 0.28953, 0.58362, 0.29351, 0.71933, 0.48424, 0.13826, 0.4657, 0.30264, 0.45775, 0.44791, 0.44716, 0.57979, 0.45113, 0.72315, 0.62465, 0.16693, 0.6114, 0.3122, 0.5997, 0.45149, 0.58778, 0.59349, 0.57078, 0.74186, 0.75313, 0.20325, 0.73118, 0.33621, 0.71132, 0.49129, 0.6952, 0.61936, 0.67559, 0.77887, 0.86515, 0.25923, 0.81974, 0.3892, 0.79251, 0.52268, 0.76676, 0.65567, 0.7467, 0.82334], "triangles": [41, 40, 46, 46, 40, 45, 41, 35, 40, 49, 50, 44, 40, 39, 45, 50, 45, 44, 45, 39, 44, 35, 34, 40, 40, 34, 39, 0, 34, 28, 34, 0, 39, 49, 44, 2, 44, 1, 2, 44, 39, 1, 39, 0, 1, 6, 7, 54, 7, 55, 54, 55, 50, 54, 50, 49, 54, 5, 6, 3, 49, 2, 54, 6, 54, 3, 54, 2, 3, 3, 4, 5, 58, 57, 9, 53, 52, 57, 53, 48, 52, 48, 47, 52, 9, 57, 8, 57, 56, 8, 57, 52, 56, 52, 51, 56, 52, 47, 51, 47, 46, 51, 8, 56, 7, 56, 55, 7, 56, 51, 55, 51, 50, 55, 51, 46, 50, 46, 45, 50, 10, 12, 13, 13, 14, 58, 12, 10, 11, 10, 13, 58, 14, 53, 58, 14, 15, 53, 15, 48, 53, 10, 58, 9, 58, 53, 57, 16, 43, 15, 15, 43, 48, 17, 38, 16, 16, 38, 43, 17, 18, 38, 38, 32, 37, 48, 43, 47, 38, 42, 43, 43, 42, 47, 38, 37, 42, 42, 41, 47, 47, 41, 46, 37, 32, 36, 37, 36, 42, 42, 36, 41, 36, 35, 41, 19, 20, 18, 20, 33, 18, 20, 21, 33, 38, 18, 33, 32, 38, 33, 33, 21, 32, 21, 22, 32, 22, 31, 32, 36, 32, 31, 22, 23, 31, 31, 30, 36, 36, 30, 35, 23, 24, 31, 31, 24, 30, 24, 25, 30, 30, 29, 35, 35, 29, 34, 30, 25, 29, 25, 26, 29, 29, 27, 34, 29, 26, 27, 34, 27, 28], "vertices": [4, 8, 9.13, -36.32, 0.00162, 44, 32.4, 44.55, 0.01035, 7, 12.11, 57.96, 0.00852, 43, 6.15, -1.6, 0.9795, 4, 46, 56.57, -17.33, 0.01068, 44, 33.43, 26.57, 0.18514, 7, 11.78, 39.96, 0.1641, 43, 7.18, -19.58, 0.64009, 4, 46, 57.1, -33.86, 0.0022, 44, 33.96, 10.04, 0.25111, 7, 11.05, 23.44, 0.45546, 43, 7.71, -36.11, 0.29122, 3, 44, 33.58, -4.97, 0.08734, 7, 9.53, 8.5, 0.84962, 43, 7.33, -51.12, 0.06304, 2, 7, 8.57, -3.31, 0.99932, 43, 7.27, -62.97, 0.00068, 1, 7, 3.55, -4.1, 1, 3, 44, 22.3, -13.1, 0.07034, 7, -2.33, 1.25, 0.9248, 43, -3.95, -59.25, 0.00486, 3, 44, 11.38, -9.65, 0.58084, 7, -12.96, 5.51, 0.40611, 43, -14.87, -55.8, 0.01305, 3, 45, 28.88, -3.22, 0.02558, 44, -0.84, -7.68, 0.94816, 7, -24.99, 8.41, 0.02626, 3, 46, 11.66, -50.92, 0.01094, 45, 18.24, -2.56, 0.33315, 44, -11.48, -7.02, 0.6559, 3, 46, 1.83, -51.97, 0.00714, 45, 8.4, -3.6, 0.79304, 44, -21.32, -8.07, 0.19982, 2, 45, 0.91, -7.93, 0.99199, 44, -28.8, -12.39, 0.00801, 1, 45, -4.17, -4.95, 1, 3, 46, -12.13, -47.47, 0.00591, 45, -5.56, 0.9, 0.99375, 44, -35.27, -3.56, 0.00034, 3, 46, -9.8, -37.62, 0.12644, 45, -3.23, 10.74, 0.77514, 44, -32.94, 6.28, 0.09842, 4, 46, -7.67, -25.04, 0.44005, 45, -1.1, 23.32, 0.38134, 44, -30.82, 18.86, 0.17593, 43, -57.07, -27.29, 0.00268, 4, 46, -7.5, -8.63, 0.87392, 45, -0.93, 39.74, 0.07357, 44, -30.65, 35.28, 0.05197, 43, -56.9, -10.87, 0.00053, 3, 41, -26.31, -32.15, 0.00961, 42, -0.29, -24.83, 0.21124, 46, -9.02, 7.04, 0.77915, 3, 41, -31.17, -17.03, 0.00348, 42, -5.15, -9.71, 0.83864, 46, -13.88, 22.16, 0.15788, 2, 42, -11.27, -2.7, 0.99007, 46, -20, 29.17, 0.00993, 1, 42, -8.68, 3.15, 1, 2, 41, -26.85, 0.75, 0.062, 42, -0.83, 8.07, 0.938, 3, 41, -15.45, 4.25, 0.50639, 42, 10.57, 11.57, 0.49352, 46, 1.84, 43.44, 0.0001, 2, 41, -5.3, 6.66, 0.90003, 42, 20.71, 13.98, 0.09997, 2, 8, -18.09, 11.56, 0.2007, 41, 7.48, 7.13, 0.7993, 2, 8, -6.52, 8.58, 0.71338, 41, 19.24, 5.04, 0.28662, 2, 8, 4.75, 4.42, 0.99996, 41, 30.79, 1.74, 4e-05, 4, 8, 6.59, -7.18, 0.83428, 41, 33.5, -9.68, 0.00027, 46, 50.8, 29.51, 0.0015, 43, 1.41, 27.26, 0.16395, 4, 8, 8.01, -21.03, 0.33717, 41, 35.97, -23.38, 0.00312, 46, 53.27, 15.81, 0.00353, 43, 3.87, 13.56, 0.65617, 4, 8, -4.68, -5.46, 0.76979, 41, 22.14, -8.82, 0.11317, 46, 39.43, 30.37, 0.01683, 43, -9.96, 28.12, 0.10021, 5, 8, -17.26, -4.07, 0.3215, 41, 9.49, -8.39, 0.5463, 42, 35.51, -1.07, 0.00584, 46, 26.78, 30.8, 0.05474, 43, -22.61, 28.55, 0.07162, 5, 8, -29.09, -4.07, 0.04365, 41, -2.31, -9.29, 0.68932, 42, 23.71, -1.97, 0.12728, 46, 14.99, 29.9, 0.10303, 43, -34.4, 27.65, 0.03672, 5, 8, -40.54, -6, 0.00314, 41, -13.58, -12.08, 0.33193, 42, 12.44, -4.76, 0.48727, 46, 3.71, 27.11, 0.16324, 43, -45.68, 24.86, 0.01441, 4, 41, -23.72, -13.97, 0.04873, 42, 2.3, -6.65, 0.80086, 46, -6.43, 25.22, 0.14881, 43, -55.82, 22.97, 0.00161, 5, 8, -1.1, -21.69, 0.31556, 41, 26.94, -24.73, 0.03723, 46, 44.23, 14.45, 0.03459, 44, 21.09, 58.36, 0.00054, 43, -5.16, 12.21, 0.61208, 6, 8, -14.63, -21.23, 0.21683, 41, 13.42, -25.31, 0.18195, 42, 39.43, -17.99, 0.00726, 46, 30.71, 13.88, 0.18845, 44, 7.57, 57.79, 0.0116, 43, -18.68, 11.64, 0.39391, 6, 8, -27.64, -21.15, 0.077, 41, 0.44, -26.21, 0.24506, 42, 26.46, -18.89, 0.06629, 46, 17.73, 12.98, 0.40959, 44, -5.41, 56.88, 0.01267, 43, -31.66, 10.73, 0.1894, 6, 8, -37.68, -22.44, 0.01861, 41, -9.47, -28.25, 0.14732, 42, 16.55, -20.93, 0.14305, 46, 7.82, 10.94, 0.61369, 44, -15.32, 54.84, 0.0047, 43, -41.57, 8.69, 0.07263, 5, 8, -48.19, -24.56, 0.001, 41, -19.8, -31.17, 0.0299, 42, 6.22, -23.85, 0.18294, 46, -2.51, 8.02, 0.78032, 43, -51.9, 5.77, 0.00584, 6, 8, 0.55, -38.86, 3e-05, 41, 29.88, -41.73, 0.0028, 46, 47.18, -2.54, 0.02509, 44, 24.03, 41.37, 0.04352, 7, 3.53, 55.42, 0.01962, 43, -2.21, -4.79, 0.90894, 8, 8, -12.61, -38.8, 0.023, 41, 16.76, -42.67, 0.03328, 42, 42.78, -35.35, 0, 46, 34.05, -3.48, 0.20299, 45, 40.63, 44.89, 0.00091, 44, 10.91, 40.42, 0.11442, 7, -9.63, 55.48, 0.01586, 43, -15.34, -5.73, 0.60955, 8, 8, -24.09, -39.7, 0.01625, 41, 5.38, -44.44, 0.04197, 42, 31.4, -37.12, 0.00061, 46, 22.68, -5.25, 0.43512, 45, 29.25, 43.12, 0.01072, 44, -0.46, 38.66, 0.15074, 7, -21.1, 54.58, 0.00631, 43, -26.71, -7.5, 0.33828, 8, 8, -34.57, -40.14, 0.00544, 41, -5.03, -45.67, 0.02299, 42, 20.99, -38.35, 0.00043, 46, 12.26, -6.48, 0.67347, 45, 18.83, 41.89, 0.03092, 44, -10.88, 37.43, 0.12343, 7, -31.58, 54.15, 0.00062, 43, -37.13, -8.73, 0.14269, 6, 8, -45.68, -42.35, 5e-05, 41, -15.95, -48.72, 0.00048, 46, 1.35, -9.53, 0.81541, 45, 7.92, 38.83, 0.0756, 44, -21.8, 34.37, 0.08531, 43, -48.04, -11.78, 0.02315, 5, 41, 31.37, -57.82, 8e-05, 46, 48.67, -18.63, 0.02795, 44, 25.53, 25.27, 0.2199, 7, 3.8, 39.26, 0.16272, 43, -0.72, -20.88, 0.58935, 6, 41, 19.86, -59, 0.00205, 46, 37.15, -19.81, 0.10982, 45, 43.73, 28.55, 0.00182, 44, 14.01, 24.09, 0.33173, 7, -7.78, 38.96, 0.10754, 43, -12.24, -22.06, 0.44704, 7, 8, -21.84, -55.73, 6e-05, 41, 8.85, -60.25, 0.00344, 46, 26.14, -21.06, 0.23296, 45, 32.71, 27.31, 0.02622, 44, 3, 22.85, 0.42738, 7, -18.85, 38.55, 0.04022, 43, -23.25, -23.31, 0.26971, 7, 8, -33.13, -56.14, 0, 41, -2.38, -61.51, 0.00141, 46, 14.91, -22.33, 0.36621, 45, 21.48, 26.04, 0.11424, 44, -8.23, 21.58, 0.3968, 7, -30.14, 38.14, 0.00438, 43, -34.48, -24.57, 0.11696, 4, 46, 3.06, -23.15, 0.46693, 45, 9.63, 25.22, 0.25893, 44, -20.08, 20.76, 0.24729, 43, -46.33, -25.39, 0.02684, 4, 46, 49.26, -33.55, 0.00782, 44, 26.12, 10.36, 0.29948, 7, 3.25, 24.34, 0.41054, 43, -0.13, -35.8, 0.28216, 4, 46, 38.46, -33.54, 0.03277, 44, 15.32, 10.36, 0.49448, 7, -7.51, 25.17, 0.25612, 43, -10.93, -35.79, 0.21663, 6, 41, 8.72, -73.35, 1e-05, 46, 26.02, -34.16, 0.07899, 45, 32.59, 14.2, 0.01959, 44, 2.88, 9.74, 0.74163, 7, -19.97, 25.49, 0.05576, 43, -23.37, -36.41, 0.10402, 5, 46, 15.75, -34.71, 0.13526, 45, 22.32, 13.65, 0.15905, 44, -7.39, 9.19, 0.66458, 7, -30.25, 25.73, 0.00135, 43, -33.64, -36.96, 0.03976, 4, 46, 2.97, -35.45, 0.18298, 45, 9.54, 12.92, 0.48845, 44, -20.17, 8.46, 0.32166, 43, -46.42, -37.7, 0.00691, 4, 46, 47.9, -46.99, 0.0001, 44, 24.76, -3.09, 0.20025, 7, 0.88, 11.04, 0.72726, 43, -1.49, -49.24, 0.07239, 4, 46, 36.72, -44.33, 0.00318, 44, 13.58, -0.42, 0.57075, 7, -10.07, 14.55, 0.36407, 43, -12.67, -46.58, 0.06201, 4, 46, 25.74, -43.74, 0.00158, 44, 2.6, 0.16, 0.94115, 7, -20.97, 15.96, 0.0458, 43, -23.65, -45.99, 0.01148, 4, 46, 14.84, -43.31, 0.03409, 45, 21.41, 5.05, 0.20024, 44, -8.3, 0.59, 0.76278, 43, -34.55, -45.56, 0.0029, 4, 46, 1.43, -44.14, 0.04892, 45, 8, 4.22, 0.70135, 44, -21.72, -0.24, 0.24953, 43, -47.97, -46.39, 0.00021], "hull": 29, "edges": [52, 50, 50, 48, 48, 46, 44, 46, 42, 44, 42, 40, 40, 38, 38, 36, 36, 34, 34, 32, 32, 30, 30, 28, 28, 26, 24, 26, 24, 22, 22, 20, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 0, 0, 56, 52, 54, 54, 56, 16, 18, 18, 20], "width": 114, "height": 79}}, "chuann_fan3": {"chuann_fan3": {"type": "mesh", "uvs": [0.44496, 0.01924, 0.5779, 0.05481, 0.67914, 0.08751, 0.75896, 0.12146, 0.8498, 0.16215, 0.93183, 0.20035, 1, 0.19121, 1, 0.24551, 0.95882, 0.33431, 0.91624, 0.44734, 0.87366, 0.57928, 0.84739, 0.73992, 0.83833, 0.86448, 0.83833, 1, 0.78599, 1, 0.66821, 0.94048, 0.57672, 0.90415, 0.46166, 0.85301, 0.33978, 0.80188, 0.23831, 0.77228, 0.14382, 0.74855, 0.0708, 0.75462, 0.03692, 0.81102, 0.01239, 0.739, 0, 0.65224, 0, 0.51432, 0, 0.38282, 0.02219, 0.25131, 0.05734, 0.13142, 0.10227, 0.03182, 0.17256, 0.00874, 0.26107, 1e-05, 0.35153, 0, 0.15461, 0.09454, 0.10571, 0.24178, 0.07398, 0.3851, 0.05944, 0.52645, 0.0568, 0.65799, 0.25375, 0.08473, 0.21541, 0.25553, 0.1784, 0.41848, 0.17312, 0.52842, 0.16519, 0.64817, 0.37535, 0.08473, 0.3357, 0.2516, 0.29869, 0.41259, 0.27754, 0.55001, 0.26564, 0.68351, 0.50092, 0.11417, 0.46391, 0.24964, 0.43086, 0.4067, 0.39518, 0.55786, 0.38328, 0.68744, 0.6093, 0.13577, 0.57097, 0.26338, 0.53793, 0.41848, 0.50885, 0.57946, 0.48109, 0.73259, 0.72725, 0.17374, 0.68729, 0.30853, 0.64631, 0.46559, 0.61856, 0.62265, 0.60005, 0.76008, 0.82274, 0.22119, 0.78176, 0.34044, 0.73831, 0.49407, 0.70493, 0.66004, 0.68507, 0.80313, 0.89602, 0.27279, 0.85284, 0.39367, 0.8105, 0.54066, 0.77983, 0.69807, 0.76689, 0.85248], "triangles": [18, 52, 17, 67, 62, 66, 16, 62, 67, 15, 16, 67, 57, 56, 62, 17, 52, 57, 16, 57, 62, 17, 57, 16, 62, 61, 66, 56, 50, 55, 56, 55, 60, 51, 50, 56, 61, 56, 60, 56, 52, 51, 57, 52, 56, 62, 56, 61, 12, 72, 11, 15, 67, 72, 14, 72, 12, 15, 72, 14, 14, 12, 13, 67, 66, 71, 72, 67, 71, 72, 71, 11, 66, 60, 65, 66, 65, 70, 61, 60, 66, 71, 66, 70, 71, 70, 10, 11, 71, 10, 65, 64, 69, 70, 69, 9, 64, 63, 69, 9, 69, 8, 70, 65, 69, 10, 70, 9, 5, 6, 7, 68, 4, 5, 63, 4, 68, 8, 5, 7, 68, 5, 8, 69, 63, 68, 69, 68, 8, 53, 1, 2, 53, 54, 48, 53, 48, 1, 58, 53, 2, 59, 53, 58, 54, 53, 59, 59, 58, 64, 60, 54, 59, 55, 54, 60, 65, 59, 64, 60, 59, 65, 58, 2, 3, 63, 3, 4, 58, 3, 63, 64, 58, 63, 38, 30, 31, 33, 29, 30, 33, 30, 38, 28, 29, 33, 39, 33, 38, 42, 36, 41, 24, 25, 36, 37, 24, 36, 42, 37, 36, 23, 24, 37, 20, 37, 42, 21, 37, 20, 23, 37, 21, 22, 23, 21, 51, 45, 50, 46, 40, 45, 46, 45, 51, 47, 42, 46, 52, 46, 51, 47, 46, 52, 19, 42, 47, 18, 47, 52, 19, 47, 18, 41, 40, 46, 42, 41, 46, 20, 42, 19, 49, 48, 54, 50, 49, 55, 50, 44, 49, 45, 44, 50, 43, 32, 0, 48, 0, 1, 48, 43, 0, 49, 43, 48, 38, 32, 43, 32, 38, 31, 44, 43, 49, 44, 38, 43, 39, 38, 44, 45, 39, 44, 55, 49, 54, 26, 27, 35, 25, 26, 35, 36, 25, 35, 36, 35, 40, 41, 36, 40, 27, 28, 34, 35, 27, 34, 40, 34, 39, 40, 39, 45, 34, 28, 33, 34, 33, 39, 35, 34, 40], "vertices": [3, 47, 12.87, -9.75, 0.88335, 48, 2.03, 54.37, 0.11379, 52, 91.65, 45.88, 0.00286, 5, 47, 16.72, -36.77, 0.38236, 51, 102.59, -33.92, 0.00147, 48, 5.89, 27.35, 0.60313, 54, 65.76, 69.33, 0.00016, 52, 95.51, 18.86, 0.01288, 3, 47, 18.94, -57.58, 0.04891, 48, 8.1, 6.54, 0.95045, 52, 97.72, -1.95, 0.00063, 3, 48, 8.79, -10.22, 0.83934, 16, 6.97, 40.41, 0.10819, 53, 34.66, 32.21, 0.05247, 3, 48, 9.31, -29.37, 0.36072, 16, 7.49, 21.25, 0.50701, 53, 35.18, 13.05, 0.13227, 3, 48, 9.59, -46.74, 0.03294, 16, 7.78, 3.89, 0.96082, 53, 35.46, -4.31, 0.00624, 1, 16, 13.29, -8.79, 1, 1, 16, 6.28, -11.11, 1, 2, 16, -7.8, -7.02, 0.81048, 53, 19.88, -15.21, 0.18952, 3, 16, -25.1, -3.69, 0.14056, 53, 2.59, -11.89, 0.8504, 54, 36.59, -12.34, 0.00905, 3, 48, -43.02, -51.8, 2e-05, 53, -17.15, -9.37, 0.51281, 54, 16.85, -9.82, 0.48716, 3, 53, -39.56, -11.2, 0.00407, 54, -5.56, -11.65, 0.80626, 18, 25.2, -4.75, 0.18967, 2, 54, -22.21, -15.24, 0.18715, 18, 8.55, -8.35, 0.81285, 1, 18, -8.95, -14.14, 1, 2, 18, -12.27, -4.11, 0.99591, 52, -13.28, -61.48, 0.00409, 3, 54, -42.83, 14.12, 0.06257, 18, -12.08, 21.02, 0.59773, 52, -13.08, -36.35, 0.3397, 3, 54, -43.96, 33.22, 0.04139, 18, -13.2, 40.12, 0.20214, 52, -14.21, -17.25, 0.75647, 3, 51, -7.84, -45.78, 0.11864, 18, -13.91, 64.37, 0.00178, 52, -14.92, 7, 0.87958, 4, 47, -94.85, -23.07, 0.00239, 51, -8.99, -20.22, 0.67554, 48, -105.69, 41.05, 8e-05, 52, -16.06, 32.55, 0.32199, 3, 51, -11.62, 0.5, 0.93355, 19, -7.68, -38.57, 0.05907, 52, -18.69, 53.28, 0.00738, 2, 51, -14.56, 19.63, 0.40274, 19, -10.63, -19.43, 0.59726, 2, 51, -19.98, 33.37, 0.04799, 19, -16.05, -5.69, 0.95201, 1, 19, -25.48, -1.61, 1, 1, 19, -17.75, 6.18, 1, 2, 50, -29.59, 0.55, 0.05174, 19, -7.33, 12.27, 0.94826, 2, 50, -11.79, 6.45, 0.68835, 19, 10.47, 18.17, 0.31165, 2, 49, -26.21, 9.92, 0.19362, 50, 5.19, 12.08, 0.80638, 2, 49, -7.83, 11.29, 0.78705, 50, 23.58, 13.45, 0.21295, 2, 49, 9.89, 9.68, 0.73396, 17, -16.2, 13.61, 0.26604, 2, 49, 25.6, 5.33, 0.12685, 17, -0.48, 9.26, 0.87315, 3, 47, -3.09, 42.93, 0.07312, 51, 82.77, 45.78, 0.00012, 17, 6.97, -3.23, 0.92675, 4, 49, 39.8, -23.76, 3e-05, 47, 3.66, 26.33, 0.49846, 51, 89.53, 29.18, 0.00115, 17, 13.72, -19.83, 0.50035, 2, 47, 9.41, 8.98, 0.90721, 17, 19.47, -37.17, 0.09279, 5, 49, 20.83, -7.4, 0.14634, 47, -15.31, 42.7, 0.03915, 51, 70.56, 45.55, 0.00384, 17, -5.25, -3.46, 0.81065, 52, 63.48, 98.32, 2e-05, 6, 49, -1.29, -4.32, 0.89135, 50, 30.11, -2.16, 0.05081, 47, -37.42, 45.77, 0.01446, 51, 48.44, 48.62, 0.01016, 17, -27.37, -0.38, 0.03312, 52, 41.36, 101.4, 0.00011, 5, 49, -21.81, -4.37, 0.26949, 50, 9.6, -2.21, 0.70075, 47, -57.94, 45.72, 0.00779, 51, 27.92, 48.57, 0.01891, 19, 31.86, 9.51, 0.00307, 5, 49, -40.98, -7.63, 0.00089, 50, -9.57, -5.47, 0.568, 47, -77.11, 42.46, 0.00162, 51, 8.75, 45.31, 0.01597, 19, 12.68, 6.25, 0.41352, 2, 51, -8.4, 40.19, 0.00424, 19, -4.47, 1.13, 0.99576, 6, 49, 28.4, -25.98, 0.04562, 50, 59.8, -23.82, 0.0027, 47, -7.74, 24.11, 0.45031, 51, 78.12, 26.96, 0.01254, 17, 2.31, -22.05, 0.48791, 52, 71.05, 79.74, 0.00092, 8, 49, 3.91, -25.94, 0.35757, 50, 35.31, -23.78, 0.0821, 47, -32.22, 24.15, 0.24427, 51, 53.64, 27, 0.10213, 19, 57.57, -12.06, 0.00151, 17, -22.17, -22, 0.20231, 48, -43.06, 88.27, 0.00074, 52, 46.56, 79.78, 0.00938, 8, 49, -19.48, -25.82, 0.24141, 50, 11.92, -23.66, 0.33345, 47, -55.61, 24.27, 0.09533, 51, 30.25, 27.12, 0.22843, 19, 34.18, -11.94, 0.07463, 17, -45.56, -21.88, 0.0186, 48, -66.45, 88.39, 0.00091, 52, 23.18, 79.9, 0.00725, 8, 49, -34.01, -29.51, 0.07215, 50, -2.6, -27.35, 0.25375, 47, -70.14, 20.58, 0.04386, 51, 15.72, 23.43, 0.36017, 19, 19.65, -15.63, 0.26529, 17, -60.09, -25.57, 0.0016, 48, -80.98, 84.7, 0.00049, 52, 8.65, 76.21, 0.00269, 5, 49, -49.97, -33.11, 0.00737, 50, -18.57, -30.95, 0.04587, 47, -86.1, 16.98, 0.00628, 51, -0.24, 19.83, 0.47075, 19, 3.69, -19.23, 0.46973, 4, 49, 36.12, -49.3, 0.00025, 50, 67.53, -47.14, 1e-05, 47, -0.01, 0.79, 0.99468, 17, 10.04, -45.36, 0.00507, 9, 49, 12.06, -48.84, 0.09292, 50, 43.46, -46.68, 0.02905, 47, -24.07, 1.25, 0.59662, 51, 61.79, 4.1, 0.13028, 17, -14.02, -44.9, 0.07263, 48, -34.91, 65.37, 0.02931, 53, -9.04, 107.8, 0.00041, 54, 24.96, 107.35, 0.00188, 52, 54.71, 56.88, 0.0469, 10, 49, -11.07, -48.63, 0.1257, 50, 20.33, -46.47, 0.0887, 47, -47.21, 1.46, 0.27444, 51, 38.66, 4.31, 0.36625, 19, 42.59, -34.75, 0.01119, 17, -37.15, -44.69, 0.02708, 48, -58.04, 65.58, 0.02398, 53, -32.17, 108.01, 0.00043, 54, 1.83, 107.56, 0.00156, 52, 31.58, 57.09, 0.08067, 10, 49, -30.16, -50.45, 0.05428, 50, 1.24, -48.29, 0.07186, 47, -66.29, -0.36, 0.09969, 51, 19.57, 2.49, 0.65474, 19, 23.5, -36.58, 0.0374, 17, -56.24, -46.52, 0.00298, 48, -77.13, 63.76, 0.01032, 53, -51.26, 106.19, 8e-05, 54, -17.25, 105.74, 0.00026, 52, 12.49, 55.27, 0.06841, 6, 49, -48.15, -53.88, 0.0017, 50, -16.75, -51.72, 0.00241, 47, -84.28, -3.79, 0.00425, 51, 1.58, -0.94, 0.9776, 48, -95.12, 60.33, 0.00055, 52, -5.5, 51.83, 0.01349, 8, 49, 40.3, -74.64, 0.00044, 50, 71.7, -72.48, 8e-05, 47, 4.17, -24.55, 0.63418, 51, 90.03, -21.69, 0.00918, 48, -6.67, 39.57, 0.33029, 53, 19.2, 82, 0.00088, 54, 53.21, 81.55, 0.00236, 52, 82.96, 31.08, 0.02258, 9, 49, 20.46, -73.34, 0.0129, 50, 51.87, -71.18, 0.00408, 47, -15.67, -23.25, 0.56461, 51, 70.19, -20.39, 0.0732, 17, -5.62, -69.4, 0.00032, 48, -26.51, 40.87, 0.23036, 53, -0.64, 83.3, 0.00773, 54, 33.37, 82.85, 0.01378, 52, 63.12, 32.38, 0.09302, 9, 49, -1.91, -73.72, 0.03051, 50, 29.49, -71.56, 0.01389, 47, -38.05, -23.63, 0.33908, 51, 47.82, -20.78, 0.22154, 17, -27.99, -69.78, 0.00203, 48, -48.88, 40.49, 0.1344, 53, -23.01, 82.92, 0.01133, 54, 10.99, 82.47, 0.02196, 52, 40.74, 32, 0.22526, 9, 49, -23.7, -73.35, 0.02183, 50, 7.71, -71.18, 0.01333, 47, -59.83, -23.25, 0.14086, 51, 26.03, -20.4, 0.42729, 17, -49.78, -69.41, 0.00034, 48, -70.67, 40.86, 0.0499, 53, -44.8, 83.29, 0.00436, 54, -10.79, 82.85, 0.00951, 52, 18.96, 32.37, 0.33257, 8, 49, -41.18, -76.61, 0.00434, 50, -9.78, -74.45, 0.00175, 47, -77.31, -26.52, 0.03471, 51, 8.55, -23.67, 0.52577, 48, -88.15, 37.6, 0.01169, 53, -62.28, 80.03, 0.00058, 54, -28.28, 79.58, 0.00081, 52, 1.47, 29.11, 0.42036, 7, 49, 44.4, -96.34, 0, 47, 8.27, -46.25, 0.23005, 51, 94.13, -43.4, 0.00365, 48, -2.57, 17.87, 0.74055, 53, 23.3, 60.3, 0.00295, 54, 57.31, 59.85, 0.00407, 52, 87.06, 9.38, 0.01873, 8, 49, 25.49, -94.45, 0.00153, 50, 56.9, -92.29, 0.00021, 47, -10.64, -44.36, 0.28005, 51, 75.22, -41.51, 0.031, 48, -21.48, 19.76, 0.51921, 53, 4.39, 62.19, 0.0345, 54, 38.4, 61.74, 0.03414, 52, 68.15, 11.27, 0.09934, 8, 49, 3.37, -94.75, 0.0058, 50, 34.77, -92.59, 0.00146, 47, -32.76, -44.66, 0.21641, 51, 53.1, -41.81, 0.08889, 48, -43.6, 19.46, 0.27545, 53, -17.73, 61.89, 0.05265, 54, 16.27, 61.44, 0.07821, 52, 46.02, 10.97, 0.28113, 9, 49, -19.26, -96.07, 0.00474, 50, 12.14, -93.9, 0.00114, 47, -55.39, -45.97, 0.10042, 51, 30.47, -43.12, 0.13296, 48, -66.23, 18.14, 0.10138, 53, -40.36, 60.57, 0.02522, 54, -6.36, 60.13, 0.06677, 18, 24.4, 67.02, 0.00014, 52, 23.39, 9.65, 0.56724, 8, 49, -40.79, -97.3, 0.00069, 50, -9.39, -95.13, 1e-05, 47, -76.93, -47.2, 0.01366, 51, 8.94, -44.35, 0.11687, 48, -87.76, 16.91, 0.00924, 53, -61.89, 59.34, 0.00143, 54, -27.89, 58.9, 0.00319, 52, 1.86, 8.42, 0.85492, 6, 51, 96.73, -67.64, 3e-05, 48, 0.03, -6.37, 0.90144, 16, -1.79, 44.26, 0.0497, 53, 25.9, 36.06, 0.04498, 54, 59.9, 35.61, 0.002, 52, 89.65, -14.86, 0.00185, 8, 49, 27.06, -118.69, 0, 47, -9.08, -68.6, 0.04377, 51, 76.78, -65.75, 0.00629, 48, -19.91, -4.48, 0.61189, 16, -21.73, 46.15, 0.0132, 53, 5.96, 37.95, 0.18781, 54, 39.96, 37.5, 0.06743, 52, 69.71, -12.97, 0.06961, 8, 49, 4.18, -117.55, 0.00031, 47, -31.96, -67.46, 0.06783, 51, 53.91, -64.61, 0.01848, 48, -42.79, -3.34, 0.28147, 53, -16.92, 39.09, 0.17844, 54, 17.08, 38.64, 0.21443, 18, 47.84, 45.54, 0.00046, 52, 46.83, -11.83, 0.23859, 8, 49, -17.86, -118.95, 0.00021, 47, -54, -68.86, 0.03403, 51, 31.87, -66.01, 0.01215, 48, -64.83, -4.74, 0.09596, 53, -38.96, 37.69, 0.06112, 54, -4.96, 37.24, 0.2646, 18, 25.8, 44.14, 0.03536, 52, 24.79, -13.23, 0.49657, 6, 47, -72.91, -71.19, 0.00398, 48, -83.75, -7.07, 0.01532, 53, -57.88, 35.36, 0.00643, 54, -23.88, 34.91, 0.13479, 18, 6.88, 41.81, 0.13119, 52, 5.87, -15.56, 0.7083, 5, 48, -0.03, -26.71, 0.41056, 16, -1.85, 23.92, 0.38566, 53, 25.84, 15.72, 0.20212, 54, 59.84, 15.27, 0.00058, 52, 89.59, -35.2, 0.00109, 7, 47, -7.19, -88.08, 0.00245, 51, 78.67, -85.22, 0.00026, 48, -18.03, -23.96, 0.3323, 16, -19.85, 26.67, 0.10768, 53, 7.84, 18.47, 0.48968, 54, 41.84, 18.02, 0.04442, 52, 71.59, -32.45, 0.0232, 7, 47, -29.79, -86.32, 0.01347, 51, 56.07, -83.47, 0.00149, 48, -40.62, -22.2, 0.15578, 16, -42.44, 28.43, 0.00014, 53, -14.75, 20.23, 0.38823, 54, 19.25, 19.78, 0.33895, 52, 49, -30.69, 0.10194, 7, 47, -53.33, -87.02, 0.00785, 51, 32.53, -84.17, 0.00018, 48, -64.17, -22.9, 0.04626, 53, -38.3, 19.53, 0.05907, 54, -4.3, 19.08, 0.5573, 18, 26.46, 25.98, 0.08625, 52, 25.45, -31.39, 0.24309, 6, 47, -73.07, -89.33, 0.00051, 48, -83.9, -25.21, 0.00603, 53, -58.04, 17.21, 0.00081, 54, -24.03, 16.77, 0.25398, 18, 6.73, 23.66, 0.39799, 52, 5.72, -33.71, 0.34069, 3, 48, -2.03, -42.97, 0.07879, 16, -3.85, 7.66, 0.73878, 53, 23.83, -0.54, 0.18243, 4, 48, -20.38, -39.86, 0.04808, 16, -22.2, 10.76, 0.12077, 53, 5.49, 2.57, 0.82954, 52, 69.24, -48.35, 0.00162, 5, 47, -31.21, -102.15, 0.00119, 48, -42.05, -38.03, 0.02635, 53, -16.18, 4.4, 0.50632, 54, 17.82, 3.95, 0.44749, 52, 47.57, -46.52, 0.01865, 5, 47, -53.48, -103.01, 0.00036, 48, -64.32, -38.89, 0.00387, 54, -4.45, 3.09, 0.86498, 18, 26.31, 9.99, 0.09374, 52, 25.3, -47.38, 0.03705, 4, 48, -85.07, -43.02, 7e-05, 54, -25.2, -1.03, 0.18101, 18, 5.56, 5.86, 0.76792, 52, 4.55, -51.51, 0.051], "hull": 33, "edges": [60, 58, 58, 56, 56, 54, 54, 52, 50, 52, 48, 50, 48, 46, 46, 44, 44, 42, 42, 40, 40, 38, 38, 36, 36, 34, 34, 32, 32, 30, 30, 28, 26, 28, 26, 24, 24, 22, 22, 20, 20, 18, 18, 16, 16, 14, 60, 62, 62, 64, 64, 0, 0, 2, 8, 10, 14, 12, 10, 12, 2, 4, 4, 6, 6, 8], "width": 202, "height": 136}}, "chuann_xian1": {"chuann_xian1": {"type": "mesh", "uvs": [1, 0.07716, 0.95642, 0.2067, 0.89111, 0.29768, 0.81606, 0.34461, 0.75828, 0.3924, 0.69716, 0.43595, 0.62364, 0.47944, 0.5627, 0.52849, 0.50485, 0.58412, 0.45221, 0.6622, 0.39258, 0.75485, 0.3403, 0.84394, 0.29293, 0.90762, 0.24919, 1, 0.22341, 1, 0.22123, 0.9298, 0.25062, 0.84323, 0.22785, 0.68505, 0.19849, 0.53956, 0.16204, 0.41142, 0.11937, 0.30372, 0.07014, 0.24979, 0.03344, 0.21101, 0, 0.17081, 0, 0.05186, 0.0409, 0.11458, 0.07596, 0.16183, 0.11933, 0.20909, 0.16632, 0.25042, 0.21332, 0.29176, 0.26371, 0.3295, 0.31549, 0.36259, 0.37133, 0.39145, 0.43608, 0.39998, 0.49368, 0.4047, 0.55855, 0.39757, 0.62112, 0.36905, 0.68475, 0.3305, 0.75096, 0.29605, 0.81256, 0.24784, 0.88283, 0.18164, 0.94286, 0.10506, 0.97842, 0, 1, 0], "triangles": [3, 39, 2, 39, 40, 2, 1, 40, 41, 1, 2, 40, 0, 41, 42, 0, 1, 41, 42, 43, 0, 7, 35, 6, 35, 36, 6, 6, 37, 5, 6, 36, 37, 5, 38, 4, 5, 37, 38, 4, 38, 3, 38, 39, 3, 11, 17, 31, 11, 32, 10, 11, 31, 32, 31, 17, 30, 10, 33, 9, 10, 32, 33, 17, 18, 30, 9, 34, 8, 9, 33, 34, 8, 35, 7, 8, 34, 35, 18, 29, 30, 18, 19, 29, 19, 28, 29, 13, 16, 12, 16, 13, 15, 13, 14, 15, 12, 16, 11, 17, 11, 16, 19, 20, 28, 21, 27, 20, 20, 27, 28, 21, 26, 27, 21, 22, 26, 22, 25, 26, 22, 23, 25, 23, 24, 25], "vertices": [2, 39, 94.77, -96.05, 0.00058, 36, -0.11, -7.15, 0.99942, 2, 39, 73.25, -87.94, 0.06872, 36, -22.19, -0.7, 0.93128, 2, 39, 51.37, -70.05, 0.27403, 36, -45.36, 15.48, 0.72597, 2, 39, 32.03, -46.28, 0.5903, 36, -66.45, 37.71, 0.4097, 2, 39, 15.96, -28.63, 0.83434, 36, -83.81, 54.1, 0.16566, 2, 39, -0.33, -9.56, 0.98419, 36, -101.51, 71.87, 0.01581, 2, 40, 44.4, -91.15, 0.16498, 39, -19.03, 13.86, 0.83502, 2, 40, 27.59, -72.46, 0.4744, 39, -35.84, 32.55, 0.5256, 2, 40, 10.72, -55.22, 0.73877, 39, -52.71, 49.79, 0.26123, 3, 38, 74.94, -59.08, 0.00716, 40, -7.45, -41.05, 0.89664, 39, -70.88, 63.96, 0.0962, 4, 37, 10.36, -166.37, 0.00034, 38, 53.59, -43.19, 0.11232, 40, -28.81, -25.16, 0.8747, 39, -92.23, 79.85, 0.01264, 4, 37, -9.59, -152.79, 0.00592, 38, 33.64, -29.61, 0.36961, 40, -48.76, -11.58, 0.62438, 39, -112.18, 93.43, 9e-05, 3, 37, -26.38, -139.45, 0.00863, 38, 16.85, -16.27, 0.69291, 40, -65.55, 1.76, 0.29846, 3, 37, -45.47, -128.96, 0.00021, 38, -2.23, -5.78, 0.99188, 40, -84.63, 12.25, 0.00791, 1, 38, -7.3, 3.29, 1, 2, 37, -43.9, -115.21, 2e-05, 38, -0.66, 7.97, 0.99998, 3, 37, -28.47, -120.92, 0.01285, 38, 14.77, 2.26, 0.78397, 40, -67.63, 20.29, 0.20318, 3, 37, -15.32, -104.44, 0.07084, 38, 27.92, 18.74, 0.45006, 40, -54.48, 36.77, 0.4791, 3, 37, -5.59, -86.17, 0.17394, 38, 37.64, 37.01, 0.2963, 40, -44.75, 55.04, 0.52977, 3, 37, 0.44, -66.26, 0.34098, 38, 43.67, 56.92, 0.21402, 40, -38.73, 74.95, 0.445, 3, 37, 2.97, -45.26, 0.59452, 38, 46.21, 77.92, 0.12914, 40, -36.19, 95.95, 0.27634, 3, 37, -1.2, -24.94, 0.85963, 38, 42.04, 98.24, 0.04428, 40, -40.36, 116.27, 0.09609, 3, 37, -4.45, -9.87, 0.97362, 38, 38.79, 113.31, 0.00828, 40, -43.61, 131.34, 0.01811, 1, 37, -6.92, 4.13, 1, 3, 37, 5.04, 10.76, 0.99997, 38, 48.28, 133.94, 1e-05, 40, -34.12, 151.97, 2e-05, 3, 37, 6.71, -7.12, 0.97784, 38, 49.94, 116.06, 0.00695, 40, -32.46, 134.09, 0.01521, 3, 37, 8.79, -22.08, 0.86886, 38, 52.02, 101.1, 0.04135, 40, -30.38, 119.13, 0.08978, 3, 37, 12.48, -39.96, 0.64737, 38, 55.72, 83.22, 0.11207, 40, -26.68, 101.25, 0.24056, 3, 37, 17.48, -58.79, 0.41025, 38, 60.72, 64.39, 0.18861, 40, -21.68, 82.42, 0.40115, 3, 37, 22.48, -77.62, 0.24007, 38, 65.71, 45.56, 0.2374, 40, -16.69, 63.59, 0.52253, 3, 37, 28.51, -97.45, 0.11782, 38, 71.74, 25.73, 0.23009, 40, -10.66, 43.77, 0.6521, 3, 37, 35.21, -117.48, 0.03965, 38, 78.45, 5.7, 0.13272, 40, -3.95, 23.73, 0.82763, 2, 37, 43.13, -138.71, 0.00025, 40, 3.97, 2.5, 0.99975, 2, 40, 15.83, -20.77, 0.91216, 39, -47.59, 84.24, 0.08784, 2, 40, 26.59, -41.29, 0.74772, 39, -36.84, 63.72, 0.25228, 2, 40, 39.95, -63.7, 0.46423, 39, -23.48, 41.31, 0.53577, 2, 40, 55.01, -84.11, 0.15216, 39, -8.42, 20.9, 0.84784, 2, 39, 7.86, 0.68, 0.99155, 36, -94.12, 82.7, 0.00845, 2, 39, 24.23, -20.68, 0.83994, 36, -76.18, 62.64, 0.16006, 2, 39, 41.08, -39.65, 0.57807, 36, -57.93, 45.01, 0.42193, 2, 39, 61.43, -60.67, 0.26808, 36, -36.04, 25.59, 0.73192, 2, 39, 80.83, -77.51, 0.06319, 36, -15.42, 10.28, 0.93681, 2, 39, 98.33, -84.16, 0.0006, 36, 2.53, 4.98, 0.9994, 1, 36, 7.3, -2.27, 1], "hull": 44, "edges": [48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 84, 86, 82, 84, 0, 86, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 46, 48, 44, 46], "width": 402, "height": 115}}, "chuann_xian2": {"chuann_xian2": {"type": "mesh", "uvs": [0.53379, 0.01664, 0.65274, 0.01669, 0.71939, 0.07126, 0.74622, 0.28567, 0.79545, 0.39258, 0.83231, 0.47262, 0.8623, 0.53777, 0.89963, 0.61884, 0.92384, 0.67141, 0.98365, 0.6923, 0.98328, 0.96446, 0.86388, 0.99365, 0.86419, 0.80116, 0.8445, 0.7148, 0.81962, 0.60569, 0.79615, 0.54031, 0.76473, 0.45275, 0.71145, 0.34496, 0.60941, 0.31049, 0.52269, 0.2812, 0.45466, 0.25822, 0.41279, 0.28256, 0.36367, 0.31113, 0.3089, 0.34298, 0.25571, 0.37391, 0.17583, 0.42037, 0.09576, 0.484, 0.03737, 0.6633, 0.00832, 0.64529, 0.00839, 0.49975, 0.03378, 0.39581, 0.07165, 0.31791, 0.15512, 0.35446, 0.23693, 0.29849, 0.29846, 0.25639, 0.35854, 0.21529, 0.39713, 0.18888, 0.43909, 0.16018, 0.5023, 0.11693], "triangles": [7, 13, 14, 7, 14, 6, 8, 12, 13, 8, 13, 7, 9, 12, 8, 10, 12, 9, 11, 12, 10, 16, 3, 4, 16, 4, 5, 15, 16, 5, 6, 14, 15, 6, 15, 5, 25, 32, 33, 26, 31, 32, 26, 32, 25, 30, 31, 26, 29, 30, 26, 27, 29, 26, 28, 29, 27, 21, 36, 37, 20, 21, 37, 21, 22, 35, 21, 35, 36, 34, 35, 22, 23, 34, 22, 24, 33, 34, 23, 24, 34, 25, 33, 24, 17, 3, 16, 20, 37, 38, 18, 19, 38, 20, 38, 19, 18, 0, 1, 18, 1, 2, 17, 18, 2, 18, 38, 0, 3, 17, 2], "vertices": [1, 6, 286.44, 11.69, 1, 1, 6, 289.68, -1.97, 1, 1, 6, 288.41, -10.35, 1, 1, 6, 277.05, -16.3, 1, 3, 6, 272.35, -23.39, 0.0289, 14, 5.52, -55.19, 1e-05, 15, 1.98, 0.49, 0.97109, 2, 15, -1.53, -4.81, 0.85944, 7, 17.31, 10.01, 0.14056, 2, 15, -4.39, -9.13, 0.59836, 7, 14.13, 5.92, 0.40164, 1, 7, 10.18, 0.84, 1, 1, 7, 7.61, -2.46, 1, 1, 7, 7.52, -9.62, 1, 1, 7, -8.08, -12.05, 1, 1, 7, -11.95, 1.61, 1, 1, 7, -0.92, 3.32, 1, 1, 7, 3.66, 6.39, 1, 2, 15, -9.39, -5.14, 0.58923, 7, 9.45, 10.28, 0.41077, 2, 15, -6.34, -1.57, 0.84178, 7, 12.77, 13.61, 0.15822, 4, 6, 268.12, -20.66, 0.03519, 14, 1.28, -52.47, 1e-05, 15, -2.25, 3.21, 0.96148, 7, 17.2, 18.07, 0.00332, 1, 6, 272.75, -13.1, 1, 1, 6, 271.92, -0.93, 1, 1, 6, 271.21, 9.42, 1, 3, 6, 270.65, 17.54, 0.51184, 14, 3.82, -14.27, 0.4876, 15, 0.28, 41.42, 0.00056, 3, 6, 268.14, 22.02, 0.24466, 14, 1.3, -9.79, 0.75502, 15, -2.24, 45.9, 0.00032, 3, 6, 265.19, 27.28, 0.04244, 14, -1.65, -4.53, 0.95749, 15, -5.19, 51.16, 6e-05, 2, 14, -5, 1.6, 0.96167, 8, 13.16, -28.84, 0.03833, 2, 14, -8.47, 8.44, 0.79593, 8, 10.23, -21.76, 0.20407, 1, 8, 5.21, -7.29, 1, 1, 8, 0.08, 1.47, 1, 1, 8, -11.26, 6.65, 1, 1, 8, -10.77, 10.2, 1, 1, 8, -2.43, 11.51, 1, 1, 8, 3.99, 9.49, 1, 1, 8, 9.15, 5.78, 1, 1, 8, 8.6, -4.28, 1, 2, 14, -4.77, 11.8, 0.76783, 8, 14.17, -18.69, 0.23217, 2, 14, -0.39, 3.91, 0.96798, 8, 17.94, -26.88, 0.03202, 3, 6, 270.46, 29.15, 0.06551, 14, 3.62, -2.66, 0.93439, 15, 0.08, 53.03, 9e-05, 3, 6, 273, 25.07, 0.23594, 14, 6.16, -6.73, 0.76375, 15, 2.63, 48.95, 0.00031, 3, 6, 275.76, 20.64, 0.47841, 14, 8.92, -11.17, 0.52106, 15, 5.39, 44.52, 0.00053, 1, 6, 279.92, 13.96, 1], "hull": 39, "edges": [0, 76, 0, 2, 2, 4, 4, 6, 16, 18, 18, 20, 20, 22, 22, 24, 32, 34, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 48, 50, 64, 66, 46, 48, 66, 68, 44, 46, 68, 70, 74, 76, 40, 42, 42, 44, 70, 72, 72, 74, 6, 8, 28, 30, 30, 32, 8, 10, 10, 12, 12, 14, 14, 16, 24, 26, 26, 28, 38, 40, 34, 36, 36, 38], "width": 118, "height": 58}}, "chuann_xian3": {"chuann_xian3": {"x": 1.46, "y": -8.68, "rotation": -81, "width": 23, "height": 26}}, "chuann_xian4": {"chuann_xian4": {"x": -1.57, "y": -10.15, "rotation": -81, "width": 21, "height": 25}}, "chuann_xian5": {"chuann_xian5": {"x": -0.04, "y": 6.84, "rotation": -81, "width": 15, "height": 24}}, "h1": {"h1": {"x": 30.48, "y": 20.07, "rotation": -71.26, "width": 10, "height": 15}}, "h2": {"h2": {"x": 26.72, "y": -37.51, "rotation": -61.68, "width": 18, "height": 16}}, "h3": {"h3": {"x": 14.42, "y": -40.93, "rotation": -62.4, "width": 17, "height": 11}}, "h4": {"h4": {"x": 11.14, "y": 40.15, "rotation": -69, "width": 13, "height": 14}}, "h5": {"h5": {"x": 3.63, "y": 46.75, "rotation": -56.85, "width": 15, "height": 15}}, "js_2": {"js_2": {"x": 48.56, "y": -3.97, "rotation": -63.15, "width": 87, "height": 77}}, "js_2a": {"js_2a": {"x": 35.71, "y": 6.05, "rotation": -65.3, "width": 54, "height": 49}}, "js_2b": {"js_2b": {"x": -2.34, "y": 0.72, "rotation": -62.51, "width": 28, "height": 21}}, "tong": {"tong": {"type": "mesh", "uvs": [0.57323, 0.03962, 0.79223, 0.12555, 0.92366, 0.18798, 1, 0.25475, 1, 0.41698, 0.96142, 0.63891, 0.8754, 0.79538, 0.6912, 1, 0.58925, 1, 0.38847, 0.97191, 0.14886, 0.88743, 0, 0.77299, 0, 0.63954, 0.04078, 0.41191, 0.10217, 0.26248, 0.20332, 0.12131, 0.30507, 0, 0.40016, 0, 0.50164, 0.50411, 0.19301, 0.38181, 0.32685, 0.44227, 0.65811, 0.56551, 0.81779, 0.61593, 0.30881, 0.18115, 0.44963, 0.23511, 0.60932, 0.29202, 0.76144, 0.33997, 0.89526, 0.39395, 0.12581, 0.61224, 0.25591, 0.68766, 0.39461, 0.7511, 0.55588, 0.79604, 0.71276, 0.81457], "triangles": [7, 32, 6, 7, 8, 32, 9, 31, 8, 8, 31, 32, 9, 10, 30, 9, 30, 31, 30, 10, 29, 10, 11, 29, 29, 11, 28, 31, 21, 32, 32, 22, 6, 32, 21, 22, 21, 31, 18, 6, 22, 5, 11, 12, 28, 31, 30, 18, 30, 29, 18, 29, 20, 18, 29, 28, 20, 12, 13, 28, 22, 27, 5, 5, 27, 4, 22, 21, 27, 28, 19, 20, 28, 13, 19, 18, 25, 21, 21, 26, 27, 21, 25, 26, 20, 24, 18, 18, 24, 25, 19, 23, 20, 20, 23, 24, 27, 3, 4, 13, 14, 19, 27, 2, 3, 27, 26, 2, 23, 14, 15, 23, 19, 14, 26, 1, 2, 26, 25, 1, 24, 0, 25, 25, 0, 1, 23, 17, 24, 24, 17, 0, 15, 16, 23, 23, 16, 17], "vertices": [2, 56, 61.35, 15.3, 0.9409, 60, 109.76, 14.84, 0.0591, 2, 56, 61.29, -13.96, 0.92885, 60, 109.7, -14.42, 0.07115, 2, 56, 59.91, -32.08, 0.92348, 60, 108.33, -32.54, 0.07652, 2, 56, 55.38, -44.18, 0.93017, 60, 103.79, -44.64, 0.06983, 2, 56, 35.5, -52.56, 0.94005, 60, 83.91, -53.02, 0.05995, 2, 56, 6.45, -59.66, 0.93177, 60, 54.87, -60.12, 0.06823, 2, 56, -16.83, -57.99, 0.92284, 60, 31.58, -58.45, 0.07716, 2, 56, -50.71, -47.69, 0.92, 60, -2.3, -48.15, 0.08, 2, 56, -55.58, -36.13, 0.92, 60, -7.17, -36.59, 0.08, 2, 56, -61.73, -11.92, 0.93313, 60, -13.32, -12.38, 0.06687, 2, 56, -62.83, 19.6, 0.93403, 60, -14.42, 19.14, 0.06597, 2, 56, -55.92, 42.38, 0.93308, 60, -7.5, 41.92, 0.06692, 2, 56, -39.56, 49.28, 0.95937, 60, 8.85, 48.82, 0.04063, 3, 56, -9.72, 56.42, 0.94391, 59, -11.4, 50.8, 0.01561, 60, 38.7, 55.96, 0.04048, 3, 56, 11.53, 57.18, 0.95803, 59, 9.85, 51.57, 0.0084, 60, 59.94, 56.72, 0.03357, 2, 56, 33.66, 53.01, 0.98362, 60, 82.08, 52.55, 0.01638, 2, 56, 53.39, 47.74, 0.9616, 60, 101.81, 47.28, 0.0384, 2, 56, 57.94, 36.96, 0.95321, 60, 106.35, 36.51, 0.04679, 2, 56, 1, -0.58, 0.9, 59, -0.68, -6.2, 0.1, 3, 56, 1.25, 40.72, 0.91753, 59, -0.44, 35.1, 0.07073, 60, 49.66, 40.26, 0.01174, 2, 56, 0.23, 22.42, 0.9, 59, -1.45, 16.81, 0.1, 2, 56, 0.96, -21.49, 0.90312, 59, -0.73, -27.1, 0.09688, 3, 56, 2.41, -42.19, 0.93686, 59, 0.72, -47.8, 0.03817, 60, 50.82, -42.65, 0.02497, 2, 56, 31.37, 37.96, 0.96864, 59, 29.69, 32.35, 0.03136, 2, 56, 31.49, 19.21, 0.93486, 59, 29.8, 13.6, 0.06514, 2, 56, 32.14, -1.83, 0.92721, 59, 30.46, -7.44, 0.07279, 3, 56, 33.54, -21.55, 0.94542, 59, 31.85, -27.16, 0.04855, 60, 81.95, -22.01, 0.00603, 3, 56, 33.31, -39.5, 0.96398, 59, 31.63, -45.12, 0.00976, 60, 81.73, -39.96, 0.02627, 3, 56, -30.2, 36.43, 0.94345, 59, -31.89, 30.82, 0.04426, 60, 18.21, 35.97, 0.01229, 3, 56, -33.23, 17.78, 0.92358, 59, -34.92, 12.17, 0.06617, 60, 15.18, 17.33, 0.01025, 3, 56, -34.38, -1.21, 0.92307, 59, -36.06, -6.82, 0.06994, 60, 14.03, -1.67, 0.00699, 3, 56, -32.18, -21.81, 0.93684, 59, -33.87, -27.42, 0.04956, 60, 16.23, -22.27, 0.0136, 3, 56, -26.95, -40.55, 0.9419, 59, -28.64, -46.16, 0.01386, 60, 21.46, -41.01, 0.04424], "hull": 18, "edges": [34, 0, 0, 2, 2, 4, 4, 6, 32, 34, 32, 30, 30, 28, 28, 26, 26, 24, 22, 24, 22, 20, 20, 18, 18, 16, 14, 16, 14, 12, 12, 10, 6, 8, 10, 8], "width": 123, "height": 133}}, "tx/d0": {"tx/d0": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}, "tx/d1": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}, "tx/d2": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}, "tx/d3": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}, "tx/d4": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}, "tx/d5": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}, "tx/d6": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}, "tx/d7": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}, "tx/d8": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}, "tx/d9": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}, "tx/d10": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}, "tx/d11": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}, "tx/d12": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}, "tx/d13": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}, "tx/d14": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}, "tx/d15": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}, "tx/d16": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}, "tx/d17": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}}, "tx/d1": {"tx/d0": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}, "tx/d1": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}, "tx/d2": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}, "tx/d3": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}, "tx/d4": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}, "tx/d5": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}, "tx/d6": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}, "tx/d7": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}, "tx/d8": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}, "tx/d9": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}, "tx/d10": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}, "tx/d11": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}, "tx/d12": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}, "tx/d13": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}, "tx/d14": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}, "tx/d15": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}, "tx/d16": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}, "tx/d17": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}}, "tx/d2": {"tx/d0": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}, "tx/d1": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}, "tx/d2": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}, "tx/d3": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}, "tx/d4": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}, "tx/d5": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}, "tx/d6": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}, "tx/d7": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}, "tx/d8": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}, "tx/d9": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}, "tx/d10": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}, "tx/d11": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}, "tx/d12": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}, "tx/d13": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}, "tx/d14": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}, "tx/d15": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}, "tx/d16": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}, "tx/d17": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}}, "tx/d3": {"tx/d0": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}, "tx/d1": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}, "tx/d2": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}, "tx/d3": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}, "tx/d4": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}, "tx/d5": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}, "tx/d6": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}, "tx/d7": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}, "tx/d8": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}, "tx/d9": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}, "tx/d10": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}, "tx/d11": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}, "tx/d12": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}, "tx/d13": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}, "tx/d14": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}, "tx/d15": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}, "tx/d16": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}, "tx/d17": {"x": 264.7, "y": 33.86, "width": 574, "height": 89}}}}], "animations": {"approach": {"slots": {"h1": {"attachment": [{"time": 0.1667, "name": null}, {"time": 0.3, "name": "h1"}, {"time": 0.4667, "name": null}, {"time": 0.6, "name": "h1"}, {"time": 0.7333, "name": null}, {"time": 0.8667, "name": "h1"}, {"time": 1.0333, "name": null}, {"time": 1.1667, "name": "h1"}, {"time": 1.3, "name": null}, {"time": 1.4333, "name": "h1"}, {"time": 1.6, "name": null}, {"time": 1.7333, "name": "h1"}, {"time": 1.8667, "name": null}, {"time": 2.0333, "name": "h1"}, {"time": 2.1667, "name": null}, {"time": 2.3, "name": "h1"}, {"time": 2.4667, "name": null}, {"time": 2.6, "name": "h1"}, {"time": 2.7333, "name": null}, {"time": 2.8667, "name": "h1"}, {"time": 3.0333, "name": null}, {"time": 3.1667, "name": "h1"}, {"time": 3.3, "name": null}, {"time": 3.4333, "name": "h1"}, {"time": 3.6, "name": null}, {"time": 3.7333, "name": "h1"}, {"time": 3.8667, "name": null}, {"time": 4.0333, "name": "h1"}, {"time": 4.1667, "name": null}, {"time": 4.3, "name": "h1"}, {"time": 4.4667, "name": null}, {"time": 4.6, "name": "h1"}, {"time": 4.7333, "name": null}, {"time": 4.8667, "name": "h1"}, {"time": 5.0333, "name": null}, {"time": 5.1667, "name": "h1"}, {"time": 5.3, "name": null}, {"time": 5.4333, "name": "h1"}, {"time": 5.6, "name": null}, {"time": 5.7333, "name": "h1"}, {"time": 5.8667, "name": null}]}, "h2": {"attachment": [{"time": 0.1667, "name": null}, {"time": 0.3, "name": "h2"}, {"time": 0.4667, "name": null}, {"time": 0.6, "name": "h2"}, {"time": 0.7333, "name": null}, {"time": 0.8667, "name": "h2"}, {"time": 1.0333, "name": null}, {"time": 1.1667, "name": "h2"}, {"time": 1.3, "name": null}, {"time": 1.4333, "name": "h2"}, {"time": 1.6, "name": null}, {"time": 1.7333, "name": "h2"}, {"time": 1.8667, "name": null}, {"time": 2.0333, "name": "h2"}, {"time": 2.1667, "name": null}, {"time": 2.3, "name": "h2"}, {"time": 2.4667, "name": null}, {"time": 2.6, "name": "h2"}, {"time": 2.7333, "name": null}, {"time": 2.8667, "name": "h2"}, {"time": 3.0333, "name": null}, {"time": 3.1667, "name": "h2"}, {"time": 3.3, "name": null}, {"time": 3.4333, "name": "h2"}, {"time": 3.6, "name": null}, {"time": 3.7333, "name": "h2"}, {"time": 3.8667, "name": null}, {"time": 4.0333, "name": "h2"}, {"time": 4.1667, "name": null}, {"time": 4.3, "name": "h2"}, {"time": 4.4667, "name": null}, {"time": 4.6, "name": "h2"}, {"time": 4.7333, "name": null}, {"time": 4.8667, "name": "h2"}, {"time": 5.0333, "name": null}, {"time": 5.1667, "name": "h2"}, {"time": 5.3, "name": null}, {"time": 5.4333, "name": "h2"}, {"time": 5.6, "name": null}, {"time": 5.7333, "name": "h2"}, {"time": 5.8667, "name": null}]}, "h3": {"attachment": [{"time": 0.1667, "name": null}, {"time": 0.3, "name": "h3"}, {"time": 0.4667, "name": null}, {"time": 0.6, "name": "h3"}, {"time": 0.7333, "name": null}, {"time": 0.8667, "name": "h3"}, {"time": 1.0333, "name": null}, {"time": 1.1667, "name": "h3"}, {"time": 1.3, "name": null}, {"time": 1.4333, "name": "h3"}, {"time": 1.6, "name": null}, {"time": 1.7333, "name": "h3"}, {"time": 1.8667, "name": null}, {"time": 2.0333, "name": "h3"}, {"time": 2.1667, "name": null}, {"time": 2.3, "name": "h3"}, {"time": 2.4667, "name": null}, {"time": 2.6, "name": "h3"}, {"time": 2.7333, "name": null}, {"time": 2.8667, "name": "h3"}, {"time": 3.0333, "name": null}, {"time": 3.1667, "name": "h3"}, {"time": 3.3, "name": null}, {"time": 3.4333, "name": "h3"}, {"time": 3.6, "name": null}, {"time": 3.7333, "name": "h3"}, {"time": 3.8667, "name": null}, {"time": 4.0333, "name": "h3"}, {"time": 4.1667, "name": null}, {"time": 4.3, "name": "h3"}, {"time": 4.4667, "name": null}, {"time": 4.6, "name": "h3"}, {"time": 4.7333, "name": null}, {"time": 4.8667, "name": "h3"}, {"time": 5.0333, "name": null}, {"time": 5.1667, "name": "h3"}, {"time": 5.3, "name": null}, {"time": 5.4333, "name": "h3"}, {"time": 5.6, "name": null}, {"time": 5.7333, "name": "h3"}, {"time": 5.8667, "name": null}]}, "h4": {"attachment": [{"time": 0.1667, "name": null}, {"time": 0.3, "name": "h4"}, {"time": 0.4667, "name": null}, {"time": 0.6, "name": "h4"}, {"time": 0.7333, "name": null}, {"time": 0.8667, "name": "h4"}, {"time": 1.0333, "name": null}, {"time": 1.1667, "name": "h4"}, {"time": 1.3, "name": null}, {"time": 1.4333, "name": "h4"}, {"time": 1.6, "name": null}, {"time": 1.7333, "name": "h4"}, {"time": 1.8667, "name": null}, {"time": 2.0333, "name": "h4"}, {"time": 2.1667, "name": null}, {"time": 2.3, "name": "h4"}, {"time": 2.4667, "name": null}, {"time": 2.6, "name": "h4"}, {"time": 2.7333, "name": null}, {"time": 2.8667, "name": "h4"}, {"time": 3.0333, "name": null}, {"time": 3.1667, "name": "h4"}, {"time": 3.3, "name": null}, {"time": 3.4333, "name": "h4"}, {"time": 3.6, "name": null}, {"time": 3.7333, "name": "h4"}, {"time": 3.8667, "name": null}, {"time": 4.0333, "name": "h4"}, {"time": 4.1667, "name": null}, {"time": 4.3, "name": "h4"}, {"time": 4.4667, "name": null}, {"time": 4.6, "name": "h4"}, {"time": 4.7333, "name": null}, {"time": 4.8667, "name": "h4"}, {"time": 5.0333, "name": null}, {"time": 5.1667, "name": "h4"}, {"time": 5.3, "name": null}, {"time": 5.4333, "name": "h4"}, {"time": 5.6, "name": null}, {"time": 5.7333, "name": "h4"}, {"time": 5.8667, "name": null}]}, "h5": {"attachment": [{"time": 0.1667, "name": null}, {"time": 0.3, "name": "h5"}, {"time": 0.4667, "name": null}, {"time": 0.6, "name": "h5"}, {"time": 0.7333, "name": null}, {"time": 0.8667, "name": "h5"}, {"time": 1.0333, "name": null}, {"time": 1.1667, "name": "h5"}, {"time": 1.3, "name": null}, {"time": 1.4333, "name": "h5"}, {"time": 1.6, "name": null}, {"time": 1.7333, "name": "h5"}, {"time": 1.8667, "name": null}, {"time": 2.0333, "name": "h5"}, {"time": 2.1667, "name": null}, {"time": 2.3, "name": "h5"}, {"time": 2.4667, "name": null}, {"time": 2.6, "name": "h5"}, {"time": 2.7333, "name": null}, {"time": 2.8667, "name": "h5"}, {"time": 3.0333, "name": null}, {"time": 3.1667, "name": "h5"}, {"time": 3.3, "name": null}, {"time": 3.4333, "name": "h5"}, {"time": 3.6, "name": null}, {"time": 3.7333, "name": "h5"}, {"time": 3.8667, "name": null}, {"time": 4.0333, "name": "h5"}, {"time": 4.1667, "name": null}, {"time": 4.3, "name": "h5"}, {"time": 4.4667, "name": null}, {"time": 4.6, "name": "h5"}, {"time": 4.7333, "name": null}, {"time": 4.8667, "name": "h5"}, {"time": 5.0333, "name": null}, {"time": 5.1667, "name": "h5"}, {"time": 5.3, "name": null}, {"time": 5.4333, "name": "h5"}, {"time": 5.6, "name": null}, {"time": 5.7333, "name": "h5"}, {"time": 5.8667, "name": null}]}, "tx/d0": {"attachment": [{"name": "tx/d0"}, {"time": 0.1, "name": "tx/d3"}, {"time": 0.1667, "name": "tx/d6"}, {"time": 0.2667, "name": "tx/d9"}, {"time": 0.3333, "name": "tx/d12"}, {"time": 0.4333, "name": "tx/d15"}, {"time": 0.5333, "name": "tx/d0"}, {"time": 0.6, "name": "tx/d3"}, {"time": 0.7, "name": "tx/d6"}, {"time": 0.7667, "name": "tx/d9"}, {"time": 0.8667, "name": "tx/d12"}, {"time": 0.9667, "name": "tx/d15"}, {"time": 1.0333, "name": "tx/d0"}, {"time": 1.1333, "name": "tx/d3"}, {"time": 1.2, "name": "tx/d6"}, {"time": 1.3, "name": "tx/d9"}, {"time": 1.4, "name": "tx/d12"}, {"time": 1.4667, "name": "tx/d15"}, {"time": 1.5667, "name": "tx/d0"}, {"time": 1.6333, "name": "tx/d3"}, {"time": 1.7333, "name": "tx/d6"}, {"time": 1.8333, "name": "tx/d9"}, {"time": 1.9, "name": "tx/d12"}, {"time": 2, "name": "tx/d0"}, {"time": 2.1, "name": "tx/d3"}, {"time": 2.1667, "name": "tx/d6"}, {"time": 2.2667, "name": "tx/d9"}, {"time": 2.3333, "name": "tx/d12"}, {"time": 2.4333, "name": "tx/d15"}, {"time": 2.5333, "name": "tx/d0"}, {"time": 2.6, "name": "tx/d3"}, {"time": 2.7, "name": "tx/d6"}, {"time": 2.7667, "name": "tx/d9"}, {"time": 2.8667, "name": "tx/d12"}, {"time": 2.9667, "name": "tx/d15"}, {"time": 3.0333, "name": "tx/d0"}, {"time": 3.1333, "name": "tx/d3"}, {"time": 3.2, "name": "tx/d6"}, {"time": 3.3, "name": "tx/d9"}, {"time": 3.4, "name": "tx/d12"}, {"time": 3.4667, "name": "tx/d15"}, {"time": 3.5667, "name": "tx/d0"}, {"time": 3.6333, "name": "tx/d3"}, {"time": 3.7333, "name": "tx/d6"}, {"time": 3.8333, "name": "tx/d9"}, {"time": 3.9, "name": "tx/d12"}, {"time": 4, "name": "tx/d0"}, {"time": 4.1, "name": "tx/d3"}, {"time": 4.1667, "name": "tx/d6"}, {"time": 4.2667, "name": "tx/d9"}, {"time": 4.3333, "name": "tx/d12"}, {"time": 4.4333, "name": "tx/d15"}, {"time": 4.5333, "name": "tx/d0"}, {"time": 4.6, "name": "tx/d3"}, {"time": 4.7, "name": "tx/d6"}, {"time": 4.7667, "name": "tx/d9"}, {"time": 4.8667, "name": "tx/d12"}, {"time": 4.9667, "name": "tx/d15"}, {"time": 5.0333, "name": "tx/d0"}, {"time": 5.1333, "name": "tx/d3"}, {"time": 5.2, "name": "tx/d6"}, {"time": 5.3, "name": "tx/d9"}, {"time": 5.4, "name": "tx/d12"}, {"time": 5.4667, "name": "tx/d15"}, {"time": 5.5667, "name": "tx/d0"}, {"time": 5.6333, "name": "tx/d3"}, {"time": 5.7333, "name": "tx/d6"}, {"time": 5.8333, "name": "tx/d9"}, {"time": 5.9, "name": "tx/d12"}, {"time": 6, "name": "tx/d15"}]}, "tx/d1": {"attachment": [{"name": "tx/d0"}, {"time": 0.1, "name": "tx/d3"}, {"time": 0.1667, "name": "tx/d6"}, {"time": 0.2667, "name": "tx/d9"}, {"time": 0.3333, "name": "tx/d12"}, {"time": 0.4333, "name": "tx/d15"}, {"time": 0.5333, "name": "tx/d0"}, {"time": 0.6, "name": "tx/d3"}, {"time": 0.7, "name": "tx/d6"}, {"time": 0.7667, "name": "tx/d9"}, {"time": 0.8667, "name": "tx/d12"}, {"time": 0.9667, "name": "tx/d15"}, {"time": 1.0333, "name": "tx/d0"}, {"time": 1.1333, "name": "tx/d3"}, {"time": 1.2, "name": "tx/d6"}, {"time": 1.3, "name": "tx/d9"}, {"time": 1.4, "name": "tx/d12"}, {"time": 1.4667, "name": "tx/d15"}, {"time": 1.5667, "name": "tx/d0"}, {"time": 1.6333, "name": "tx/d3"}, {"time": 1.7333, "name": "tx/d6"}, {"time": 1.8333, "name": "tx/d9"}, {"time": 1.9, "name": "tx/d12"}, {"time": 2, "name": "tx/d0"}, {"time": 2.1, "name": "tx/d3"}, {"time": 2.1667, "name": "tx/d6"}, {"time": 2.2667, "name": "tx/d9"}, {"time": 2.3333, "name": "tx/d12"}, {"time": 2.4333, "name": "tx/d15"}, {"time": 2.5333, "name": "tx/d0"}, {"time": 2.6, "name": "tx/d3"}, {"time": 2.7, "name": "tx/d6"}, {"time": 2.7667, "name": "tx/d9"}, {"time": 2.8667, "name": "tx/d12"}, {"time": 2.9667, "name": "tx/d15"}, {"time": 3.0333, "name": "tx/d0"}, {"time": 3.1333, "name": "tx/d3"}, {"time": 3.2, "name": "tx/d6"}, {"time": 3.3, "name": "tx/d9"}, {"time": 3.4, "name": "tx/d12"}, {"time": 3.4667, "name": "tx/d15"}, {"time": 3.5667, "name": "tx/d0"}, {"time": 3.6333, "name": "tx/d3"}, {"time": 3.7333, "name": "tx/d6"}, {"time": 3.8333, "name": "tx/d9"}, {"time": 3.9, "name": "tx/d12"}, {"time": 4, "name": "tx/d0"}, {"time": 4.1, "name": "tx/d3"}, {"time": 4.1667, "name": "tx/d6"}, {"time": 4.2667, "name": "tx/d9"}, {"time": 4.3333, "name": "tx/d12"}, {"time": 4.4333, "name": "tx/d15"}, {"time": 4.5333, "name": "tx/d0"}, {"time": 4.6, "name": "tx/d3"}, {"time": 4.7, "name": "tx/d6"}, {"time": 4.7667, "name": "tx/d9"}, {"time": 4.8667, "name": "tx/d12"}, {"time": 4.9667, "name": "tx/d15"}, {"time": 5.0333, "name": "tx/d0"}, {"time": 5.1333, "name": "tx/d3"}, {"time": 5.2, "name": "tx/d6"}, {"time": 5.3, "name": "tx/d9"}, {"time": 5.4, "name": "tx/d12"}, {"time": 5.4667, "name": "tx/d15"}, {"time": 5.5667, "name": "tx/d0"}, {"time": 5.6333, "name": "tx/d3"}, {"time": 5.7333, "name": "tx/d6"}, {"time": 5.8333, "name": "tx/d9"}, {"time": 5.9, "name": "tx/d12"}, {"time": 6, "name": "tx/d15"}]}, "tx/d2": {"attachment": [{"name": "tx/d15"}, {"time": 0.1, "name": "tx/d0"}, {"time": 0.2, "name": "tx/d3"}, {"time": 0.2667, "name": "tx/d6"}, {"time": 0.3667, "name": "tx/d9"}, {"time": 0.4667, "name": "tx/d12"}, {"time": 0.5333, "name": "tx/d15"}, {"time": 0.6333, "name": "tx/d0"}, {"time": 0.7, "name": "tx/d3"}, {"time": 0.8, "name": "tx/d6"}, {"time": 0.9, "name": "tx/d9"}, {"time": 0.9667, "name": "tx/d12"}, {"time": 1.0667, "name": "tx/d15"}, {"time": 1.1333, "name": "tx/d0"}, {"time": 1.2333, "name": "tx/d3"}, {"time": 1.3, "name": "tx/d6"}, {"time": 1.4, "name": "tx/d9"}, {"time": 1.4667, "name": "tx/d12"}, {"time": 1.5667, "name": "tx/d15"}, {"time": 1.6667, "name": "tx/d0"}, {"time": 1.7667, "name": "tx/d3"}, {"time": 1.8667, "name": "tx/d6"}, {"time": 1.9333, "name": "tx/d9"}, {"time": 2, "name": "tx/d15"}, {"time": 2.1, "name": "tx/d0"}, {"time": 2.2, "name": "tx/d3"}, {"time": 2.2667, "name": "tx/d6"}, {"time": 2.3667, "name": "tx/d9"}, {"time": 2.4667, "name": "tx/d12"}, {"time": 2.5333, "name": "tx/d15"}, {"time": 2.6333, "name": "tx/d0"}, {"time": 2.7, "name": "tx/d3"}, {"time": 2.8, "name": "tx/d6"}, {"time": 2.9, "name": "tx/d9"}, {"time": 2.9667, "name": "tx/d12"}, {"time": 3.0667, "name": "tx/d15"}, {"time": 3.1333, "name": "tx/d0"}, {"time": 3.2333, "name": "tx/d3"}, {"time": 3.3, "name": "tx/d6"}, {"time": 3.4, "name": "tx/d9"}, {"time": 3.4667, "name": "tx/d12"}, {"time": 3.5667, "name": "tx/d15"}, {"time": 3.6667, "name": "tx/d0"}, {"time": 3.7667, "name": "tx/d3"}, {"time": 3.8667, "name": "tx/d6"}, {"time": 3.9333, "name": "tx/d9"}, {"time": 4, "name": "tx/d15"}, {"time": 4.1, "name": "tx/d0"}, {"time": 4.2, "name": "tx/d3"}, {"time": 4.2667, "name": "tx/d6"}, {"time": 4.3667, "name": "tx/d9"}, {"time": 4.4667, "name": "tx/d12"}, {"time": 4.5333, "name": "tx/d15"}, {"time": 4.6333, "name": "tx/d0"}, {"time": 4.7, "name": "tx/d3"}, {"time": 4.8, "name": "tx/d6"}, {"time": 4.9, "name": "tx/d9"}, {"time": 4.9667, "name": "tx/d12"}, {"time": 5.0667, "name": "tx/d15"}, {"time": 5.1333, "name": "tx/d0"}, {"time": 5.2333, "name": "tx/d3"}, {"time": 5.3, "name": "tx/d6"}, {"time": 5.4, "name": "tx/d9"}, {"time": 5.4667, "name": "tx/d12"}, {"time": 5.5667, "name": "tx/d15"}, {"time": 5.6667, "name": "tx/d0"}, {"time": 5.7667, "name": "tx/d3"}, {"time": 5.8667, "name": "tx/d6"}, {"time": 5.9333, "name": "tx/d9"}, {"time": 6, "name": "tx/d12"}]}, "tx/d3": {"attachment": [{"name": "tx/d0"}, {"time": 0.1, "name": "tx/d3"}, {"time": 0.1667, "name": "tx/d6"}, {"time": 0.2667, "name": "tx/d9"}, {"time": 0.3333, "name": "tx/d12"}, {"time": 0.4333, "name": "tx/d15"}, {"time": 0.5333, "name": "tx/d0"}, {"time": 0.6, "name": "tx/d3"}, {"time": 0.7, "name": "tx/d6"}, {"time": 0.7667, "name": "tx/d9"}, {"time": 0.8667, "name": "tx/d12"}, {"time": 0.9667, "name": "tx/d15"}, {"time": 1.0333, "name": "tx/d0"}, {"time": 1.1333, "name": "tx/d3"}, {"time": 1.2, "name": "tx/d6"}, {"time": 1.3, "name": "tx/d9"}, {"time": 1.4, "name": "tx/d12"}, {"time": 1.4667, "name": "tx/d15"}, {"time": 1.5667, "name": "tx/d0"}, {"time": 1.6333, "name": "tx/d3"}, {"time": 1.7333, "name": "tx/d6"}, {"time": 1.8333, "name": "tx/d9"}, {"time": 1.9, "name": "tx/d12"}, {"time": 2, "name": "tx/d0"}, {"time": 2.1, "name": "tx/d3"}, {"time": 2.1667, "name": "tx/d6"}, {"time": 2.2667, "name": "tx/d9"}, {"time": 2.3333, "name": "tx/d12"}, {"time": 2.4333, "name": "tx/d15"}, {"time": 2.5333, "name": "tx/d0"}, {"time": 2.6, "name": "tx/d3"}, {"time": 2.7, "name": "tx/d6"}, {"time": 2.7667, "name": "tx/d9"}, {"time": 2.8667, "name": "tx/d12"}, {"time": 2.9667, "name": "tx/d15"}, {"time": 3.0333, "name": "tx/d0"}, {"time": 3.1333, "name": "tx/d3"}, {"time": 3.2, "name": "tx/d6"}, {"time": 3.3, "name": "tx/d9"}, {"time": 3.4, "name": "tx/d12"}, {"time": 3.4667, "name": "tx/d15"}, {"time": 3.5667, "name": "tx/d0"}, {"time": 3.6333, "name": "tx/d3"}, {"time": 3.7333, "name": "tx/d6"}, {"time": 3.8333, "name": "tx/d9"}, {"time": 3.9, "name": "tx/d12"}, {"time": 4, "name": "tx/d0"}, {"time": 4.1, "name": "tx/d3"}, {"time": 4.1667, "name": "tx/d6"}, {"time": 4.2667, "name": "tx/d9"}, {"time": 4.3333, "name": "tx/d12"}, {"time": 4.4333, "name": "tx/d15"}, {"time": 4.5333, "name": "tx/d0"}, {"time": 4.6, "name": "tx/d3"}, {"time": 4.7, "name": "tx/d6"}, {"time": 4.7667, "name": "tx/d9"}, {"time": 4.8667, "name": "tx/d12"}, {"time": 4.9667, "name": "tx/d15"}, {"time": 5.0333, "name": "tx/d0"}, {"time": 5.1333, "name": "tx/d3"}, {"time": 5.2, "name": "tx/d6"}, {"time": 5.3, "name": "tx/d9"}, {"time": 5.4, "name": "tx/d12"}, {"time": 5.4667, "name": "tx/d15"}, {"time": 5.5667, "name": "tx/d0"}, {"time": 5.6333, "name": "tx/d3"}, {"time": 5.7333, "name": "tx/d6"}, {"time": 5.8333, "name": "tx/d9"}, {"time": 5.9, "name": "tx/d12"}, {"time": 6, "name": "tx/d15"}]}}, "bones": {"chuann1": {"rotate": [{"angle": 2.07, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 4.14, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": 2.07, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": 4.14, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": 2.07, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.5, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "angle": 4.14, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6, "angle": 2.07}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "y": 21.96, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 3, "y": 21.96, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 5, "y": 21.96, "curve": 0.25, "c3": 0.75}, {"time": 6}]}, "chuann1a": {"rotate": [{"angle": 2.29, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 4.58, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": 2.29, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": 4.58, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": 2.29, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.5, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "angle": 4.58, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6, "angle": 2.29}]}, "chuann6": {"rotate": [{"angle": 2.14, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5, "angle": 1.07, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 2.14, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.5, "angle": 1.07, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 2.14, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4.5, "angle": 1.07, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": 2.14}]}, "chuann5": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": 155.89, "y": 18.94, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": 155.89, "y": 18.94, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 5, "x": 155.89, "y": 18.94, "curve": 0.25, "c3": 0.75}, {"time": 6}]}, "chuann11": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": -176.37, "y": -21.43, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": -176.37, "y": -21.43, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 5, "x": -176.37, "y": -21.43, "curve": 0.25, "c3": 0.75}, {"time": 6}]}, "chuann1a11": {"translate": [{"x": -1.03, "y": -0.11, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "x": -3.18, "y": -0.57, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 4.41, "y": 1.05, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": -3.18, "y": -0.57, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 4.41, "y": 1.05, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": -1.03, "y": -0.11, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.1667, "x": -3.18, "y": -0.57, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 4.41, "y": 1.05, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "x": -3.18, "y": -0.57, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "x": 4.41, "y": 1.05, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "x": -1.03, "y": -0.11, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.1667, "x": -3.18, "y": -0.57, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 4.41, "y": 1.05, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "x": -3.18, "y": -0.57, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "x": 4.41, "y": 1.05, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6, "x": -1.03, "y": -0.11}]}, "chuann1a12": {"translate": [{"x": 2.25, "y": 0.59, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3333, "x": -3.18, "y": -0.57, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 4.41, "y": 1.05, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -3.18, "y": -0.57, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 4.41, "y": 1.05, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 2.25, "y": 0.59, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.3333, "x": -3.18, "y": -0.57, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "x": 4.41, "y": 1.05, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": -3.18, "y": -0.57, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "x": 4.41, "y": 1.05, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "x": 2.25, "y": 0.59, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 4.3333, "x": -3.18, "y": -0.57, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "x": 4.41, "y": 1.05, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": -3.18, "y": -0.57, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "x": 4.41, "y": 1.05, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 6, "x": 2.25, "y": 0.59}]}, "chuann1a13": {"translate": [{"x": 4.41, "y": 1.05, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -3.18, "y": -0.57, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 4.41, "y": 1.05, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": -3.18, "y": -0.57, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 4.41, "y": 1.05, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "x": -3.18, "y": -0.57, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": 4.41, "y": 1.05, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": -3.18, "y": -0.57, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 4.41, "y": 1.05, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "x": -3.18, "y": -0.57, "curve": 0.25, "c3": 0.75}, {"time": 5, "x": 4.41, "y": 1.05, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "x": -3.18, "y": -0.57, "curve": 0.25, "c3": 0.75}, {"time": 6, "x": 4.41, "y": 1.05}]}, "chuann1a14": {"translate": [{"x": -1.03, "y": -0.11, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "x": -3.18, "y": -0.57, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 4.41, "y": 1.05, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": -3.18, "y": -0.57, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 4.41, "y": 1.05, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": -1.03, "y": -0.11, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.1667, "x": -3.18, "y": -0.57, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 4.41, "y": 1.05, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "x": -3.18, "y": -0.57, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "x": 4.41, "y": 1.05, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "x": -1.03, "y": -0.11, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.1667, "x": -3.18, "y": -0.57, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 4.41, "y": 1.05, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "x": -3.18, "y": -0.57, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "x": 4.41, "y": 1.05, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6, "x": -1.03, "y": -0.11}]}, "chuann1a15": {"translate": [{"x": 2.25, "y": 0.59, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3333, "x": -3.18, "y": -0.57, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 4.41, "y": 1.05, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -3.18, "y": -0.57, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 4.41, "y": 1.05, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 2.25, "y": 0.59, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.3333, "x": -3.18, "y": -0.57, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "x": 4.41, "y": 1.05, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": -3.18, "y": -0.57, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "x": 4.41, "y": 1.05, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "x": 2.25, "y": 0.59, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 4.3333, "x": -3.18, "y": -0.57, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "x": 4.41, "y": 1.05, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": -3.18, "y": -0.57, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "x": 4.41, "y": 1.05, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 6, "x": 2.25, "y": 0.59}]}, "chuann1a16": {"translate": [{"x": 4.41, "y": 1.05, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -3.18, "y": -0.57, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 4.41, "y": 1.05, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": -3.18, "y": -0.57, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 4.41, "y": 1.05, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "x": -3.18, "y": -0.57, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": 4.41, "y": 1.05, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": -3.18, "y": -0.57, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 4.41, "y": 1.05, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "x": -3.18, "y": -0.57, "curve": 0.25, "c3": 0.75}, {"time": 5, "x": 4.41, "y": 1.05, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "x": -3.18, "y": -0.57, "curve": 0.25, "c3": 0.75}, {"time": 6, "x": 4.41, "y": 1.05}]}, "chuann12": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": 5.86, "y": 0.71, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": 5.86, "y": 0.71, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 5, "x": 5.86, "y": 0.71, "curve": 0.25, "c3": 0.75}, {"time": 6}]}, "chuann13": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": 11.07, "y": 1.34, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": 11.07, "y": 1.34, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 5, "x": 11.07, "y": 1.34, "curve": 0.25, "c3": 0.75}, {"time": 6}]}, "chuann15": {"translate": [{"x": -3.05, "y": 20.19, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -4.26, "y": 28.19, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": -4.26, "y": 28.19, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": -3.05, "y": 20.19, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "x": -4.26, "y": 28.19, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "x": -4.26, "y": 28.19, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "x": -3.05, "y": 20.19, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 4.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "x": -4.26, "y": 28.19, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "x": -4.26, "y": 28.19, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 6, "x": -3.05, "y": 20.19}]}, "chuann14": {"translate": [{"x": -1.21, "y": 8, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -4.26, "y": 28.19, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -4.26, "y": 28.19, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": -1.21, "y": 8, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.1667, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": -4.26, "y": 28.19, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "x": -4.26, "y": 28.19, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "x": -1.21, "y": 8, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.1667, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": -4.26, "y": 28.19, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "x": -4.26, "y": 28.19, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6, "x": -1.21, "y": 8}]}, "chuann1a4": {"translate": [{"x": -0.61, "y": 2.11, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 0.1667, "x": 0.45, "y": -3, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "x": 1.15, "y": -6.37, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -1.3, "y": 5.49, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 1.15, "y": -6.37, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": -1.3, "y": 5.49, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": -0.61, "y": 2.11, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 2.1667, "x": 0.45, "y": -3, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "x": 1.15, "y": -6.37, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "x": -1.3, "y": 5.49, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": 1.15, "y": -6.37, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "x": -1.3, "y": 5.49, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "x": -0.61, "y": 2.11, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 4.1667, "x": 0.45, "y": -3, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.3333, "x": 1.15, "y": -6.37, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "x": -1.3, "y": 5.49, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": 1.15, "y": -6.37, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "x": -1.3, "y": 5.49, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 6, "x": -0.61, "y": 2.11}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 0.138, "y": 0.138, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 0.138, "y": 0.138, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "x": 0.138, "y": 0.138, "curve": 0.25, "c3": 0.75}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": 0.138, "y": 0.138, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "x": 0.138, "y": 0.138, "curve": 0.25, "c3": 0.75}, {"time": 5, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "x": 0.138, "y": 0.138, "curve": 0.25, "c3": 0.75}, {"time": 6}]}, "chuann1a5": {"translate": [{"x": -2.99, "y": 12.6, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.1667, "x": -1.17, "y": 3.6, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.5, "x": 3.44, "y": -19.1, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": -2.99, "y": 12.6, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 3.44, "y": -19.1, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": -2.99, "y": 12.6, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2.1667, "x": -1.17, "y": 3.6, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.5, "x": 3.44, "y": -19.1, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": -2.99, "y": 12.6, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": 3.44, "y": -19.1, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": -2.99, "y": 12.6, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4.1667, "x": -1.17, "y": 3.6, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 4.5, "x": 3.44, "y": -19.1, "curve": 0.25, "c3": 0.75}, {"time": 5, "x": -2.99, "y": 12.6, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "x": 3.44, "y": -19.1, "curve": 0.25, "c3": 0.75}, {"time": 6, "x": -2.99, "y": 12.6}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 0.138, "y": 0.138, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 0.138, "y": 0.138, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "x": 0.138, "y": 0.138, "curve": 0.25, "c3": 0.75}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": 0.138, "y": 0.138, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "x": 0.138, "y": 0.138, "curve": 0.25, "c3": 0.75}, {"time": 5, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "x": 0.138, "y": 0.138, "curve": 0.25, "c3": 0.75}, {"time": 6}]}, "chuann1a6": {"translate": [{"x": -2.08, "y": 7.59, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "x": -4.11, "y": 17.32, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 3.06, "y": -16.97, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": -4.11, "y": 17.32, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 3.06, "y": -16.97, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": -2.08, "y": 7.59, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.1667, "x": -4.11, "y": 17.32, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 3.06, "y": -16.97, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "x": -4.11, "y": 17.32, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "x": 3.06, "y": -16.97, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "x": -2.08, "y": 7.59, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.1667, "x": -4.11, "y": 17.32, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 3.06, "y": -16.97, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "x": -4.11, "y": 17.32, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "x": 3.06, "y": -16.97, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6, "x": -2.08, "y": 7.59}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 0.138, "y": 0.138, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 0.138, "y": 0.138, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "x": 0.138, "y": 0.138, "curve": 0.25, "c3": 0.75}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": 0.138, "y": 0.138, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "x": 0.138, "y": 0.138, "curve": 0.25, "c3": 0.75}, {"time": 5, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "x": 0.138, "y": 0.138, "curve": 0.25, "c3": 0.75}, {"time": 6}]}, "chuann1a10": {"translate": [{"x": -1.17, "y": 3.6, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3333, "x": 3.44, "y": -19.1, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -2.99, "y": 12.6, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 3.44, "y": -19.1, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": -2.99, "y": 12.6, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": -1.17, "y": 3.6, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.3333, "x": 3.44, "y": -19.1, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "x": -2.99, "y": 12.6, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": 3.44, "y": -19.1, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "x": -2.99, "y": 12.6, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "x": -1.17, "y": 3.6, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 4.3333, "x": 3.44, "y": -19.1, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "x": -2.99, "y": 12.6, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": 3.44, "y": -19.1, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "x": -2.99, "y": 12.6, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 6, "x": -1.17, "y": 3.6}], "scale": [{"x": 1.114, "y": 1.114, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "x": 1.179, "y": 1.179, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 0.951, "y": 0.951, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": 1.179, "y": 1.179, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 0.951, "y": 0.951, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 1.114, "y": 1.114, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.1667, "x": 1.179, "y": 1.179, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 0.951, "y": 0.951, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "x": 1.179, "y": 1.179, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "x": 0.951, "y": 0.951, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "x": 1.114, "y": 1.114, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.1667, "x": 1.179, "y": 1.179, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 0.951, "y": 0.951, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "x": 1.179, "y": 1.179, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "x": 0.951, "y": 0.951, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6, "x": 1.114, "y": 1.114}]}, "chuann1a7": {"translate": [{"x": -1.3, "y": 5.49, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3333, "x": 0.45, "y": -3, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.5, "x": 1.15, "y": -6.37, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": -1.3, "y": 5.49, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 1.15, "y": -6.37, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": -1.3, "y": 5.49, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2.3333, "x": 0.45, "y": -3, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.5, "x": 1.15, "y": -6.37, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": -1.3, "y": 5.49, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": 1.15, "y": -6.37, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": -1.3, "y": 5.49, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4.3333, "x": 0.45, "y": -3, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.5, "x": 1.15, "y": -6.37, "curve": 0.25, "c3": 0.75}, {"time": 5, "x": -1.3, "y": 5.49, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "x": 1.15, "y": -6.37, "curve": 0.25, "c3": 0.75}, {"time": 6, "x": -1.3, "y": 5.49}]}, "chuann1a8": {"translate": [{"x": -1.17, "y": 3.6, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "x": -2.99, "y": 12.6, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3333, "x": -1.17, "y": 3.6, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "x": 3.44, "y": -19.1, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": -2.99, "y": 12.6, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 3.44, "y": -19.1, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": -1.17, "y": 3.6, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.1667, "x": -2.99, "y": 12.6, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2.3333, "x": -1.17, "y": 3.6, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "x": 3.44, "y": -19.1, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "x": -2.99, "y": 12.6, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "x": 3.44, "y": -19.1, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "x": -1.17, "y": 3.6, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.1667, "x": -2.99, "y": 12.6, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4.3333, "x": -1.17, "y": 3.6, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 4.6667, "x": 3.44, "y": -19.1, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "x": -2.99, "y": 12.6, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "x": 3.44, "y": -19.1, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6, "x": -1.17, "y": 3.6}]}, "chuann1a9": {"translate": [{"x": 1.02, "y": -7.24, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3333, "x": -4.11, "y": 17.32, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 3.06, "y": -16.97, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -4.11, "y": 17.32, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 3.06, "y": -16.97, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 1.02, "y": -7.24, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.3333, "x": -4.11, "y": 17.32, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "x": 3.06, "y": -16.97, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": -4.11, "y": 17.32, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "x": 3.06, "y": -16.97, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "x": 1.02, "y": -7.24, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 4.3333, "x": -4.11, "y": 17.32, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "x": 3.06, "y": -16.97, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": -4.11, "y": 17.32, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "x": 3.06, "y": -16.97, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 6, "x": 1.02, "y": -7.24}]}, "chuann1a20": {"translate": [{"x": 0.34, "y": -2.92, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "x": 0.98, "y": -6.18, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -1.25, "y": 5.29, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": 0.98, "y": -6.18, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -1.25, "y": 5.29, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 0.34, "y": -2.92, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.1667, "x": 0.98, "y": -6.18, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": -1.25, "y": 5.29, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "x": 0.98, "y": -6.18, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "x": -1.25, "y": 5.29, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "x": 0.34, "y": -2.92, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.1667, "x": 0.98, "y": -6.18, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": -1.25, "y": 5.29, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "x": 0.98, "y": -6.18, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "x": -1.25, "y": 5.29, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6, "x": 0.34, "y": -2.92}]}, "chuann1a27": {"translate": [{"x": -0.62, "y": 2.02, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 0.1667, "x": 0.34, "y": -2.92, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "x": 0.98, "y": -6.18, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -1.25, "y": 5.29, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 0.98, "y": -6.18, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": -1.25, "y": 5.29, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": -0.62, "y": 2.02, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 2.1667, "x": 0.34, "y": -2.92, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "x": 0.98, "y": -6.18, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "x": -1.25, "y": 5.29, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": 0.98, "y": -6.18, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "x": -1.25, "y": 5.29, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "x": -0.62, "y": 2.02, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 4.1667, "x": 0.34, "y": -2.92, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.3333, "x": 0.98, "y": -6.18, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "x": -1.25, "y": 5.29, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": 0.98, "y": -6.18, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "x": -1.25, "y": 5.29, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 6, "x": -0.62, "y": 2.02}]}, "chuann1a24": {"translate": [{"x": 0.8, "y": 0.19, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 2.83, "y": 0.67, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 2.83, "y": 0.67, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 0.8, "y": 0.19, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.1667, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 2.83, "y": 0.67, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "x": 2.83, "y": 0.67, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "x": 0.8, "y": 0.19, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.1667, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 2.83, "y": 0.67, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "x": 2.83, "y": 0.67, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6, "x": 0.8, "y": 0.19}]}, "chuann1a31": {"translate": [{"x": 2.25, "y": 0.53, "curve": 0.315, "c2": 0.28, "c3": 0.673, "c4": 0.69}, {"time": 0.2, "x": 0.8, "y": 0.19, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "x": 2.83, "y": 0.67, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "x": 2.83, "y": 0.67, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 2, "x": 2.25, "y": 0.53, "curve": 0.315, "c2": 0.28, "c3": 0.673, "c4": 0.69}, {"time": 2.2, "x": 0.8, "y": 0.19, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3667, "curve": 0.25, "c3": 0.75}, {"time": 2.8667, "x": 2.83, "y": 0.67, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 3.8667, "x": 2.83, "y": 0.67, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 4, "x": 2.25, "y": 0.53, "curve": 0.315, "c2": 0.28, "c3": 0.673, "c4": 0.69}, {"time": 4.2, "x": 0.8, "y": 0.19, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.8667, "x": 2.83, "y": 0.67, "curve": 0.25, "c3": 0.75}, {"time": 5.3667, "curve": 0.25, "c3": 0.75}, {"time": 5.8667, "x": 2.83, "y": 0.67, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 6, "x": 2.25, "y": 0.53}]}, "chuann16": {"translate": [{"x": -0.24, "y": 0.08, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "x": -3.74, "y": -0.94, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 8.58, "y": 2.67, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": -3.74, "y": -0.94, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 8.58, "y": 2.67, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": -0.24, "y": 0.08, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.1667, "x": -3.74, "y": -0.94, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 8.58, "y": 2.67, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "x": -3.74, "y": -0.94, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "x": 8.58, "y": 2.67, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "x": -0.24, "y": 0.08, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.1667, "x": -3.74, "y": -0.94, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 8.58, "y": 2.67, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "x": -3.74, "y": -0.94, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "x": 8.58, "y": 2.67, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6, "x": -0.24, "y": 0.08}]}, "chuann18": {"translate": [{"x": 5.08, "y": 1.64, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 0.1667, "x": -0.24, "y": 0.08, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "x": -3.74, "y": -0.94, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 8.58, "y": 2.67, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -3.74, "y": -0.94, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 8.58, "y": 2.67, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 5.08, "y": 1.64, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 2.1667, "x": -0.24, "y": 0.08, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "x": -3.74, "y": -0.94, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "x": 8.58, "y": 2.67, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": -3.74, "y": -0.94, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "x": 8.58, "y": 2.67, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "x": 5.08, "y": 1.64, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 4.1667, "x": -0.24, "y": 0.08, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.3333, "x": -3.74, "y": -0.94, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "x": 8.58, "y": 2.67, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": -3.74, "y": -0.94, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "x": 8.58, "y": 2.67, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 6, "x": 5.08, "y": 1.64}]}, "chuann19": {"translate": [{"x": -0.9, "y": 2.33, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.2333, "x": 1.35, "y": -5.34, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "x": -3.57, "y": 11.48, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "x": 1.35, "y": -5.34, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "x": -3.57, "y": 11.48, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 2, "x": -0.9, "y": 2.33, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 2.2333, "x": 1.35, "y": -5.34, "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "x": -3.57, "y": 11.48, "curve": 0.25, "c3": 0.75}, {"time": 3.2333, "x": 1.35, "y": -5.34, "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "x": -3.57, "y": 11.48, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 4, "x": -0.9, "y": 2.33, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 4.2333, "x": 1.35, "y": -5.34, "curve": 0.25, "c3": 0.75}, {"time": 4.7333, "x": -3.57, "y": 11.48, "curve": 0.25, "c3": 0.75}, {"time": 5.2333, "x": 1.35, "y": -5.34, "curve": 0.25, "c3": 0.75}, {"time": 5.7333, "x": -3.57, "y": 11.48, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 6, "x": -0.9, "y": 2.33}]}, "chuann20": {"translate": [{"x": -3.46, "y": 11.1, "curve": 0.277, "c2": 0.12, "c3": 0.754}, {"time": 0.4667, "x": 1.35, "y": -5.34, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "x": -3.57, "y": 11.48, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "x": 1.35, "y": -5.34, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "x": -3.57, "y": 11.48, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 2, "x": -3.46, "y": 11.1, "curve": 0.277, "c2": 0.12, "c3": 0.754}, {"time": 2.4667, "x": 1.35, "y": -5.34, "curve": 0.25, "c3": 0.75}, {"time": 2.9667, "x": -3.57, "y": 11.48, "curve": 0.25, "c3": 0.75}, {"time": 3.4667, "x": 1.35, "y": -5.34, "curve": 0.25, "c3": 0.75}, {"time": 3.9667, "x": -3.57, "y": 11.48, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 4, "x": -3.46, "y": 11.1, "curve": 0.277, "c2": 0.12, "c3": 0.754}, {"time": 4.4667, "x": 1.35, "y": -5.34, "curve": 0.25, "c3": 0.75}, {"time": 4.9667, "x": -3.57, "y": 11.48, "curve": 0.25, "c3": 0.75}, {"time": 5.4667, "x": 1.35, "y": -5.34, "curve": 0.25, "c3": 0.75}, {"time": 5.9667, "x": -3.57, "y": 11.48, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 6, "x": -3.46, "y": 11.1}]}, "chuann21": {"translate": [{"x": 5.09, "y": 1.64, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3333, "x": -3.74, "y": -0.94, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 8.58, "y": 2.67, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -3.74, "y": -0.94, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 8.58, "y": 2.67, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 5.09, "y": 1.64, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.3333, "x": -3.74, "y": -0.94, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "x": 8.58, "y": 2.67, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": -3.74, "y": -0.94, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "x": 8.58, "y": 2.67, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "x": 5.09, "y": 1.64, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 4.3333, "x": -3.74, "y": -0.94, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "x": 8.58, "y": 2.67, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": -3.74, "y": -0.94, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "x": 8.58, "y": 2.67, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 6, "x": 5.09, "y": 1.64}]}, "chuann22": {"translate": [{"x": 8.58, "y": 2.67, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.1667, "x": 5.09, "y": 1.64, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.5, "x": -3.74, "y": -0.94, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 8.58, "y": 2.67, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": -3.74, "y": -0.94, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 8.58, "y": 2.67, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2.1667, "x": 5.09, "y": 1.64, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.5, "x": -3.74, "y": -0.94, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": 8.58, "y": 2.67, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": -3.74, "y": -0.94, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 8.58, "y": 2.67, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4.1667, "x": 5.09, "y": 1.64, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 4.5, "x": -3.74, "y": -0.94, "curve": 0.25, "c3": 0.75}, {"time": 5, "x": 8.58, "y": 2.67, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "x": -3.74, "y": -0.94, "curve": 0.25, "c3": 0.75}, {"time": 6, "x": 8.58, "y": 2.67}]}, "chuann23": {"translate": [{"x": -0.9, "y": 2.33, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.2333, "x": 1.35, "y": -5.34, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "x": -3.57, "y": 11.48, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "x": 1.35, "y": -5.34, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "x": -3.57, "y": 11.48, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 2, "x": -0.9, "y": 2.33, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 2.2333, "x": 1.35, "y": -5.34, "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "x": -3.57, "y": 11.48, "curve": 0.25, "c3": 0.75}, {"time": 3.2333, "x": 1.35, "y": -5.34, "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "x": -3.57, "y": 11.48, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 4, "x": -0.9, "y": 2.33, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 4.2333, "x": 1.35, "y": -5.34, "curve": 0.25, "c3": 0.75}, {"time": 4.7333, "x": -3.57, "y": 11.48, "curve": 0.25, "c3": 0.75}, {"time": 5.2333, "x": 1.35, "y": -5.34, "curve": 0.25, "c3": 0.75}, {"time": 5.7333, "x": -3.57, "y": 11.48, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 6, "x": -0.9, "y": 2.33}]}, "chuann24": {"translate": [{"x": -3.46, "y": 11.1, "curve": 0.277, "c2": 0.12, "c3": 0.754}, {"time": 0.4667, "x": 1.35, "y": -5.34, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "x": -3.57, "y": 11.48, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "x": 1.35, "y": -5.34, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "x": -3.57, "y": 11.48, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 2, "x": -3.46, "y": 11.1, "curve": 0.277, "c2": 0.12, "c3": 0.754}, {"time": 2.4667, "x": 1.35, "y": -5.34, "curve": 0.25, "c3": 0.75}, {"time": 2.9667, "x": -3.57, "y": 11.48, "curve": 0.25, "c3": 0.75}, {"time": 3.4667, "x": 1.35, "y": -5.34, "curve": 0.25, "c3": 0.75}, {"time": 3.9667, "x": -3.57, "y": 11.48, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 4, "x": -3.46, "y": 11.1, "curve": 0.277, "c2": 0.12, "c3": 0.754}, {"time": 4.4667, "x": 1.35, "y": -5.34, "curve": 0.25, "c3": 0.75}, {"time": 4.9667, "x": -3.57, "y": 11.48, "curve": 0.25, "c3": 0.75}, {"time": 5.4667, "x": 1.35, "y": -5.34, "curve": 0.25, "c3": 0.75}, {"time": 5.9667, "x": -3.57, "y": 11.48, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 6, "x": -3.46, "y": 11.1}]}, "tong": {"rotate": [{"angle": 7.29, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "angle": 14.57, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": 7.29, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "angle": 14.57, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": 7.29, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.5, "angle": 14.57, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6, "angle": 7.29}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": -6.7, "y": 17, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": -6.7, "y": 17, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 5, "x": -6.7, "y": 17, "curve": 0.25, "c3": 0.75}, {"time": 6}]}, "tong2": {"translate": [{"x": 220.12, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": 74.76, "curve": 0.25, "c3": 0.75}, {"time": 6}]}, "js_2": {"rotate": [{"angle": 6.46, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "angle": -3.05, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 15.98, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": 6.46, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "angle": -3.05, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": 15.98, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": 6.46, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.5, "angle": -3.05, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "angle": 15.98, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6, "angle": 6.46}]}, "tong4": {"translate": [{"x": -34.4, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": -68.81, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": -34.4, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": -68.81, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": -34.4, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.5, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "x": -68.81, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6, "x": -34.4}]}, "tong3": {"translate": [{"x": 38.6, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 77.2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": 38.6, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": 77.2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": 38.6, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.5, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "x": 77.2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6, "x": 38.6}]}, "tx/d0": {"scale": [{"x": 0.792, "curve": 0.25, "c3": 0.75}, {"time": 3}]}, "tx/d2": {"translate": [{"x": -151.69, "curve": 0.25, "c3": 0.75}, {"time": 3}]}, "tx/d3": {"translate": [{"x": -165.35, "curve": 0.25, "c3": 0.75}, {"time": 3}]}, "js_3": {"scale": [{"x": 0.995, "y": 0.995, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.2667, "x": 0.937, "y": 0.937, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "x": 1.043, "y": 1.043, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "x": 0.937, "y": 0.937, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "x": 1.043, "y": 1.043, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 2, "x": 0.995, "y": 0.995, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 2.2667, "x": 0.937, "y": 0.937, "curve": 0.25, "c3": 0.75}, {"time": 2.7667, "x": 1.043, "y": 1.043, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "x": 0.937, "y": 0.937, "curve": 0.25, "c3": 0.75}, {"time": 3.7667, "x": 1.043, "y": 1.043, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 4, "x": 0.995, "y": 0.995, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 4.2667, "x": 0.937, "y": 0.937, "curve": 0.25, "c3": 0.75}, {"time": 4.7667, "x": 1.043, "y": 1.043, "curve": 0.25, "c3": 0.75}, {"time": 5.2667, "x": 0.937, "y": 0.937, "curve": 0.25, "c3": 0.75}, {"time": 5.7667, "x": 1.043, "y": 1.043, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 6, "x": 0.995, "y": 0.995}]}, "chuan": {"translate": [{"x": 86.98, "curve": 0.25, "c3": 0.75}, {"time": 2.8667, "x": 211.24, "curve": 0.25, "c3": 0.75}, {"time": 6}], "scale": [{"x": 1.171, "y": 1.171, "curve": 0.25, "c3": 0.75}, {"time": 2.8667, "x": 1.007, "y": 1.007, "curve": 0.25, "c3": 0.75}, {"time": 6}]}}}, "approach_idle": {"slots": {"h1": {"attachment": [{"time": 0.1667, "name": null}, {"time": 0.3, "name": "h1"}, {"time": 0.4667, "name": null}, {"time": 0.6, "name": "h1"}, {"time": 0.7333, "name": null}, {"time": 0.8667, "name": "h1"}, {"time": 1.0333, "name": null}, {"time": 1.1667, "name": "h1"}, {"time": 1.3, "name": null}, {"time": 1.4333, "name": "h1"}, {"time": 1.6, "name": null}, {"time": 1.7333, "name": "h1"}, {"time": 1.8667, "name": null}]}, "h2": {"attachment": [{"time": 0.1667, "name": null}, {"time": 0.3, "name": "h2"}, {"time": 0.4667, "name": null}, {"time": 0.6, "name": "h2"}, {"time": 0.7333, "name": null}, {"time": 0.8667, "name": "h2"}, {"time": 1.0333, "name": null}, {"time": 1.1667, "name": "h2"}, {"time": 1.3, "name": null}, {"time": 1.4333, "name": "h2"}, {"time": 1.6, "name": null}, {"time": 1.7333, "name": "h2"}, {"time": 1.8667, "name": null}]}, "h3": {"attachment": [{"time": 0.1667, "name": null}, {"time": 0.3, "name": "h3"}, {"time": 0.4667, "name": null}, {"time": 0.6, "name": "h3"}, {"time": 0.7333, "name": null}, {"time": 0.8667, "name": "h3"}, {"time": 1.0333, "name": null}, {"time": 1.1667, "name": "h3"}, {"time": 1.3, "name": null}, {"time": 1.4333, "name": "h3"}, {"time": 1.6, "name": null}, {"time": 1.7333, "name": "h3"}, {"time": 1.8667, "name": null}]}, "h4": {"attachment": [{"time": 0.1667, "name": null}, {"time": 0.3, "name": "h4"}, {"time": 0.4667, "name": null}, {"time": 0.6, "name": "h4"}, {"time": 0.7333, "name": null}, {"time": 0.8667, "name": "h4"}, {"time": 1.0333, "name": null}, {"time": 1.1667, "name": "h4"}, {"time": 1.3, "name": null}, {"time": 1.4333, "name": "h4"}, {"time": 1.6, "name": null}, {"time": 1.7333, "name": "h4"}, {"time": 1.8667, "name": null}]}, "h5": {"attachment": [{"time": 0.1667, "name": null}, {"time": 0.3, "name": "h5"}, {"time": 0.4667, "name": null}, {"time": 0.6, "name": "h5"}, {"time": 0.7333, "name": null}, {"time": 0.8667, "name": "h5"}, {"time": 1.0333, "name": null}, {"time": 1.1667, "name": "h5"}, {"time": 1.3, "name": null}, {"time": 1.4333, "name": "h5"}, {"time": 1.6, "name": null}, {"time": 1.7333, "name": "h5"}, {"time": 1.8667, "name": null}]}, "tx/d0": {"attachment": [{"name": "tx/d0"}, {"time": 0.1, "name": "tx/d3"}, {"time": 0.1667, "name": "tx/d6"}, {"time": 0.2667, "name": "tx/d9"}, {"time": 0.3333, "name": "tx/d12"}, {"time": 0.4333, "name": "tx/d15"}, {"time": 0.5333, "name": "tx/d0"}, {"time": 0.6, "name": "tx/d3"}, {"time": 0.7, "name": "tx/d6"}, {"time": 0.7667, "name": "tx/d9"}, {"time": 0.8667, "name": "tx/d12"}, {"time": 0.9667, "name": "tx/d15"}, {"time": 1.0333, "name": "tx/d0"}, {"time": 1.1333, "name": "tx/d3"}, {"time": 1.2, "name": "tx/d6"}, {"time": 1.3, "name": "tx/d9"}, {"time": 1.4, "name": "tx/d12"}, {"time": 1.4667, "name": "tx/d15"}, {"time": 1.5667, "name": "tx/d0"}, {"time": 1.6333, "name": "tx/d3"}, {"time": 1.7333, "name": "tx/d6"}, {"time": 1.8333, "name": "tx/d9"}, {"time": 1.9, "name": "tx/d12"}, {"time": 2, "name": "tx/d15"}]}, "tx/d1": {"attachment": [{"name": "tx/d0"}, {"time": 0.1, "name": "tx/d3"}, {"time": 0.1667, "name": "tx/d6"}, {"time": 0.2667, "name": "tx/d9"}, {"time": 0.3333, "name": "tx/d12"}, {"time": 0.4333, "name": "tx/d15"}, {"time": 0.5333, "name": "tx/d0"}, {"time": 0.6, "name": "tx/d3"}, {"time": 0.7, "name": "tx/d6"}, {"time": 0.7667, "name": "tx/d9"}, {"time": 0.8667, "name": "tx/d12"}, {"time": 0.9667, "name": "tx/d15"}, {"time": 1.0333, "name": "tx/d0"}, {"time": 1.1333, "name": "tx/d3"}, {"time": 1.2, "name": "tx/d6"}, {"time": 1.3, "name": "tx/d9"}, {"time": 1.4, "name": "tx/d12"}, {"time": 1.4667, "name": "tx/d15"}, {"time": 1.5667, "name": "tx/d0"}, {"time": 1.6333, "name": "tx/d3"}, {"time": 1.7333, "name": "tx/d6"}, {"time": 1.8333, "name": "tx/d9"}, {"time": 1.9, "name": "tx/d12"}, {"time": 2, "name": "tx/d15"}]}, "tx/d2": {"attachment": [{"name": "tx/d15"}, {"time": 0.1, "name": "tx/d0"}, {"time": 0.2, "name": "tx/d3"}, {"time": 0.2667, "name": "tx/d6"}, {"time": 0.3667, "name": "tx/d9"}, {"time": 0.4667, "name": "tx/d12"}, {"time": 0.5333, "name": "tx/d15"}, {"time": 0.6333, "name": "tx/d0"}, {"time": 0.7, "name": "tx/d3"}, {"time": 0.8, "name": "tx/d6"}, {"time": 0.9, "name": "tx/d9"}, {"time": 0.9667, "name": "tx/d12"}, {"time": 1.0667, "name": "tx/d15"}, {"time": 1.1333, "name": "tx/d0"}, {"time": 1.2333, "name": "tx/d3"}, {"time": 1.3, "name": "tx/d6"}, {"time": 1.4, "name": "tx/d9"}, {"time": 1.4667, "name": "tx/d12"}, {"time": 1.5667, "name": "tx/d15"}, {"time": 1.6667, "name": "tx/d0"}, {"time": 1.7667, "name": "tx/d3"}, {"time": 1.8667, "name": "tx/d6"}, {"time": 1.9333, "name": "tx/d9"}, {"time": 2, "name": "tx/d12"}]}, "tx/d3": {"attachment": [{"name": "tx/d0"}, {"time": 0.1, "name": "tx/d3"}, {"time": 0.1667, "name": "tx/d6"}, {"time": 0.2667, "name": "tx/d9"}, {"time": 0.3333, "name": "tx/d12"}, {"time": 0.4333, "name": "tx/d15"}, {"time": 0.5333, "name": "tx/d0"}, {"time": 0.6, "name": "tx/d3"}, {"time": 0.7, "name": "tx/d6"}, {"time": 0.7667, "name": "tx/d9"}, {"time": 0.8667, "name": "tx/d12"}, {"time": 0.9667, "name": "tx/d15"}, {"time": 1.0333, "name": "tx/d0"}, {"time": 1.1333, "name": "tx/d3"}, {"time": 1.2, "name": "tx/d6"}, {"time": 1.3, "name": "tx/d9"}, {"time": 1.4, "name": "tx/d12"}, {"time": 1.4667, "name": "tx/d15"}, {"time": 1.5667, "name": "tx/d0"}, {"time": 1.6333, "name": "tx/d3"}, {"time": 1.7333, "name": "tx/d6"}, {"time": 1.8333, "name": "tx/d9"}, {"time": 1.9, "name": "tx/d12"}, {"time": 2, "name": "tx/d15"}]}}, "bones": {"chuann1": {"rotate": [{"angle": 2.07, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 4.14, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": 2.07}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "y": 21.96, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "chuann1a": {"rotate": [{"angle": 2.29, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 4.58, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": 2.29}]}, "chuann6": {"rotate": [{"angle": 2.14, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5, "angle": 1.07, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 2.14}]}, "chuann5": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": 155.89, "y": 18.94, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "chuann11": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": -176.37, "y": -21.43, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "chuann1a11": {"translate": [{"x": -1.03, "y": -0.11, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "x": -3.18, "y": -0.57, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 4.41, "y": 1.05, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": -3.18, "y": -0.57, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 4.41, "y": 1.05, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": -1.03, "y": -0.11}]}, "chuann1a12": {"translate": [{"x": 2.25, "y": 0.59, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3333, "x": -3.18, "y": -0.57, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 4.41, "y": 1.05, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -3.18, "y": -0.57, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 4.41, "y": 1.05, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 2.25, "y": 0.59}]}, "chuann1a13": {"translate": [{"x": 4.41, "y": 1.05, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -3.18, "y": -0.57, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 4.41, "y": 1.05, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": -3.18, "y": -0.57, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 4.41, "y": 1.05}]}, "chuann1a14": {"translate": [{"x": -1.03, "y": -0.11, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "x": -3.18, "y": -0.57, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 4.41, "y": 1.05, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": -3.18, "y": -0.57, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 4.41, "y": 1.05, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": -1.03, "y": -0.11}]}, "chuann1a15": {"translate": [{"x": 2.25, "y": 0.59, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3333, "x": -3.18, "y": -0.57, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 4.41, "y": 1.05, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -3.18, "y": -0.57, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 4.41, "y": 1.05, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 2.25, "y": 0.59}]}, "chuann1a16": {"translate": [{"x": 4.41, "y": 1.05, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -3.18, "y": -0.57, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 4.41, "y": 1.05, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": -3.18, "y": -0.57, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 4.41, "y": 1.05}]}, "chuann12": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": 5.86, "y": 0.71, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "chuann13": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": 11.07, "y": 1.34, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "chuann15": {"translate": [{"x": -3.05, "y": 20.19, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -4.26, "y": 28.19, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": -4.26, "y": 28.19, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": -3.05, "y": 20.19}]}, "chuann14": {"translate": [{"x": -1.21, "y": 8, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -4.26, "y": 28.19, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -4.26, "y": 28.19, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": -1.21, "y": 8}]}, "chuann1a4": {"translate": [{"x": -0.61, "y": 2.11, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 0.1667, "x": 0.45, "y": -3, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "x": 1.15, "y": -6.37, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -1.3, "y": 5.49, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 1.15, "y": -6.37, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": -1.3, "y": 5.49, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": -0.61, "y": 2.11}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 0.138, "y": 0.138, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 0.138, "y": 0.138, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "chuann1a5": {"translate": [{"x": -2.99, "y": 12.6, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.1667, "x": -1.17, "y": 3.6, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.5, "x": 3.44, "y": -19.1, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": -2.99, "y": 12.6, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 3.44, "y": -19.1, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": -2.99, "y": 12.6}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 0.138, "y": 0.138, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 0.138, "y": 0.138, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "chuann1a6": {"translate": [{"x": -2.08, "y": 7.59, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "x": -4.11, "y": 17.32, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 3.06, "y": -16.97, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": -4.11, "y": 17.32, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 3.06, "y": -16.97, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": -2.08, "y": 7.59}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 0.138, "y": 0.138, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 0.138, "y": 0.138, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "chuann1a10": {"translate": [{"x": -1.17, "y": 3.6, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3333, "x": 3.44, "y": -19.1, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -2.99, "y": 12.6, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 3.44, "y": -19.1, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": -2.99, "y": 12.6, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": -1.17, "y": 3.6}], "scale": [{"x": 1.114, "y": 1.114, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "x": 1.179, "y": 1.179, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 0.951, "y": 0.951, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": 1.179, "y": 1.179, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 0.951, "y": 0.951, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 1.114, "y": 1.114}]}, "chuann1a7": {"translate": [{"x": -1.3, "y": 5.49, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3333, "x": 0.45, "y": -3, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.5, "x": 1.15, "y": -6.37, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": -1.3, "y": 5.49, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 1.15, "y": -6.37, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": -1.3, "y": 5.49}]}, "chuann1a8": {"translate": [{"x": -1.17, "y": 3.6, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "x": -2.99, "y": 12.6, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3333, "x": -1.17, "y": 3.6, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "x": 3.44, "y": -19.1, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": -2.99, "y": 12.6, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 3.44, "y": -19.1, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": -1.17, "y": 3.6}]}, "chuann1a9": {"translate": [{"x": 1.02, "y": -7.24, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3333, "x": -4.11, "y": 17.32, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 3.06, "y": -16.97, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -4.11, "y": 17.32, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 3.06, "y": -16.97, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 1.02, "y": -7.24}]}, "chuann1a20": {"translate": [{"x": 0.34, "y": -2.92, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "x": 0.98, "y": -6.18, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -1.25, "y": 5.29, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": 0.98, "y": -6.18, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -1.25, "y": 5.29, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 0.34, "y": -2.92}]}, "chuann1a27": {"translate": [{"x": -0.62, "y": 2.02, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 0.1667, "x": 0.34, "y": -2.92, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "x": 0.98, "y": -6.18, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -1.25, "y": 5.29, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 0.98, "y": -6.18, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": -1.25, "y": 5.29, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": -0.62, "y": 2.02}]}, "chuann1a24": {"translate": [{"x": 0.8, "y": 0.19, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 2.83, "y": 0.67, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 2.83, "y": 0.67, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 0.8, "y": 0.19}]}, "chuann1a31": {"translate": [{"x": 2.25, "y": 0.53, "curve": 0.315, "c2": 0.28, "c3": 0.673, "c4": 0.69}, {"time": 0.2, "x": 0.8, "y": 0.19, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "x": 2.83, "y": 0.67, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "x": 2.83, "y": 0.67, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 2, "x": 2.25, "y": 0.53}]}, "chuann16": {"translate": [{"x": -0.24, "y": 0.08, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "x": -3.74, "y": -0.94, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 8.58, "y": 2.67, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": -3.74, "y": -0.94, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 8.58, "y": 2.67, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": -0.24, "y": 0.08}]}, "chuann18": {"translate": [{"x": 5.08, "y": 1.64, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 0.1667, "x": -0.24, "y": 0.08, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "x": -3.74, "y": -0.94, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 8.58, "y": 2.67, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -3.74, "y": -0.94, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 8.58, "y": 2.67, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 5.08, "y": 1.64}]}, "chuann19": {"translate": [{"x": -0.9, "y": 2.33, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.2333, "x": 1.35, "y": -5.34, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "x": -3.57, "y": 11.48, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "x": 1.35, "y": -5.34, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "x": -3.57, "y": 11.48, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 2, "x": -0.9, "y": 2.33}]}, "chuann20": {"translate": [{"x": -3.46, "y": 11.1, "curve": 0.277, "c2": 0.12, "c3": 0.754}, {"time": 0.4667, "x": 1.35, "y": -5.34, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "x": -3.57, "y": 11.48, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "x": 1.35, "y": -5.34, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "x": -3.57, "y": 11.48, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 2, "x": -3.46, "y": 11.1}]}, "chuann21": {"translate": [{"x": 5.09, "y": 1.64, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3333, "x": -3.74, "y": -0.94, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 8.58, "y": 2.67, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -3.74, "y": -0.94, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 8.58, "y": 2.67, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 5.09, "y": 1.64}]}, "chuann22": {"translate": [{"x": 8.58, "y": 2.67, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.1667, "x": 5.09, "y": 1.64, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.5, "x": -3.74, "y": -0.94, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 8.58, "y": 2.67, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": -3.74, "y": -0.94, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 8.58, "y": 2.67}]}, "chuann23": {"translate": [{"x": -0.9, "y": 2.33, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.2333, "x": 1.35, "y": -5.34, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "x": -3.57, "y": 11.48, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "x": 1.35, "y": -5.34, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "x": -3.57, "y": 11.48, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 2, "x": -0.9, "y": 2.33}]}, "chuann24": {"translate": [{"x": -3.46, "y": 11.1, "curve": 0.277, "c2": 0.12, "c3": 0.754}, {"time": 0.4667, "x": 1.35, "y": -5.34, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "x": -3.57, "y": 11.48, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "x": 1.35, "y": -5.34, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "x": -3.57, "y": 11.48, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 2, "x": -3.46, "y": 11.1}]}, "tong": {"rotate": [{"angle": 7.29, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "angle": 14.57, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": 7.29}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": -6.7, "y": 17, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "js_2": {"rotate": [{"angle": 6.46, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "angle": -3.05, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 15.98, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": 6.46}]}, "tong4": {"translate": [{"x": -34.4, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": -68.81, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": -34.4}]}, "tong3": {"translate": [{"x": 38.6, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 77.2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": 38.6}]}, "js_3": {"scale": [{"x": 0.995, "y": 0.995, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.2667, "x": 0.937, "y": 0.937, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "x": 1.043, "y": 1.043, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "x": 0.937, "y": 0.937, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "x": 1.043, "y": 1.043, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 2, "x": 0.995, "y": 0.995}]}}}, "idle": {"slots": {"h1": {"attachment": [{"name": null}]}, "h2": {"attachment": [{"name": null}]}, "h3": {"attachment": [{"name": null}]}, "h4": {"attachment": [{"name": null}]}, "h5": {"attachment": [{"name": null}]}, "js_2": {"attachment": [{"name": null}]}, "js_2a": {"attachment": [{"name": null}]}, "js_2b": {"attachment": [{"name": null}]}, "tong": {"attachment": [{"name": null}]}, "tx/d0": {"attachment": [{"name": "tx/d0"}, {"time": 0.1, "name": "tx/d3"}, {"time": 0.1667, "name": "tx/d6"}, {"time": 0.2667, "name": "tx/d9"}, {"time": 0.3333, "name": "tx/d12"}, {"time": 0.4333, "name": "tx/d15"}, {"time": 0.5333, "name": "tx/d0"}, {"time": 0.6, "name": "tx/d3"}, {"time": 0.7, "name": "tx/d6"}, {"time": 0.7667, "name": "tx/d9"}, {"time": 0.8667, "name": "tx/d12"}, {"time": 0.9667, "name": "tx/d15"}, {"time": 1.0333, "name": "tx/d0"}, {"time": 1.1333, "name": "tx/d3"}, {"time": 1.2, "name": "tx/d6"}, {"time": 1.3, "name": "tx/d9"}, {"time": 1.4, "name": "tx/d12"}, {"time": 1.4667, "name": "tx/d15"}, {"time": 1.5667, "name": "tx/d0"}, {"time": 1.6333, "name": "tx/d3"}, {"time": 1.7333, "name": "tx/d6"}, {"time": 1.8333, "name": "tx/d9"}, {"time": 1.9, "name": "tx/d12"}, {"time": 2, "name": "tx/d15"}]}, "tx/d1": {"attachment": [{"name": "tx/d0"}, {"time": 0.1, "name": "tx/d3"}, {"time": 0.1667, "name": "tx/d6"}, {"time": 0.2667, "name": "tx/d9"}, {"time": 0.3333, "name": "tx/d12"}, {"time": 0.4333, "name": "tx/d15"}, {"time": 0.5333, "name": "tx/d0"}, {"time": 0.6, "name": "tx/d3"}, {"time": 0.7, "name": "tx/d6"}, {"time": 0.7667, "name": "tx/d9"}, {"time": 0.8667, "name": "tx/d12"}, {"time": 0.9667, "name": "tx/d15"}, {"time": 1.0333, "name": "tx/d0"}, {"time": 1.1333, "name": "tx/d3"}, {"time": 1.2, "name": "tx/d6"}, {"time": 1.3, "name": "tx/d9"}, {"time": 1.4, "name": "tx/d12"}, {"time": 1.4667, "name": "tx/d15"}, {"time": 1.5667, "name": "tx/d0"}, {"time": 1.6333, "name": "tx/d3"}, {"time": 1.7333, "name": "tx/d6"}, {"time": 1.8333, "name": "tx/d9"}, {"time": 1.9, "name": "tx/d12"}, {"time": 2, "name": "tx/d15"}]}, "tx/d2": {"attachment": [{"name": "tx/d15"}, {"time": 0.1, "name": "tx/d0"}, {"time": 0.2, "name": "tx/d3"}, {"time": 0.2667, "name": "tx/d6"}, {"time": 0.3667, "name": "tx/d9"}, {"time": 0.4667, "name": "tx/d12"}, {"time": 0.5333, "name": "tx/d15"}, {"time": 0.6333, "name": "tx/d0"}, {"time": 0.7, "name": "tx/d3"}, {"time": 0.8, "name": "tx/d6"}, {"time": 0.9, "name": "tx/d9"}, {"time": 0.9667, "name": "tx/d12"}, {"time": 1.0667, "name": "tx/d15"}, {"time": 1.1333, "name": "tx/d0"}, {"time": 1.2333, "name": "tx/d3"}, {"time": 1.3, "name": "tx/d6"}, {"time": 1.4, "name": "tx/d9"}, {"time": 1.4667, "name": "tx/d12"}, {"time": 1.5667, "name": "tx/d15"}, {"time": 1.6667, "name": "tx/d0"}, {"time": 1.7667, "name": "tx/d3"}, {"time": 1.8667, "name": "tx/d6"}, {"time": 1.9333, "name": "tx/d9"}, {"time": 2, "name": "tx/d12"}]}, "tx/d3": {"attachment": [{"name": "tx/d0"}, {"time": 0.1, "name": "tx/d3"}, {"time": 0.1667, "name": "tx/d6"}, {"time": 0.2667, "name": "tx/d9"}, {"time": 0.3333, "name": "tx/d12"}, {"time": 0.4333, "name": "tx/d15"}, {"time": 0.5333, "name": "tx/d0"}, {"time": 0.6, "name": "tx/d3"}, {"time": 0.7, "name": "tx/d6"}, {"time": 0.7667, "name": "tx/d9"}, {"time": 0.8667, "name": "tx/d12"}, {"time": 0.9667, "name": "tx/d15"}, {"time": 1.0333, "name": "tx/d0"}, {"time": 1.1333, "name": "tx/d3"}, {"time": 1.2, "name": "tx/d6"}, {"time": 1.3, "name": "tx/d9"}, {"time": 1.4, "name": "tx/d12"}, {"time": 1.4667, "name": "tx/d15"}, {"time": 1.5667, "name": "tx/d0"}, {"time": 1.6333, "name": "tx/d3"}, {"time": 1.7333, "name": "tx/d6"}, {"time": 1.8333, "name": "tx/d9"}, {"time": 1.9, "name": "tx/d12"}, {"time": 2, "name": "tx/d15"}]}}, "bones": {"chuann1": {"rotate": [{"angle": 2.07, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 4.14, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": 2.07}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "y": 21.96, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "chuann1a": {"rotate": [{"angle": 2.29, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 4.58, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": 2.29}]}, "chuann6": {"rotate": [{"angle": 2.14, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5, "angle": 1.07, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 2.14}]}, "chuann5": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": 155.89, "y": 18.94, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "chuann11": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": -176.37, "y": -21.43, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "chuann1a11": {"translate": [{"x": -1.03, "y": -0.11, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "x": -3.18, "y": -0.57, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 4.41, "y": 1.05, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": -3.18, "y": -0.57, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 4.41, "y": 1.05, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": -1.03, "y": -0.11}]}, "chuann1a12": {"translate": [{"x": 2.25, "y": 0.59, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3333, "x": -3.18, "y": -0.57, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 4.41, "y": 1.05, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -3.18, "y": -0.57, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 4.41, "y": 1.05, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 2.25, "y": 0.59}]}, "chuann1a13": {"translate": [{"x": 4.41, "y": 1.05, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -3.18, "y": -0.57, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 4.41, "y": 1.05, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": -3.18, "y": -0.57, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 4.41, "y": 1.05}]}, "chuann1a14": {"translate": [{"x": -1.03, "y": -0.11, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "x": -3.18, "y": -0.57, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 4.41, "y": 1.05, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": -3.18, "y": -0.57, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 4.41, "y": 1.05, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": -1.03, "y": -0.11}]}, "chuann1a15": {"translate": [{"x": 2.25, "y": 0.59, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3333, "x": -3.18, "y": -0.57, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 4.41, "y": 1.05, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -3.18, "y": -0.57, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 4.41, "y": 1.05, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 2.25, "y": 0.59}]}, "chuann1a16": {"translate": [{"x": 4.41, "y": 1.05, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -3.18, "y": -0.57, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 4.41, "y": 1.05, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": -3.18, "y": -0.57, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 4.41, "y": 1.05}]}, "chuann12": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": 5.86, "y": 0.71, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "chuann13": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": 11.07, "y": 1.34, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "chuann15": {"translate": [{"x": -3.05, "y": 20.19, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -4.26, "y": 28.19, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": -4.26, "y": 28.19, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": -3.05, "y": 20.19}]}, "chuann14": {"translate": [{"x": -1.21, "y": 8, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -4.26, "y": 28.19, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -4.26, "y": 28.19, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": -1.21, "y": 8}]}, "chuann1a4": {"translate": [{"x": -0.61, "y": 2.11, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 0.1667, "x": 0.45, "y": -3, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "x": 1.15, "y": -6.37, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -1.3, "y": 5.49, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 1.15, "y": -6.37, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": -1.3, "y": 5.49, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": -0.61, "y": 2.11}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 0.138, "y": 0.138, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 0.138, "y": 0.138, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "chuann1a5": {"translate": [{"x": -2.99, "y": 12.6, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.1667, "x": -1.17, "y": 3.6, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.5, "x": 3.44, "y": -19.1, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": -2.99, "y": 12.6, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 3.44, "y": -19.1, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": -2.99, "y": 12.6}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 0.138, "y": 0.138, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 0.138, "y": 0.138, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "chuann1a6": {"translate": [{"x": -2.08, "y": 7.59, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "x": -4.11, "y": 17.32, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 3.06, "y": -16.97, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": -4.11, "y": 17.32, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 3.06, "y": -16.97, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": -2.08, "y": 7.59}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 0.138, "y": 0.138, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 0.138, "y": 0.138, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "chuann1a10": {"translate": [{"x": -1.17, "y": 3.6, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3333, "x": 3.44, "y": -19.1, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -2.99, "y": 12.6, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 3.44, "y": -19.1, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": -2.99, "y": 12.6, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": -1.17, "y": 3.6}], "scale": [{"x": 1.114, "y": 1.114, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "x": 1.179, "y": 1.179, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 0.951, "y": 0.951, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": 1.179, "y": 1.179, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 0.951, "y": 0.951, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 1.114, "y": 1.114}]}, "chuann1a7": {"translate": [{"x": -1.3, "y": 5.49, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3333, "x": 0.45, "y": -3, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.5, "x": 1.15, "y": -6.37, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": -1.3, "y": 5.49, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 1.15, "y": -6.37, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": -1.3, "y": 5.49}]}, "chuann1a8": {"translate": [{"x": -1.17, "y": 3.6, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "x": -2.99, "y": 12.6, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3333, "x": -1.17, "y": 3.6, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "x": 3.44, "y": -19.1, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": -2.99, "y": 12.6, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 3.44, "y": -19.1, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": -1.17, "y": 3.6}]}, "chuann1a9": {"translate": [{"x": 1.02, "y": -7.24, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3333, "x": -4.11, "y": 17.32, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 3.06, "y": -16.97, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -4.11, "y": 17.32, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 3.06, "y": -16.97, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 1.02, "y": -7.24}]}, "chuann1a20": {"translate": [{"x": 0.34, "y": -2.92, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "x": 0.98, "y": -6.18, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -1.25, "y": 5.29, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": 0.98, "y": -6.18, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -1.25, "y": 5.29, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 0.34, "y": -2.92}]}, "chuann1a27": {"translate": [{"x": -0.62, "y": 2.02, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 0.1667, "x": 0.34, "y": -2.92, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "x": 0.98, "y": -6.18, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -1.25, "y": 5.29, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 0.98, "y": -6.18, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": -1.25, "y": 5.29, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": -0.62, "y": 2.02}]}, "chuann1a24": {"translate": [{"x": 0.8, "y": 0.19, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 2.83, "y": 0.67, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 2.83, "y": 0.67, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 0.8, "y": 0.19}]}, "chuann1a31": {"translate": [{"x": 2.25, "y": 0.53, "curve": 0.315, "c2": 0.28, "c3": 0.673, "c4": 0.69}, {"time": 0.2, "x": 0.8, "y": 0.19, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "x": 2.83, "y": 0.67, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "x": 2.83, "y": 0.67, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 2, "x": 2.25, "y": 0.53}]}, "chuann16": {"translate": [{"x": -0.24, "y": 0.08, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "x": -3.74, "y": -0.94, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 8.58, "y": 2.67, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": -3.74, "y": -0.94, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 8.58, "y": 2.67, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": -0.24, "y": 0.08}]}, "chuann18": {"translate": [{"x": 5.08, "y": 1.64, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 0.1667, "x": -0.24, "y": 0.08, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "x": -3.74, "y": -0.94, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 8.58, "y": 2.67, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -3.74, "y": -0.94, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 8.58, "y": 2.67, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 5.08, "y": 1.64}]}, "chuann19": {"translate": [{"x": -0.9, "y": 2.33, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.2333, "x": 1.35, "y": -5.34, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "x": -3.57, "y": 11.48, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "x": 1.35, "y": -5.34, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "x": -3.57, "y": 11.48, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 2, "x": -0.9, "y": 2.33}]}, "chuann20": {"translate": [{"x": -3.46, "y": 11.1, "curve": 0.277, "c2": 0.12, "c3": 0.754}, {"time": 0.4667, "x": 1.35, "y": -5.34, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "x": -3.57, "y": 11.48, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "x": 1.35, "y": -5.34, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "x": -3.57, "y": 11.48, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 2, "x": -3.46, "y": 11.1}]}, "chuann21": {"translate": [{"x": 5.09, "y": 1.64, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3333, "x": -3.74, "y": -0.94, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 8.58, "y": 2.67, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -3.74, "y": -0.94, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 8.58, "y": 2.67, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 5.09, "y": 1.64}]}, "chuann22": {"translate": [{"x": 8.58, "y": 2.67, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.1667, "x": 5.09, "y": 1.64, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.5, "x": -3.74, "y": -0.94, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 8.58, "y": 2.67, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": -3.74, "y": -0.94, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 8.58, "y": 2.67}]}, "chuann23": {"translate": [{"x": -0.9, "y": 2.33, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.2333, "x": 1.35, "y": -5.34, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "x": -3.57, "y": 11.48, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "x": 1.35, "y": -5.34, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "x": -3.57, "y": 11.48, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 2, "x": -0.9, "y": 2.33}]}, "chuann24": {"translate": [{"x": -3.46, "y": 11.1, "curve": 0.277, "c2": 0.12, "c3": 0.754}, {"time": 0.4667, "x": 1.35, "y": -5.34, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "x": -3.57, "y": 11.48, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "x": 1.35, "y": -5.34, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "x": -3.57, "y": 11.48, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 2, "x": -3.46, "y": 11.1}]}, "tong": {"rotate": [{"angle": 7.29, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "angle": 14.57, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": 7.29}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": -6.7, "y": 17, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "tong2": {"translate": [{"x": 220.12}]}, "js_2": {"rotate": [{"angle": 6.46, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "angle": -3.05, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 15.98, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": 6.46}]}, "tong4": {"translate": [{"x": -34.4, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": -68.81, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": -34.4}]}, "tong3": {"translate": [{"x": 38.6, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 77.2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": 38.6}]}, "tx/d0": {"scale": [{"x": 0.792}]}, "tx/d2": {"translate": [{"x": -151.69}]}, "tx/d3": {"translate": [{"x": -165.35}]}, "js_3": {"scale": [{"x": 0.995, "y": 0.995, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.2667, "x": 0.937, "y": 0.937, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "x": 1.043, "y": 1.043, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "x": 0.937, "y": 0.937, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "x": 1.043, "y": 1.043, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 2, "x": 0.995, "y": 0.995}]}, "chuan": {"translate": [{"x": 86.98}], "scale": [{"x": 1.171, "y": 1.171}]}}}}}