/**
 * @describe TOAST弹窗组件
 * <AUTHOR>
 * @date 2024-09-12 11:42:29
 */

import {
    Component,
    Label,
    Tween,
    tween,
    _decorator,
    Node,
    UITransform,
    v3,
    UIOpacity,
    log,
} from 'cc'
import { BaseComponent } from '@/core/manager/gui/base/BaseComponent'
const { ccclass, property } = _decorator

export enum ToastType {
    FIXED,
    SLIDE,
}

export type ToastProps = {
    title: string
    type?: ToastType
    fixed_time?: number
}

@ccclass('Toast')
export class Toast extends BaseComponent<ToastProps> {
    @property({ type: Node, tooltip: '固定节点' })
    fixed_node: Node

    @property({ type: Node, tooltip: '滑动节点' })
    slide_node: Node

    @property({ type: Label, tooltip: '固定标签节点' })
    fixed_label: Label

    @property({ type: Label, tooltip: '滑动标签节点' })
    slide_label: Label

    override onLoad() {
        this.fixed_node.active = this.slide_node.active = false
    }

    protected override start(): void {
        this.toast()
    }

    toast() {
        return new Promise<void>(async (resolve, _reject) => {
            const { title, type, fixed_time } = this.props
            if (type == ToastType.FIXED) {
                this.fixed_node.active = true
                this.fixed_label.string = `${title}`
                // v2.4
                // this.fixed_label['_forceUpdateRenderData'](true);
                // v3.0
                this.fixed_label.updateRenderData(true)
                const fixed_label_height =
                    this.fixed_label.node.getComponent(UITransform)!.height
                const fixed_node_height =
                    this.fixed_node.getComponent(UITransform)!.height
                if (fixed_label_height - fixed_node_height > 0) {
                    this.fixed_node.getComponent(UITransform)!.height =
                        fixed_label_height + 100
                }
                this.scheduleOnce(() => {
                    this.node.destroy()
                    resolve()
                }, fixed_time)
            } else {
                this.slide_node.active = true
                this.slide_label.string = `${title}`
                // v2.4
                // this.slide_label['_forceUpdateRenderData'](true);
                // v3.0
                this.slide_label.updateRenderData(true)
                const deslabelW =
                    this.slide_label.node.getComponent(UITransform)!.width
                const bgw = this.slide_node.getComponent(UITransform)!.width
                if (bgw - deslabelW < 100) {
                    this.slide_node.getComponent(UITransform)!.width =
                        deslabelW + 100
                }
                this.playAnim(this.node).then(() => {
                    resolve()
                })
            }
        })
    }

    playAnim(node: Node) {
        return new Promise<void>((resolve, reject) => {
            const delay: number = 1.2
            const duration: number = 0.5
            const uiOpacity = this.node.getComponent(UIOpacity)
            const uiOpacityTween = tween(uiOpacity)
                .delay(delay)
                .to(duration, { opacity: 0 })
                .call(() => {
                    Tween.stopAllByTarget(node)
                    this.node.destroy()
                    resolve()
                })

            tween(node)
                .by(duration, { position: v3(0, 400, 0) })
                .call(() => {
                    uiOpacityTween.start()
                })
                .start()
        })
    }

    override onDestroy(): void {
        Tween.stopAllByTarget(this.node)
        this.unscheduleAllCallbacks()
    }
}
