/**
 * @describe 全屏区域组件
 * <AUTHOR>
 * @date 2023-09-04 17:43:27
 */

import {
    _decorator,
    CCFloat,
    Component,
    Node,
    SafeArea,
    Widget,
    screen,
    UITransform,
    log,
    view,
    macro,
    find,
    Canvas,
    Scene,
    director,
    game,
    Game,
    math,
    __private,
    DebugView,
} from 'cc'
import { BaseComponent } from '@/core/manager/gui/base/BaseComponent'

const { ccclass, property } = _decorator

@ccclass('FullScreenArea')
export class FullScreenArea extends Component {
    override onLoad() {
        screen.on('window-resize', this.updateSize, this)
        this.updateSize()
        // view.setOrientation(rate > designRate ? /**横屏 */ macro.ORIENTATION_LANDSCAPE :/**竖屏 */macro.ORIENTATION_PORTRAIT)
        // const visibleSize = view.getVisibleSize();
        // const designSize = view.getDesignResolutionSize();
        // window.ccLog(visibleSize, designSize)
        // // 屏幕分辨率
        // const devicePixelRatio = screen.devicePixelRatio
        // // 获取屏幕尺寸
        // const { width, height } = screen.windowSize;
        // // 设置节点尺寸
        // window.ccLog('设置节点尺寸', width, height, width * devicePixelRatio, height / devicePixelRatio)

        // window.ccLog('---', this.getComponent(UITransform))
    }

    protected override onDestroy(): void {
        screen.off('window-resize', this.updateSize, this)
    }

    private updateSize() {
        // 获取屏幕尺寸
        const { width, height } = screen.windowSize
        const designSize = view.getDesignResolutionSize()

        // 经过计算后的canvas宽高
        let canvas: math.Size | null = null

        // 计算宽高比
        const model =
            width / height > designSize.width / designSize.height
                ? 'HEIGHT'
                : 'WIDTH'
        if (model == 'HEIGHT') {
            // 固定高度
            const canvasWidth =
                (designSize.width /
                    (height / (designSize.height / designSize.width))) *
                width
            canvas = new math.Size(canvasWidth, designSize.height)
        } else if (model == 'WIDTH') {
            // 固定宽度
            const canvasHeight =
                designSize.height /
                (width / (designSize.width / designSize.height) / height)
            canvas = new math.Size(designSize.width, canvasHeight)
        } else {
            canvas = designSize
        }
        window.ccLog('screen', width, height)
        window.ccLog('model', model)
        window.ccLog('designSize', designSize)
        window.ccLog('FullScreenArea: ', canvas)
        this.getComponent(UITransform)!.setContentSize(canvas)
    }
}
