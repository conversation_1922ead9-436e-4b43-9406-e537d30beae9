/**
 * @describe 随乐游socket类
 * <AUTHOR>
 * @date 2024-09-12 11:41:27
 */

import { error, Game, game, log } from 'cc'
import { SuiLeYooSocket } from '@/core/manager/request/websocket/SuiLeYooSocket'
import { BaseManager } from '@/core/manager/BaseManager'
import { Manager } from '@/core/manager'
import { BaseWebSocket } from './BaseWebSocket'

declare module '@/core/manager' {
    interface Manager {
        suiLeYooSocket: SocialGameSuiLeYoosSocket & SuiLeYooSocket
    }
}

export class SocialGameSuiLeYoosSocket extends BaseWebSocket<SuiLeYooSocket> {
    constructor(cat: Manager) {
        super(cat)
    }

    /**创建 */
    create = async (url: string) => {
        if (!this.ins) {
            return console.error(
                '[SuiLeYooSocket] is alreay exist! please invoke destroy first'
            )
        }
        this.ins = new SuiLeYooSocket(url)
        // 添加服务端监听
        await this.ins.connect()
    }
}

export const createProxySuileyooSocket = (cat: Manager) => {
    const instance = new SocialGameSuiLeYoosSocket(cat)
    return new Proxy(instance, handler) as SuiLeYooSocket &
        SocialGameSuiLeYoosSocket
}

const handler: ProxyHandler<SocialGameSuiLeYoosSocket & SuiLeYooSocket> = {
    get(target, prop: PropertyKey, receiver: any) {
        if (target.hasOwnProperty(prop)) {
            return Reflect.get(
                target,
                prop,
                receiver
            ) as SocialGameSuiLeYoosSocket
        } else if (target.ins) {
            // 对于其他属性，返回 WrapperSocialGameClient 类型
            return (...args: any[]) =>
                Reflect.get(target.ins!, prop, receiver).apply(
                    target.ins,
                    args
                ) as SuiLeYooSocket
        } else {
            const err = new Error(
                '[suileyoo ws] is not available, please invoke [create]'
            )
            error(err)
            throw err
        }
    },

    set(target, prop: PropertyKey, value: any) {
        if (typeof prop === 'string') {
            // 使用类型断言将 prop 断言为 keyof SocialGameBusinessSocket
            ;(target as any)[prop] = value
            window.ccLog(`Setting property ${prop} to ${value}`)
            return true
        }
        return false
    },
}
