import { _decorator, Button, Component, Input, Label, log, Node } from 'cc'
import UILayer from '@/core/manager/gui/layer/UILayer'
import RootUILayer from '@/core/manager/gui/layer/RootUILayer'
import { cat } from '@/core/manager'
const { ccclass, property } = _decorator

export type NoticeProps = {
    text: string
    confrim?: () => void
}

/**
 * @describe 通知框
 * <AUTHOR>
 * @date 2024-09-12 11:49:51
 */

@ccclass('Notice')
export class Notice<
    T extends NoticeProps = NoticeProps
> extends RootUILayer<T> {
    @property({ type: Label, tooltip: '提示文本' })
    text: Label

    @property({ type: Button, tooltip: '确定按钮' })
    btn_confirm: Button

    protected override onLoad(): void {
        this.btn_confirm.node.on(
            Button.EventType.CLICK,
            this.onConfrimHandler,
            this
        )
    }

    override start() {
        this.props && this.updateProps(this.props)
    }

    protected onConfrimHandler() {
        this.props?.confrim?.()
        cat.gui.hideNotice()
    }

    updateProps(props: NoticeProps): void {
        this.text.string = `${props.text}`
    }
}
