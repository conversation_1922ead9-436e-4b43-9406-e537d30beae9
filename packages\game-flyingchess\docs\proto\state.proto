syntax = "proto3";

package flyingchess.v1;

// 游戏状态枚举
enum State {
  // 未指定状态
  STATE_UNSPECIFIED = 0;

  // ---- 游戏状态 ----
  // 游戏开始
  STATE_GAME_START = 1;
  // 游戏结束
  STATE_GAME_OVER = 2;
  // 游戏正在结束（结算中）
  STATE_GAME_FINISHING = 3;
  // 游戏已结束（结算完成）
  STATE_GAME_FINISHED = 4;

  // ---- 回合状态 ----
  // 回合开始
  STATE_ROUND_START = 10;
  // 玩家投掷骰子
  STATE_PLAYER_ROLL_DICE = 11;
  // 玩家投掷骰子完成
  STATE_PLAYER_ROLLED_DICE = 12;
  // 玩家选择棋子
  STATE_PLAYER_SELECT_CHESS = 13;
  // 玩家选择棋子完成
  STATE_PLAYER_SELECTED_CHESS = 14;
  // 棋子移动中
  STATE_CHESS_MOVING = 15;
  // 棋子移动完成
  STATE_CHESS_MOVED = 16;
  // 特殊效果触发（如撞子、迭子、飞棋等）
  STATE_SPECIAL_EFFECT = 17;
  // 特殊效果完成
  STATE_SPECIAL_EFFECT_FINISHED = 18;
  // 回合结束
  STATE_ROUND_END = 19;

  // ---- 托管状态 ----
  // 玩家托管
  STATE_PLAYER_HOSTING = 30;
  // 玩家取消托管
  STATE_PLAYER_CANCEL_HOSTING = 31;

  // ---- 断线重连状态 ----
  // 玩家断线
  STATE_PLAYER_DISCONNECTED = 40;
  // 玩家重连
  STATE_PLAYER_RECONNECTED = 41;
}

// 棋子状态枚举
enum ChessState {
  // 未指定状态
  CHESS_STATE_UNSPECIFIED = 0;
  // 在起点
  CHESS_STATE_IN_START = 1;
  // 在棋盘上
  CHESS_STATE_ON_BOARD = 2;
  // 在终点
  CHESS_STATE_IN_END = 3;
}

// 格子类型枚举
enum GridType {
  // 未指定类型
  GRID_TYPE_UNSPECIFIED = 0;
  // 起点格子
  GRID_TYPE_START = 1;
  // 普通格子
  GRID_TYPE_NORMAL = 2;
  // 跳跃格子（与自己颜色相同的格子）
  GRID_TYPE_JUMP = 3;
  // 飞行格子（飞机标记格子）
  GRID_TYPE_FLY = 4;
  // 冲刺格子（通向终点的格子）
  GRID_TYPE_SPRINT = 5;
  // 终点格子
  GRID_TYPE_END = 6;
}

// 特殊效果类型枚举
enum SpecialEffectType {
  // 未指定类型
  SPECIAL_EFFECT_UNSPECIFIED = 0;
  // 撞子（将对方棋子撞回起点）
  SPECIAL_EFFECT_BUMP = 1;
  // 迭子（多个棋子叠在一起）
  SPECIAL_EFFECT_STACK = 2;
  // 跳跃（跳到下一个相同颜色的格子）
  SPECIAL_EFFECT_JUMP = 3;
  // 飞行（沿着虚线飞到指定格子）
  SPECIAL_EFFECT_FLY = 4;
  // 连投（投掷到6点可以再投一次）
  SPECIAL_EFFECT_ROLL_AGAIN = 5;
}
