/**
 * @describe 音效
 * <AUTHOR>
 * @date 2023-08-03 10:59:44
 */
import { AudioClip, AudioSource, error, _decorator } from 'cc'
import { CommonAudio } from './CommonAudio'
import { AudioEffectConstant } from '@/core/business/constant'
import store from '@/core/business/store'
import { JSBridgeClient } from '@/core/business/jsbridge/JSBridge'

const { ccclass, menu } = _decorator

/**
 * 注：用playOneShot播放的音乐效果，在播放期间暂时没办法即时关闭音乐
 */

/** 游戏音效 */
@ccclass('AudioEffect')
export class AudioEffect extends CommonAudio {
    private effects: Map<string, AudioClip> = new Map<string, AudioClip>()

    preloadAll() {
        window.ccLog('preload all audio effect')
        Object.values(AudioEffectConstant).forEach((url) => this.load(url))
        return this
    }

    /**
     * 加载音效并播放
     * @param url           音效资源地址
     * @param callback      资源加载完成并开始播放回调
     */
    load(url: string): Promise<AudioClip> {
        window.ccLog('开始加载音效', url)

        return new Promise((resolve, reject) => {
            if (this.effects.has(url)) {
                resolve(this.effects.get(url)!)
            }
            this.cat.res.load(
                url,
                AudioClip,
                (err: Error | null, data: AudioClip) => {
                    if (err) {
                        window.ccLog('加载音效失败', url)
                        return reject(err)
                    }
                    this.effects.set(url, data)
                    window.ccLog('加载音效成功', url)
                    // this.playOneShot(data, this.volume)
                    resolve(data)
                }
            )
        })
    }

    /** 释放所有已使用过的音效资源 */
    release() {
        for (let key in this.effects) {
            this.cat.res.release(key)
        }
        this.effects.clear()
    }

    async loadAndPlay(url: string) {
        const data = await this.load(url)

        if (store.global.isSupportNativePlayAudio) {
            //window.ccLog('AudioEffect-->playAudio(), data:', data.nativeUrl)
            JSBridgeClient.playAudio(data.nativeUrl)
        } else {
            this.playOneShot(data, this.volume)
        }
    }
}
