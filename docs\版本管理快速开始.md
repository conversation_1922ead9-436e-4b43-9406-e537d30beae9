# Monorepo 版本管理指南

本项目是一个 monorepo，包含多个游戏子项目，每个游戏都有独立的版本管理。使用 `scripts/version.js` 脚本进行自动化版本管理，支持为每个游戏项目独立管理版本号、生成 changelog 和创建 git tags，并自动推送到远端。

## 🎮 游戏项目

-   **小心鲨手** (shark) - `packages/game-shark`
-   **海盗桶** (pirate) - `packages/game-pirate`
-   **只言片语** (dixit) - `packages/game-dixit`

## 🚀 快速使用

### 基本用法

```bash
# 预览模式（推荐先预览）
$env:DRY_RUN="true"; yarn release:shark

# 为指定游戏发布版本（自动推送到远端）
yarn release:shark    # 小心鲨手
yarn release:pirate   # 海盗桶
yarn release:dixit    # 只言片语
yarn release:all      # 所有有变更的游戏

# 指定版本类型
node scripts/version.js shark patch   # 小心鲨手补丁版本
node scripts/version.js pirate minor  # 海盗桶次要版本
node scripts/version.js dixit major   # 只言片语主要版本
```

### 版本类型

支持以下版本类型：

-   `patch` - 补丁版本（1.0.0 → 1.0.1）
-   `minor` - 次要版本（1.0.0 → 1.1.0）
-   `major` - 主要版本（1.0.0 → 2.0.0）
-   `prerelease` - 预发布版本（1.0.0 → 1.0.1-0）

如果不指定版本类型，脚本会根据 commit 信息自动判断。

## 📝 Commit 规范

请遵循 [Conventional Commits](https://www.conventionalcommits.org/) 规范：

```bash
# 新功能 (触发 minor 版本)
git commit -m "feat: 添加用户登录功能"

# Bug修复 (触发 patch 版本)
git commit -m "fix: 修复登录页面样式问题"

# 重大更改 (触发 major 版本)
git commit -m "feat!: 重构用户认证系统

BREAKING CHANGE: 用户认证API发生重大变更"
```

## ⚙️ 环境变量控制

可以通过环境变量控制脚本的行为：

### DRY_RUN - 预览模式

```bash
# 预览模式，不会实际执行更改
$env:DRY_RUN="true"; yarn release:shark
```

### SKIP_CHANGELOG - 跳过 changelog 生成

```bash
# 跳过 changelog 生成
$env:SKIP_CHANGELOG="true"; yarn release:shark
```

### SKIP_COMMIT - 跳过 git commit

```bash
# 跳过 git commit
$env:SKIP_COMMIT="true"; yarn release:shark
```

### SKIP_TAG - 跳过 git tag

```bash
# 跳过 git tag 创建
$env:SKIP_TAG="true"; yarn release:shark
```

### SKIP_PUSH - 跳过推送 tags 到远端 ⭐ 新功能

```bash
# 跳过推送 tags 到远端（只在本地创建 tags）
$env:SKIP_PUSH="true"; yarn release:shark
```

### 组合使用

```bash
# 只更新版本号和 changelog，不提交也不推送
$env:SKIP_COMMIT="true"; $env:SKIP_TAG="true"; $env:SKIP_PUSH="true"; yarn release:shark
```

## 🚀 自动推送功能 ⭐ 新功能

### 默认行为

当运行 `yarn release:*` 命令后，脚本会自动执行以下步骤：

1. 检查游戏是否有变更
2. 更新版本号
3. 生成 changelog
4. 创建 git commit
5. 创建 git tag
6. **自动推送所有 tags 到远端**

### 推送逻辑

-   只有在成功创建了新版本的情况下才会推送 tags
-   如果设置了 `SKIP_TAG=true` 或 `SKIP_PUSH=true`，则不会推送
-   如果是 `DRY_RUN=true` 预览模式，也不会推送
-   推送失败不会影响版本管理流程，但会在汇总中显示失败状态

### 输出示例

```
📊 版本管理汇总:
✅ 成功: 1
⏭️  跳过: 0
❌ 失败: 0
🚀 Tags推送: ✅ 成功
```

## 📁 生成的文件

-   `CHANGELOG.md` - 自动生成的更新日志
-   `version-result.json` - 版本管理结果（CI 使用）
-   Git tag - 自动创建的版本标签（自动推送到远端）

## 🔧 工作流程

1. **开发** → 遵循 commit 规范提交代码
2. **版本管理** → 运行版本管理命令
3. **自动推送** → tags 自动推送到远端
4. **构建部署** → 使用生成的版本号进行构建和部署

## 🛠️ 故障排除

### 推送失败

如果 tags 推送失败，可能的原因：

1. 网络连接问题
2. 没有推送权限
3. 远程仓库不存在

可以手动推送：

```bash
git push --tags
```

### 跳过推送

如果不想自动推送，可以设置环境变量：

```bash
$env:SKIP_PUSH="true"; yarn release:shark
```

### 只推送不创建版本

如果只想推送现有的 tags：

```bash
git push --tags
```

## 📖 详细文档

更多详细信息请查看 [docs/VERSION_MANAGEMENT.md](docs/VERSION_MANAGEMENT.md)
