import store from "../../store";
import { log } from "cc";
import { HTML5, WECHAT } from "cc/env";
import AdverWX from "./adverWX";
import { CustomPlatform } from "../../store/global";

export default function Adver() {
    let plateforAD: AdverWX | null = null
    if (!plateforAD) {
        // if (store.global.customPlatform == CustomPlatform.HuPuH5) {
        //     // plateforAD = new AdverHuPu()
        // }
        // /**获取平台 */
        // else if (WECHAT) {
        //     plateforAD = new AdverWX()
        // }
    }

    return plateforAD
}