// @generated by protoc-gen-es v2.2.5 with parameter "target=ts,import_extension=none,json_types=true"
// @generated from file game/game.proto (package client.game, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv1";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file game/game.proto.
 */
export const file_game_game: GenFile = /*@__PURE__*/
  fileDesc("Cg9nYW1lL2dhbWUucHJvdG8SC2NsaWVudC5nYW1lIjQKBlJvdGF0ZRIJCgF4GAEgASgFEgkKAXkYAiABKAUSCQoBehgDIAEoBRIJCgF3GAQgASgFIsIBChREYXRhQnJvYWRjYXN0Q29udGVudBIoCgZyb3RhdGUYASABKAsyEy5jbGllbnQuZ2FtZS5Sb3RhdGVIAIgBARIQCghzZWxlY3RlZBgCIAMoBRIZChFzZWxlY3RlZENhcmRJbmRleBgDIAEoBRISCgVpbmRleBgEIAEoBUgBiAEBEhkKDGJhcnJlbF9hbmdsZRgFIAEoBUgCiAEBQgkKB19yb3RhdGVCCAoGX2luZGV4Qg8KDV9iYXJyZWxfYW5nbGViBnByb3RvMw");

/**
 * @generated from message client.game.Rotate
 */
export type Rotate = Message<"client.game.Rotate"> & {
  /**
   * @generated from field: int32 x = 1;
   */
  x: number;

  /**
   * @generated from field: int32 y = 2;
   */
  y: number;

  /**
   * @generated from field: int32 z = 3;
   */
  z: number;

  /**
   * @generated from field: int32 w = 4;
   */
  w: number;
};

/**
 * @generated from message client.game.Rotate
 */
export type RotateJson = {
  /**
   * @generated from field: int32 x = 1;
   */
  x?: number;

  /**
   * @generated from field: int32 y = 2;
   */
  y?: number;

  /**
   * @generated from field: int32 z = 3;
   */
  z?: number;

  /**
   * @generated from field: int32 w = 4;
   */
  w?: number;
};

/**
 * Describes the message client.game.Rotate.
 * Use `create(RotateSchema)` to create a new message.
 */
export const RotateSchema: GenMessage<Rotate, RotateJson> = /*@__PURE__*/
  messageDesc(file_game_game, 0);

/**
 * 数据同步内容
 *
 * @generated from message client.game.DataBroadcastContent
 */
export type DataBroadcastContent = Message<"client.game.DataBroadcastContent"> & {
  /**
   * @generated from field: optional client.game.Rotate rotate = 1;
   */
  rotate?: Rotate;

  /**
   * @generated from field: repeated int32 selected = 2;
   */
  selected: number[];

  /**
   * @generated from field: int32 selectedCardIndex = 3;
   */
  selectedCardIndex: number;

  /**
   * @generated from field: optional int32 index = 4;
   */
  index?: number;

  /**
   * @generated from field: optional int32 barrel_angle = 5;
   */
  barrelAngle?: number;
};

/**
 * 数据同步内容
 *
 * @generated from message client.game.DataBroadcastContent
 */
export type DataBroadcastContentJson = {
  /**
   * @generated from field: optional client.game.Rotate rotate = 1;
   */
  rotate?: RotateJson;

  /**
   * @generated from field: repeated int32 selected = 2;
   */
  selected?: number[];

  /**
   * @generated from field: int32 selectedCardIndex = 3;
   */
  selectedCardIndex?: number;

  /**
   * @generated from field: optional int32 index = 4;
   */
  index?: number;

  /**
   * @generated from field: optional int32 barrel_angle = 5;
   */
  barrelAngle?: number;
};

/**
 * Describes the message client.game.DataBroadcastContent.
 * Use `create(DataBroadcastContentSchema)` to create a new message.
 */
export const DataBroadcastContentSchema: GenMessage<DataBroadcastContent, DataBroadcastContentJson> = /*@__PURE__*/
  messageDesc(file_game_game, 1);

