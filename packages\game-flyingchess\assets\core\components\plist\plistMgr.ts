import { assetManager, ImageAsset, TextAsset, resources, SpriteAtlas } from 'cc'
import { PlistParse } from './PlistParse'
import { plistCache, plistCacheData } from './plistCache'

export class plistMgr {
    private static instance: plistMgr = null!

    static getInstance(): plistMgr {
        if (!plistMgr.instance) {
            plistMgr.instance = new plistMgr()
        }
        return plistMgr.instance
    }

    public async loadRemoteAssets(url: string): Promise<plistCacheData> {
        // 使用Cocos的资源管理系统加载
        let plistStr = `${url}.plist`
        let pngStr = `${url}.png`
        const cachedItem = plistCache.getInstance().getItemFrame(url)
        if (cachedItem) {
            return cachedItem
        } else {
            const [pngAsset, plistAsset] = await Promise.all([
                this.loadRemoteImage(pngStr),
                this.loadRemoteText(plistStr),
            ])
            let altas = PlistParse.parse(pngAsset, plistAsset)
            plistCache.getInstance().add(url, { spAtlas: altas })
            return { spAtlas: altas }
        }
    }

    private async loadRemoteImage(url: string): Promise<ImageAsset> {
        return new Promise((resolve, reject) => {
            assetManager.loadRemote<ImageAsset>(
                url,
                { ext: '.png' },
                (err, imageAsset) => {
                    if (err) {
                        reject(err)
                    } else {
                        resolve(imageAsset)
                    }
                }
            )
        })
    }

    private async loadRemoteText(url: string): Promise<TextAsset> {
        return new Promise((resolve, reject) => {
            assetManager.loadRemote<TextAsset>(
                url,
                { ext: '.plist' },
                (err, textAsset) => {
                    if (err) {
                        reject(err)
                    } else {
                        console.log(
                            'textAsset========',
                            textAsset,
                            textAsset.text
                        )
                        resolve(textAsset)
                    }
                }
            )
        })
    }

    /**
     * 加载本地 plist 资源
     * @param pngPath 图片资源路径（不含扩展名）
     * @param plistPath plist 资源路径（不含扩展名）
     * @returns 包含 SpriteAtlas 的 plistCacheData 对象
     */
    public async loadLocalAssets(
        pngPath: string,
        plistPath: string
    ): Promise<plistCacheData> {
        // 生成缓存键
        const cacheKey = `local_${pngPath}_${plistPath}`

        // 检查缓存
        const cachedItem = plistCache.getInstance().getItemFrame(cacheKey)
        if (cachedItem) {
            return cachedItem
        }

        // 加载本地资源
        const [pngAsset, plistAsset] = await Promise.all([
            this.loadLocalImage(pngPath),
            this.loadLocalPlist(plistPath),
        ])

        // 解析 plist 并创建 SpriteAtlas
        const atlas = PlistParse.parse(pngAsset, plistAsset)

        // 添加到缓存
        plistCache.getInstance().add(cacheKey, { spAtlas: atlas })

        return { spAtlas: atlas }
    }

    /**
     * 直接使用资源对象加载 plist 动画
     * @param pngAsset 图片资源对象
     * @param plistAsset plist 资源对象
     * @returns 包含 SpriteAtlas 的 plistCacheData 对象
     */
    public loadAssetsDirectly(
        pngAsset: ImageAsset,
        plistAsset: TextAsset
    ): plistCacheData {
        // 生成缓存键（使用资源的 uuid 作为唯一标识）
        const cacheKey = `direct_${pngAsset.uuid}_${plistAsset.uuid}`

        // 检查缓存
        const cachedItem = plistCache.getInstance().getItemFrame(cacheKey)
        if (cachedItem) {
            return cachedItem
        }

        // 解析 plist 并创建 SpriteAtlas
        const atlas = PlistParse.parse(pngAsset, plistAsset)

        // 添加到缓存
        plistCache.getInstance().add(cacheKey, { spAtlas: atlas })

        return { spAtlas: atlas }
    }

    /**
     * 加载本地图片资源
     * @param path 资源路径（不含扩展名）
     * @returns ImageAsset 对象
     */
    private loadLocalImage(path: string): Promise<ImageAsset> {
        return new Promise((resolve, reject) => {
            resources.load<ImageAsset>(
                `${path}`,
                ImageAsset,
                (err, imageAsset) => {
                    if (err) {
                        console.error(`加载本地图片资源失败: ${path}`, err)
                        reject(err)
                    } else {
                        resolve(imageAsset)
                    }
                }
            )
        })
    }

    /**
     * 加载本地 plist 资源
     * @param path 资源路径（不含扩展名）
     * @returns TextAsset 对象
     */
    private loadLocalPlist(path: string): Promise<TextAsset> {
        return new Promise((resolve, reject) => {
            resources.load<TextAsset>(
                `${path}`,
                TextAsset,
                (err, textAsset) => {
                    if (err) {
                        console.error(`加载本地 plist 资源失败: ${path}`, err)
                        reject(err)
                    } else {
                        resolve(textAsset)
                    }
                }
            )
        })
    }
}
