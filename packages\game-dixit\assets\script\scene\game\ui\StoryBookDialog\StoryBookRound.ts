import {
    _decorator,
    Node,
    Label,
    Button,
    UITransform,
    v3,
    Vec3,
    tween,
    UIOpacity,
} from 'cc'
import {} from '@/core/business/ws/types.b'
import { BaseComponent } from '@/core/manager/gui/base/BaseComponent'
import {
    StorybookResponse_RoundJson,
    StorybookResponse_RoundSchema,
} from '@/pb-generate/server/dixit/v1/handler_pb'
import { create } from '@bufbuild/protobuf'
import { GameEventConstant } from '@/core/business/constant'
import { cat } from '@/core/manager'
import { CardItem, cardSizeMap } from '../../components/card/CardItem'
import store from '@/core/business/store'
import { audioEffect } from '@/core/business/hooks/Decorator'

const { ccclass, property } = _decorator

// 故事书模式枚举
export enum StoryBookMode {
    CARD_MODE = 'card_mode', // 卡牌模式
    STORY_MODE = 'story_mode', // 查看故事模式
    EMPTY_MODE = 'empty_mode', // 空状态模式
}

export interface StoryBookRoundProps {
    roundData: StorybookResponse_RoundJson
    mode: StoryBookMode // 当前模式
    onModeChange?: (mode: StoryBookMode) => void // 模式切换回调
}

/**
 * 故事书轮次组件
 */
@ccclass('StoryBookRound')
export class StoryBookRound extends BaseComponent<StoryBookRoundProps> {
    @property({ type: Node, tooltip: '标题区域' })
    title_area: Node = null!

    @property({ type: Node, tooltip: '卡牌模式容器' })
    card_mode_container: Node = null!

    @property({ type: Node, tooltip: '卡牌容器' })
    cards_container: Node = null!

    @property({ type: Node, tooltip: '故事模式容器' })
    story_mode_container: Node = null!

    @property({ type: Node, tooltip: '空状态容器' })
    empty_state_container: Node = null!

    @property({ type: Label, tooltip: '故事内容' })
    story_label: Label = null!

    @property({ type: Label, tooltip: '故事内容额外提示' })
    story_tips_label: Label = null!

    @property({ type: Label, tooltip: '玩家名称' })
    player_label: Label = null!

    @property({ type: Button, tooltip: '返回按钮' })
    btn_back: Button = null!

    @property({ type: Button, tooltip: '关闭按钮' })
    btn_close: Button = null!

    // 卡牌项数组
    private cardItems: CardItem[] = []

    override props: StoryBookRoundProps = {
        roundData: create(StorybookResponse_RoundSchema),
        mode: StoryBookMode.CARD_MODE,
    }

    protected override initUI(): void {
        // 确保 title_area 有 Button 组件，如果没有则添加
        this.initEvents()
        this.updateRoundInfo()
    }

    protected override onAutoObserver(): void {
        this.addAutorun([
            () => {
                this.updateMode()
            },
        ])
    }

    /**
     * 初始化事件
     */
    private initEvents(): void {
        // 标题区域点击事件，切换到故事模式
        if (this.title_area) {
            // 添加多种点击事件类型来增强响应性
            const titleClickHandler = () => {
                console.log('[StoryBookRound] 标题区域被点击')
                this.onTitleClick()
            }

            // 注册多种事件类型来增强响应性
            this.title_area.on(
                Node.EventType.TOUCH_END,
                titleClickHandler,
                this
            )
        } else {
            console.warn('[StoryBookRound] title_area 为空，无法注册点击事件')
        }

        // 返回按钮点击事件，切换回卡牌模式
        if (this.btn_back) {
            console.log('[StoryBookRound] 注册返回按钮点击事件', this.btn_back)
            this.btn_back.node.on(
                Node.EventType.TOUCH_END,
                () => {
                    console.log('[StoryBookRound] 返回按钮被点击')
                    this.onBackClick()
                },
                this
            )
        } else {
            console.warn('[StoryBookRound] btn_back 为空，无法注册点击事件')
        }

        // 关闭按钮点击事件
        if (this.btn_close) {
            console.log('[StoryBookRound] 注册关闭按钮点击事件', this.btn_close)
            this.btn_close.node.on(
                Node.EventType.TOUCH_END,
                this.onCloseClick,
                this
            )
        } else {
            console.warn('[StoryBookRound] btn_close 为空，无法注册点击事件')
        }
    }

    /**
     * 更新轮次信息
     */
    private updateRoundInfo(): void {
        const { roundCount, story, playerIndex, storyFrom } =
            this.props.roundData
        console.log(
            '[StoryBookRound] updateRoundInfo 被调用，轮次:',
            roundCount
        )
        this.story_tips_label.node.active = !story || storyFrom === 2
        // 更新故事内容
        if (story) {
            this.story_label.string = story
            console.log('[StoryBookRound] 更新故事内容:', story)
        } else {
            this.story_tips_label.string = '（谜语人未输入谜面）'
        }

        if (storyFrom === 2) {
            this.story_tips_label.string = '（此谜面为系统自动生成）'
        }

        // 更新玩家名称
        if (this.player_label) {
            // 通过索引获取玩家信息
            const player =
                playerIndex !== undefined
                    ? store.game.getPlayerByIndex(playerIndex)
                    : undefined
            const playerName = cat.util.stringUtil.sub(
                player?.nickname || `玩家${playerIndex || 0}`,
                18
            )
            this.player_label.string = `谜语人: ${playerName}`

            console.log(
                '[StoryBookRound] 更新玩家名称:',
                this.player_label.string,
                '玩家索引:',
                playerIndex
            )
        } else {
            console.warn('[StoryBookRound] player_label 为空')
        }

        // 渲染卡牌（这部分逻辑后续实现）
        this.renderCards()
    }

    /**
     * 渲染卡牌
     */
    private renderCards(): void {
        // 清空卡牌容器
        if (this.cards_container) {
            this.cards_container.removeAllChildren()
        }

        // 清空卡牌项数组
        this.cardItems = []

        // 获取选中的卡牌数据
        const selectedCards = this.props.roundData.selectedCards || []
        console.log('[StoryBookRound] 渲染卡牌，数量:', selectedCards.length)

        // 如果没有卡牌数据，直接返回，空状态将在 updateMode 中处理
        if (selectedCards.length === 0) {
            console.log('[StoryBookRound] 没有卡牌数据，显示空状态')
            return
        }

        // 创建卡牌
        selectedCards.forEach((card, i) => {
            // 从卡牌池获取卡牌节点
            const cardNode = cat.cardPool.get()
            const cardItem = cardNode.getComponent(CardItem)

            if (cardItem) {
                // 设置卡牌属性
                // 在故事书中显示卡牌时，我们需要直接设置玩家标签和投票标签，而不需要动画效果
                cardItem.setOptions({
                    props: {
                        cardId: card.card!,
                        isSwitch: false,
                        isSelected: false,
                        showPlayerTag: true,
                        showVoteLabel: true,
                        selectedCardData: card,
                        noAnimation: true, // 禁用动画效果
                        enablePreview: true, // 启用点击预览功能
                    },
                })
                this.cardItems.push(cardItem)
                console.log(
                    `[StoryBookRound] 创建卡牌 ${i}，cardId:`,
                    card.card
                )

                // 将卡牌添加到容器中
                cardNode.setParent(this.cards_container)

                // 设置卡牌大小
                cardNode.setScale(cardSizeMap.XS)
            }
        })

        // 布局卡牌
        this.arrangeCardsInRowLayout()
    }

    /**
     * 更新模式显示
     */
    private updateMode(): void {
        const { mode } = this.props

        console.log('[StoryBookRound] updateMode 被调用，当前模式:', mode)

        // 空状态模式特殊处理，不使用渐隐渐现效果
        if (mode === StoryBookMode.EMPTY_MODE) {
            console.log('[StoryBookRound] 切换到空状态模式显示')
            // 隐藏所有容器
            if (this.card_mode_container) {
                this.card_mode_container.active = false
            }
            if (this.story_mode_container) {
                this.story_mode_container.active = false
            }

            // 显示空状态容器
            this.title_area.active = false
            if (this.empty_state_container) {
                this.empty_state_container.active = true
                console.log('[StoryBookRound] 显示空状态容器')
            } else {
                console.warn('[StoryBookRound] empty_state_container 为空')
            }
            return
        }

        // 确保空状态容器隐藏（对于非空状态模式）
        if (this.empty_state_container) {
            this.empty_state_container.active = false
        }

        // 确保标题区域显示（对于非空状态模式）
        this.title_area.active = true

        // 获取当前活动的容器和目标容器（仅在卡牌模式和故事模式之间切换）
        let currentContainer: Node | null = null
        let targetContainer: Node | null = null

        // 确定当前活动的容器
        if (this.card_mode_container && this.card_mode_container.active) {
            currentContainer = this.card_mode_container
        } else if (
            this.story_mode_container &&
            this.story_mode_container.active
        ) {
            currentContainer = this.story_mode_container
        }

        // 确定目标容器
        if (mode === StoryBookMode.CARD_MODE) {
            console.log('[StoryBookRound] 切换到卡牌模式显示')
            targetContainer = this.card_mode_container
        } else if (mode === StoryBookMode.STORY_MODE) {
            console.log('[StoryBookRound] 切换到故事模式显示')
            targetContainer = this.story_mode_container
        }

        // 执行转场动画（仅在卡牌模式和故事模式之间）
        this.performTransition(currentContainer, targetContainer)
    }

    /**
     * 执行转场动画
     * @param currentContainer 当前容器
     * @param targetContainer 目标容器
     */
    private performTransition(
        currentContainer: Node | null,
        targetContainer: Node | null
    ): void {
        // 转场动画持续时间（秒）
        const fadeDuration = 0.3

        // 如果没有当前容器（首次加载）或目标容器为空，直接显示目标容器
        if (!currentContainer || !targetContainer) {
            // 隐藏卡牌和故事模式容器（不影响空状态容器）
            if (this.card_mode_container) {
                this.card_mode_container.active = false
            }
            if (this.story_mode_container) {
                this.story_mode_container.active = false
            }

            // 直接显示目标容器
            if (targetContainer) {
                targetContainer.active = true
                // 确保目标容器完全不透明
                this.setNodeOpacity(targetContainer, 255)
                console.log('[StoryBookRound] 直接显示目标容器')
            }
            return
        }

        // 如果当前容器和目标容器相同，不执行转场
        if (currentContainer === targetContainer) {
            console.log('[StoryBookRound] 当前容器和目标容器相同，不执行转场')
            return
        }

        // 确保目标容器是激活的但透明的
        targetContainer.active = true
        this.setNodeOpacity(targetContainer, 0)

        // 获取当前容器的UIOpacity组件
        const currentOpacity = this.getOrAddUIOpacity(currentContainer)
        const targetOpacity = this.getOrAddUIOpacity(targetContainer)

        // 当前容器渐隐
        tween(currentOpacity)
            .to(fadeDuration, { opacity: 0 })
            .call(() => {
                // 当前容器渐隐完成后，隐藏它
                currentContainer!.active = false

                // 目标容器渐现
                tween(targetOpacity).to(fadeDuration, { opacity: 255 }).start()
            })
            .start()
    }

    /**
     * 获取或添加UIOpacity组件
     * @param node 目标节点
     * @returns UIOpacity组件
     */
    private getOrAddUIOpacity(node: Node): UIOpacity {
        let opacity = node.getComponent(UIOpacity)
        if (!opacity) {
            opacity = node.addComponent(UIOpacity)
        }
        return opacity
    }

    /**
     * 设置节点的不透明度
     * @param node 目标节点
     * @param value 不透明度值（0-255）
     */
    private setNodeOpacity(node: Node, value: number): void {
        const opacity = this.getOrAddUIOpacity(node)
        opacity.opacity = value
    }

    /**
     * 标题点击事件，切换到故事模式
     */
    @audioEffect()
    private onTitleClick(): void {
        console.log(
            '[StoryBookRound] onTitleClick 被调用，当前模式:',
            this.props.mode
        )

        // 如果当前是卡牌模式，切换到故事模式
        if (this.props.mode === StoryBookMode.CARD_MODE) {
            console.log('[StoryBookRound] 将从卡牌模式切换到故事模式')
            this.changeMode(StoryBookMode.STORY_MODE)
        } else {
            console.log('[StoryBookRound] 当前不是卡牌模式，不切换')
        }
    }

    /**
     * 返回按钮点击事件，切换回卡牌模式
     */
    @audioEffect()
    private onBackClick(): void {
        console.log(
            '[StoryBookRound] onBackClick 被调用，当前模式:',
            this.props.mode
        )

        // 如果当前是故事模式，切换回卡牌模式
        if (this.props.mode === StoryBookMode.STORY_MODE) {
            console.log('[StoryBookRound] 将从故事模式切换回卡牌模式')
            this.changeMode(StoryBookMode.CARD_MODE)
        } else {
            console.log('[StoryBookRound] 当前不是故事模式，不切换')
        }
    }

    /**
     * 切换模式
     */
    private changeMode(mode: StoryBookMode): void {
        console.log('[StoryBookRound] changeMode 被调用，切换到模式:', mode)

        // 调用回调函数通知父组件模式已切换
        if (this.props.onModeChange) {
            console.log('[StoryBookRound] 调用 onModeChange 回调')
            this.props.onModeChange(mode)
        } else {
            console.warn('[StoryBookRound] onModeChange 回调为空')

            // 如果没有回调，直接更新当前组件的模式
            this.props.mode = mode
            this.updateMode()
        }
    }

    /**
     * 关闭按钮点击事件
     */
    @audioEffect()
    private onCloseClick(): void {
        console.log('[StoryBookRound] onCloseClick 被调用')
        cat.event.dispatchEvent(GameEventConstant.CLOSE_COMMON_UI)
    }

    /**
     * 将卡牌排列为两行布局
     */
    private arrangeCardsInRowLayout(): void {
        const totalCards = this.cardItems.length

        // 如果没有卡牌，直接返回
        if (totalCards === 0) {
            console.log('[StoryBookRound] 没有卡牌需要布局')
            return
        }

        let cardsPerRow = 3

        // 根据卡牌数量确定每行卡牌数
        if (totalCards <= 4) {
            cardsPerRow = 2
        }

        const rows = Math.ceil(totalCards / cardsPerRow)
        const lastRowCards = totalCards % cardsPerRow || cardsPerRow
        const cardWidth = 220
        const cardHeight = 330

        // 为每张卡牌设置位置
        this.cardItems.forEach((cardItem, index) => {
            const row = Math.floor(index / cardsPerRow)
            const isLastRow = row === rows - 1
            const rowCards = isLastRow ? lastRowCards : cardsPerRow
            const col = index % cardsPerRow
            let targetX

            // 计算卡牌的X坐标，使其水平居中
            if (rowCards < cardsPerRow) {
                // 最后一行卡牌数量少于每行最大数量时，居中显示
                targetX = col * cardWidth - ((rowCards - 1) * cardWidth) / 2
            } else {
                targetX = col * cardWidth - ((cardsPerRow - 1) * cardWidth) / 2
            }

            const targetY = 100 - row * cardHeight
            const targetPos = v3(targetX, targetY, 0)

            // 设置卡牌位置
            cardItem.node.setPosition(targetPos)
        })
    }
}
