import { cat } from '@/core/manager'
import store from '../store'

type IShare = {
    title?: string
    imageUrl?: string
}

export default function Share() {
    const { channel } = store.global
    // 分享设置
    const setting = ({ title = '', imageUrl = '' }: IShare = {}) => {
        cat.platform.onShareAppMessage({
            title,
            imageUrl,
            query: `wxgamecid=${channel}`,
        })

        cat.platform.onShareTimeline({
            title,
            imageUrl,
            query: `wxgamecid=${channel}`,
        })
    }
    return { setting }
}
