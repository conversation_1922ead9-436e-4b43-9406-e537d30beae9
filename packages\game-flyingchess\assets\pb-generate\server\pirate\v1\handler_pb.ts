// @generated by protoc-gen-es v2.2.5 with parameter "target=ts,json_types=true"
// @generated from file pirate/v1/handler.proto (package pirate.v1, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv1";
import { enumDesc, fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import type { Card, CardJson, CardState, CardStateJson } from "./card_pb";
import { file_pirate_v1_card } from "./card_pb";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file pirate/v1/handler.proto.
 */
export const file_pirate_v1_handler: GenFile = /*@__PURE__*/
  fileDesc("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", [file_pirate_v1_card]);

/**
 * 出牌时的请求
 *
 * @generated from message pirate.v1.PostCardRequest
 */
export type PostCardRequest = Message<"pirate.v1.PostCardRequest"> & {
  /**
   * 手牌index
   *
   * @generated from field: repeated int32 hand_card_indices = 1;
   */
  handCardIndices: number[];

  /**
   * 手牌的实际牌
   *
   * @generated from field: repeated pirate.v1.Card cards = 2;
   */
  cards: Card[];
};

/**
 * 出牌时的请求
 *
 * @generated from message pirate.v1.PostCardRequest
 */
export type PostCardRequestJson = {
  /**
   * 手牌index
   *
   * @generated from field: repeated int32 hand_card_indices = 1;
   */
  handCardIndices?: number[];

  /**
   * 手牌的实际牌
   *
   * @generated from field: repeated pirate.v1.Card cards = 2;
   */
  cards?: CardJson[];
};

/**
 * Describes the message pirate.v1.PostCardRequest.
 * Use `create(PostCardRequestSchema)` to create a new message.
 */
export const PostCardRequestSchema: GenMessage<PostCardRequest, PostCardRequestJson> = /*@__PURE__*/
  messageDesc(file_pirate_v1_handler, 0);

/**
 * @generated from message pirate.v1.PostCardResponse
 */
export type PostCardResponse = Message<"pirate.v1.PostCardResponse"> & {
  /**
   * @generated from field: bool success = 1;
   */
  success: boolean;

  /**
   * 玩家索引
   *
   * @generated from field: int32 player_index = 2;
   */
  playerIndex: number;

  /**
   * 出牌的手牌索引
   *
   * @generated from field: repeated int32 hand_card_indices = 3;
   */
  handCardIndices: number[];

  /**
   * 出牌的牌
   *
   * @generated from field: repeated pirate.v1.Card cards = 4;
   */
  cards: Card[];
};

/**
 * @generated from message pirate.v1.PostCardResponse
 */
export type PostCardResponseJson = {
  /**
   * @generated from field: bool success = 1;
   */
  success?: boolean;

  /**
   * 玩家索引
   *
   * @generated from field: int32 player_index = 2;
   */
  playerIndex?: number;

  /**
   * 出牌的手牌索引
   *
   * @generated from field: repeated int32 hand_card_indices = 3;
   */
  handCardIndices?: number[];

  /**
   * 出牌的牌
   *
   * @generated from field: repeated pirate.v1.Card cards = 4;
   */
  cards?: CardJson[];
};

/**
 * Describes the message pirate.v1.PostCardResponse.
 * Use `create(PostCardResponseSchema)` to create a new message.
 */
export const PostCardResponseSchema: GenMessage<PostCardResponse, PostCardResponseJson> = /*@__PURE__*/
  messageDesc(file_pirate_v1_handler, 1);

/**
 * 摸牌时的请求
 *
 * @generated from message pirate.v1.DrawCardRequest
 */
export type DrawCardRequest = Message<"pirate.v1.DrawCardRequest"> & {
  /**
   * @generated from field: repeated int32 deck_card_indices = 1;
   */
  deckCardIndices: number[];
};

/**
 * 摸牌时的请求
 *
 * @generated from message pirate.v1.DrawCardRequest
 */
export type DrawCardRequestJson = {
  /**
   * @generated from field: repeated int32 deck_card_indices = 1;
   */
  deckCardIndices?: number[];
};

/**
 * Describes the message pirate.v1.DrawCardRequest.
 * Use `create(DrawCardRequestSchema)` to create a new message.
 */
export const DrawCardRequestSchema: GenMessage<DrawCardRequest, DrawCardRequestJson> = /*@__PURE__*/
  messageDesc(file_pirate_v1_handler, 2);

/**
 * @generated from message pirate.v1.DrawCardResponse
 */
export type DrawCardResponse = Message<"pirate.v1.DrawCardResponse"> & {
  /**
   * @generated from field: bool success = 1;
   */
  success: boolean;

  /**
   * 玩家索引
   *
   * @generated from field: int32 player_index = 2;
   */
  playerIndex: number;

  /**
   * 摸牌的牌index
   *
   * @generated from field: repeated int32 deck_card_indices = 3;
   */
  deckCardIndices: number[];

  /**
   * 摸牌的牌
   *
   * @generated from field: repeated pirate.v1.Card cards = 4;
   */
  cards: Card[];
};

/**
 * @generated from message pirate.v1.DrawCardResponse
 */
export type DrawCardResponseJson = {
  /**
   * @generated from field: bool success = 1;
   */
  success?: boolean;

  /**
   * 玩家索引
   *
   * @generated from field: int32 player_index = 2;
   */
  playerIndex?: number;

  /**
   * 摸牌的牌index
   *
   * @generated from field: repeated int32 deck_card_indices = 3;
   */
  deckCardIndices?: number[];

  /**
   * 摸牌的牌
   *
   * @generated from field: repeated pirate.v1.Card cards = 4;
   */
  cards?: CardJson[];
};

/**
 * Describes the message pirate.v1.DrawCardResponse.
 * Use `create(DrawCardResponseSchema)` to create a new message.
 */
export const DrawCardResponseSchema: GenMessage<DrawCardResponse, DrawCardResponseJson> = /*@__PURE__*/
  messageDesc(file_pirate_v1_handler, 3);

/**
 * 透视桌面上的牌index的请求
 *
 * @generated from message pirate.v1.PeekCardRequest
 */
export type PeekCardRequest = Message<"pirate.v1.PeekCardRequest"> & {
  /**
   * @generated from field: repeated int32 deck_card_indices = 1;
   */
  deckCardIndices: number[];
};

/**
 * 透视桌面上的牌index的请求
 *
 * @generated from message pirate.v1.PeekCardRequest
 */
export type PeekCardRequestJson = {
  /**
   * @generated from field: repeated int32 deck_card_indices = 1;
   */
  deckCardIndices?: number[];
};

/**
 * Describes the message pirate.v1.PeekCardRequest.
 * Use `create(PeekCardRequestSchema)` to create a new message.
 */
export const PeekCardRequestSchema: GenMessage<PeekCardRequest, PeekCardRequestJson> = /*@__PURE__*/
  messageDesc(file_pirate_v1_handler, 4);

/**
 * @generated from message pirate.v1.PeekCardResponse
 */
export type PeekCardResponse = Message<"pirate.v1.PeekCardResponse"> & {
  /**
   * @generated from field: bool success = 1;
   */
  success: boolean;

  /**
   * 玩家索引
   *
   * @generated from field: int32 player_index = 2;
   */
  playerIndex: number;

  /**
   * 透视的牌的index
   *
   * @generated from field: repeated int32 deck_card_indices = 3;
   */
  deckCardIndices: number[];

  /**
   * 透视的牌的状态
   *
   * @generated from field: repeated pirate.v1.CardState card_states = 4;
   */
  cardStates: CardState[];
};

/**
 * @generated from message pirate.v1.PeekCardResponse
 */
export type PeekCardResponseJson = {
  /**
   * @generated from field: bool success = 1;
   */
  success?: boolean;

  /**
   * 玩家索引
   *
   * @generated from field: int32 player_index = 2;
   */
  playerIndex?: number;

  /**
   * 透视的牌的index
   *
   * @generated from field: repeated int32 deck_card_indices = 3;
   */
  deckCardIndices?: number[];

  /**
   * 透视的牌的状态
   *
   * @generated from field: repeated pirate.v1.CardState card_states = 4;
   */
  cardStates?: CardStateJson[];
};

/**
 * Describes the message pirate.v1.PeekCardResponse.
 * Use `create(PeekCardResponseSchema)` to create a new message.
 */
export const PeekCardResponseSchema: GenMessage<PeekCardResponse, PeekCardResponseJson> = /*@__PURE__*/
  messageDesc(file_pirate_v1_handler, 5);

/**
 * 索取玩家手牌index的请求
 *
 * @generated from message pirate.v1.StealCardRequest
 */
export type StealCardRequest = Message<"pirate.v1.StealCardRequest"> & {
  /**
   * 目标玩家的索引
   *
   * @generated from field: int32 target_player_index = 1;
   */
  targetPlayerIndex: number;

  /**
   * 目标玩家的的手牌index
   *
   * @generated from field: repeated int32 target_hand_card_indices = 2;
   */
  targetHandCardIndices: number[];
};

/**
 * 索取玩家手牌index的请求
 *
 * @generated from message pirate.v1.StealCardRequest
 */
export type StealCardRequestJson = {
  /**
   * 目标玩家的索引
   *
   * @generated from field: int32 target_player_index = 1;
   */
  targetPlayerIndex?: number;

  /**
   * 目标玩家的的手牌index
   *
   * @generated from field: repeated int32 target_hand_card_indices = 2;
   */
  targetHandCardIndices?: number[];
};

/**
 * Describes the message pirate.v1.StealCardRequest.
 * Use `create(StealCardRequestSchema)` to create a new message.
 */
export const StealCardRequestSchema: GenMessage<StealCardRequest, StealCardRequestJson> = /*@__PURE__*/
  messageDesc(file_pirate_v1_handler, 6);

/**
 * @generated from message pirate.v1.StealCardResponse
 */
export type StealCardResponse = Message<"pirate.v1.StealCardResponse"> & {
  /**
   * @generated from field: bool success = 1;
   */
  success: boolean;

  /**
   * 玩家索引
   *
   * @generated from field: int32 player_index = 2;
   */
  playerIndex: number;

  /**
   * 目标玩家的索引
   *
   * @generated from field: int32 target_player_index = 3;
   */
  targetPlayerIndex: number;

  /**
   * 目标玩家手牌index
   *
   * @generated from field: repeated int32 target_hand_card_indices = 4;
   */
  targetHandCardIndices: number[];

  /**
   * 目标玩家的手牌
   *
   * @generated from field: repeated pirate.v1.Card target_hand_cards = 5;
   */
  targetHandCards: Card[];
};

/**
 * @generated from message pirate.v1.StealCardResponse
 */
export type StealCardResponseJson = {
  /**
   * @generated from field: bool success = 1;
   */
  success?: boolean;

  /**
   * 玩家索引
   *
   * @generated from field: int32 player_index = 2;
   */
  playerIndex?: number;

  /**
   * 目标玩家的索引
   *
   * @generated from field: int32 target_player_index = 3;
   */
  targetPlayerIndex?: number;

  /**
   * 目标玩家手牌index
   *
   * @generated from field: repeated int32 target_hand_card_indices = 4;
   */
  targetHandCardIndices?: number[];

  /**
   * 目标玩家的手牌
   *
   * @generated from field: repeated pirate.v1.Card target_hand_cards = 5;
   */
  targetHandCards?: CardJson[];
};

/**
 * Describes the message pirate.v1.StealCardResponse.
 * Use `create(StealCardResponseSchema)` to create a new message.
 */
export const StealCardResponseSchema: GenMessage<StealCardResponse, StealCardResponseJson> = /*@__PURE__*/
  messageDesc(file_pirate_v1_handler, 7);

/**
 * 索取、交换、冻结、替身时，选择玩家index的请求
 *
 * @generated from message pirate.v1.SelectTargetRequest
 */
export type SelectTargetRequest = Message<"pirate.v1.SelectTargetRequest"> & {
  /**
   * 目标玩家的索引
   *
   * @generated from field: int32 target_player_index = 1;
   */
  targetPlayerIndex: number;
};

/**
 * 索取、交换、冻结、替身时，选择玩家index的请求
 *
 * @generated from message pirate.v1.SelectTargetRequest
 */
export type SelectTargetRequestJson = {
  /**
   * 目标玩家的索引
   *
   * @generated from field: int32 target_player_index = 1;
   */
  targetPlayerIndex?: number;
};

/**
 * Describes the message pirate.v1.SelectTargetRequest.
 * Use `create(SelectTargetRequestSchema)` to create a new message.
 */
export const SelectTargetRequestSchema: GenMessage<SelectTargetRequest, SelectTargetRequestJson> = /*@__PURE__*/
  messageDesc(file_pirate_v1_handler, 8);

/**
 * @generated from message pirate.v1.SelectTargetResponse
 */
export type SelectTargetResponse = Message<"pirate.v1.SelectTargetResponse"> & {
  /**
   * @generated from field: bool success = 1;
   */
  success: boolean;

  /**
   * 玩家索引
   *
   * @generated from field: int32 player_index = 2;
   */
  playerIndex: number;

  /**
   * 目标玩家的索引
   *
   * @generated from field: int32 target_player_index = 3;
   */
  targetPlayerIndex: number;
};

/**
 * @generated from message pirate.v1.SelectTargetResponse
 */
export type SelectTargetResponseJson = {
  /**
   * @generated from field: bool success = 1;
   */
  success?: boolean;

  /**
   * 玩家索引
   *
   * @generated from field: int32 player_index = 2;
   */
  playerIndex?: number;

  /**
   * 目标玩家的索引
   *
   * @generated from field: int32 target_player_index = 3;
   */
  targetPlayerIndex?: number;
};

/**
 * Describes the message pirate.v1.SelectTargetResponse.
 * Use `create(SelectTargetResponseSchema)` to create a new message.
 */
export const SelectTargetResponseSchema: GenMessage<SelectTargetResponse, SelectTargetResponseJson> = /*@__PURE__*/
  messageDesc(file_pirate_v1_handler, 9);

/**
 * 打破【托管】时的请求
 *
 * @generated from message pirate.v1.BreakHostingRequest
 */
export type BreakHostingRequest = Message<"pirate.v1.BreakHostingRequest"> & {
};

/**
 * 打破【托管】时的请求
 *
 * @generated from message pirate.v1.BreakHostingRequest
 */
export type BreakHostingRequestJson = {
};

/**
 * Describes the message pirate.v1.BreakHostingRequest.
 * Use `create(BreakHostingRequestSchema)` to create a new message.
 */
export const BreakHostingRequestSchema: GenMessage<BreakHostingRequest, BreakHostingRequestJson> = /*@__PURE__*/
  messageDesc(file_pirate_v1_handler, 10);

/**
 * @generated from message pirate.v1.BreakHostingResponse
 */
export type BreakHostingResponse = Message<"pirate.v1.BreakHostingResponse"> & {
  /**
   * @generated from field: bool success = 1;
   */
  success: boolean;

  /**
   * 玩家索引
   *
   * @generated from field: int32 player_index = 2;
   */
  playerIndex: number;
};

/**
 * @generated from message pirate.v1.BreakHostingResponse
 */
export type BreakHostingResponseJson = {
  /**
   * @generated from field: bool success = 1;
   */
  success?: boolean;

  /**
   * 玩家索引
   *
   * @generated from field: int32 player_index = 2;
   */
  playerIndex?: number;
};

/**
 * Describes the message pirate.v1.BreakHostingResponse.
 * Use `create(BreakHostingResponseSchema)` to create a new message.
 */
export const BreakHostingResponseSchema: GenMessage<BreakHostingResponse, BreakHostingResponseJson> = /*@__PURE__*/
  messageDesc(file_pirate_v1_handler, 11);

/**
 * 玩家退出游戏时的请求
 *
 * @generated from message pirate.v1.ExitGameRequest
 */
export type ExitGameRequest = Message<"pirate.v1.ExitGameRequest"> & {
};

/**
 * 玩家退出游戏时的请求
 *
 * @generated from message pirate.v1.ExitGameRequest
 */
export type ExitGameRequestJson = {
};

/**
 * Describes the message pirate.v1.ExitGameRequest.
 * Use `create(ExitGameRequestSchema)` to create a new message.
 */
export const ExitGameRequestSchema: GenMessage<ExitGameRequest, ExitGameRequestJson> = /*@__PURE__*/
  messageDesc(file_pirate_v1_handler, 12);

/**
 * @generated from message pirate.v1.ExitGameResponse
 */
export type ExitGameResponse = Message<"pirate.v1.ExitGameResponse"> & {
  /**
   * @generated from field: bool success = 1;
   */
  success: boolean;

  /**
   * 玩家索引
   *
   * @generated from field: int32 player_index = 2;
   */
  playerIndex: number;
};

/**
 * @generated from message pirate.v1.ExitGameResponse
 */
export type ExitGameResponseJson = {
  /**
   * @generated from field: bool success = 1;
   */
  success?: boolean;

  /**
   * 玩家索引
   *
   * @generated from field: int32 player_index = 2;
   */
  playerIndex?: number;
};

/**
 * Describes the message pirate.v1.ExitGameResponse.
 * Use `create(ExitGameResponseSchema)` to create a new message.
 */
export const ExitGameResponseSchema: GenMessage<ExitGameResponse, ExitGameResponseJson> = /*@__PURE__*/
  messageDesc(file_pirate_v1_handler, 13);

/**
 * 由任何玩家发起，广播到所有玩家
 *
 * @generated from message pirate.v1.DataBroadcastRequest
 */
export type DataBroadcastRequest = Message<"pirate.v1.DataBroadcastRequest"> & {
  /**
   * 客户端广播数据 json格式
   *
   * @generated from field: string data = 1;
   */
  data: string;
};

/**
 * 由任何玩家发起，广播到所有玩家
 *
 * @generated from message pirate.v1.DataBroadcastRequest
 */
export type DataBroadcastRequestJson = {
  /**
   * 客户端广播数据 json格式
   *
   * @generated from field: string data = 1;
   */
  data?: string;
};

/**
 * Describes the message pirate.v1.DataBroadcastRequest.
 * Use `create(DataBroadcastRequestSchema)` to create a new message.
 */
export const DataBroadcastRequestSchema: GenMessage<DataBroadcastRequest, DataBroadcastRequestJson> = /*@__PURE__*/
  messageDesc(file_pirate_v1_handler, 14);

/**
 * @generated from message pirate.v1.DataBroadcastResponse
 */
export type DataBroadcastResponse = Message<"pirate.v1.DataBroadcastResponse"> & {
  /**
   * @generated from field: bool success = 1;
   */
  success: boolean;
};

/**
 * @generated from message pirate.v1.DataBroadcastResponse
 */
export type DataBroadcastResponseJson = {
  /**
   * @generated from field: bool success = 1;
   */
  success?: boolean;
};

/**
 * Describes the message pirate.v1.DataBroadcastResponse.
 * Use `create(DataBroadcastResponseSchema)` to create a new message.
 */
export const DataBroadcastResponseSchema: GenMessage<DataBroadcastResponse, DataBroadcastResponseJson> = /*@__PURE__*/
  messageDesc(file_pirate_v1_handler, 15);

/**
 * 路由，给客户端调用，也就是下面pirateService的路由
 * buf:lint:ignore ENUM_VALUE_UPPER_SNAKE_CASE
 * buf:lint:ignore ENUM_VALUE_PREFIX
 *
 * @generated from enum pirate.v1.Route
 */
export enum Route {
  /**
   * @generated from enum value: ROUTE_UNSPECIFIED = 0;
   */
  ROUTE_UNSPECIFIED = 0,

  /**
   * 出牌
   *
   * @generated from enum value: PostCard = 1;
   */
  PostCard = 1,

  /**
   * 摸牌
   *
   * @generated from enum value: DrawCard = 2;
   */
  DrawCard = 2,

  /**
   * 透视桌面上的牌
   *
   * @generated from enum value: PeekCard = 3;
   */
  PeekCard = 3,

  /**
   * 索取玩家手牌
   *
   * @generated from enum value: StealCard = 4;
   */
  StealCard = 4,

  /**
   * 选择目标玩家
   *
   * @generated from enum value: SelectTarget = 5;
   */
  SelectTarget = 5,

  /**
   * 打破【托管】
   *
   * @generated from enum value: BreakHosting = 6;
   */
  BreakHosting = 6,

  /**
   * ---- 其它 ----
   * 由任何玩家发起，广播到所有玩家
   *
   * @generated from enum value: DataBroadcast = 11;
   */
  DataBroadcast = 11,

  /**
   * 观看
   *
   * @generated from enum value: Watch = 12;
   */
  Watch = 12,
}

/**
 * 路由，给客户端调用，也就是下面pirateService的路由
 * buf:lint:ignore ENUM_VALUE_UPPER_SNAKE_CASE
 * buf:lint:ignore ENUM_VALUE_PREFIX
 *
 * @generated from enum pirate.v1.Route
 */
export type RouteJson = "ROUTE_UNSPECIFIED" | "PostCard" | "DrawCard" | "PeekCard" | "StealCard" | "SelectTarget" | "BreakHosting" | "DataBroadcast" | "Watch";

/**
 * Describes the enum pirate.v1.Route.
 */
export const RouteSchema: GenEnum<Route, RouteJson> = /*@__PURE__*/
  enumDesc(file_pirate_v1_handler, 0);

/**
 * rpc主路由和方法
 *
 * @generated from service pirate.v1.PirateService
 */
export const PirateService: GenService<{
  /**
   * 出牌
   *
   * @generated from rpc pirate.v1.PirateService.PostCard
   */
  postCard: {
    methodKind: "unary";
    input: typeof PostCardRequestSchema;
    output: typeof PostCardResponseSchema;
  },
  /**
   * 摸牌
   *
   * @generated from rpc pirate.v1.PirateService.DrawCard
   */
  drawCard: {
    methodKind: "unary";
    input: typeof DrawCardRequestSchema;
    output: typeof DrawCardResponseSchema;
  },
  /**
   * 透视桌面上的牌
   *
   * @generated from rpc pirate.v1.PirateService.PeekCard
   */
  peekCard: {
    methodKind: "unary";
    input: typeof PeekCardRequestSchema;
    output: typeof PeekCardResponseSchema;
  },
  /**
   * 索取玩家手牌
   *
   * @generated from rpc pirate.v1.PirateService.StealCard
   */
  stealCard: {
    methodKind: "unary";
    input: typeof StealCardRequestSchema;
    output: typeof StealCardResponseSchema;
  },
  /**
   * 选择目标玩家
   *
   * @generated from rpc pirate.v1.PirateService.SelectTarget
   */
  selectTarget: {
    methodKind: "unary";
    input: typeof SelectTargetRequestSchema;
    output: typeof SelectTargetResponseSchema;
  },
  /**
   * 打破【托管】
   *
   * @generated from rpc pirate.v1.PirateService.BreakHosting
   */
  breakHosting: {
    methodKind: "unary";
    input: typeof BreakHostingRequestSchema;
    output: typeof BreakHostingResponseSchema;
  },
  /**
   * 由任何玩家发起，广播到所有玩家
   *
   * @generated from rpc pirate.v1.PirateService.DataBroadcast
   */
  dataBroadcast: {
    methodKind: "unary";
    input: typeof DataBroadcastRequestSchema;
    output: typeof DataBroadcastResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_pirate_v1_handler, 0);

