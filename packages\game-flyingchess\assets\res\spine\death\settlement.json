{"skeleton": {"hash": "wrfLzVYFzEssaFJJYIlwoKxaNyo", "spine": "3.8.99", "x": -672.98, "y": -73.34, "width": 1340, "height": 495.22, "images": "./images/", "audio": "E:/XM/商业策划部/海盗桶/19-结算界面"}, "bones": [{"name": "root"}, {"name": "hdr_9", "parent": "root", "x": -162.97, "y": 90.11}, {"name": "hdr_10", "parent": "hdr_9", "length": 131.66, "rotation": -9.65, "x": 0.16, "y": -2.61}, {"name": "hdr_11", "parent": "hdr_10", "length": 124.26, "rotation": 18.65, "x": 131.66}, {"name": "hdr_8", "parent": "hdr_11", "length": 429.64, "rotation": 17.36, "x": 123.12, "y": -1.61}, {"name": "hdr_7", "parent": "hdr_8", "length": 25.23, "rotation": -166.64, "x": 42.77, "y": -53.72}, {"name": "hdr_12", "parent": "hdr_7", "length": 17.44, "rotation": 14.5, "x": 25.23}, {"name": "hdr_13", "parent": "hdr_12", "length": 10.61, "rotation": 6.04, "x": 17.44}, {"name": "hdr_14", "parent": "hdr_13", "length": 8.95, "rotation": 12.64, "x": 10.61}, {"name": "hdr_15", "parent": "hdr_14", "length": 8.54, "rotation": 1.46, "x": 8.95}, {"name": "hdr_16", "parent": "hdr_9", "length": 57.9, "rotation": 169.16, "x": -0.38, "y": 0.05}, {"name": "hdr_18", "parent": "hdr_16", "length": 76.62, "rotation": -57.86, "x": 34.59, "y": -35.88}, {"name": "hdr_17", "parent": "hdr_18", "length": 59.09, "rotation": 26.19, "x": 74.37, "y": 0.88}, {"name": "hdr_19", "parent": "hdr_17", "length": 64.09, "rotation": -55.08, "x": 59.09}, {"name": "hdr_21", "parent": "hdr_16", "length": 72.46, "rotation": 32.91, "x": 25.44, "y": 31.36}, {"name": "hdr_22", "parent": "hdr_21", "length": 55.67, "rotation": -23.31, "x": 72.46}, {"name": "hdr_23", "parent": "hdr_22", "length": 42.33, "rotation": 60.28, "x": 59.89, "y": 0.7}, {"name": "hdr_5", "parent": "hdr_11", "length": 77.57, "rotation": -174, "x": 73.67, "y": 113.3}, {"name": "hdr_20", "parent": "hdr_5", "length": 101.73, "rotation": 84.47, "x": 76.92, "y": 0.17}, {"name": "hdr_24", "parent": "hdr_20", "length": 64.32, "rotation": 17.1, "x": 101.73}, {"name": "hdr_26", "parent": "hdr_11", "length": 111.24, "rotation": -34.6, "x": 129.52, "y": -59.57}, {"name": "hdr_27", "parent": "hdr_26", "length": 144.45, "rotation": 22.28, "x": 111.24}, {"name": "hdr_25", "parent": "root", "length": 395.45, "rotation": 127.42, "x": -408.09, "y": -22.91}, {"name": "hdr_29", "parent": "hdr_25", "x": 165.66, "y": 5.15}, {"name": "hdr_28", "parent": "root", "length": 323.23, "rotation": 93.95, "x": -372.84, "y": 23.28}, {"name": "hdr_30", "parent": "root", "length": 365.58, "rotation": 74.44, "x": 537.7, "y": -28.73}, {"name": "hdr_3", "parent": "root", "length": 36.42, "rotation": -80.84, "x": 516.42, "y": 522.27}, {"name": "hdr_4", "parent": "hdr_3", "length": 34.88, "rotation": -5.35, "x": 36.42}, {"name": "hdr_6", "parent": "hdr_4", "length": 27.65, "rotation": -13.47, "x": 34.88}, {"name": "hdr_31", "parent": "hdr_6", "length": 23.61, "rotation": -18.16, "x": 27.65}, {"name": "hdr_32", "parent": "hdr_31", "length": 21.34, "rotation": -14.97, "x": 23.61}, {"name": "hdr_33", "parent": "hdr_32", "length": 20.51, "rotation": -18.46, "x": 21.34}, {"name": "hdr_34", "parent": "root"}, {"name": "hdr_35", "parent": "hdr_10", "x": 69.44, "y": 6.51}], "slots": [{"name": "hdr_18", "bone": "hdr_34", "attachment": "hdr_18"}, {"name": "hdr_17", "bone": "hdr_28", "attachment": "hdr_17"}, {"name": "hdr_16", "bone": "hdr_30", "attachment": "hdr_16"}, {"name": "hdr_15", "bone": "hdr_29", "attachment": "hdr_15"}, {"name": "hdr_14", "bone": "hdr_21", "attachment": "hdr_14"}, {"name": "hdr_13", "bone": "hdr_26", "attachment": "hdr_13"}, {"name": "hdr_12", "bone": "hdr_18", "attachment": "hdr_12"}, {"name": "hdr_11", "bone": "hdr_18", "attachment": "hdr_11"}, {"name": "hdr_10", "bone": "hdr_17", "attachment": "hdr_10"}, {"name": "hdr_9", "bone": "hdr_9", "attachment": "hdr_9"}, {"name": "hdr_5", "bone": "hdr_20", "attachment": "hdr_5"}, {"name": "hdr_4", "bone": "hdr_11", "attachment": "hdr_4"}, {"name": "hdr_8", "bone": "hdr_8", "attachment": "hdr_8"}, {"name": "hdr_7", "bone": "hdr_7", "attachment": "hdr_7"}, {"name": "hdr_6", "bone": "hdr_8", "attachment": "hdr_6"}, {"name": "hdr_3", "bone": "hdr_3"}], "skins": [{"name": "default", "attachments": {"hdr_3": {"hdr_3": {"type": "mesh", "uvs": [0.50611, 1e-05, 0.65966, 0.02098, 0.79414, 0.06829, 0.90475, 0.1491, 0.98522, 0.26789, 1, 0.39367, 0.97703, 0.52289, 0.91291, 0.62777, 0.83929, 0.72076, 0.72374, 0.8186, 0.61621, 0.88788, 0.50272, 0.93433, 0.38614, 0.96868, 0.29431, 0.98481, 0.21681, 0.99843, 0.0881, 0.9896, 0.15114, 0.94202, 0.19223, 0.9082, 0.23674, 0.8411, 0.20872, 0.73698, 0.15237, 0.66923, 0.09776, 0.58468, 0.04958, 0.50403, 0, 0.39798, 0.00409, 0.27309, 0.05887, 0.16584, 0.17522, 0.05767, 0.31033, 0.00302, 0.19239, 0.19607, 0.39102, 0.1545, 0.62877, 0.13994, 0.80935, 0.17736, 0.21045, 0.3811, 0.43316, 0.34368, 0.65586, 0.33536, 0.84245, 0.36239, 0.2887, 0.55781, 0.46626, 0.53078, 0.67091, 0.53286, 0.83041, 0.54118, 0.34889, 0.70126, 0.47228, 0.68671, 0.61674, 0.70334, 0.73411, 0.71997, 0.35491, 0.80936, 0.44519, 0.81976, 0.55956, 0.84678, 0.34287, 0.90499, 0.24356, 0.94242], "triangles": [15, 16, 14, 14, 48, 13, 14, 16, 48, 13, 48, 47, 16, 17, 48, 48, 17, 18, 12, 13, 47, 12, 47, 11, 47, 48, 18, 47, 45, 11, 11, 45, 46, 47, 44, 45, 47, 18, 44, 18, 19, 44, 19, 40, 44, 45, 44, 40, 11, 46, 10, 10, 46, 9, 9, 46, 42, 46, 45, 42, 45, 41, 42, 41, 45, 40, 9, 43, 8, 9, 42, 43, 19, 20, 40, 41, 40, 36, 40, 20, 36, 42, 41, 37, 8, 43, 7, 42, 38, 43, 43, 39, 7, 43, 38, 39, 38, 42, 37, 36, 37, 41, 20, 21, 36, 7, 39, 6, 21, 22, 36, 36, 32, 37, 37, 34, 38, 39, 38, 35, 22, 32, 36, 6, 39, 35, 38, 34, 35, 32, 33, 37, 37, 33, 34, 6, 35, 5, 22, 23, 32, 32, 23, 24, 35, 4, 5, 32, 28, 33, 34, 31, 35, 4, 35, 31, 33, 30, 34, 24, 28, 32, 24, 25, 28, 4, 31, 3, 28, 29, 33, 33, 29, 30, 34, 30, 31, 25, 26, 28, 28, 26, 29, 30, 2, 31, 31, 2, 3, 26, 27, 29, 29, 0, 30, 29, 27, 0, 30, 1, 2, 30, 0, 1], "vertices": [1, 26, -5.52, -0.39, 1, 2, 26, 0.19, 15.02, 0.99717, 27, -37.47, 11.58, 0.00283, 2, 26, 9.54, 27.82, 0.92048, 27, -29.36, 25.19, 0.07952, 3, 26, 23.52, 37.33, 0.69705, 27, -16.33, 35.96, 0.30167, 28, -58.18, 23.04, 0.00128, 3, 26, 42.69, 42.79, 0.34687, 27, 2.25, 43.19, 0.61259, 28, -41.8, 34.4, 0.04054, 3, 26, 61.81, 41.28, 0.10746, 27, 21.43, 43.47, 0.70252, 28, -23.21, 39.14, 0.19002, 4, 26, 80.82, 35.77, 0.01169, 27, 40.86, 39.76, 0.48357, 28, -3.44, 40.06, 0.50117, 29, -42.03, 28.37, 0.00357, 4, 26, 95.48, 26.59, 0, 27, 56.32, 31.98, 0.18898, 28, 13.4, 36.1, 0.73707, 29, -24.79, 29.86, 0.07395, 3, 27, 69.91, 23.33, 0.02958, 28, 28.64, 30.85, 0.64703, 29, -8.68, 29.62, 0.32339, 3, 28, 45.33, 21.38, 0.18624, 29, 10.13, 25.83, 0.7852, 30, -19.69, 21.47, 0.02856, 3, 28, 57.61, 12.02, 0.00946, 29, 24.72, 20.76, 0.66679, 30, -4.29, 20.34, 0.32374, 2, 29, 36.52, 13.51, 0.15049, 30, 8.98, 16.39, 0.84951, 2, 30, 21.13, 10.96, 0.76277, 31, -3.67, 10.33, 0.23723, 2, 30, 29.48, 5.55, 0.06608, 31, 5.96, 7.84, 0.93392, 1, 31, 14.09, 5.74, 1, 1, 31, 25.3, -1.93, 1, 2, 30, 34.92, -9.9, 0.00342, 31, 16.02, -5.09, 0.99658, 5, 27, 93.82, -46.36, 5e-05, 28, 68.12, -31.35, 0.00015, 29, 48.23, -17.17, 0.00238, 30, 28.22, -10.23, 0.10463, 31, 9.76, -7.52, 0.89278, 5, 27, 83.95, -41.02, 0.00483, 28, 57.28, -28.46, 0.0144, 29, 37.03, -17.8, 0.09388, 30, 17.56, -13.73, 0.66188, 31, 0.76, -14.22, 0.22501, 6, 26, 100.1, -49.05, 0.00263, 27, 67.97, -42.9, 0.06132, 28, 42.18, -34.01, 0.15325, 29, 24.4, -27.79, 0.38738, 30, 7.95, -26.64, 0.39192, 31, -4.27, -29.51, 0.0035, 5, 26, 88.99, -53.25, 0.01628, 27, 57.3, -48.12, 0.15667, 28, 33.02, -41.57, 0.27454, 29, 18.06, -37.83, 0.35508, 30, 4.41, -37.98, 0.19743, 5, 26, 75.39, -56.87, 0.05899, 27, 44.09, -52.99, 0.29504, 28, 21.31, -49.38, 0.31385, 29, 9.37, -48.9, 0.23988, 30, -1.12, -50.92, 0.09224, 5, 26, 62.48, -59.91, 0.13707, 27, 31.53, -57.22, 0.4041, 28, 10.07, -56.43, 0.26722, 29, 0.89, -59.09, 0.1483, 30, -6.68, -62.96, 0.0433, 5, 26, 45.74, -62.48, 0.27906, 27, 15.1, -61.34, 0.44985, 28, -4.94, -64.26, 0.17864, 29, -10.94, -71.22, 0.07719, 30, -14.97, -77.73, 0.01527, 5, 26, 27.07, -59.04, 0.47687, 27, -3.82, -59.65, 0.39015, 28, -23.73, -67.02, 0.09565, 29, -27.93, -79.7, 0.03397, 30, -29.19, -90.31, 0.00337, 5, 26, 11.89, -50.76, 0.67254, 27, -19.7, -52.83, 0.27068, 28, -40.77, -64.09, 0.04357, 29, -45.03, -82.22, 0.01297, 30, -45.06, -97.17, 0.00024, 4, 26, -2.4, -36.08, 0.87463, 27, -35.29, -39.54, 0.11345, 28, -59.02, -54.81, 0.00991, 29, -65.27, -79.09, 0.00202, 4, 26, -8.34, -20.75, 0.96554, 27, -42.64, -24.84, 0.03343, 28, -69.59, -42.21, 0.00099, 29, -79.24, -70.43, 4e-05, 5, 26, 18.66, -37.65, 0.68932, 27, -14.18, -39.14, 0.26249, 28, -38.59, -49.5, 0.03714, 29, -47.51, -67.68, 0.01082, 30, -51.22, -83.76, 0.00024, 4, 26, 15.74, -16.06, 0.9109, 27, -19.1, -17.91, 0.08348, 28, -48.32, -30, 0.0046, 29, -62.83, -52.19, 0.00102, 2, 26, 17.53, 8.94, 0.98811, 27, -19.65, 7.14, 0.01189, 3, 26, 26.16, 26.75, 0.73121, 27, -12.71, 25.68, 0.26816, 28, -52.26, 13.89, 0.00063, 5, 26, 46.72, -40.26, 0.25715, 27, 14.01, -39.12, 0.50617, 28, -11.18, -42.91, 0.16529, 29, -23.52, -52.88, 0.06016, 30, -31.87, -63.26, 0.01123, 5, 26, 44.83, -16.27, 0.17696, 27, 9.89, -15.41, 0.76636, 28, -20.71, -20.81, 0.04575, 29, -39.47, -34.85, 0.01, 30, -51.93, -49.96, 0.00093, 3, 26, 47.31, 7.02, 0.04378, 27, 10.18, 8, 0.95528, 28, -25.88, 2.03, 0.00094, 3, 26, 54.48, 25.71, 0.13617, 27, 15.58, 27.28, 0.76455, 28, -25.12, 22.03, 0.09928, 5, 26, 74.55, -36.42, 0.03277, 27, 41.35, -32.71, 0.28054, 28, 13.92, -30.3, 0.40791, 29, -3.6, -33.07, 0.215, 30, -17.74, -38.98, 0.06378, 5, 26, 73.46, -17.36, 0.00458, 27, 38.49, -13.84, 0.27758, 28, 6.74, -12.61, 0.65641, 29, -15.94, -18.5, 0.05265, 30, -33.42, -28.09, 0.00878, 2, 27, 40.24, 7.58, 0.18901, 28, 3.45, 8.62, 0.81099, 4, 26, 81.11, 20.13, 0.00176, 27, 42.61, 24.21, 0.35955, 28, 1.88, 25.35, 0.62715, 29, -32.39, 16.05, 0.01154, 5, 26, 97.08, -33.66, 0.00106, 27, 63.53, -27.86, 0.04448, 28, 34.35, -20.41, 0.19305, 29, 12.73, -17.31, 0.55702, 30, -6.04, -19.53, 0.20438, 4, 27, 62.18, -14.78, 0.00965, 28, 30, -8.01, 0.24255, 29, 4.73, -6.88, 0.72448, 30, -16.46, -11.53, 0.02332, 2, 28, 29.95, 7.37, 0.41481, 29, -0.12, 7.71, 0.58519, 3, 27, 69.06, 12.31, 0.00626, 28, 30.37, 19.94, 0.53355, 29, -3.63, 19.79, 0.46019, 5, 27, 79.97, -28.32, 0.00462, 28, 50.45, -17.03, 0.01534, 29, 26.97, -9.08, 0.23864, 30, 5.59, -7.9, 0.73414, 31, -12.44, -12.48, 0.00726, 2, 29, 23.94, 0.04, 0.41909, 30, 0.31, 0.13, 0.58091, 3, 28, 52.45, 5.1, 0.00374, 29, 21.97, 12.58, 0.70533, 30, -4.83, 11.73, 0.29094, 1, 30, 17.12, 1.05, 1, 5, 27, 99.37, -41.33, 0, 28, 72.35, -25.17, 0, 29, 50.31, -9.98, 6e-05, 30, 28.37, -2.74, 0.00338, 31, 7.54, -0.37, 0.99655], "hull": 28, "edges": [0, 54, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 28, 30, 36, 38, 46, 48, 48, 50, 50, 52, 52, 54, 34, 36, 30, 32, 32, 34, 38, 40, 40, 42, 42, 44, 44, 46, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28], "width": 105, "height": 152}}, "hdr_4": {"hdr_4": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [86.43, 34.14, 20.25, 44.62, 40.89, 174.99, 107.07, 164.52], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 67, "height": 132}}, "hdr_5": {"hdr_5": {"type": "mesh", "uvs": [0.66149, 0.31272, 0.6546, 0.34047, 0.63594, 0.35216, 0.632, 0.3666, 0.64274, 0.3997, 0.67444, 0.45052, 0.76253, 0.56935, 0.84679, 0.6465, 1, 0.69214, 1, 0.71637, 0.95713, 0.89598, 0.93809, 0.91805, 0.82334, 0.97137, 0.66353, 0.99612, 0.54754, 0.9952, 0.43766, 0.9777, 0.35085, 0.93713, 0.31785, 0.90454, 0.25408, 0.73692, 0.22041, 0.68459, 0.01098, 0.42424, 0.00017, 0.34303, 0.01523, 0.29567, 0.04669, 0.23985, 0.1051, 0.19242, 0.17675, 0.14017, 0.23981, 0.09942, 0.30367, 0.06049, 0.43663, 0.01675, 0.55619, 0.00597, 0.64818, 0.00608, 0.5671, 0.05239, 0.5664, 0.09762, 0.57702, 0.14884, 0.58607, 0.20323, 0.59719, 0.25024, 0.60755, 0.2868, 0.62314, 0.3101, 0.29882, 0.29408, 0.34282, 0.22974, 0.40735, 0.17704, 0.30909, 0.38764], "triangles": [12, 13, 17, 17, 13, 14, 16, 17, 14, 10, 11, 12, 16, 14, 15, 10, 12, 17, 10, 17, 7, 17, 18, 7, 10, 7, 9, 18, 6, 7, 7, 8, 9, 6, 19, 5, 4, 5, 41, 19, 6, 18, 5, 19, 41, 41, 3, 4, 3, 41, 2, 19, 20, 41, 41, 21, 38, 41, 20, 21, 41, 38, 37, 2, 41, 37, 2, 37, 1, 38, 21, 22, 22, 23, 38, 35, 36, 39, 39, 34, 35, 39, 40, 34, 30, 0, 34, 36, 35, 0, 0, 37, 36, 0, 35, 34, 24, 25, 39, 39, 25, 40, 40, 33, 34, 34, 33, 30, 25, 26, 40, 26, 27, 40, 40, 32, 33, 32, 27, 28, 32, 40, 27, 33, 32, 30, 30, 32, 31, 32, 28, 31, 28, 29, 31, 31, 29, 30, 39, 36, 38, 38, 24, 39, 23, 24, 38, 37, 38, 36, 1, 37, 0], "vertices": [2, 17, 45.75, 25.59, 0.80642, 18, 22.3, 33.48, 0.19358, 2, 17, 48.06, 31.44, 0.36822, 18, 28.33, 31.74, 0.63178, 2, 17, 50.63, 33.47, 0.2889, 18, 30.61, 29.38, 0.7111, 2, 17, 51.87, 36.5, 0.20638, 18, 33.74, 28.43, 0.79362, 2, 17, 52.71, 43.99, 0.08849, 18, 41.28, 28.32, 0.91151, 3, 17, 52.46, 55.89, 0.01854, 18, 53.1, 29.73, 0.97739, 19, -37.74, 42.71, 0.00408, 2, 18, 81, 34.45, 0.77925, 19, -9.69, 39.03, 0.22075, 2, 18, 99.57, 40.33, 0.32698, 19, 9.79, 39.18, 0.67302, 2, 18, 112.35, 54.51, 0.10529, 19, 26.17, 48.98, 0.89471, 2, 18, 117.72, 53.61, 0.08905, 19, 31.05, 46.54, 0.91095, 1, 19, 65.18, 24.44, 1, 1, 19, 68.73, 20.43, 1, 1, 19, 74.07, 4.29, 1, 1, 19, 71.55, -13.21, 1, 1, 19, 65.92, -24.01, 1, 1, 19, 57.23, -32.57, 1, 1, 19, 44.99, -36.64, 1, 1, 19, 36.88, -36.46, 1, 2, 18, 109.41, -24.4, 0.35167, 19, 0.16, -25.58, 0.64833, 2, 18, 97.21, -25.96, 0.76343, 19, -11.95, -23.48, 0.23657, 2, 17, 118.22, 32.16, 0.30941, 18, 35.82, -38.02, 0.69059, 2, 17, 114.58, 14.21, 0.40525, 18, 17.61, -36.13, 0.59475, 2, 17, 110.3, 4.33, 0.4547, 18, 7.35, -32.82, 0.5453, 2, 17, 103.86, -6.95, 0.61759, 18, -4.49, -27.5, 0.38241, 2, 17, 95.17, -15.67, 0.85085, 18, -14.01, -19.69, 0.14915, 1, 17, 84.87, -25.08, 1, 1, 17, 76.1, -32.22, 1, 1, 17, 67.35, -38.95, 1, 2, 17, 51.32, -44.84, 0.99902, 18, -47.27, 21.14, 0.00098, 2, 17, 38.57, -43.94, 0.99879, 18, -47.6, 33.92, 0.00121, 2, 17, 29.25, -41.41, 0.99873, 18, -45.99, 43.45, 0.00127, 2, 17, 40.16, -33.55, 0.99883, 18, -37.11, 33.34, 0.00117, 2, 17, 42.87, -23.74, 0.99899, 18, -27.09, 31.59, 0.00101, 2, 17, 44.77, -12.32, 0.99934, 18, -15.54, 30.8, 0.00066, 1, 17, 47.02, -0.25, 1, 1, 17, 48.63, 10.27, 1, 1, 17, 49.71, 18.49, 1, 2, 17, 49.48, 23.98, 0.67609, 18, 21.05, 29.61, 0.32391, 2, 17, 81.44, 11.69, 0.30835, 18, 11.9, -3.39, 0.69165, 2, 17, 73.24, -1.1, 0.85426, 18, -1.62, 3.55, 0.14574, 2, 17, 63.62, -10.8, 0.97047, 18, -12.21, 12.18, 0.02953, 1, 18, 32.84, -5.79, 1], "hull": 31, "edges": [8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 54, 56, 0, 60, 4, 6, 6, 8, 0, 2, 2, 4, 56, 58, 58, 60, 46, 48, 48, 50, 50, 52, 52, 54, 42, 44, 44, 46], "width": 105, "height": 225}}, "hdr_6": {"hdr_6": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [46.51, -131.77, -0.08, -108.69, 26.55, -54.92, 73.15, -78.01], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 52, "height": 60}}, "hdr_7": {"hdr_7": {"type": "mesh", "uvs": [0.81455, 0.01497, 0.89578, 0.06922, 0.95291, 0.10737, 1, 0.14788, 1, 0.18142, 0.92314, 0.32627, 0.87069, 0.42511, 0.80353, 0.55167, 0.73957, 0.6722, 0.69145, 0.76289, 0.64077, 0.85841, 0.4462, 0.96991, 0.2531, 0.97001, 0.13653, 0.90046, 0.01063, 0.82535, 0.02001, 0.60292, 0.0922, 0.48477, 0.19156, 0.32211, 0.31584, 0.22101, 0.41601, 0.13952, 0.58022, 0.0661, 0.69463, 0.01496, 0.2824, 0.79363, 0.3663, 0.60363, 0.47257, 0.46363, 0.60401, 0.32113, 0.74104, 0.20363, 0.82774, 0.11363], "triangles": [14, 15, 22, 13, 14, 22, 11, 22, 10, 12, 13, 22, 12, 22, 11, 23, 16, 17, 9, 23, 8, 23, 15, 16, 10, 22, 23, 22, 15, 23, 9, 10, 23, 24, 18, 25, 17, 18, 24, 24, 25, 7, 24, 23, 17, 8, 24, 7, 23, 24, 8, 27, 0, 1, 2, 3, 4, 27, 26, 21, 27, 21, 0, 20, 21, 26, 25, 20, 26, 19, 20, 25, 2, 27, 1, 4, 27, 2, 4, 5, 27, 26, 27, 5, 6, 26, 5, 25, 26, 6, 25, 18, 19, 7, 25, 6], "vertices": [1, 5, 2.05, -5.26, 1, 1, 5, 0.65, 0.56, 1, 1, 5, -0.33, 4.65, 1, 1, 5, -0.76, 8.48, 1, 2, 5, 0.65, 10.18, 0.99991, 6, -21.24, 16.01, 9e-05, 4, 5, 10.25, 14.64, 0.94289, 6, -10.84, 17.93, 0.05541, 7, -26.23, 20.8, 0.00029, 8, -31.4, 28.36, 0.00141, 4, 5, 16.8, 17.68, 0.75334, 6, -3.74, 19.23, 0.22356, 7, -19.03, 21.35, 0.01093, 8, -24.25, 27.32, 0.01216, 5, 5, 25.18, 21.58, 0.38415, 6, 5.36, 20.9, 0.46858, 7, -9.82, 22.06, 0.08042, 8, -15.1, 25.99, 0.06293, 9, -23.38, 26.6, 0.00391, 5, 5, 33.17, 25.29, 0.13587, 6, 14.02, 22.49, 0.43336, 7, -1.04, 22.73, 0.20654, 8, -6.39, 24.73, 0.18086, 9, -14.7, 25.11, 0.04337, 5, 5, 39.18, 28.08, 0.04756, 6, 20.53, 23.69, 0.2777, 7, 5.57, 23.23, 0.24744, 8, 0.17, 23.77, 0.29669, 9, -8.18, 23.99, 0.13062, 5, 5, 45.51, 31.02, 0.01255, 6, 27.39, 24.95, 0.14903, 7, 12.53, 23.76, 0.2071, 8, 7.07, 22.77, 0.36198, 9, -1.3, 22.81, 0.26934, 4, 6, 40.08, 19.94, 0.01778, 7, 24.61, 17.45, 0.04885, 8, 17.48, 13.96, 0.20646, 9, 8.88, 13.74, 0.72691, 3, 7, 30.27, 7.56, 0.00023, 8, 20.84, 3.07, 0.0029, 9, 11.96, 2.77, 0.99687, 2, 8, 18.47, -4.85, 0.06225, 9, 9.4, -5.09, 0.93775, 3, 7, 29.08, -9.6, 0.00762, 8, 15.92, -13.41, 0.29256, 9, 6.63, -13.58, 0.69982, 4, 6, 35.13, -14.62, 0.02796, 7, 16.06, -16.4, 0.29463, 8, 1.72, -17.19, 0.50549, 9, -7.66, -17, 0.17192, 4, 6, 26.32, -15.72, 0.20452, 7, 7.17, -16.57, 0.51809, 8, -6.98, -15.42, 0.24884, 9, -16.32, -15.01, 0.02856, 4, 5, 43.28, -13.15, 0.0118, 6, 14.18, -17.25, 0.71809, 7, -5.06, -16.81, 0.25498, 8, -18.97, -12.97, 0.01513, 3, 5, 33.38, -13.59, 0.19904, 6, 4.48, -15.2, 0.76362, 7, -14.49, -13.75, 0.03733, 3, 5, 25.39, -13.96, 0.60483, 6, -3.34, -13.55, 0.39479, 7, -22.09, -11.29, 0.00038, 2, 5, 14.84, -11.49, 0.98416, 6, -12.94, -8.53, 0.01584, 1, 5, 7.49, -9.78, 1, 4, 6, 36.29, 5.3, 0.00036, 7, 19.3, 3.29, 0.00281, 8, 9.2, 1.3, 0.38609, 9, 0.29, 1.3, 0.61073, 5, 5, 47.22, 7.73, 0.00026, 6, 23.22, 1.98, 0.00786, 7, 5.96, 1.36, 0.96319, 8, -4.24, 2.35, 0.02868, 9, -13.12, 2.68, 0, 3, 5, 36.49, 4.63, 0.00615, 6, 12.06, 1.66, 0.9909, 8, -14.91, 5.62, 0.00294, 3, 5, 24.52, 2.35, 0.69521, 6, -0.1, 2.45, 0.3042, 8, -26.18, 10.27, 0.00058, 3, 5, 13.34, 1.55, 0.99877, 6, -11.12, 4.48, 0.00122, 8, -35.97, 15.71, 0, 1, 5, 5.61, 0.25, 1], "hull": 22, "edges": [0, 42, 4, 6, 6, 8, 20, 22, 22, 24, 28, 30, 8, 10, 38, 40, 40, 42, 10, 12, 34, 36, 36, 38, 12, 14, 14, 16, 30, 32, 32, 34, 16, 18, 18, 20, 24, 26, 26, 28, 0, 2, 2, 4], "width": 59, "height": 66}}, "hdr_8": {"hdr_8": {"type": "mesh", "uvs": [0.34368, 0.00232, 0.38835, 0.05001, 0.39476, 0.07127, 0.55222, 0.05618, 0.65288, 0.07979, 0.79111, 0.17462, 0.88279, 0.37277, 0.89306, 0.58209, 0.8919, 0.60637, 0.97488, 0.69515, 1, 0.80057, 1, 0.81779, 0.7704, 0.82673, 0.73888, 0.83284, 0.68746, 0.91736, 0.59744, 0.95461, 0.51504, 0.91583, 0.51352, 0.9058, 0.2802, 0.99698, 0.07543, 0.77079, 0.01153, 0.55839, 0.00081, 0.39145, 0.02467, 0.31424, 0.10045, 0.21334, 0.18875, 0.15774, 0.18987, 0.06571, 0.25715, 0.00168], "triangles": [23, 20, 21, 25, 26, 24, 0, 2, 26, 1, 2, 0, 2, 24, 26, 6, 7, 13, 22, 23, 21, 24, 19, 20, 12, 9, 10, 6, 3, 4, 6, 4, 5, 7, 8, 13, 12, 8, 9, 11, 12, 10, 13, 8, 12, 6, 17, 2, 3, 6, 2, 13, 17, 6, 24, 2, 17, 24, 20, 23, 17, 19, 24, 17, 15, 16, 13, 14, 17, 14, 15, 17, 18, 19, 17], "vertices": [265.23, 246.36, 275.22, 216.72, 273.69, 206.47, 346.63, 178.14, 386.42, 146.21, 428.2, 76.4, 428.05, -26.13, 389.49, -115.41, 383.97, -125.25, 402.49, -180.4, 391.92, -229.75, 388.37, -236.91, 284.69, -190.17, 269.45, -185.79, 229.23, -209.63, 181.63, -205.34, 153.07, -171.11, 154.46, -166.6, 32.19, -153.25, -12.04, -14.21, 3.36, 88.15, 33, 159.91, 59.48, 186.77, 113.88, 212.07, 164.49, 215.78, 183.95, 253.8, 226.98, 265.64], "hull": 27, "edges": [0, 52, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52], "width": 495, "height": 464}}, "hdr_9": {"hdr_9": {"type": "mesh", "uvs": [0.63611, 0.01567, 0.68076, 0.06046, 0.77404, 0.17308, 0.84354, 0.28254, 0.87076, 0.38619, 0.8942, 0.47112, 0.92087, 0.53748, 0.94855, 0.5926, 0.99517, 0.65332, 0.99557, 0.68518, 0.96727, 0.77051, 0.91636, 0.866, 0.8658, 0.96473, 0.7364, 1, 0.71042, 0.98425, 0.62927, 0.94103, 0.58958, 0.88102, 0.58989, 0.87157, 0.37398, 0.85091, 0.34627, 0.84596, 0.26323, 0.85422, 0.06873, 0.82102, 0.06606, 0.79391, 0.06962, 0.71292, 0.06549, 0.65011, 0.05599, 0.58064, 0.03357, 0.53334, 0.06465, 0.46917, 0.03965, 0.43883, 0.00484, 0.35593, 0.00488, 0.34478, 0.00166, 0.32368, 0.00591, 0.32075, 0.03229, 0.33898, 0.05617, 0.33855, 0.10717, 0.31315, 0.1284, 0.29229, 0.15867, 0.24509, 0.17156, 0.20807, 0.15897, 0.15689, 0.18568, 0.15885, 0.20159, 0.18116, 0.27515, 0.15615, 0.30516, 0.1711, 0.40768, 0.15129, 0.47083, 0.13539, 0.53364, 0.09126, 0.58398, 0.0809, 0.58463, 0.03387, 0.60915, 0.00016, 0.29903, 0.31674, 0.39503, 0.30022, 0.49253, 0.27131, 0.58253, 0.23827, 0.69353, 0.19284, 0.30953, 0.53977, 0.43253, 0.5439, 0.55853, 0.53357, 0.68453, 0.4964, 0.80153, 0.4489, 0.30653, 0.73595, 0.45953, 0.74834, 0.57053, 0.74834, 0.72653, 0.72356, 0.86003, 0.674, 0.16775, 0.73758, 0.18798, 0.53264, 0.18942, 0.32969], "triangles": [12, 13, 63, 63, 13, 14, 14, 15, 63, 12, 63, 11, 11, 63, 64, 16, 17, 15, 15, 17, 63, 61, 62, 17, 17, 62, 63, 11, 64, 10, 10, 64, 9, 9, 7, 8, 7, 9, 64, 62, 58, 63, 62, 57, 58, 63, 59, 64, 63, 58, 59, 64, 6, 7, 64, 5, 6, 64, 59, 5, 57, 53, 58, 57, 52, 53, 58, 54, 59, 58, 53, 54, 59, 4, 5, 59, 54, 3, 59, 3, 4, 3, 54, 2, 52, 45, 53, 53, 47, 54, 47, 1, 54, 1, 47, 0, 47, 48, 0, 48, 49, 0, 45, 46, 53, 53, 46, 47, 54, 1, 2, 18, 61, 17, 20, 60, 19, 20, 65, 60, 61, 18, 60, 18, 19, 60, 61, 57, 62, 60, 56, 61, 61, 56, 57, 60, 65, 66, 60, 55, 56, 60, 66, 55, 55, 51, 56, 56, 52, 57, 56, 51, 52, 55, 66, 50, 55, 50, 51, 51, 44, 52, 44, 45, 52, 21, 65, 20, 21, 22, 65, 22, 23, 65, 66, 65, 24, 65, 23, 24, 24, 25, 66, 66, 67, 50, 67, 27, 35, 35, 36, 67, 27, 34, 35, 29, 30, 33, 28, 33, 34, 30, 32, 33, 30, 31, 32, 36, 37, 67, 37, 38, 67, 67, 41, 50, 67, 38, 41, 41, 42, 50, 50, 43, 51, 50, 42, 43, 51, 43, 44, 38, 40, 41, 38, 39, 40, 25, 27, 66, 25, 26, 27, 66, 27, 67, 28, 34, 27, 28, 29, 33], "vertices": [4, 10, -134.93, -178.49, 0.01879, 11, 30.58, -219.4, 0.10005, 2, 137.62, 178.18, 0.17605, 3, 62.61, 166.92, 0.7051, 4, 10, -155.02, -169.02, 0.01575, 11, 11.88, -231.38, 0.08776, 2, 157.51, 168.29, 0.15723, 3, 78.29, 151.2, 0.73926, 4, 10, -198.03, -143.77, 0.00599, 11, -32.38, -254.37, 0.04459, 2, 199.99, 142.16, 0.07939, 3, 110.19, 112.86, 0.87003, 4, 10, -231.48, -117.64, 0.00123, 11, -72.31, -268.79, 0.01763, 2, 232.89, 115.34, 0.02488, 3, 132.79, 76.92, 0.95626, 4, 10, -247.92, -89.97, 0.0001, 11, -104.48, -268, 0.00595, 2, 248.76, 87.33, 0.00498, 3, 138.87, 45.32, 0.98896, 3, 11, -131.01, -267.77, 0.0009, 2, 262.2, 64.47, 6e-05, 3, 144.3, 19.35, 0.99904, 2, 11, -152.96, -270.72, 1e-05, 3, 151.86, -1.47, 0.99999, 1, 3, 160.33, -19.11, 1, 1, 3, 176.07, -39.55, 1, 1, 3, 174.77, -48.76, 1, 1, 3, 159.64, -71.59, 1, 1, 3, 135.07, -95.93, 1, 1, 3, 110.49, -121.23, 1, 2, 2, 225.56, -98.42, 0.00544, 3, 57.5, -123.27, 0.99456, 2, 2, 214.49, -95.63, 0.01235, 3, 47.9, -117.1, 0.98765, 2, 2, 180.21, -88.66, 0.07843, 3, 17.65, -99.53, 0.92157, 3, 14, -136.37, 138.09, 0.00093, 2, 161.55, -74.06, 0.20381, 3, 4.64, -79.73, 0.79526, 3, 14, -137.52, 135.58, 0.00137, 2, 161.21, -71.32, 0.23267, 3, 5.19, -77.02, 0.76597, 3, 14, -59.36, 97.38, 0.15205, 2, 74.63, -79.93, 0.83404, 3, -79.6, -57.5, 0.01391, 3, 14, -49.57, 91.86, 0.20829, 2, 63.4, -80.37, 0.78796, 3, -90.37, -54.33, 0.00375, 2, 14, -17.73, 81.55, 0.41843, 2, 30.9, -88.34, 0.58157, 2, 14, 51.09, 43.19, 0.94221, 2, -47.81, -91.89, 0.05779, 2, 14, 49.11, 35.45, 0.94654, 2, -50.2, -84.27, 0.05346, 2, 14, 38.9, 14.07, 0.98267, 2, -52.75, -60.72, 0.01733, 2, 10, 55.53, 46.6, 0.02733, 14, 33.54, -3.55, 0.97267, 2, 10, 63.09, 27.4, 0.54348, 14, 29.46, -23.78, 0.45652, 3, 10, 74.54, 15.53, 0.83417, 11, -22.28, 61.18, 1e-05, 14, 32.63, -39.97, 0.16582, 2, 10, 65.8, -5.23, 0.88558, 11, -9.36, 42.73, 0.11442, 2, 10, 77.33, -12.04, 0.64531, 11, 2.55, 48.87, 0.35469, 2, 10, 95.63, -33.18, 0.46883, 11, 30.18, 53.12, 0.53117, 2, 10, 96.22, -36.38, 0.46643, 11, 33.21, 51.92, 0.53357, 2, 10, 98.65, -42.19, 0.46505, 11, 39.42, 50.89, 0.53495, 2, 10, 97.14, -43.35, 0.46519, 11, 39.6, 48.99, 0.53481, 2, 10, 85.72, -40.12, 0.45153, 11, 30.79, 41.04, 0.54847, 2, 10, 76.32, -42.05, 0.37982, 11, 27.42, 32.05, 0.62018, 2, 10, 57.57, -53.18, 0.07223, 11, 26.88, 10.26, 0.92777, 2, 11, 29.46, 0.09, 0.99994, 3, -151.61, 119.05, 6e-05, 4, 10, 40.98, -76.6, 0.00153, 11, 37.88, -16.25, 0.9918, 2, -40.36, 79.96, 0.00368, 3, -137.43, 130.76, 0.00299, 4, 10, 37.92, -88.19, 0.0038, 11, 46.07, -25.01, 0.98227, 2, -37.06, 91.48, 0.00837, 3, -130.62, 140.63, 0.00555, 3, 11, 61.83, -25.71, 0.98878, 2, -44.56, 105.37, 0.00584, 3, -133.29, 156.18, 0.00537, 4, 10, 35.05, -103.37, 0.00262, 11, 57.39, -35.51, 0.98269, 2, -33.88, 106.6, 0.00856, 3, -122.77, 153.93, 0.00613, 4, 10, 27.54, -98.18, 0.01603, 11, 49, -39.11, 0.95366, 2, -26.48, 101.25, 0.02066, 3, -117.47, 146.5, 0.00964, 4, 10, -0.13, -110.91, 0.07911, 11, 45.07, -69.31, 0.77463, 2, 1.45, 113.41, 0.10628, 3, -87.12, 149.09, 0.03998, 4, 10, -12.79, -108.89, 0.09916, 11, 36.62, -78.96, 0.6805, 2, 14.07, 111.13, 0.15715, 3, -75.89, 142.89, 0.06319, 4, 10, -52.18, -122.32, 0.09381, 11, 27.04, -119.46, 0.37689, 2, 53.73, 123.74, 0.30598, 3, -34.28, 142.16, 0.22332, 4, 10, -76.24, -131.66, 0.06371, 11, 22.14, -144.8, 0.24583, 2, 77.98, 132.57, 0.30845, 3, -8.48, 142.78, 0.38202, 4, 10, -98.62, -149.06, 0.03843, 11, 24.98, -173.01, 0.16109, 2, 100.71, 149.51, 0.25476, 3, 18.47, 151.56, 0.54572, 4, 10, -117.93, -155.84, 0.02475, 11, 20.45, -192.96, 0.11799, 2, 120.16, 155.89, 0.20328, 3, 38.93, 151.39, 0.65399, 4, 10, -115.6, -169.38, 0.02059, 11, 33.15, -198.19, 0.10596, 2, 118.11, 169.47, 0.18504, 3, 41.34, 164.91, 0.6884, 4, 10, -123.43, -180.9, 0.01949, 11, 38.74, -210.95, 0.10264, 2, 126.18, 180.82, 0.1799, 3, 52.61, 173.09, 0.69797, 5, 10, -18.37, -66.66, 0.20418, 11, -2.11, -61.22, 0.55476, 2, 18.77, 68.79, 0.19202, 3, -84.97, 101.27, 0.03635, 33, -50.67, 62.28, 0.01268, 5, 10, -55.37, -78.66, 0.12331, 11, -11.63, -98.93, 0.33667, 2, 56.01, 80.02, 0.3867, 3, -46.1, 100.01, 0.1418, 33, -13.44, 73.51, 0.01152, 4, 10, -92.28, -94.32, 0.05946, 11, -18, -138.51, 0.19692, 2, 93.23, 94.91, 0.37296, 3, -6.07, 102.22, 0.37066, 4, 10, -126, -110.6, 0.02795, 11, -22.15, -175.73, 0.11531, 2, 127.28, 110.49, 0.24422, 3, 31.17, 106.09, 0.61252, 4, 10, -167.33, -132.02, 0.01087, 11, -26, -222.12, 0.06429, 2, 169.05, 131.05, 0.12302, 3, 77.32, 112.22, 0.80183, 5, 10, -34.76, -3.49, 0.03791, 11, -64.32, -41.5, 0.01275, 2, 33.85, 5.3, 0.90889, 3, -90.99, 36.29, 0.00045, 33, -35.59, -1.21, 0.04, 5, 10, -83.56, -11.61, 0.00828, 11, -83.4, -87.13, 0.01294, 2, 82.8, 12.4, 0.92424, 3, -42.34, 27.37, 0.01455, 33, 13.36, 5.89, 0.04, 5, 10, -132.74, -24.09, 0.00301, 11, -98.99, -135.42, 0.01038, 2, 132.23, 23.86, 0.34383, 3, 8.16, 22.43, 0.62105, 33, 62.78, 17.35, 0.02173, 4, 10, -180.44, -44.28, 0.00098, 11, -107.27, -186.55, 0.00679, 2, 180.34, 43.06, 0.0216, 3, 59.89, 25.23, 0.97063, 4, 10, -224.03, -66.75, 0.00014, 11, -111.44, -235.41, 0.00442, 2, 224.39, 64.61, 0.00435, 3, 108.51, 31.58, 0.99109, 3, 14, -46.84, 56.08, 0.24533, 2, 42.27, -51.38, 0.74076, 33, -27.18, -57.89, 0.01392, 4, 14, -102.48, 82.54, 0.03299, 2, 103.51, -44.63, 0.8606, 3, -40.95, -33.29, 0.08395, 33, 34.06, -51.14, 0.02246, 4, 14, -143.83, 99.31, 0.0019, 2, 147.5, -37.15, 0.34716, 3, 3.13, -40.27, 0.64641, 33, 78.05, -43.66, 0.00453, 2, 2, 208.11, -19.5, 0.00184, 3, 66.2, -42.92, 0.99816, 1, 3, 121.47, -37.02, 1, 2, 14, 5.05, 35.56, 0.74969, 2, -12.65, -61.2, 0.25031, 4, 10, 13.62, 3.65, 0.87061, 14, -24.98, -16.84, 0.04257, 2, -14.67, -0.84, 0.08017, 33, -84.11, -7.35, 0.00665, 4, 10, 24.19, -54.66, 0.04487, 11, 10.37, -18.8, 0.94597, 2, -24.03, 57.68, 0.00666, 3, -129.08, 104.43, 0.0025], "hull": 50, "edges": [0, 98, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98], "width": 402, "height": 292}}, "hdr_10": {"hdr_10": {"type": "mesh", "uvs": [0.61806, 0.0064, 0.69526, 0.04511, 0.75627, 0.10641, 0.72224, 0.23449, 0.75037, 0.30336, 0.89385, 0.46275, 0.98365, 0.5625, 0.98287, 0.65432, 0.97343, 0.69733, 0.85086, 0.82669, 0.69114, 0.92016, 0.48538, 0.99751, 0.46239, 0.99247, 0.37648, 0.9183, 0.17748, 0.71682, 0.09003, 0.63132, 0.02671, 0.58997, 0.00706, 0.55281, 0.03026, 0.48363, 0.13366, 0.35917, 0.46265, 0.04054, 0.52909, 0.00641], "triangles": [3, 4, 19, 1, 3, 20, 20, 3, 19, 1, 20, 0, 20, 21, 0, 15, 19, 14, 14, 19, 4, 16, 18, 15, 15, 18, 19, 16, 17, 18, 3, 1, 2, 10, 11, 13, 11, 12, 13, 10, 13, 9, 14, 5, 13, 4, 5, 14, 9, 13, 5, 8, 9, 7, 7, 9, 5, 7, 5, 6], "vertices": [1, 13, 76.28, -13.1, 1, 3, 11, 179.11, -27.48, 0.00016, 12, 81.47, -71.68, 0.00018, 13, 71.58, -22.68, 0.99967, 3, 11, 167.82, -30.61, 0.0018, 12, 69.96, -69.51, 0.00436, 13, 63.21, -30.88, 0.99384, 3, 11, 150.98, -19.85, 0.02705, 12, 59.6, -52.42, 0.07656, 13, 43.27, -29.59, 0.89639, 3, 11, 139.99, -19.04, 0.08545, 12, 50.09, -46.83, 0.21487, 13, 33.25, -34.18, 0.69968, 3, 11, 111.28, -25.55, 0.48292, 12, 21.45, -40.01, 0.3598, 13, 11.26, -53.76, 0.15728, 3, 11, 93.31, -29.63, 0.82943, 12, 3.53, -35.74, 0.13535, 13, -2.5, -66.02, 0.03521, 3, 11, 80.25, -24.44, 0.9783, 12, -5.9, -25.32, 0.01492, 13, -16.44, -67.78, 0.00678, 2, 11, 74.51, -21.04, 0.99823, 13, -23.1, -67.58, 0.00177, 1, 11, 61.19, -0.72, 1, 2, 11, 54.54, 21.59, 0.2159, 12, -8.65, 27.33, 0.78409, 2, 11, 52.11, 47.93, 0.0265, 12, 0.79, 52.05, 0.9735, 2, 11, 53.79, 50.12, 0.02468, 12, 3.26, 53.27, 0.97532, 2, 11, 67.95, 55.2, 0.00735, 12, 18.21, 51.58, 0.99265, 2, 12, 55.91, 44.32, 0.82788, 13, -38.16, 22.76, 0.17212, 2, 12, 72.17, 41.47, 0.53975, 13, -26.52, 34.46, 0.46025, 2, 12, 81.81, 41.72, 0.40582, 13, -21.21, 42.51, 0.59418, 2, 12, 87.32, 39.06, 0.35969, 13, -15.87, 45.5, 0.64031, 2, 12, 92.5, 29.46, 0.26209, 13, -5.03, 44.26, 0.73791, 2, 12, 96.61, 7.38, 0.03852, 13, 15.42, 34.99, 0.96148, 1, 13, 68.74, 3.93, 1, 1, 13, 74.93, -2.96, 1], "hull": 22, "edges": [0, 42, 0, 2, 2, 4, 4, 6, 6, 8, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 8, 10, 10, 12], "width": 115, "height": 153}}, "hdr_11": {"hdr_11": {"type": "mesh", "uvs": [0.72712, 0.01053, 0.83133, 0.13315, 0.98808, 0.51285, 0.99231, 0.60275, 0.93119, 0.71062, 0.74493, 0.87601, 0.48826, 0.99007, 0.29057, 0.98987, 0.23659, 0.94328, 0.14843, 0.84039, 0.06309, 0.74079, 0, 0.66717, 0, 0.64801, 0.13462, 0.41023, 0.28246, 0.27427, 0.40015, 0.16604, 0.49671, 0.10823, 0.62353, 0.03231, 0.68306, 0.00619, 0.44559, 0.37038, 0.58722, 0.50192, 0.70187, 0.69027], "triangles": [20, 13, 19, 19, 13, 14, 10, 13, 20, 13, 10, 12, 10, 11, 12, 14, 15, 19, 19, 15, 16, 5, 6, 21, 6, 9, 21, 8, 6, 7, 6, 8, 9, 5, 21, 4, 9, 10, 20, 9, 20, 21, 4, 21, 3, 21, 2, 3, 21, 20, 2, 20, 1, 2, 20, 19, 1, 1, 16, 17, 16, 1, 19, 17, 0, 1, 17, 18, 0], "vertices": [2, 11, 104.12, -26.78, 0.92493, 12, 14.49, -37.95, 0.07507, 2, 11, 89.79, -30.81, 0.98626, 12, -0.15, -35.24, 0.01374, 1, 11, 50.57, -29.99, 1, 1, 11, 42.32, -27.16, 1, 1, 11, 34.48, -18.46, 1, 2, 11, 25.35, 2.29, 0.99726, 12, -43.36, 22.9, 0.00274, 2, 11, 23.06, 26.87, 0.82365, 12, -34.57, 45.97, 0.17635, 2, 11, 29.25, 42.71, 0.68252, 12, -22.02, 57.45, 0.31748, 2, 11, 35.15, 45.39, 0.63147, 12, -15.55, 57.25, 0.36853, 2, 11, 47.2, 48.83, 0.46376, 12, -3.21, 55.02, 0.53624, 2, 11, 58.87, 52.16, 0.28878, 12, 8.72, 52.86, 0.71122, 2, 11, 67.49, 54.62, 0.21414, 12, 17.55, 51.26, 0.78586, 2, 11, 69.22, 53.94, 0.20873, 12, 18.8, 49.89, 0.79127, 2, 11, 86.51, 34.78, 0.03242, 12, 25.86, 25.06, 0.96758, 2, 11, 94.18, 18.14, 0.00013, 12, 25.4, 6.75, 0.99987, 2, 11, 100.28, 4.9, 0.15738, 12, 25.03, -7.83, 0.84262, 2, 11, 102.49, -4.87, 0.54217, 12, 22.7, -17.57, 0.45783, 2, 11, 105.39, -17.71, 0.86237, 12, 19.63, -30.37, 0.13763, 2, 11, 105.89, -23.4, 0.91087, 12, 17.57, -35.7, 0.08913, 1, 12, 8.75, 4.14, 1, 2, 11, 64.08, 1.75, 0.94027, 12, -8.85, 5.32, 0.05973, 1, 11, 43.48, -0.8, 1], "hull": 19, "edges": [0, 36, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 22, 24, 24, 26, 34, 36, 20, 22, 16, 18, 18, 20, 26, 28, 28, 30, 30, 32, 32, 34], "width": 86, "height": 97}}, "hdr_12": {"hdr_12": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-2.73, -14.6, 25.24, 57.14, 85.8, 33.53, 57.83, -38.21], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 77, "height": 65}}, "hdr_13": {"hdr_13": {"type": "mesh", "uvs": [0.13793, 0.00343, 0.19171, 0.03418, 0.39089, 0.16326, 0.42075, 0.17953, 0.43893, 0.17908, 0.67842, 0.33652, 0.7105, 0.2909, 0.77291, 0.29221, 0.83511, 0.33362, 0.94349, 0.51048, 0.9787, 0.61784, 0.99638, 0.7472, 0.9962, 0.92328, 0.87299, 0.92397, 0.84438, 0.93865, 0.7324, 0.96542, 0.59101, 0.98503, 0.39457, 0.98522, 0.34809, 0.97561, 0.20372, 0.86174, 0.09905, 0.74876, 0.02394, 0.6471, 0, 0.61023, 0, 0.60611, 0.01182, 0.5367, 0.03688, 0.42307, 0.11955, 0.07272], "triangles": [16, 17, 4, 17, 3, 4, 3, 17, 2, 16, 5, 15, 16, 4, 5, 17, 18, 2, 14, 7, 8, 14, 15, 7, 5, 6, 15, 15, 6, 7, 14, 8, 13, 12, 13, 11, 13, 9, 10, 13, 8, 9, 11, 13, 10, 18, 19, 2, 19, 20, 1, 1, 20, 26, 26, 0, 1, 19, 1, 2, 21, 25, 20, 20, 25, 26, 21, 23, 24, 21, 24, 25, 21, 22, 23], "vertices": [1, 20, 35.34, 15.37, 1, 1, 20, 48.76, 19.59, 1, 2, 20, 98.91, 34.32, 0.59976, 21, 1.6, 36.43, 0.40024, 2, 20, 106.34, 36.71, 0.40585, 21, 9.39, 35.82, 0.59415, 2, 20, 110.57, 38.77, 0.30502, 21, 14.08, 36.12, 0.69498, 1, 21, 76.6, 29.5, 1, 1, 21, 84.72, 32.94, 1, 1, 21, 100.87, 33.79, 1, 1, 21, 117.1, 32.03, 1, 1, 21, 145.79, 22.18, 1, 1, 21, 155.3, 15.74, 1, 1, 21, 160.36, 7.61, 1, 1, 21, 160.97, -3.81, 1, 1, 21, 129.12, -5.71, 1, 1, 21, 121.78, -7.09, 1, 1, 21, 92.92, -10.5, 1, 1, 21, 56.44, -13.9, 1, 2, 20, 122.86, -13.45, 0.13551, 21, 5.65, -16.85, 0.86449, 2, 20, 111.73, -18.09, 0.64029, 21, -6.41, -16.92, 0.35971, 1, 20, 74.81, -27.57, 1, 1, 20, 47.19, -32.67, 1, 1, 20, 26.79, -35.11, 1, 1, 20, 20.16, -35.63, 1, 1, 20, 20.05, -35.39, 1, 1, 20, 20.86, -30, 1, 1, 20, 23.52, -20.53, 1, 1, 20, 32.99, 9.26, 1], "hull": 27, "edges": [0, 52, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52], "width": 259, "height": 65}}, "hdr_14": {"hdr_14": {"type": "mesh", "uvs": [0.72486, 0.03874, 0.91371, 0.0068, 0.94111, 0.02519, 0.96058, 0.10195, 0.97597, 0.24874, 0.99234, 0.4049, 0.99341, 0.61769, 0.98082, 0.75119, 0.81235, 0.79301, 0.6436, 0.8349, 0.5179, 0.8661, 0.36412, 0.90427, 0.31088, 0.91507, 0.17142, 0.99076, 0.11911, 0.98984, 0.06381, 0.97451, 0.02507, 0.92197, 0.0148, 0.86738, 0.01486, 0.66254, 0.01493, 0.42574, 0.04745, 0.20938, 0.07634, 0.12671, 0.12708, 0.09162, 0.17548, 0.08467, 0.30948, 0.10668, 0.46695, 0.08136, 0.58722, 0.06202, 0.62477, 0.42559, 0.50256, 0.44928, 0.76153, 0.40584, 0.3658, 0.46113, 0.16793, 0.46508, 0.90992, 0.38215], "triangles": [0, 4, 32, 1, 3, 0, 3, 1, 2, 4, 0, 3, 32, 4, 5, 29, 0, 32, 27, 26, 0, 27, 0, 29, 28, 25, 26, 28, 26, 27, 30, 24, 25, 30, 25, 28, 23, 20, 22, 31, 23, 24, 31, 24, 30, 20, 21, 22, 23, 31, 20, 19, 20, 31, 18, 19, 31, 29, 32, 6, 6, 32, 5, 6, 8, 29, 7, 8, 6, 8, 9, 27, 8, 27, 29, 28, 27, 9, 10, 28, 9, 30, 28, 10, 30, 12, 31, 11, 30, 10, 12, 18, 31, 17, 13, 14, 12, 30, 11, 15, 16, 17, 17, 14, 15, 13, 17, 18, 12, 13, 18], "vertices": [51.67, -41.15, 27.21, -34.61, 24.51, -31.57, 24.94, -23.63, 28.45, -9.53, 32.18, 5.47, 39.88, 24.85, 46.35, 36.35, 68.65, 31.73, 91, 27.1, 107.64, 23.65, 128, 19.43, 134.96, 17.75, 154.93, 17.66, 161.35, 14.96, 167.6, 10.81, 170.44, 4.1, 169.7, -1.37, 162.15, -19.97, 153.42, -41.48, 141.45, -59.5, 134.84, -65.57, 127.29, -66.22, 121.07, -64.43, 105.37, -55.73, 85.03, -50.17, 69.49, -45.91, 78.25, -11.02, 94.18, -14.97, 60.66, -5.98, 111.48, -20.73, 136.01, -30.26, 41.5, -0.71], "hull": 27, "edges": [2, 4, 4, 6, 10, 12, 12, 14, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 14, 16, 16, 18, 18, 20, 20, 22, 48, 50, 50, 52, 2, 0, 0, 52, 6, 8, 8, 10, 34, 36, 36, 38], "width": 133, "height": 98}}, "hdr_15": {"hdr_15": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-187.19, -17.77, -12.2, 210.97, 261.81, 1.33, 86.81, -227.4], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 288, "height": 345}}, "hdr_16": {"hdr_16": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [33.19, -117.74, 0.19, 0.75, 333.51, 93.58, 366.5, -24.91], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 123, "height": 346}}, "hdr_17": {"hdr_17": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-11.52, -27.13, -4.42, 75.63, 330.78, 52.46, 323.68, -50.3], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 103, "height": 336}}, "hdr_18": {"hdr_18": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [667.02, -73.34, -551.98, -73.34, -551.98, 63.66, 667.02, 63.66], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 1219, "height": 137}}}}], "animations": {"idle": {"slots": {"hdr_3": {"color": [{"time": 0.4667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "color": "ffffffff"}], "attachment": [{"time": 0.4667, "name": "hdr_3"}]}, "hdr_15": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.7333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "color": "ffffffff"}]}, "hdr_16": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "color": "ffffffff"}]}, "hdr_17": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.6333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.7, "color": "ffffffff"}]}}, "bones": {"hdr_9": {"rotate": [{"angle": 25.85, "curve": 0.25, "c3": 0.785, "c4": 0.21}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 9.37, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 1.41, "curve": 0.25, "c3": 0.75}, {"time": 0.4}], "translate": [{"y": 42.28, "curve": 0.25, "c3": 0.785, "c4": 0.21}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "y": 14.15, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "hdr_10": {"rotate": [{"angle": 8.26, "curve": 0.25, "c3": 0.785, "c4": 0.21}, {"time": 0.2, "angle": 0.34, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -5.83, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -0.45, "curve": 0.25, "c3": 0.75}, {"time": 0.4}]}, "hdr_11": {"rotate": [{"angle": 3.34, "curve": 0.25, "c3": 0.785, "c4": 0.21}, {"time": 0.2, "angle": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -6.61, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -0.81, "curve": 0.25, "c3": 0.75}, {"time": 0.4}]}, "hdr_8": {"rotate": [{"angle": 4.21, "curve": 0.25, "c3": 0.785, "c4": 0.21}, {"time": 0.2, "angle": 1.96, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -7.21, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -1.28, "curve": 0.25, "c3": 0.75}, {"time": 0.4}]}, "hdr_7": {"rotate": [{"angle": -33.79, "curve": 0.25, "c3": 0.785, "c4": 0.21}, {"time": 0.2333, "angle": -37.14, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 21.5, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -14.12, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 1.42, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}]}, "hdr_12": {"rotate": [{"angle": -33.79, "curve": 0.25, "c3": 0.785, "c4": 0.21}, {"time": 0.2667, "angle": -37.14, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 4.63, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -4.31, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}]}, "hdr_13": {"rotate": [{"angle": -33.79, "curve": 0.25, "c3": 0.785, "c4": 0.21}, {"time": 0.3, "angle": -37.14, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 4.63, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -4.31, "curve": 0.25, "c3": 0.75}, {"time": 0.4667}]}, "hdr_14": {"rotate": [{"angle": -33.79, "curve": 0.25, "c3": 0.785, "c4": 0.21}, {"time": 0.3333, "angle": -37.14, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 4.63, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -4.31, "curve": 0.25, "c3": 0.75}, {"time": 0.5}]}, "hdr_15": {"rotate": [{"angle": -33.79, "curve": 0.25, "c3": 0.785, "c4": 0.21}, {"time": 0.3667, "angle": -37.14, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 4.63, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -4.31, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}]}, "hdr_18": {"rotate": [{"angle": 12.5, "curve": 0.25, "c3": 0.785, "c4": 0.21}, {"time": 0.2333, "angle": 9.14, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -1.43, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -0.98, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 0.45, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}]}, "hdr_5": {"rotate": [{"angle": -77.06, "curve": 0.25, "c3": 0.785, "c4": 0.21}, {"time": 0.2, "angle": -20.49, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 0.59, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}], "translate": [{"x": -21.9, "y": -31.46, "curve": 0.25, "c3": 0.785, "c4": 0.21}, {"time": 0.2}]}, "hdr_20": {"rotate": [{"angle": -62.29, "curve": 0.25, "c3": 0.785, "c4": 0.21}, {"time": 0.2, "angle": -31.32, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 1.43, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -0.69, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "hdr_24": {"rotate": [{"curve": 0.25, "c3": 0.785, "c4": 0.21}, {"time": 0.2, "angle": -22.37, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 3.68, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -3.41, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}], "translate": [{"x": -1.32, "y": 1.8, "curve": 0.25, "c3": 0.785, "c4": 0.21}, {"time": 0.2}]}, "hdr_26": {"rotate": [{"angle": 26.67, "curve": 0.25, "c3": 0.785, "c4": 0.21}, {"time": 0.2, "curve": "stepped"}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 8.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "hdr_25": {"rotate": [{"time": 0.7333, "curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 0.8333, "angle": -7.76, "curve": 0.328, "c2": 0.31, "c3": 0.662, "c4": 0.65}, {"time": 0.9, "angle": 1.96, "curve": 0.33, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 0.9667}], "translate": [{"x": -330.49, "y": 431.98, "curve": "stepped"}, {"time": 0.7333, "x": -330.49, "y": 431.98, "curve": 0.25, "c3": 0.761, "c4": 0.26}, {"time": 0.8333}]}, "hdr_28": {"rotate": [{"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -0.71, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 0.81, "curve": 0.25, "c3": 0.75}, {"time": 0.9}], "translate": [{"x": -29.58, "y": 427.88, "curve": "stepped"}, {"time": 0.6333, "x": -29.58, "y": 427.88, "curve": 0.25, "c3": 0.75}, {"time": 0.7}]}, "hdr_30": {"rotate": [{"time": 0.7667, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 2, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -0.61, "curve": 0.25, "c3": 0.75}, {"time": 0.9667}], "translate": [{"x": 115.06, "y": 413.18, "curve": "stepped"}, {"time": 0.6667, "x": 115.06, "y": 413.18, "curve": 0.25, "c3": 0.836, "c4": 0.24}, {"time": 0.7667}]}, "hdr_3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -14.19, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "angle": -7.39}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": -28.45, "y": -166.18, "curve": 0.17, "c2": 0.43, "c3": 0.739, "c4": 0.89}, {"time": 1.9333, "x": 34.5, "y": 81.72}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 0.303, "y": 0.255, "curve": 0.25, "c3": 0.75}, {"time": 1.9333}]}, "hdr_4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -0.28, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 0.6, "angle": -2.2, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": 3.76, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 1.2, "angle": -0.28, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 1.3333, "angle": -2.2, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "angle": 3.76, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 1.9333, "angle": -0.28}]}, "hdr_6": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 4.7, "curve": 0.339, "c2": 0.35, "c3": 0.758}, {"time": 0.7333, "angle": -5.87, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": 7.49, "curve": 0.273, "c3": 0.619, "c4": 0.41}, {"time": 1.2, "angle": 4.7, "curve": 0.339, "c2": 0.35, "c3": 0.758}, {"time": 1.4667, "angle": -5.87, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 7.49, "curve": 0.273, "c3": 0.619, "c4": 0.41}, {"time": 1.9333, "angle": 4.7}]}, "hdr_31": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 9.93, "curve": 0.358, "c2": 0.65, "c3": 0.693}, {"time": 0.5, "angle": 10.62, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -8.88, "curve": 0.245, "c3": 0.714, "c4": 0.85}, {"time": 1.2, "angle": 9.93, "curve": 0.358, "c2": 0.65, "c3": 0.693}, {"time": 1.2333, "angle": 10.62, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": -8.88, "curve": 0.245, "c3": 0.714, "c4": 0.85}, {"time": 1.9333, "angle": 9.93}]}, "hdr_32": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 3.76, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.6333, "angle": 18.48, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -14.97, "curve": 0.247, "c3": 0.63, "c4": 0.52}, {"time": 1.2, "angle": 3.76, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 1.3667, "angle": 18.48, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "angle": -14.97, "curve": 0.247, "c3": 0.63, "c4": 0.52}, {"time": 1.9333, "angle": 3.76}]}, "hdr_33": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -19.31, "curve": 0.315, "c2": 0.27, "c3": 0.757}, {"time": 0.7667, "angle": 30.9, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -25.53, "curve": 0.287, "c3": 0.627, "c4": 0.38}, {"time": 1.2, "angle": -19.31, "curve": 0.315, "c2": 0.27, "c3": 0.757}, {"time": 1.5, "angle": 30.9, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": -25.53, "curve": 0.287, "c3": 0.627, "c4": 0.38}, {"time": 1.9333, "angle": -19.31}]}, "hdr_34": {"scale": [{"x": 0.586, "y": 0.586, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 1.04, "y": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 0.982, "y": 0.982, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "hdr_35": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 9.91, "y": -336.71, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": -30.71, "y": 328.22, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 24.71, "y": -278.04, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": -17.37, "y": 113.69, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.9333, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "x": -17.49, "y": 102.84, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "x": -11.49, "y": 67.54, "curve": 0.25, "c3": 0.75}, {"time": 1.2667}]}}}, "idle2": {"slots": {"hdr_3": {"attachment": [{"name": "hdr_3"}]}}, "bones": {"hdr_18": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 5.79, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -2.07, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 4.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -1.02, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 0.44, "curve": 0.25, "c3": 0.75}, {"time": 0.4667}]}, "hdr_17": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 7.58, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -4.85, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 6.64, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -2.36, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 1.68, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}]}, "hdr_19": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 10.64, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -7.11, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 9.56, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -3.53, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 3.33, "curve": 0.25, "c3": 0.75}, {"time": 0.6333}]}, "hdr_20": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 0.77, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 0.33, "curve": 0.25, "c3": 0.75}, {"time": 0.4}]}, "hdr_24": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 2.48, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 0.54, "curve": 0.25, "c3": 0.75}, {"time": 0.4}]}, "hdr_35": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -17.49, "y": 102.84, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -11.49, "y": 67.54, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "hdr_32": {"rotate": [{"angle": 3.76, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.1, "angle": 18.48, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -14.97, "curve": 0.247, "c3": 0.63, "c4": 0.52}, {"time": 0.5, "angle": 3.76, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.6, "angle": 18.48, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -14.97, "curve": 0.247, "c3": 0.63, "c4": 0.52}, {"time": 1, "angle": 3.76}]}, "hdr_33": {"rotate": [{"angle": -19.31, "curve": 0.315, "c2": 0.27, "c3": 0.757}, {"time": 0.2, "angle": 30.9, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -25.53, "curve": 0.287, "c3": 0.627, "c4": 0.38}, {"time": 0.5, "angle": -19.31, "curve": 0.315, "c2": 0.27, "c3": 0.757}, {"time": 0.7, "angle": 30.9, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": -25.53, "curve": 0.287, "c3": 0.627, "c4": 0.38}, {"time": 1, "angle": -19.31}]}, "hdr_4": {"rotate": [{"angle": -0.28, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 0.1, "angle": -2.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 3.76, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 0.5, "angle": -0.28, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 0.6, "angle": -2.2, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 3.76, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 1, "angle": -0.28}]}, "hdr_6": {"rotate": [{"angle": 4.7, "curve": 0.339, "c2": 0.35, "c3": 0.758}, {"time": 0.1667, "angle": -5.87, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 7.49, "curve": 0.273, "c3": 0.619, "c4": 0.41}, {"time": 0.5, "angle": 4.7, "curve": 0.339, "c2": 0.35, "c3": 0.758}, {"time": 0.6667, "angle": -5.87, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 7.49, "curve": 0.273, "c3": 0.619, "c4": 0.41}, {"time": 1, "angle": 4.7}]}, "hdr_31": {"rotate": [{"angle": 9.93, "curve": 0.358, "c2": 0.65, "c3": 0.693}, {"time": 0.0333, "angle": 10.62, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -8.88, "curve": 0.245, "c3": 0.714, "c4": 0.85}, {"time": 0.5, "angle": 9.93, "curve": 0.358, "c2": 0.65, "c3": 0.693}, {"time": 0.5333, "angle": 10.62, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -8.88, "curve": 0.245, "c3": 0.714, "c4": 0.85}, {"time": 1, "angle": 9.93}]}, "hdr_3": {"rotate": [{"angle": -7.39}], "translate": [{"x": 34.5, "y": 81.72, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 34.5, "y": 57.26, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 34.5, "y": 81.72}]}}}}}