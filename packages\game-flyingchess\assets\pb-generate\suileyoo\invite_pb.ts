// @generated by protoc-gen-es v2.2.5 with parameter "target=ts,import_extension=none,json_types=true"
// @generated from file invite.proto (package protos, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv1";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file invite.proto.
 */
export const file_invite: GenFile = /*@__PURE__*/
  fileDesc("CgxpbnZpdGUucHJvdG8SBnByb3RvcyJIChxJbnZpdGVBdXRvRm9sbG93Tm90aWZpY2F0aW9uEhIKCmludml0ZXJfaWQYASABKAQSFAoMZGlzcGxheV9uYW1lGAIgASgJQjAKJGNvbS5zdG50cy5jbG91ZC5zdWlsZXlvby5lY2hvLnByb3Rvc1oILi9wcm90b3NiBnByb3RvMw");

/**
 * 邀请的好友登录自动成为好友通知
 *
 * @generated from message protos.InviteAutoFollowNotification
 */
export type InviteAutoFollowNotification = Message<"protos.InviteAutoFollowNotification"> & {
  /**
   * 邀请人用户ID
   *
   * @generated from field: uint64 inviter_id = 1;
   */
  inviterId: bigint;

  /**
   * 邀请人昵称
   *
   * @generated from field: string display_name = 2;
   */
  displayName: string;
};

/**
 * 邀请的好友登录自动成为好友通知
 *
 * @generated from message protos.InviteAutoFollowNotification
 */
export type InviteAutoFollowNotificationJson = {
  /**
   * 邀请人用户ID
   *
   * @generated from field: uint64 inviter_id = 1;
   */
  inviterId?: string;

  /**
   * 邀请人昵称
   *
   * @generated from field: string display_name = 2;
   */
  displayName?: string;
};

/**
 * Describes the message protos.InviteAutoFollowNotification.
 * Use `create(InviteAutoFollowNotificationSchema)` to create a new message.
 */
export const InviteAutoFollowNotificationSchema: GenMessage<InviteAutoFollowNotification, InviteAutoFollowNotificationJson> = /*@__PURE__*/
  messageDesc(file_invite, 0);

