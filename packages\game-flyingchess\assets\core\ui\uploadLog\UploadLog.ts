import {
    _decorator,
    Button,
    Component,
    EditBox,
    Input,
    Label,
    log,
    Node,
} from 'cc'
import UILayer from '@/core/manager/gui/layer/UILayer'
import RootUILayer from '@/core/manager/gui/layer/RootUILayer'
import { cat } from '@/core/manager'
const { ccclass, property } = _decorator

export type UploadLogProps = {
    confrim?: (value: string) => void
    cancel?: () => void
}

@ccclass('UploadLog')
export class UploadLog<
    T extends UploadLogProps = UploadLogProps
> extends RootUILayer<T> {
    @property({ type: EditBox, tooltip: '日志名称输入框' })
    editBox: EditBox

    @property({ type: Button, tooltip: '确定按钮' })
    btn_confirm: Button

    @property({ type: Button, tooltip: '关闭按钮' })
    btn_cancel: Button

    protected override onLoad(): void {
        this.btn_confirm.node.on(
            Button.EventType.CLICK,
            this.onConfrimHandler,
            this
        )
        this.btn_cancel.node.on(
            Button.EventType.CLICK,
            this.onCancelHanlder,
            this
        )
    }

    override start() {
        this.props && this.updateProps(this.props)
    }

    protected onConfrimHandler() {
        this.props?.confrim?.(this.editBox.string)
        cat.gui.hideNotice()
    }

    protected onCancelHanlder() {
        this.props?.cancel?.()
    }

    updateProps(props: UploadLogProps): void {}
}
