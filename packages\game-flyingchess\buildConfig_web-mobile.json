{"name": "flyingchess", "server": "", "platform": "web-mobile", "buildPath": "project://build", "debug": false, "buildMode": "normal", "mangleProperties": false, "md5Cache": false, "skipCompressTexture": true, "sourceMaps": "false", "overwriteProjectSettings": {"includeModules": {"physics": "inherit-project-setting", "physics-2d": "inherit-project-setting", "gfx-webgl2": "inherit-project-setting"}, "macroConfig": {"cleanupImageCache": "inherit-project-setting"}}, "nativeCodeBundleMode": "both", "polyfills": {"asyncFunctions": true, "coreJs": true}, "experimentalEraseModules": false, "startSceneAssetBundle": false, "bundleConfigs": [], "inlineEnum": true, "useBuiltinServer": false, "md5CacheOptions": {"excludes": [], "includes": [], "replaceOnly": [], "handleTemplateMd5Link": true}, "mainBundleIsRemote": false, "mainBundleCompressionType": "merge_all_json", "useSplashScreen": false, "bundleCommonChunk": false, "packAutoAtlas": true, "startScene": "f5f815a8-25f1-473f-9164-dc15e89eb9cd", "outputName": "web-mobile", "taskName": "web-mobile", "scenes": [{"url": "db://assets/scene/game.scene", "uuid": "00f825dd-1bc3-4e9b-a0f5-b35c2b69630f"}, {"url": "db://assets/scene/loading.scene", "uuid": "e8889a4d-786f-47a5-a3f5-258fcf213dd8"}, {"url": "db://assets/scene/start.scene", "uuid": "f5f815a8-25f1-473f-9164-dc15e89eb9cd"}], "wasmCompressionMode": false, "packages": {"web-mobile": {"useWebGPU": false, "orientation": "auto", "embedWebDebugger": false, "__version__": "1.0.1"}, "adsense-h5g-plugin": {"enableAdsense": false, "enableTestAd": false, "__version__": "1.0.1", "AFPHostPropertyCode": "ca-host-pub-5396158963872751", "AFPHostDomain": "douyougame.com", "otherAFPHostPropertyCode": "", "otherAFPDomain": ""}, "cocos-service": {"configID": "a95341", "services": []}}, "__version__": "1.3.8"}