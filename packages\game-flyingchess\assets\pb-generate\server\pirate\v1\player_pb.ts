// @generated by protoc-gen-es v2.2.5 with parameter "target=ts,json_types=true"
// @generated from file pirate/v1/player.proto (package pirate.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv1";
import type { Card, CardJson, CardState, CardStateJson } from "./card_pb";
import { file_pirate_v1_card } from "./card_pb";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file pirate/v1/player.proto.
 */
export const file_pirate_v1_player: GenFile = /*@__PURE__*/
  fileDesc("ChZwaXJhdGUvdjEvcGxheWVyLnByb3RvEglwaXJhdGUudjEijAMKBlBsYXllchIKCgJpZBgBIAEoCRIQCghuaWNrbmFtZRgCIAEoCRIRCgljb3Zlcl91cmwYAyABKAkSDgoGZXhpdGVkGA4gASgIEhEKCWdhbWVfb3ZlchgEIAEoCBIPCgdob3N0aW5nGAUgASgIEhkKEWRlbGF5X2VmZmVjdF9jYXJkGA8gASgIEg0KBWluZGV4GAYgASgNEg8KB3RlYW1faWQYByABKAkSKQoLY2FyZF9zdGF0ZXMYCiADKA4yFC5waXJhdGUudjEuQ2FyZFN0YXRlEiMKCmhhbmRfY2FyZHMYCyADKA4yDy5waXJhdGUudjEuQ2FyZBIXCg9oYW5kX2NhcmRfY291bnQYDCABKAUSJgoNZGlzY2FyZF9jYXJkcxgNIAMoDjIPLnBpcmF0ZS52MS5DYXJkEg0KBXNjb3JlGBAgASgFEhQKDHNjb3JlX2NoYW5nZRgRIAEoBRIWCg5kcmF3YWJsZV9jb3VudBgSIAEoBRIUCgxmcm96ZW5fY291bnQYEyABKAUiWwoEVGVhbRIKCgJpZBgBIAEoCRIWCg5wbGF5ZXJfaW5kaWNlcxgDIAMoDRIOCgZleGl0ZWQYBCABKAgSEQoJZ2FtZV9vdmVyGAUgASgIEgwKBHJhbmsYBiABKA0iQQocUGxheWVyU3RhdHVzQ2hhbmdlZEJyb2FkY2FzdBIhCgZwbGF5ZXIYASABKAsyES5waXJhdGUudjEuUGxheWVyIm0KEVBsYXllckluZm9NZXNzYWdlEh4KBXRlYW1zGAEgAygLMg8ucGlyYXRlLnYxLlRlYW0SIgoHcGxheWVycxgCIAMoCzIRLnBpcmF0ZS52MS5QbGF5ZXISFAoMcGxheWVyX2luZGV4GAMgASgFIpEBChZQbGF5ZXJEcmF3bkNhcmRNZXNzYWdlEhQKDHBsYXllcl9pbmRleBgBIAEoBRIdChVkcmF3bl9kZWNrX2NhcmRfaW5kZXgYAiABKAUSIwoKZHJhd25fY2FyZBgDIAEoDjIPLnBpcmF0ZS52MS5DYXJkEh0KFXJlbWFpbmluZ19kcmF3bl9jYXJkcxgEIAEoBSJWCh1QbGF5ZXJIb3N0aW5nRHJhd25DYXJkTWVzc2FnZRIUCgxwbGF5ZXJfaW5kZXgYASABKAUSHwoXZHJhd25fZGVja19jYXJkX2luZGljZXMYAiADKAUioAEKGVBsYXllclBvc3RlZENhcmRCcm9hZGNhc3QSFAoMcGxheWVyX2luZGV4GAEgASgFEhkKEWhhbmRfY2FyZF9pbmRpY2VzGAIgAygFEiUKDHBvc3RlZF9jYXJkcxgDIAMoDjIPLnBpcmF0ZS52MS5DYXJkEisKEmRlY2tfZGlzY2FyZF9jYXJkcxgEIAMoDjIPLnBpcmF0ZS52MS5DYXJkIl4KHlBsYXllclNlbGVjdGFibGVUYXJnZXRzTWVzc2FnZRIZChFmcm9tX3BsYXllcl9pbmRleBgBIAEoBRIhChlzZWxlY3RhYmxlX3BsYXllcl9pbmRpY2VzGAIgAygFIloKIFBsYXllckFmdGVyU2VsZWN0VGFyZ2V0QnJvYWRjYXN0EhkKEWZyb21fcGxheWVyX2luZGV4GAEgASgFEhsKE3RhcmdldF9wbGF5ZXJfaW5kZXgYAiABKAUiNwoZUGxheWVyRHJhd25EZWF0aEJyb2FkY2FzdBIaChJkcmF3bl9wbGF5ZXJfaW5kZXgYASABKAViBnByb3RvMw", [file_pirate_v1_card]);

/**
 * 用户信息
 *
 * @generated from message pirate.v1.Player
 */
export type Player = Message<"pirate.v1.Player"> & {
  /**
   * 用户id
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * 用户昵称
   *
   * @generated from field: string nickname = 2;
   */
  nickname: string;

  /**
   * 用户头像
   *
   * @generated from field: string cover_url = 3;
   */
  coverUrl: string;

  /**
   * 是否退出
   *
   * @generated from field: bool exited = 14;
   */
  exited: boolean;

  /**
   * 是否 Game over
   *
   * @generated from field: bool game_over = 4;
   */
  gameOver: boolean;

  /**
   * 是否系统托管
   *
   * @generated from field: bool hosting = 5;
   */
  hosting: boolean;

  /**
   * 延时牌效果
   *
   * @generated from field: bool delay_effect_card = 15;
   */
  delayEffectCard: boolean;

  /**
   * 玩家在战局中的索引
   *
   * @generated from field: uint32 index = 6;
   */
  index: number;

  /**
   * 组队id
   *
   * @generated from field: string team_id = 7;
   */
  teamId: string;

  /**
   * 牌状态，广播时，此项无
   *
   * @generated from field: repeated pirate.v1.CardState card_states = 10;
   */
  cardStates: CardState[];

  /**
   * 手牌，广播时，此项无
   *
   * @generated from field: repeated pirate.v1.Card hand_cards = 11;
   */
  handCards: Card[];

  /**
   * 手牌数量
   *
   * @generated from field: int32 hand_card_count = 12;
   */
  handCardCount: number;

  /**
   * 已经出的牌
   *
   * @generated from field: repeated pirate.v1.Card discard_cards = 13;
   */
  discardCards: Card[];

  /**
   * 累计得分
   *
   * @generated from field: int32 score = 16;
   */
  score: number;

  /**
   * 最近一次得分的变化值
   *
   * @generated from field: int32 score_change = 17;
   */
  scoreChange: number;

  /**
   * 可攻击次数，不含当次，表示使用“强化”等牌后产生的额外的攻击次数
   *
   * @generated from field: int32 drawable_count = 18;
   */
  drawableCount: number;

  /**
   * 冻结次数，0 表示未被冻结，>0 表示被冻结的次数，
   * 被其他玩家使用冻结后，该值+1，解冻一次该值-1
   *
   * @generated from field: int32 frozen_count = 19;
   */
  frozenCount: number;
};

/**
 * 用户信息
 *
 * @generated from message pirate.v1.Player
 */
export type PlayerJson = {
  /**
   * 用户id
   *
   * @generated from field: string id = 1;
   */
  id?: string;

  /**
   * 用户昵称
   *
   * @generated from field: string nickname = 2;
   */
  nickname?: string;

  /**
   * 用户头像
   *
   * @generated from field: string cover_url = 3;
   */
  coverUrl?: string;

  /**
   * 是否退出
   *
   * @generated from field: bool exited = 14;
   */
  exited?: boolean;

  /**
   * 是否 Game over
   *
   * @generated from field: bool game_over = 4;
   */
  gameOver?: boolean;

  /**
   * 是否系统托管
   *
   * @generated from field: bool hosting = 5;
   */
  hosting?: boolean;

  /**
   * 延时牌效果
   *
   * @generated from field: bool delay_effect_card = 15;
   */
  delayEffectCard?: boolean;

  /**
   * 玩家在战局中的索引
   *
   * @generated from field: uint32 index = 6;
   */
  index?: number;

  /**
   * 组队id
   *
   * @generated from field: string team_id = 7;
   */
  teamId?: string;

  /**
   * 牌状态，广播时，此项无
   *
   * @generated from field: repeated pirate.v1.CardState card_states = 10;
   */
  cardStates?: CardStateJson[];

  /**
   * 手牌，广播时，此项无
   *
   * @generated from field: repeated pirate.v1.Card hand_cards = 11;
   */
  handCards?: CardJson[];

  /**
   * 手牌数量
   *
   * @generated from field: int32 hand_card_count = 12;
   */
  handCardCount?: number;

  /**
   * 已经出的牌
   *
   * @generated from field: repeated pirate.v1.Card discard_cards = 13;
   */
  discardCards?: CardJson[];

  /**
   * 累计得分
   *
   * @generated from field: int32 score = 16;
   */
  score?: number;

  /**
   * 最近一次得分的变化值
   *
   * @generated from field: int32 score_change = 17;
   */
  scoreChange?: number;

  /**
   * 可攻击次数，不含当次，表示使用“强化”等牌后产生的额外的攻击次数
   *
   * @generated from field: int32 drawable_count = 18;
   */
  drawableCount?: number;

  /**
   * 冻结次数，0 表示未被冻结，>0 表示被冻结的次数，
   * 被其他玩家使用冻结后，该值+1，解冻一次该值-1
   *
   * @generated from field: int32 frozen_count = 19;
   */
  frozenCount?: number;
};

/**
 * Describes the message pirate.v1.Player.
 * Use `create(PlayerSchema)` to create a new message.
 */
export const PlayerSchema: GenMessage<Player, PlayerJson> = /*@__PURE__*/
  messageDesc(file_pirate_v1_player, 0);

/**
 * @generated from message pirate.v1.Team
 */
export type Team = Message<"pirate.v1.Team"> & {
  /**
   * 队伍id
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * 玩家索引列表
   *
   * @generated from field: repeated uint32 player_indices = 3;
   */
  playerIndices: number[];

  /**
   * 是否全部退出
   *
   * @generated from field: bool exited = 4;
   */
  exited: boolean;

  /**
   * 是否全部 Game over
   *
   * @generated from field: bool game_over = 5;
   */
  gameOver: boolean;

  /**
   * 排名
   *
   * @generated from field: uint32 rank = 6;
   */
  rank: number;
};

/**
 * @generated from message pirate.v1.Team
 */
export type TeamJson = {
  /**
   * 队伍id
   *
   * @generated from field: string id = 1;
   */
  id?: string;

  /**
   * 玩家索引列表
   *
   * @generated from field: repeated uint32 player_indices = 3;
   */
  playerIndices?: number[];

  /**
   * 是否全部退出
   *
   * @generated from field: bool exited = 4;
   */
  exited?: boolean;

  /**
   * 是否全部 Game over
   *
   * @generated from field: bool game_over = 5;
   */
  gameOver?: boolean;

  /**
   * 排名
   *
   * @generated from field: uint32 rank = 6;
   */
  rank?: number;
};

/**
 * Describes the message pirate.v1.Team.
 * Use `create(TeamSchema)` to create a new message.
 */
export const TeamSchema: GenMessage<Team, TeamJson> = /*@__PURE__*/
  messageDesc(file_pirate_v1_player, 1);

/**
 * 玩家在线状态改变、出局、退出的广播
 *
 * @generated from message pirate.v1.PlayerStatusChangedBroadcast
 */
export type PlayerStatusChangedBroadcast = Message<"pirate.v1.PlayerStatusChangedBroadcast"> & {
  /**
   * @generated from field: pirate.v1.Player player = 1;
   */
  player?: Player;
};

/**
 * 玩家在线状态改变、出局、退出的广播
 *
 * @generated from message pirate.v1.PlayerStatusChangedBroadcast
 */
export type PlayerStatusChangedBroadcastJson = {
  /**
   * @generated from field: pirate.v1.Player player = 1;
   */
  player?: PlayerJson;
};

/**
 * Describes the message pirate.v1.PlayerStatusChangedBroadcast.
 * Use `create(PlayerStatusChangedBroadcastSchema)` to create a new message.
 */
export const PlayerStatusChangedBroadcastSchema: GenMessage<PlayerStatusChangedBroadcast, PlayerStatusChangedBroadcastJson> = /*@__PURE__*/
  messageDesc(file_pirate_v1_player, 2);

/**
 * 玩家信息
 *
 * @generated from message pirate.v1.PlayerInfoMessage
 */
export type PlayerInfoMessage = Message<"pirate.v1.PlayerInfoMessage"> & {
  /**
   * 玩家分组信息
   *
   * @generated from field: repeated pirate.v1.Team teams = 1;
   */
  teams: Team[];

  /**
   * 玩家信息（当前玩家下，会有hand cards的数据）
   *
   * @generated from field: repeated pirate.v1.Player players = 2;
   */
  players: Player[];

  /**
   * 当前玩家索引，广播用户会返回-1
   *
   * @generated from field: int32 player_index = 3;
   */
  playerIndex: number;
};

/**
 * 玩家信息
 *
 * @generated from message pirate.v1.PlayerInfoMessage
 */
export type PlayerInfoMessageJson = {
  /**
   * 玩家分组信息
   *
   * @generated from field: repeated pirate.v1.Team teams = 1;
   */
  teams?: TeamJson[];

  /**
   * 玩家信息（当前玩家下，会有hand cards的数据）
   *
   * @generated from field: repeated pirate.v1.Player players = 2;
   */
  players?: PlayerJson[];

  /**
   * 当前玩家索引，广播用户会返回-1
   *
   * @generated from field: int32 player_index = 3;
   */
  playerIndex?: number;
};

/**
 * Describes the message pirate.v1.PlayerInfoMessage.
 * Use `create(PlayerInfoMessageSchema)` to create a new message.
 */
export const PlayerInfoMessageSchema: GenMessage<PlayerInfoMessage, PlayerInfoMessageJson> = /*@__PURE__*/
  messageDesc(file_pirate_v1_player, 3);

/**
 * 玩家摸牌的消息
 *
 * @generated from message pirate.v1.PlayerDrawnCardMessage
 */
export type PlayerDrawnCardMessage = Message<"pirate.v1.PlayerDrawnCardMessage"> & {
  /**
   * 玩家索引
   *
   * @generated from field: int32 player_index = 1;
   */
  playerIndex: number;

  /**
   * 玩家摸的桌面卡牌的索引（一次一张）
   *
   * @generated from field: int32 drawn_deck_card_index = 2;
   */
  drawnDeckCardIndex: number;

  /**
   * 玩家卡牌（只有自己有数据）
   *
   * @generated from field: pirate.v1.Card drawn_card = 3;
   */
  drawnCard: Card;

  /**
   * 剩余的摸牌消费数量
   *
   * @generated from field: int32 remaining_drawn_cards = 4;
   */
  remainingDrawnCards: number;
};

/**
 * 玩家摸牌的消息
 *
 * @generated from message pirate.v1.PlayerDrawnCardMessage
 */
export type PlayerDrawnCardMessageJson = {
  /**
   * 玩家索引
   *
   * @generated from field: int32 player_index = 1;
   */
  playerIndex?: number;

  /**
   * 玩家摸的桌面卡牌的索引（一次一张）
   *
   * @generated from field: int32 drawn_deck_card_index = 2;
   */
  drawnDeckCardIndex?: number;

  /**
   * 玩家卡牌（只有自己有数据）
   *
   * @generated from field: pirate.v1.Card drawn_card = 3;
   */
  drawnCard?: CardJson;

  /**
   * 剩余的摸牌消费数量
   *
   * @generated from field: int32 remaining_drawn_cards = 4;
   */
  remainingDrawnCards?: number;
};

/**
 * Describes the message pirate.v1.PlayerDrawnCardMessage.
 * Use `create(PlayerDrawnCardMessageSchema)` to create a new message.
 */
export const PlayerDrawnCardMessageSchema: GenMessage<PlayerDrawnCardMessage, PlayerDrawnCardMessageJson> = /*@__PURE__*/
  messageDesc(file_pirate_v1_player, 4);

/**
 * @generated from message pirate.v1.PlayerHostingDrawnCardMessage
 */
export type PlayerHostingDrawnCardMessage = Message<"pirate.v1.PlayerHostingDrawnCardMessage"> & {
  /**
   * 玩家索引
   *
   * @generated from field: int32 player_index = 1;
   */
  playerIndex: number;

  /**
   * 托管时，摸牌的桌面卡牌的索引
   *
   * @generated from field: repeated int32 drawn_deck_card_indices = 2;
   */
  drawnDeckCardIndices: number[];
};

/**
 * @generated from message pirate.v1.PlayerHostingDrawnCardMessage
 */
export type PlayerHostingDrawnCardMessageJson = {
  /**
   * 玩家索引
   *
   * @generated from field: int32 player_index = 1;
   */
  playerIndex?: number;

  /**
   * 托管时，摸牌的桌面卡牌的索引
   *
   * @generated from field: repeated int32 drawn_deck_card_indices = 2;
   */
  drawnDeckCardIndices?: number[];
};

/**
 * Describes the message pirate.v1.PlayerHostingDrawnCardMessage.
 * Use `create(PlayerHostingDrawnCardMessageSchema)` to create a new message.
 */
export const PlayerHostingDrawnCardMessageSchema: GenMessage<PlayerHostingDrawnCardMessage, PlayerHostingDrawnCardMessageJson> = /*@__PURE__*/
  messageDesc(file_pirate_v1_player, 5);

/**
 * 玩家出牌的广播
 *
 * @generated from message pirate.v1.PlayerPostedCardBroadcast
 */
export type PlayerPostedCardBroadcast = Message<"pirate.v1.PlayerPostedCardBroadcast"> & {
  /**
   * 玩家索引
   *
   * @generated from field: int32 player_index = 1;
   */
  playerIndex: number;

  /**
   * 玩家手牌出牌的索引
   *
   * @generated from field: repeated int32 hand_card_indices = 2;
   */
  handCardIndices: number[];

  /**
   * 玩家出的牌
   *
   * @generated from field: repeated pirate.v1.Card posted_cards = 3;
   */
  postedCards: Card[];

  /**
   * 桌面所有出的牌
   *
   * @generated from field: repeated pirate.v1.Card deck_discard_cards = 4;
   */
  deckDiscardCards: Card[];
};

/**
 * 玩家出牌的广播
 *
 * @generated from message pirate.v1.PlayerPostedCardBroadcast
 */
export type PlayerPostedCardBroadcastJson = {
  /**
   * 玩家索引
   *
   * @generated from field: int32 player_index = 1;
   */
  playerIndex?: number;

  /**
   * 玩家手牌出牌的索引
   *
   * @generated from field: repeated int32 hand_card_indices = 2;
   */
  handCardIndices?: number[];

  /**
   * 玩家出的牌
   *
   * @generated from field: repeated pirate.v1.Card posted_cards = 3;
   */
  postedCards?: CardJson[];

  /**
   * 桌面所有出的牌
   *
   * @generated from field: repeated pirate.v1.Card deck_discard_cards = 4;
   */
  deckDiscardCards?: CardJson[];
};

/**
 * Describes the message pirate.v1.PlayerPostedCardBroadcast.
 * Use `create(PlayerPostedCardBroadcastSchema)` to create a new message.
 */
export const PlayerPostedCardBroadcastSchema: GenMessage<PlayerPostedCardBroadcast, PlayerPostedCardBroadcastJson> = /*@__PURE__*/
  messageDesc(file_pirate_v1_player, 6);

/**
 * @generated from message pirate.v1.PlayerSelectableTargetsMessage
 */
export type PlayerSelectableTargetsMessage = Message<"pirate.v1.PlayerSelectableTargetsMessage"> & {
  /**
   * 发起选择的玩家索引
   *
   * @generated from field: int32 from_player_index = 1;
   */
  fromPlayerIndex: number;

  /**
   * 可选择的玩家索引
   *
   * @generated from field: repeated int32 selectable_player_indices = 2;
   */
  selectablePlayerIndices: number[];
};

/**
 * @generated from message pirate.v1.PlayerSelectableTargetsMessage
 */
export type PlayerSelectableTargetsMessageJson = {
  /**
   * 发起选择的玩家索引
   *
   * @generated from field: int32 from_player_index = 1;
   */
  fromPlayerIndex?: number;

  /**
   * 可选择的玩家索引
   *
   * @generated from field: repeated int32 selectable_player_indices = 2;
   */
  selectablePlayerIndices?: number[];
};

/**
 * Describes the message pirate.v1.PlayerSelectableTargetsMessage.
 * Use `create(PlayerSelectableTargetsMessageSchema)` to create a new message.
 */
export const PlayerSelectableTargetsMessageSchema: GenMessage<PlayerSelectableTargetsMessage, PlayerSelectableTargetsMessageJson> = /*@__PURE__*/
  messageDesc(file_pirate_v1_player, 7);

/**
 * @generated from message pirate.v1.PlayerAfterSelectTargetBroadcast
 */
export type PlayerAfterSelectTargetBroadcast = Message<"pirate.v1.PlayerAfterSelectTargetBroadcast"> & {
  /**
   * 发起选择的玩家索引
   *
   * @generated from field: int32 from_player_index = 1;
   */
  fromPlayerIndex: number;

  /**
   * 玩家选择的目标索引
   *
   * @generated from field: int32 target_player_index = 2;
   */
  targetPlayerIndex: number;
};

/**
 * @generated from message pirate.v1.PlayerAfterSelectTargetBroadcast
 */
export type PlayerAfterSelectTargetBroadcastJson = {
  /**
   * 发起选择的玩家索引
   *
   * @generated from field: int32 from_player_index = 1;
   */
  fromPlayerIndex?: number;

  /**
   * 玩家选择的目标索引
   *
   * @generated from field: int32 target_player_index = 2;
   */
  targetPlayerIndex?: number;
};

/**
 * Describes the message pirate.v1.PlayerAfterSelectTargetBroadcast.
 * Use `create(PlayerAfterSelectTargetBroadcastSchema)` to create a new message.
 */
export const PlayerAfterSelectTargetBroadcastSchema: GenMessage<PlayerAfterSelectTargetBroadcast, PlayerAfterSelectTargetBroadcastJson> = /*@__PURE__*/
  messageDesc(file_pirate_v1_player, 8);

/**
 * 击中弱点广播
 *
 * @generated from message pirate.v1.PlayerDrawnDeathBroadcast
 */
export type PlayerDrawnDeathBroadcast = Message<"pirate.v1.PlayerDrawnDeathBroadcast"> & {
  /**
   * 发起选择的玩家索引
   *
   * @generated from field: int32 drawn_player_index = 1;
   */
  drawnPlayerIndex: number;
};

/**
 * 击中弱点广播
 *
 * @generated from message pirate.v1.PlayerDrawnDeathBroadcast
 */
export type PlayerDrawnDeathBroadcastJson = {
  /**
   * 发起选择的玩家索引
   *
   * @generated from field: int32 drawn_player_index = 1;
   */
  drawnPlayerIndex?: number;
};

/**
 * Describes the message pirate.v1.PlayerDrawnDeathBroadcast.
 * Use `create(PlayerDrawnDeathBroadcastSchema)` to create a new message.
 */
export const PlayerDrawnDeathBroadcastSchema: GenMessage<PlayerDrawnDeathBroadcast, PlayerDrawnDeathBroadcastJson> = /*@__PURE__*/
  messageDesc(file_pirate_v1_player, 9);

