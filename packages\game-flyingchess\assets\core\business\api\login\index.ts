import { log } from 'cc'

import store from '../../store'
import { BaseAPI } from '../BaseAPI'

export class LoginAPI extends BaseAPI {}
/**登录 */
// export const login = async (data: web.v1.LoginRequest) => {
//     const res = await request.http<web.v1.LoginResponse>({
//         url: '/v1/login',
//         method: 'POST',
//         data
//     })
//     const { login } = store
//     window.ccLog('res', res)
//     login.token = res.access_token
//     login.ws_url = res.ws_url
// }

// /**刷新token */
// export const refresh = () => {
//     return request.http<web.v1.LoginResponse>({
//         url: '/v1/refresh/token',
//         method: 'POST',
//     })
// }

// /**获取服务器时间 */
// export const get_service_time = async () => {
//     const res = await request.http<web.v1.GetServiceTimeResponse>({
//         url: '/v1/service_time',
//         method: 'GET',
//     })
//     store.global.diffServerTimer = Number(res.time) - ~~(Date.now() / 1000);
//     window.ccLog('服务器时间差：', store.global.diffServerTimer);
// }
