/**
 * @describe HTTP通信模块
 * <AUTHOR>
 * @date 2023-08-02 19:33:39
 */

import { log } from 'cc'
import Crypto from 'crypto-es'
import { CustomPlatform } from '@/core/business/store/global'
import { BaseManager } from '../../BaseManager'
import { Manager } from '../../index'
import store from '@/core/business/store'
import ky, { Input, KyInstance } from 'ky'
import { getCommonSign } from '@/core/business/hooks/api-sign/CommonSign'
import { attachSign } from '@/core/business/hooks/api-sign/SuileyooSign'

type MethodType =
    | 'OPTIONS'
    | 'GET'
    | 'HEAD'
    | 'POST'
    | 'PUT'
    | 'DELETE'
    | 'TRACE'
    | 'CONNECT'

type RequestData = Record<string, any>
interface Options {
    // 域名
    baseURL?: string
    url: Input
    // 请求头
    headers?: Record<string, string>
    // 方法
    method?: MethodType
    // 数据
    data?: RequestData
    // 超时
    timeout?: number // Optional, default is 5000ms
    // 重试
    retries?: number // Optional, default is 3 retries
}

type RequiredOptions = Omit<Required<Options>, 'baseURL'> & {
    baseURL?: string
}

interface IResponse<T> {
    code: number // 请求消息码
    message: string // 错误信息
    data: T // 输出数据
    success: boolean // 请求成功标识
}

// export class HttpClient extends BaseManager {

//     constructor(cat: Manager) {
//         super(cat);

//     }

//     // 定义一个Fetch拦截器
//     interceptor(options: Options): RequiredOptions {

//         const url = (options?.baseURL ?? this.cat.env?.http_base_url) + options.url;

//         const headers = options.headers || {};

//         if (store.login.token) {
//             headers.Authorization = `Bearer ${store.login.token}`
//         }

//         const method = options.method || 'GET';
//         const data = options.data || {};

//         if (store.global.customPlatform == CustomPlatform.SuiLeYoo) {
//             Object.assign(data, attachSign(options.data) || {})
//         } else {
//             const sign = this.getSign(data);
//             Object.assign(headers, { 'X-Signature': sign.sign, 'X-Timestamp': sign.timestamp });
//         }

//         const timeout = options.timeout ?? 5000

//         const retries = options.retries ?? 3

//         // 返回修改后的请求对象
//         return { url, headers, method, data, timeout, retries };
//     };

//     // 获取签名
//     private getSign = (data: RequestData) => {
//         let timestamp = (~~(Date.now() / 1000) + store.global.diffServerTimer).toString(); //10位时间戳
//         let data_timestamp: RequestData = { ...data, 'X-Timestamp': timestamp };

//         let keys = Object.keys(data_timestamp);

//         const nonce = keys.sort().reduce((prev, cur) => {
//             // 排除paginate.limit paginate.page category_id=0 status=0
//             if (
//                 !(
//                     ['paginate.limit', 'paginate.page'].includes(cur) ||
//                     (['category_id', 'status'].includes(cur) && data_timestamp[cur] == 0) ||
//                     typeof data_timestamp[cur] === 'object'
//                 )
//             ) {
//                 prev += `${prev.length ? '&' : ''}${cur}=${data_timestamp[cur]}`;
//             }

//             return prev;
//         }, '');
//         let sign = Crypto.SHA1(Crypto.MD5(nonce + this.cat?.env.secret).toString()).toString();

//         return { sign, timestamp };
//     };

//     private _request = <T>(options: RequiredOptions): Promise<IResponse<T>> => {
//         return new Promise((resolve, reject) => {
//             // 格式化数据
//             let nonce: string = '';
//             for (let key in options.data) {
//                 nonce += `${key}=${options.data[key]}&`;
//             }
//             nonce = nonce.substring(0, nonce.length - 1);
//             //1.创建对象
//             const xhr = new XMLHttpRequest();
//             //2.初始化
//             xhr.open(options.method, (options.method == 'GET') && nonce ? options.url + '?' + nonce : options.url, true);

//             //3.设置请求头
//             xhr.setRequestHeader('Content-type',
//                 store.global.customPlatform == CustomPlatform.SuiLeYoo ?
//                     'application/x-www-form-urlencoded' :
//                     'application/json; charset=utf-8')
//             if (options.headers) {
//                 for (let key in options.headers) {
//                     xhr.setRequestHeader(key, options.headers[key]);
//                 }
//             }

//             //3.绑定事件，处理相应结果
//             xhr.onreadystatechange = () => {
//                 if (xhr.readyState == 4) {
//                     //判断相应状态码 200 -299 为成功的
//                     if (xhr.status >= 200 && xhr.status <= 300) {
//                         //表示成功
//                         resolve(JSON.parse(xhr.response));
//                     } else {
//                         //如果失败
//                         reject(`客户端状态码错误:${xhr.status}`);
//                     }
//                 }
//             };

//             const params = store.global.customPlatform == CustomPlatform.SuiLeYoo ? Object.keys(options.data)
//                 .map(function (key) {
//                     return key + '=' + options.data[key];
//                 })
//                 .join('&') : JSON.stringify(options.data)
//             //4.发送
//             xhr.send(options.method === 'POST' ? params : null);
//         });
//     };

//     async request<T extends Options>(options: T, interceptor?: (options: T) => RequiredOptions): Promise<Response> {

//         const res = interceptor ? interceptor(options) : this.interceptor(options) ?? options

//         const { url, headers, method, data, timeout, retries, baseURL } = res;

//         const fullURL = `${(baseURL ?? this.cat.env?.http_base_url)}${url}`

//         const requestOptions: RequestInit = {
//             method,
//             headers: {
//                 'Content-Type': 'application/x-www-form-urlencoded',
//                 ...headers,
//             },
//             body: method !== 'GET' ? JSON.stringify(data) : undefined,
//         };

//         return this.fetchWithRetryAndTimeout(fullURL, requestOptions, timeout, retries);
//     }

//     private async fetchWithRetryAndTimeout(url: string, options: RequestInit, timeout: number, retries: number): Promise<Response> {
//         for (let attempt = 0; attempt < retries; attempt++) {
//             const controller = new AbortController();
//             const timeoutId = setTimeout(() => controller.abort(), timeout);

//             try {
//                 const response = await fetch(url, {
//                     ...options,
//                     signal: controller.signal,
//                 });

//                 clearTimeout(timeoutId);

//                 if (!response.ok) {
//                     throw new Error(`Request failed with status ${response.status} `);
//                 }
//                 return response;
//             } catch (err) {
//                 clearTimeout(timeoutId);

//                 if (attempt === retries - 1) {
//                     throw new Error(`Request failed after ${retries} attempts: ${err instanceof Error ? err.message : 'Unknown error'} `);
//                 }

//                 if (err instanceof Error && err.name === 'AbortError') {
//                     console.warn(`Attempt ${attempt + 1} timed out.Retrying...`);
//                 } else if (err instanceof Error) {
//                     console.warn(`Attempt ${attempt + 1} failed: ${err.message}. Retrying...`);
//                 } else {
//                     console.warn(`Attempt ${attempt + 1} failed with an unknown error.Retrying...`);
//                 }
//             }
//         }
//         throw new Error('Request failed');
//     }

// }

export const http = async <T>(
    options: Options,
    original?: KyInstance
): Promise<T> => {
    const extended = (original ?? ky.create()).extend({
        hooks: {
            beforeRequest: [
                (request) => {
                    if (
                        store.global.customPlatform == CustomPlatform.SuiLeYoo
                    ) {
                        options.data = attachSign(options?.data)
                        request.headers.set(
                            'Content-type',
                            'application/x-www-form-urlencoded'
                        )
                    } else {
                        const { sign, timestamp } = getCommonSign(options?.data)
                        request.headers.set('X-Signature', sign)
                        request.headers.set('X-Timestam', timestamp)
                        request.headers.set(
                            'Content-type',
                            'application/json; charset=utf-8'
                        )
                    }
                },
            ],
            beforeError: [
                (error) => {
                    const { response } = error
                    if (response && response.body) {
                        error.name = 'GitHubError'
                        error.message = `(${response.status})`
                    }

                    return error
                },
            ],
        },
        headers: {
            Authorization: `Bearer ${store.login.token}`,
        },
        body: JSON.stringify(options.data),
    })

    const response = (await extended(options.url).json()) as T

    window.ccLog('response', response)
    return response
}

// export const http = async <T>(url: string, options: Options): Promise<T> => {
//     // const opt = await this.#beforeRequest(options);

//     await HttpClient(url).request(options)

//     // return await new Promise<T>((resolve, reject) => {
//     //     this.#request<T>(opt).then(
//     //         res => {
//     //             window.ccLog(
//     //                 `% cHTTP请求地址 %c${ opt.method } %c ${ opt.url } `,
//     //                 'background:#35495e ; padding: 1px; border-radius: 3px 0 0 3px;  color: #fff',
//     //                 'background:#41b883 ; padding: 1px; border-radius: 0 3px 3px 0;  color: #fff',
//     //                 'background:transparent'
//     //             );
//     //             // server状态码
//     //             if (res.code === 0) {
//     //                 resolve(res.data);
//     //             } else {
//     //                 reject(res);
//     //             }
//     //         },
//     //         err => {
//     //             reject(err.errMsg);
//     //         }
//     //     );
//     // }).catch((data: IResponse<T> | string): Promise<T> => {
//     //     if (typeof data == 'string') {
//     //         return Promise.reject(`${ data } `);
//     //     } else {
//     //         this.cat.gui.showToast({
//     //             title: `${ data.message } `,
//     //         });
//     //         return Promise.reject(`code:${ data.code } -${ data.message } `);
//     //     }
//     // });
// };
