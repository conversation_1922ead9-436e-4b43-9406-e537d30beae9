/**
 * @describe UI容器管理
 * <AUTHOR>
 * @date 2024-09-12 11:45:31
 */

import { _decorator, Component, isValid, log, Node, Sprite, Color } from 'cc'
import UILayer from '../layer/UILayer'
import { cat } from '../../index'
import { GlobalEventConstant } from '../../constant'
import { Gui, LayerType } from '../Gui'
import { BlackMask } from '../black-mask/BlackMask'
const { ccclass, property } = _decorator

@ccclass('UIContainer')
export class UIContainer extends UILayer {
    @property({ type: BlackMask })
    scene_mask: BlackMask

    @property({ type: Node })
    ui_container: Node

    gui: Gui = null!

    // 存储遮罩原始透明度
    private originalMaskAlpha: number = 180 // 默认值，根据预制体中的设置

    protected override onLoad(): void {
        this.setSceneMaskActive(false)
    }

    get brother() {
        return this.ui_container.children || []
    }

    protected override onEventListener(): void {
        cat.event.on(
            GlobalEventConstant.ROOT_MASK_CHANGE,
            this.uiMaskChanged,
            this
        )
    }

    /**
     * 增加层级
     * @param noMask 是否不显示遮罩（透明遮罩）
     */
    addMask(noMask?: boolean): void {
        window.ccLog('addMask', noMask)
        const last = this.brother[this.brother.length - 1]
        last && this.scene_mask.node.setSiblingIndex(last.getSiblingIndex())
        this.blockMaskSiblingIndexChanged()

        // 查找遮罩节点下的Sprite组件
        const maskSprite = this.scene_mask.node.getComponentInChildren(Sprite)
        if (maskSprite) {
            // 如果是第一次添加遮罩，保存原始透明度
            if (this.originalMaskAlpha === 180) {
                // 只在默认值时保存
                this.originalMaskAlpha = maskSprite.color.a
            }

            // 根捬 noMask 参数设置透明度
            const alpha = noMask ? 0 : this.originalMaskAlpha
            maskSprite.color = new Color(
                maskSprite.color.r,
                maskSprite.color.g,
                maskSprite.color.b,
                alpha
            )
        }
    }
    /**减少层级 */
    subMask() {
        window.ccLog('subMask')
        const last = this.brother[this.brother.length - 2]
        last && this.scene_mask.node.setSiblingIndex(last.getSiblingIndex())
        this.blockMaskSiblingIndexChanged()

        // 如果没有弹窗了，恢复遮罩的原始透明度
        if (
            this.brother.length === 0 &&
            this.scene_mask.tween.children.length === 0
        ) {
            const maskSprite =
                this.scene_mask.node.getComponentInChildren(Sprite)
            if (maskSprite) {
                maskSprite.color = new Color(
                    maskSprite.color.r,
                    maskSprite.color.g,
                    maskSprite.color.b,
                    this.originalMaskAlpha
                )
            }
        }
    }

    blockMaskSiblingIndexChanged() {
        window.ccLog(
            'blockMaskSiblingIndexChanged',
            this.brother.length,
            this.scene_mask.tween.children.length
        )
        this.brother.length > 1 || this.scene_mask.tween.children.length > 1
            ? this.show()
            : this.hide()
    }

    /**
     * 将UI节点挂载在tween上执行动画
     * @param node 节点
     */
    addNodeToTween(node: Node) {
        if (isValid(this) && this.scene_mask) {
            this.scene_mask.tween.addChild(node)
        }
        return this
    }

    show() {
        window.ccLog('UIContainer show ----------------------')
        this.setSceneMaskActive(true)
    }

    hide() {
        window.ccLog('UIContainer hide ----------------------')
        this.setSceneMaskActive(false)
    }

    setGui(gui: Gui) {
        this.gui = gui
        return this
    }

    /**根层的变化 */
    uiMaskChanged() {
        // 如果根ui节点 没有子节点 则隐藏根遮罩
        // 直接调用blockMaskSiblingIndexChanged来处理遮罩状态
        this.blockMaskSiblingIndexChanged()
    }

    setSceneMaskActive(active: boolean) {
        this.scene_mask.node.active = this.gui.root_mask.active ? false : active
    }
}
