import {
    Component,
    Prefab,
    Node,
    director,
    Constructor,
    instantiate,
    Vec3,
} from 'cc'
type PoolTotalCallback<T> = (item: T | Node, data: any, index: number) => void
type PoolPushCallback<T> = (item: T | Node, index: number) => void
/** 对象池管理类 */
export class BasePool<T extends Component = Component> {
    private prefab: Prefab | Node
    private component: Constructor<T>
    private gener!: Generator
    private _caches: any[]
    private _uses: any[]
    get caches() {
        return Object.assign([], this._caches)
    }
    get uses() {
        return Object.assign([], this._uses)
    }
    constructor(prefab: Prefab | Node, component?: Constructor<T>) {
        this.prefab = prefab
        // this.component = component
        this._caches = []
        this._uses = []
    }
    /**
     * 从对象池中创建一组对象
     */
    public total(
        arrayOrNumber: any[] | number,
        done: PoolTotalCallback<T>,
        duration: number = 10
    ): Promise<void> {
        return new Promise(async (resolve, reject) => {
            if (
                arrayOrNumber == undefined ||
                arrayOrNumber == null ||
                (typeof arrayOrNumber == 'number' && isNaN(arrayOrNumber))
            )
                return resolve()
            this.gener?.return('')
            let length =
                arrayOrNumber instanceof Array
                    ? arrayOrNumber.length
                    : arrayOrNumber
            let data = arrayOrNumber instanceof Array ? arrayOrNumber : null
            this.gener = this.getGenerator(
                length,
                this.task.bind(this),
                data,
                done
            )
            await this.exeGenerator(this.gener, duration)
            this.surplus(length)
            resolve()
        })
    }
    /**
     * 添加一个对象
     */
    push(done: PoolPushCallback<T>): void {
        let node = this.get()
        done(node, this._uses.length - 1)
    }
    /**
     * 弹出一个对象
     * @param node 要弹出的对象
     */
    put(node: Node): boolean {
        if (!node) return false
        let curre = node
        let index = this._uses.findIndex((item) => {
            if (item instanceof Node) return item.uuid == curre.uuid
            return item.node.uuid == curre.uuid
        })
        if (index == -1) return false
        let obj = this._uses.splice(index, 1)[0]
        obj instanceof Node
            ? obj.removeFromParent()
            : obj.node.removeFromParent()
        this._caches.push(obj)
        node.emit('onDisable')
        return true
    }
    /**
     * 回收所有对象到对象池
     */
    recycle(): void {
        try {
            while (this._uses.length > 0) {
                let obj = this._uses.pop()
                obj instanceof Node
                    ? obj.removeFromParent()
                    : obj.node.removeFromParent()
                this._caches.push(obj)
            }
        } catch (error) {
            console.error(error)
        }
    }
    /**
     * 清理对象池
     */
    clear() {
        let list = [...this._uses, ...this._caches]
        for (let i = 0; i < list.length; i++) {
            const obj = list[i]
            obj instanceof Node ? obj.destroy() : obj.node.destroy()
        }
        this._caches = []
        this._uses = []
    }
    private task(
        index: number,
        array: any[] | null,
        done: PoolTotalCallback<T>
    ) {
        let data = array?.[index] || index
        let node = this.get()
        done(node, data, index)
    }
    private surplus(length: number) {
        try {
            while (this._uses.length - length) {
                let obj = this._uses.pop()
                if (obj instanceof Node) {
                    obj.removeFromParent()
                } else {
                    obj.node.removeFromParent()
                }
                this._caches.push(obj)
            }
        } catch (error) {
            console.error(error)
        }
    }
    get(): Node {
        if (this._caches.length == 0) {
            let obj = instantiate(this.prefab) as Node
            // let item = obj.getComponent(this.component) || obj
            this._caches.push(obj)
        }
        let node = this._caches.pop()
        node?.setScale(Vec3.ONE)
        node.emit('onEnable')
        this._uses.push(node)
        return node
    }
    private *getGenerator(
        length: number,
        callback: Function,
        ...params: any
    ): Generator {
        for (let i = 0; i < length; i++) {
            yield callback(i, ...params)
        }
    }
    private exeGenerator(generator: Generator, duration: number) {
        return new Promise((resolve, reject) => {
            let gen = generator
            let execute = () => {
                let startTime = new Date().getTime()
                for (let iter = gen.next(); ; iter = gen.next()) {
                    if (iter == null || iter.done) {
                        resolve(null)
                        return
                    }
                    if (new Date().getTime() - startTime > duration) {
                        setTimeout(() => {
                            execute()
                        }, director.getDeltaTime() * 1000)
                        return
                    }
                }
            }
            execute()
        })
    }
}
