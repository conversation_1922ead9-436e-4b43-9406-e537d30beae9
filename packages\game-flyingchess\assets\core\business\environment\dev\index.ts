/**
 * @describe 开发环境配置
 * <AUTHOR>
 * @date 2023-08-02 19:46:13
 */

import { queryStringToObject } from '../util'
const queryParams = queryStringToObject(location.search.slice(1))

// ws://192.168.33.33:6551/ws/conn
// http://192.168.33.33:6550
const ENV_TYPE: RuntineEnv = {
    static_base_url: '',
    http_base_url: 'https://dev-cloud-game-api.stnts.com/',
    event_tracking_url: 'https://dssp-test.stnts.com/?opt=put&type=json',
    event_tracking_key: 'alibabatutudodo@',
    bucket: '',
    region: '',
    secret: '5bozaf6by&w3^4i$mll1uj0yzyfhx8t_',
    // player_ws_url: 'wss://gam-dev.yiqiyoo.com/ws/conn',
    // audience_ws_url: 'wss://gam-dev.yiqiyoo.com/ws/aud',
    player_ws_url:
        queryParams.player_ws_url || 'wss://game-server-dev.mityoo.com/ws/conn',
    audience_ws_url:
        queryParams.audience_ws_url ||
        'wss://game-server-dev.mityoo.com/ws/aud',
    stop_service_url: '',
}
export default ENV_TYPE
