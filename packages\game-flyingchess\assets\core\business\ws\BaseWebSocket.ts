/**
 * @describe 基础socket类
 * <AUTHOR>
 * @date 2024-09-12 11:41:27
 */

import { Game, game, log } from 'cc'
import { Manager } from '@/core/manager'
import { BaseManager } from '@/core/manager/BaseManager'

// 定义一个接口，包含 destroy 方法
interface Destroyable {
    destroy(): void
}
export class BaseWebSocket<T extends Destroyable> extends BaseManager {
    ins: T | null = null

    constructor(cat: Manager) {
        super(cat)
        // 游戏关闭事件
        game.on(Game.EVENT_CLOSE, () => {
            // 兼容IOS
            this.destroy()
        })
    }

    /**销毁 */
    destroy = () => {
        window.ccLog('销毁ws')
        this.ins?.destroy()
        this.ins = null
    }
}
