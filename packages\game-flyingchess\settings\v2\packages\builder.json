{"__version__": "1.3.9", "splash-setting": {"totalTime": 500, "logo": {"type": "default"}}, "bundleConfig": {"custom": {"default": {"displayName": "i18n:builder.asset_bundle.defaultConfig", "configs": {"native": {"preferredOptions": {"isRemote": false, "compressionType": "merge_dep"}}, "web": {"preferredOptions": {"isRemote": false, "compressionType": "merge_all_json"}, "fallbackOptions": {"compressionType": "merge_dep"}}, "miniGame": {"fallbackOptions": {"isRemote": false, "compressionType": "merge_dep"}, "configMode": "fallback"}}}}}}