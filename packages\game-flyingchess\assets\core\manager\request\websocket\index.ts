import {
    Desc<PERSON>essage,
    DescMethod,
    MessageShape,
    clone,
    create,
    fromBinary,
} from '@bufbuild/protobuf'
import {
    ClientOption,
    Message as pMessage,
    PitayaError,
    Events,
    PitayaErrorSchema,
} from 'pitayaclient'
import { director, error, log, warnID } from 'cc'

import {
    AudienceOptions,
    AudienceOptionsSchema,
    SocialGameClient,
    SocialGameClientOption,
} from 'sgc'
import { DEBUG } from 'cc/env'
import { SocketRoute } from '@/core/business/ws'
import { GlobalEventConstant } from '../../constant'
import { cat } from '../../index'
import { ReconnectPrompt } from '@/core/ui/reconnection/Reconnection'
import { reflect, ReflectMessage } from '@bufbuild/protobuf/reflect'
import store from '@/core/business/store'

export type RequestOptions = {
    audOptions?: AudienceOptions
    show_log?: boolean
    showToast?: boolean
}

type ConnectionState =
    | keyof Events
    | 'init'
    | 'online'
    | 'offline'
    | 'show'
    | 'hide'
    | 'reconnect'
    | 'destroy'

type ConnectionEvent = {
    [K in ConnectionState]: K extends
        | 'error'
        | 'disconnect'
        | 'close'
        | 'paramError'
        ? { type: K; ev?: Event | string }
        : { type: K }
}[ConnectionState]

// 声明函数类型
type GameNotifyType = (
    route: string,
    reqReflect: ReflectMessage,
    options?: { audOptions?: AudienceOptions; show_log?: boolean }
) => Promise<void>

/**
 * 1.ws正常连接情况:网页后台切前台->同步机制(重新加载场景)
 * 2.ws异常断开情况:网页后台切前台->重连机制->同步机制
 */

/**
 * @describe 基于社交游戏封装的游戏基础ws类
 * <AUTHOR>
 * @date 2024-09-12 11:47:51
 */

export class WrapperSocialGameClient extends SocialGameClient {
    /**需要重连(标记接下来需要重连) */
    private isNeedReconnect = false

    /**网络在线 */
    private isOnline = true

    /**在后台 */
    private isInBackground: boolean = false

    /**状态机运行 */
    private running: boolean = false

    private index: number = 0

    constructor(opts: SocialGameClientOption, clientOption?: ClientOption) {
        super(opts, clientOption)

        this.running = true
        // this.handleEvent.next(); // 初始化状态机
        this.addEvent()
    }

    /**初始化 */
    addEvent() {
        this.on('connected', () => this.handleEvent({ type: 'connected' }))
            .on('reconnected', () => this.handleEvent({ type: 'reconnected' }))
            .on('disconnect', (ev?: Event) =>
                this.handleEvent({ type: 'disconnect', ev })
            )
            .on('paramError', (ev?: Event) =>
                this.handleEvent({ type: 'paramError', ev })
            )
            .on('kick', () => this.handleEvent({ type: 'kick' }))
            .on('reconnecting', () =>
                this.handleEvent({ type: 'reconnecting' })
            )
            // .on("reconnectTimeout", () => this.handleEvent({ type: 'reconnectTimeout' }))
            .on('maxReconnect', () =>
                this.handleEvent({ type: 'maxReconnect' })
            )
            .on('error', (ev: Event | string) =>
                this.handleEvent({ type: 'error', ev })
            )

        cat.event
            .on(GlobalEventConstant.EVENT_SHOW, this.onShowHandler, this)
            .on(GlobalEventConstant.EVENT_HIDE, this.onHideHandler, this)
            .on(GlobalEventConstant.ONLINE, this.onOnlineHandler, this)
            .on(GlobalEventConstant.OFFLINE, this.onOfflineHandler, this)
    }

    removeEvent() {
        this.removeAllListeners()
        cat.event
            .off(GlobalEventConstant.EVENT_SHOW, this.onShowHandler, this)
            .off(GlobalEventConstant.EVENT_HIDE, this.onHideHandler, this)
            .off(GlobalEventConstant.ONLINE, this.onOnlineHandler, this)
            .off(GlobalEventConstant.OFFLINE, this.onOfflineHandler, this)
    }

    private onShowHandler() {
        window.ccLog('Application moved to foreground')
        this.isInBackground = false
        if (this.isNeedReconnect && this.isOnline) {
            window.ccLog('Attempting to reconnect after moving to foreground')
            this.handleEvent({ type: 'show' })
        }
    }

    private onHideHandler() {
        window.ccLog('Application moved to background')
        this.isInBackground = true
        this.handleEvent({ type: 'hide' })
    }

    /**网络在线处理 */
    private onOnlineHandler() {
        this.isOnline = true
        window.ccLog(`正在检查网络状态:${this.isOnline ? '在线' : '断开'}`)
        this.handleEvent({ type: 'online' })
    }

    /**网络断开处理 */
    private onOfflineHandler() {
        this.isOnline = false
        window.ccLog(`正在检查网络状态:${this.isOnline ? '在线' : '断开'}`)
        this.handleEvent({ type: 'offline' })
    }

    private async handleEvent(event: ConnectionEvent) {
        if (!this.running) return

        switch (event.type) {
            case 'init': //初始状态
                break
            case 'connected': //连接成功状态
                window.ccLog('ws连接成功状态 connected')
                this.isNeedReconnect = false
                break
            case 'reconnected': //重连成功状态
                window.ccLog('ws重连成功状态 reconnected')
                this.isNeedReconnect = false
                window.ccLog(this.isSocketConnected)
                window.ccLog(
                    `%c 重连成功状态`,
                    'background:#ff00ff ; padding: 1px; border-radius: 3px 0 0 3px;  color: #fff',
                    this.index
                )
                try {
                    const res = await this.connectRequest(store.user.isAudience)
                    window.ccLog('-------', res)
                    cat.gui.showToast({ title: ReconnectPrompt.RECONNECTED })
                    cat.gui.hideReconnect()
                    cat.gui.reloadScene()
                } catch (err) {
                    console.error(err)
                    cat.gui.showReconnect(ReconnectPrompt.GAME_ERROR)
                }

                break
            case 'disconnect': //断开连接状态
                window.ccLog('断开连接状态 disconnect', event.ev)
                window.ccLog(
                    `%c 断开连接状态`,
                    'background:#ff00ff ; padding: 1px; border-radius: 3px 0 0 3px;  color: #fff',
                    this.index
                )
                this.isNeedReconnect = true
                this.handleEvent({ type: 'reconnect' })

                // if (this.isReconnecting) { //正在重连则说明此次重连失败
                //     // cat.gui.showReconnect(ReconnectPrompt.RECONNECTED_ERROR);
                //     // cat.gui.showToast({ title: ReconnectPrompt.RECONNECTED_ERROR })
                // } else {

                // }
                break

            case 'paramError': //参数错误状态
                window.ccLog(
                    '参数错误状态 paramError',
                    JSON.stringify(event.ev)
                )
                cat.gui.showReconnect(ReconnectPrompt.CONNECT_PARAM_ERROR)
                this.handleEvent({ type: 'destroy' })
                break
            case 'kick': //被踢出状态
                window.ccLog(
                    `%c 被踢出状态`,
                    'background:#ff00ff ; padding: 1px; border-radius: 3px 0 0 3px;  color: #fff',
                    this.index
                )
                window.ccLog('被踢出状态 kick')
                cat.gui.showReconnect(ReconnectPrompt.KICK)
                this.handleEvent({ type: 'destroy' })
                break
            case 'reconnecting': //正在重连状态
                window.ccLog('正在重连状态 reconnecting')
                if (this.isOnline && !this.isInBackground) {
                    // Logic for attempting reconnection
                    // Call reconnect method or similar logic here
                }
                break
            // case 'reconnectTimeout'://重连倒计时状态
            //     window.ccLog('重连倒计时状态 reconnectTimeout');
            //     cat.gui.showReconnect(ReconnectPrompt.RECONNECTING);
            //     break;
            case 'maxReconnect': //超出最大重连状态
                window.ccLog('超出最大重连状态 maxReconnect')
                cat.gui.showReconnect(ReconnectPrompt.MAX_RECONNECT)
                break
            case 'error': //ws报错状态
                if (this.isOnline) {
                    window.ccLog('ws报错状态 error', event.ev)
                    if (typeof event.ev === 'string') {
                        cat.gui.showToast({ title: event.ev })
                    }
                    cat.gui.showReconnect(ReconnectPrompt.RECONNECTED_ERROR)
                }
                break
            case 'online': //在线状态
                cat.gui.showReconnect(ReconnectPrompt.ONLINE)
                window.ccLog('在线状态 online')
                this.handleEvent({ type: 'reconnect' })
                break
            case 'offline': //离线状态
                // 为了彻底清除上一次的ws(避免有重新连接已发起 但是没完成 导致下次重连时 可能重复重连) 需要将上一次的ws状态清理
                this.disconnect(true)
                window.ccLog('离线状态 offline')
                this.isNeedReconnect = true
                cat.gui.showReconnect(ReconnectPrompt.OFFLINE)
                break
            case 'show': //前台状态
                window.ccLog('前台状态 show')
                this.handleEvent({ type: 'reconnect' })
                break
            case 'hide': //后台状态
                window.ccLog('后台状态 hide')
                // this.disconnect(true)
                // this.isNeedReconnect = true
                break
            case 'reconnect': //重连状态
                console.log('重连状态 reconnect')
                if (
                    this.isNeedReconnect &&
                    !this.isInBackground &&
                    this.isOnline
                ) {
                    this.reset()
                    this.index += 1
                    window.ccLog(
                        `%c ws重连次数`,
                        'background:#ff00ff ; padding: 1px; border-radius: 3px 0 0 3px;  color: #fff',
                        this.index
                    )
                    // 立即重连
                    this.reconnectImmediately()
                }
                break
            case 'destroy': //销毁状态
                window.ccLog('销毁状态 destroy')
                this.destroyStateMachine()
                break
            default:
                window.ccLog('Unknown event:', event.type)
        }
    }

    destroy() {
        this.handleEvent({ type: 'destroy' })
    }

    /**
     * 断开
     * @param activeDisconnect 主动断开
     */
    override disconnect(activeDisconnect: boolean = false) {
        // store.game.is_matching = false
        super.disconnect(activeDisconnect)
    }

    private destroyStateMachine() {
        window.ccLog('Destroying state machine')
        this.running = false
        this.removeEvent()
        this.disconnect(true)
        try {
            // this.handleEvent.return(undefined); // 退出生成器
        } catch (e) {
            window.ccLog('Failed to gracefully terminate the state machine:', e)
        }
    }

    async Request<U extends DescMessage>(
        route: SocketRoute,
        reqReflect: ReflectMessage,
        resSchema: U,
        {
            audOptions = create(AudienceOptionsSchema, {
                forwardReq: false,
                forwardResp: false,
            }),
            show_log = true,
            showToast = true,
        } = {}
    ) {
        window.ccLog('GameNotifyType', this.isSocketConnected)
        if (!this.isSocketConnected) {
            throw `${
                !this.isSocketConnected ? 'Socket未连接' : '未加入战局/观战'
            }`
        }
        if (DEBUG && show_log) {
            window.ccLog(
                `%c ws客户端消息 %c [Request][${new Date()}] %c ${route} %c`,
                'background:#ff00ff ; padding: 1px; border-radius: 3px 0 0 3px;  color: #fff',
                'background:#3d7d3d ; padding: 1px; color: #fff',
                'background:#ff00ff ; padding: 1px; border-radius: 0 3px 3px 0;  color: #fff',
                'background:transparent',
                clone(reqReflect.desc, reqReflect.message)
            )
        }

        return this.GameRequest(
            route,
            reqReflect,
            resSchema,
            audOptions,
            showToast
        )
    }

    protected override async GameRequest<U extends DescMessage>(
        route: SocketRoute,
        reqReflect: ReflectMessage,
        resp: U,
        audOptions?: AudienceOptions,
        showToast: boolean = true
    ): Promise<MessageShape<U>> {
        return new Promise((resolve, reject) => {
            super.GameRequest(route, reqReflect, resp, audOptions).then(
                (res) => {
                    if (DEBUG) {
                        window.ccLog(
                            `%c ws服务端消息 %c [Response][${new Date()}] %c ${route} %c`,
                            'background:#ff00ff ; padding: 1px; border-radius: 3px 0 0 3px;  color: #fff',
                            'background:#3d7daa ; padding: 1px; color: #fff',
                            'background:#ff00ff ; padding: 1px; border-radius: 0 3px 3px 0;  color: #fff',
                            'background:transparent',
                            clone(resp, res)
                        )
                    }
                    resolve(res)
                },
                (err: PitayaError) => {
                    if (showToast) {
                        cat.gui.showToast({ title: err.msg })
                    }
                    console.error(err)
                }
            )
        })
    }

    protected override async gameRequest<U extends DescMessage>(
        route: string,
        reqReflect: ReflectMessage,
        resSchema: U
    ): Promise<MessageShape<U>> {
        if (DEBUG) {
            window.ccLog(
                `%c ws客户端消息 %c [Request][${new Date()}] %c ${route} %c`,
                'background:#ff00ff ; padding: 1px; border-radius: 3px 0 0 3px;  color: #fff',
                'background:#3d7d3d ; padding: 1px; color: #fff',
                'background:#ff00ff ; padding: 1px; border-radius: 0 3px 3px 0;  color: #fff',
                'background:transparent',
                clone(reqReflect.desc, reqReflect.message)
            )
        }
        return super.gameRequest(route, reqReflect, resSchema)
    }

    protected override GameNotify = async (
        route: string,
        reqReflect: ReflectMessage,
        audOptions?: AudienceOptions
    ) => {
        try {
            super.GameNotify(route, reqReflect, audOptions)
        } catch (err) {
            console.error(`Error in connectRequest for route ${route}: `, err)
            throw err
        }
    }

    Notify: GameNotifyType = async (
        route,
        reqReflect,
        {
            audOptions = create(AudienceOptionsSchema, {
                forwardReq: false,
                forwardResp: false,
            }),
            show_log = true,
        } = {}
    ) => {
        if (!this.isSocketConnected) {
            return
        }
        if (DEBUG && show_log) {
            window.ccLog(
                `%c ws客户端消息 %c[Notify][${new Date()}] %c ${route} %c`,
                'background:#ff00ff ; padding: 1px; border-radius: 3px 0 0 3px;  color: #fff',
                'background:#3d7d3d ; padding: 1px; color: #fff',
                'background:#ff00ff ; padding: 1px; border-radius: 0 3px 3px 0;  color: #fff',
                'background:transparent',
                clone(reqReflect.desc, reqReflect.message)
            )
        }
        this.GameNotify(route, reqReflect, audOptions)
    }

    override listen<T extends DescMessage>(
        route: string,
        respType: T,
        fn?: (data: MessageShape<T>) => void
    ): this {
        if (this.routes.has(route)) {
            return this
        }
        this.routes.set(route, (body: Uint8Array) => {
            const data = fromBinary(respType, body)
            // TODO 待优化
            if (
                DEBUG &&
                !['EVENT_OPPONENT_SNAPSHOT', 'WatchBroadcast'].includes(route)
            ) {
                window.ccLog(
                    `%c ws服务端消息:[${new Date()}] %c ${route} %c`,
                    'background:#35495e ; padding: 1px; border-radius: 3px 0 0 3px;  color: #fff',
                    'background:#410083 ; padding: 1px; border-radius: 0 3px 3px 0;  color: #fff',
                    'background:transparent',
                    clone(respType, data)
                )
            } else {
                window.ccLog(`ws服务端消息:[${new Date()}] ${route}`)
            }
            if (cat.event.has(route))
                cat.event.dispatchEvent(route, clone(respType, data))
            fn && fn(clone(respType, data))
        })

        return this
    }

    override onData(body: Uint8Array): void {
        super.onData(body)
    }

    registerService(methods: DescMethod[]) {
        methods.forEach((method) => {
            this.registerMethod(method)
        })
    }

    registerMethod(method: DescMethod) {
        const methodName = method.name
        const localName = method.localName
        if (methodName && localName) {
            Object.defineProperty(this, localName, {
                enumerable: true,
                configurable: true,
                writable: true,
                value: async function (request: any, options?: RequestOptions) {
                    return this.Request(
                        methodName,
                        reflect(method.input, create(method.input, request)),
                        method.output,
                        options
                    )
                },
            })
        }
    }
}
