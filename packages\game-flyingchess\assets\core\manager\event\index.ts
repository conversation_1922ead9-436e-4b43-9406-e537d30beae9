/**
* @describe 事件消息管理
* <AUTHOR>
* @date 2023-08-03 18:15:38
*/

import { Component, error, log, warn } from 'cc';

export type ListenerFunc = (data: any, event: string | number) => void;

export type EventKeyType = string | number

interface EventBase {
    /**注册事件 */
    on(event: string, listener: ListenerFunc, obj: object): void

    /**取消所有事件 */
    offAll(): void

    /**注册单次触发事件 */
    once(event: string, listener: ListenerFunc, obj: object): void

    /**取消单个事件 */
    off(event: string, listener: Function, obj: object): void

    /**调用事件 */
    dispatchEvent<T>(event: string, args: Record<string, T>): void
}

interface IEventData {
    /**监听回调 */
    listener: ListenerFunc;
    /**监听对象 */
    obj: object;
}


export class MessageManager implements EventBase {

    private events: Map<EventKeyType, Map<object, ListenerFunc>> = new Map()
    /**
     * 注册全局事件
     * @param event      事件名
     * @param listener   处理事件的侦听器函数
     * @param obj     侦听函数绑定的作用域对象
     */
    on<T extends EventKeyType>(event: T, listener: ListenerFunc | null, obj: object | Symbol) {
        if (!event || !listener) {
            warn(`注册【${event}】事件的侦听器函数为空`);
            return this;
        }

        // 获取事件
        const _event = this.events.get(event) ?? this.events.set(event, new Map()).get(event)!

        if (_event.has(obj)) {
            warn(`名为【${event}】的事件重复注册侦听器`);
            return this
        }

        _event.set(obj, listener)

        return this

    }

    /**
     * 监听一次事件，事件响应后，该监听自动移除
     * @param event     事件名
     * @param listener  事件触发回调方法
     * @param obj    侦听函数绑定的作用域对象
     */
    once<T extends EventKeyType>(event: T, listener: ListenerFunc, obj: object | Symbol) {
        let _listener: ListenerFunc | null = ($event: string, $args: any) => {
            this.off(event, _listener, obj);
            _listener = null
            listener.call(obj, $event, $args);
        };
        this.on(event, _listener, obj);
    }

    /**
     * 移除单个事件
     * @param event     事件名
     * @param listener  处理事件的侦听器函数
     * @param obj    侦听函数绑定的作用域对象
     */
    off<T extends EventKeyType>(event: T, listener: Function | null, obj: object) {

        if (!this.events.get(event)?.has(obj)) {
            // warn(`${event}事件未注册`)
            return this
        }
        const _object = this.events.get(event)!

        if (_object.get(obj) !== listener) {
            error(`${obj}注册事件和取消事件不一致`)
            return this
        }
        _object.delete(obj)
        return this
    }

    /**
     * 触发全局事件
     * @param event(string)      事件名
     * @param args(any)          事件参数
     */
    dispatchEvent<T extends EventKeyType>(event: T, args?: any) {
        let _event = this.events.has(event)
        if (!_event) {
            // warn(`${event}事件未注册`)
            return this
        }

        const _listeners = this.events.get(event)!

        for (const [key, value] of _listeners) {
            value.call(key, args, event);
        }

        return this


    }

    /**移除所有事件 */
    offAll() {
        this.events.clear()
    }

    /**是否存在事件 */
    has(event: EventKeyType) {
        return this.events.has(event)
    }

    /**删除事件通过组件 */
    deleteEventByComponent(component: Component) {
        for (const [event, objects] of this.events) {
            for (const [obj] of objects) {
                if (obj === component) {
                    objects.delete(obj)
                }
            }
            if (objects.size === 0) {
                this.events.delete(event)
            }
        }
    }

    /**获取事件 */
    getEvents(event: EventKeyType) {
        return this.events.get(event)
    }

}
