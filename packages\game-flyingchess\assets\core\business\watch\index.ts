import {
    WatchBroadcastMessage,
    WatchBroadcastType,
    PlayerSchema as BasePlayerSchema,
    BattleInitialDataSchema,
    WatchBroadcastMessageSchema,
    Event as SGCEvent,
} from 'sgc'
import store from '../store'
import {
    businessEventResponsePairs,
    SocketEvent,
    SocketRoute,
    BsseSocketEvent,
    BusinessSocketEvent,
    ASYNCSocketRoute,
} from '../ws'
import { error, log } from 'cc'
import {
    DescMessage,
    fromBinary,
    Message,
    MessageShape,
} from '@bufbuild/protobuf'

import { cat, Manager } from '@/core/manager'
import { BaseManager } from '@/core/manager/BaseManager'

// 事件响应类型
type EventResponsePair<T extends SocketEvent> = {
    event: T
    responseType: DescMessage
    cb?: () => void
}

const baseEventResponsePairs: EventResponsePair<BsseSocketEvent>[] = [
    // 战局初始化- 广播，协议名称：BattleInitialize，返回参数：EmptyResponse
    { event: 'BattleInitialize', responseType: BattleInitialDataSchema },

    // 玩家状态变更推送- 广播，协议名称：PlayerStatusChanged，返回参数：EmptyResponse
    { event: 'PlayerStatusChanged', responseType: BasePlayerSchema },

    // 观战 - 广播，协议名称：WatchBroadcast，返回参数：WatchBroadcastMessage
    { event: 'WatchBroadcast', responseType: WatchBroadcastMessageSchema },
]

const allEventResponsePairs: EventResponsePair<
    BsseSocketEvent | BusinessSocketEvent | ASYNCSocketRoute
>[] = [...baseEventResponsePairs, ...businessEventResponsePairs]

// 定义 RouteToClassMap 对象，映射每个事件响应类型到对应的类构造函数
const RouteToClassMap: Record<
    BsseSocketEvent | BusinessSocketEvent | ASYNCSocketRoute,
    DescMessage | undefined
> = allEventResponsePairs.reduce((map, pair) => {
    map[pair.event] = pair.responseType
    return map
}, {} as Record<BsseSocketEvent | BusinessSocketEvent | ASYNCSocketRoute, DescMessage | undefined>)

const filter: (SocketEvent | SocketRoute)[] = ['DataBroadcast']

// 定义一个类型，根据传入的 route 获取对应的类构造函数类型，并调用其 fromBinary 方法的返回类型

/**观战系统 */
export class WatchSystem extends BaseManager {
    constructor(cat: Manager) {
        super(cat)

        this.init()
    }

    init() {
        this.cat.event.on<SocketEvent>(
            'WatchBroadcast',
            this.onWatchBroadcastHandler,
            this
        )
    }

    private onWatchBroadcastHandler(e: WatchBroadcastMessage) {
        const { type, offset, data, request, response } = e
        const { user } = store
        // 根据不同的offset处理响应的玩家
        const route = e.route as SocketEvent
        window.ccLog(
            `%c 观战路由 %c[${new Date()}] %c ${route} %c`,
            'background:#ff00ff ; padding: 1px; border-radius: 3px 0 0 3px;  color: #fff',
            'background:#3d7d3d ; padding: 1px; color: #fff',
            'background:#ff00ff ; padding: 1px; border-radius: 0 3px 3px 0;  color: #fff',
            'background:transparent',
            e
        )
        // window.ccLog(`观战路由:${route}   当前用户玩家索引:${user.userIndex}`, e)
        // try {

        let nonce: Message | WatchBroadcastMessage | null = null
        if (!RouteToClassMap[route]) {
            if (!filter.includes(route)) {
                error(`未处理路由:${route}`)
            }
            return
        }
        // 私人数据
        if (
            [
                WatchBroadcastType.PRIVATE,
                WatchBroadcastType.PLAYER_REQUEST,
            ].includes(type)
        ) {
            if (offset === user.userIndex) {
                nonce =
                    type === WatchBroadcastType.PLAYER_REQUEST
                        ? e
                        : fromBinary(RouteToClassMap[route], data)
            } else {
                // 非当前观战视角的玩家数据
            }
        } else if (filter.includes(route) && user.userIndex !== offset) {
            // 过滤拦截
        } else {
            nonce = fromBinary(RouteToClassMap[route], data)
        }

        if (nonce) cat.event.dispatchEvent(route, nonce)
        // } catch (err) {
        //     error('error route', route)
        // }
    }
}
