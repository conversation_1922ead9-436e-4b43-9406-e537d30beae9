import {
    _decorator,
    Component,
    Node,
    CameraComponent,
    view,
    game,
    Game,
    log,
} from 'cc'
import { cat } from '@/core/manager'
const { ccclass, property, requireComponent } = _decorator

export const UPDATE_FOV = 'update-fov'

@ccclass
@requireComponent(CameraComponent)
export class FitWidthCamera extends Component {
    private _camera!: CameraComponent
    private _defaultTanHalfFov!: number

    override onLoad() {
        this._camera = this.getComponent(CameraComponent)!
        this._defaultTanHalfFov = Math.tan(
            ((this._camera.fov * 0.5) / 180) * Math.PI
        )
        this.updateFov()
        view.on('canvas-resize', this.updateFov) //监听游戏尺寸变化
    }

    protected override onDestroy(): void {
        view.off('canvas-resize', this.updateFov)
    }

    updateFov = () => {
        // this.scheduleOnce(() => {
        let tanHalfFov2 =
            (view.getVisibleSize().height /
                view.getDesignResolutionSize().height) *
            this._defaultTanHalfFov
        window.ccLog('--------tanHalfFov2', tanHalfFov2)
        this._camera.fov = (Math.atan(tanHalfFov2) / Math.PI) * 180 * 2
        window.ccLog(
            'canvas-resize',
            view.getVisibleSize().height,
            view.getDesignResolutionSize().height,
            this._camera.fov
        )

        cat.event.dispatchEvent(UPDATE_FOV)
        // }, 2)
    }
}
