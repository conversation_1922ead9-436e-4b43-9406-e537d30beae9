import { cat } from '@/core/manager'
import UILayer from '@/core/manager/gui/layer/UILayer'
import {
    _decorator,
    v3,
    Node,
    Prefab,
    instantiate,
    Label,
    Button,
    tween,
    Color,
    UITransform,
    Vec3,
    UIOpacity,
    sp,
} from 'cc'
import store from '@/core/business/store'
import { CardItem, cardSizeMap } from '../../components/card/CardItem'
import { CommonDialog } from '../CommonDialog/CommonDialog'
import { GameInfoBroadcast_RoundInfo_SelectedCard } from '@/pb-generate/server/dixit/v1/game_pb'
import { sleep } from '@/core/business/util/TimeUtils'
import { PlayerItem } from '../../components/player/PlayerItem'
import { AudioEffectConstant } from '@/core/business/constant'

const { ccclass, property } = _decorator

// 唱票环节的子环节
enum CountingPhase {
    PHASE_1 = 1,
    PHASE_2 = 2,
    PHASE_3 = 3,
    PHASE_4 = 4,
    PHASE_5 = 5,
    PHASE_6 = 6,
    PHASE_7 = 7,
    COMPLETED = 8,
}

export interface CountingVoteDialogProps {}

@ccclass('CountingVoteDialog')
export class CountingVoteDialog extends CommonDialog<CountingVoteDialogProps> {
    @property({ type: Node, tooltip: '卡牌容器' })
    card_container: Node = null!

    @property({ type: sp.Skeleton, tooltip: '魔法棒动画节点' })
    magicwand_spine: sp.Skeleton = null!

    @property({ type: sp.Skeleton, tooltip: '翻牌动画节点' })
    flipcard_spine: sp.Skeleton = null!

    private cardItems: CardItem[] = []
    private currentPhase: CountingPhase = CountingPhase.PHASE_1
    private isAnimating: boolean = false
    private phaseInterval: number = 1000 // 每个子环节持续1秒
    // 当前是否为两行布局
    private _isRowLayout: boolean = true
    // 是否停止环节流转
    private isPhaseFlowStopped: boolean = false
    // 玩家位置数组，左右两列分布在屏幕两边，支持4-6人
    private playerPositions: Vec3[] = []
    // 存储分数节点，用于后续添加动效
    private scoreNodes: Map<number, Node[]> = new Map()
    // 计分板位置，用于环节7中所有分数飞向的目标位置
    private scoreBoardPosition: Vec3 = v3(-420, 480, 0)

    /**
     * 分数标签样式配置
     */
    private readonly SCORE_LABEL_STYLE = {
        fontSize: 52,
        lineHeight: 52,
        isBold: true,
        masterColor: new Color(255, 234, 0, 255), // 黄色 #ffea00 - 说书人卡牌
        nonMasterColor: new Color(14, 255, 224, 255), // 蓝色 #0effe0 - 非说书人卡牌
        outlineColor: new Color(43, 32, 9, 255), // 描边颜色 #2b2009
        outlineWidth: 5,
    }

    /**
     * 应用分数标签样式
     * @param label 标签组件
     * @param score 分数
     * @param isMasterCardScore 是否是说书人卡牌的分数
     */
    private applyScoreLabelStyle(
        label: Label,
        score: number,
        isMasterCardScore: boolean
    ): void {
        label.string = `+${score}`
        label.fontSize = this.SCORE_LABEL_STYLE.fontSize
        label.lineHeight = this.SCORE_LABEL_STYLE.lineHeight
        label.isBold = this.SCORE_LABEL_STYLE.isBold
        label.color = isMasterCardScore
            ? this.SCORE_LABEL_STYLE.masterColor
            : this.SCORE_LABEL_STYLE.nonMasterColor

        // 添加描边
        label.enableOutline = true
        label.outlineColor = this.SCORE_LABEL_STYLE.outlineColor
        label.outlineWidth = this.SCORE_LABEL_STYLE.outlineWidth
    }

    // 获取当前布局状态
    private get isRowLayout(): boolean {
        return this._isRowLayout
    }

    // 设置当前布局状态
    private set isRowLayout(value: boolean) {
        this._isRowLayout = value
        // 当布局状态变化时可以执行其他逻辑
        console.log(
            `[CountingVoteDialog] 布局状态变化为: ${
                value ? '两行布局' : '两列布局'
            }`
        )
    }

    override props: CountingVoteDialogProps = {}

    protected override initUI(): void {
        super.initUI()

        // 初始化玩家位置
        this.initPlayerPositions()

        // 初始化卡牌
        this.initCards()

        // 初始化为两行布局
        this.arrangeCardsInRowLayout()

        // 开始唱票环节
        this.startCountingPhase()
    }

    // 初始化玩家位置
    private initPlayerPositions(): void {
        // 左右两列分布在屏幕两边
        // 屏幕宽度为1080，高度为1920
        const leftX = -440 // 左侧列X坐标
        const rightX = 440 // 右侧列X坐标
        const topY = 280 // 顶部Y坐标
        const middleY = -60 // 中部Y坐标
        const bottomY = -400 // 底部Y坐标

        // 初始化6个玩家位置
        this.playerPositions = [
            // 左列三个位置，从上到下
            v3(leftX, topY, 0), // 左上 - 玩家0
            v3(leftX, middleY, 0), // 左中 - 玩家1
            v3(leftX, bottomY, 0), // 左下 - 玩家2
            // 右列三个位置，从上到下
            v3(rightX, topY, 0), // 右上 - 玩家3
            v3(rightX, middleY, 0), // 右中 - 玩家4
            v3(rightX, bottomY, 0), // 右下 - 玩家5
        ]

        console.log(
            '[CountingVoteDialog] 初始化玩家位置，数量:',
            this.playerPositions.length
        )

        // // 创建调试标签，显示玩家索引
        // this.createDebugLabels()
    }

    /**
     * 创建调试标签，在每个玩家位置显示索引数字
     */
    private createDebugLabels(): void {
        console.log('[CountingVoteDialog] 创建玩家位置调试标签')

        // 遍历所有玩家位置
        this.playerPositions.forEach((position, index) => {
            // 创建标签节点
            const debugLabel = new Node(`DebugLabel_${index}`)
            debugLabel.setParent(this.node)
            debugLabel.setPosition(position)

            // 添加UI变换组件
            const transform = debugLabel.addComponent(UITransform)
            transform.setContentSize(80, 80)

            // 添加标签组件
            const label = debugLabel.addComponent(Label)
            label.string = `${index}`
            label.fontSize = 60
            label.lineHeight = 60

            // 设置颜色，左右两侧使用不同颜色
            if (index < 3) {
                // 左侧玩家使用红色
                label.color = new Color(255, 100, 100, 255)
            } else {
                // 右侧玩家使用蓝色
                label.color = new Color(100, 100, 255, 255)
            }

            // 添加背景圆圈
            const bgNode = new Node(`DebugLabelBg_${index}`)
            bgNode.setParent(debugLabel)
            bgNode.setPosition(v3(0, 0, 0))

            const bgTransform = bgNode.addComponent(UITransform)
            bgTransform.setContentSize(80, 80)

            // 设置背景圆圈的颜色
            const bgColor =
                index < 3
                    ? new Color(255, 200, 200, 150)
                    : new Color(200, 200, 255, 150)

            // 添加背景组件并设置颜色
            const sprite = bgNode.addComponent(UIOpacity)
            sprite.opacity = 150

            // 添加动画效果，使标签更明显
            tween(debugLabel)
                .to(0.5, { scale: v3(1.2, 1.2, 1.2) })
                .to(0.5, { scale: v3(1.0, 1.0, 1.0) })
                .union()
                .repeatForever()
                .start()

            console.log(
                `[CountingVoteDialog] 创建玩家${index}的调试标签，位置:`,
                position
            )
        })
    }

    private initCards(): void {
        const selectedCards = store.game.selectedCards
        console.log(
            '[CountingVoteDialog] 初始化卡牌，数量:',
            selectedCards.length
        )

        selectedCards.forEach((card, i) => {
            const cardNode = cat.cardPool.get()
            const cardItem = cardNode.getComponent(CardItem)

            if (cardItem) {
                cardItem.setOptions({
                    props: {
                        cardId: card.card!,
                        isSwitch: false,
                        isSelected: false,
                        selectedCardData: card,
                    },
                })
                this.cardItems.push(cardItem)
                console.log(
                    `[CountingVoteDialog] 创建卡牌 ${i}，cardId:`,
                    card.card
                )

                cardNode.setParent(this.card_container)

                // 设置卡牌大小为唱票尺寸
                cardNode.setScale(cardSizeMap.XS)

                // 设置卡牌位置
                // 初始化时位置会在arrangeCardsInRowLayout中设置
            }
        })
    }

    // 开始唱票环节
    private async startCountingPhase() {
        this.currentPhase = CountingPhase.PHASE_1
        await this.executePhase1()
        this.goToNextPhase()
    }

    // 进入下一环节
    private async goToNextPhase() {
        // 如果环节流转已停止或已完成，则不再继续
        if (
            this.isPhaseFlowStopped ||
            this.currentPhase >= CountingPhase.COMPLETED
        ) {
            return
        }

        this.currentPhase++
        console.log(`[CountingVoteDialog] 进入环节 ${this.currentPhase}/7`)

        // 执行当前环节的逻辑
        switch (this.currentPhase) {
            case CountingPhase.PHASE_2:
                await this.executePhase2()
                break
            case CountingPhase.PHASE_3:
                await this.executePhase3()
                break
            case CountingPhase.PHASE_4:
                await this.executePhase4()
                break
            case CountingPhase.PHASE_5:
                await this.executePhase5()
                break
            case CountingPhase.PHASE_6:
                await this.executePhase6()
                break
            case CountingPhase.PHASE_7:
                await this.executePhase7()
                break
            case CountingPhase.COMPLETED:
                this.completeCountingPhase()
                return
        }
        if (
            store.game.playerList.every(
                (player) =>
                    player.index ===
                        store.game.roomData.roundInfo?.currentPlayerCursor ||
                    !player.voted
            ) &&
            this.currentPhase === CountingPhase.PHASE_4
        ) {
            cat.gui.showToast({ title: '本轮无人投票，无人得分' })
            return
        }

        // 再次检查是否停止环节流转
        if (!this.isPhaseFlowStopped) {
            this.goToNextPhase()
        }
    }

    // 环节1逻辑
    private async executePhase1() {
        console.log('[CountingVoteDialog] 执行环节1')
        // 这里实现环节1的具体逻辑
        // 等待
        // await sleep(this.phaseInterval)
    }

    // 环节2逻辑
    private async executePhase2() {
        console.log('[CountingVoteDialog] 执行环节2')
        // 这里实现环节2的具体逻辑
        // 展示所有玩家投票的票型

        // 为每张卡牌显示投票图章
        const showVoteTagPromises: Promise<void>[] = []

        // 依次显示每张卡牌的投票图章
        for (let i = 0; i < this.cardItems.length; i++) {
            const cardItem = this.cardItems[i]
            const promise = new Promise<void>((resolve) => {
                // 每张卡牌的显示有一定的延迟，以创造逐个显示的效果
                setTimeout(() => {
                    // 设置卡牌显示投票图章
                    const currentProps = cardItem.props
                    cardItem.setUpdateProps({
                        ...currentProps,
                        showVoteLabel: true,
                    })

                    // 打印投票信息
                    const selectedCardData = cardItem.props?.selectedCardData
                    if (
                        selectedCardData &&
                        selectedCardData.votedPlayerIndices
                    ) {
                        console.log(
                            `[CountingVoteDialog] 卡牌 ${i} 收到投票:`,
                            selectedCardData.votedPlayerIndices
                        )
                    }

                    // 等待一定时间后解决Promise
                    setTimeout(() => resolve(), 300) // 等待图章动画完成
                }, i * 200) // 每张卡牌间隔200ms显示
            })

            showVoteTagPromises.push(promise)
        }

        // 等待所有卡牌的投票图章显示完成
        await Promise.all(showVoteTagPromises)

        // 等待一段时间再进入下一环节
        // await sleep(this.phaseInterval)
    }

    // 环节3逻辑
    private async executePhase3() {
        console.log('[CountingVoteDialog] 执行环节3')
        // 这里实现环节3的具体逻辑

        // 播放魔法棒动效，揭示说书人的卡牌

        // 1. 找出说书人的卡牌
        const masterCard = this.cardItems.find((cardItem) => {
            const selectedCardData = cardItem.props?.selectedCardData
            return selectedCardData && selectedCardData.isMasterCard
        })

        if (!masterCard) {
            console.error('[CountingVoteDialog] 未找到说书人卡牌')
            await sleep(this.phaseInterval)
            return
        }

        console.log(
            '[CountingVoteDialog] 找到说书人卡牌:',
            masterCard.props.selectedCardData
        )

        // 2. 播放魔法棒动效
        await this.playMagicWandAnimation(masterCard)

        // 显示所有卡牌的出牌者标签
        this.showCardsPlayerTag()

        await sleep(this.phaseInterval)
    }

    /**
     * 播放魔法棒动效
     * @param masterCard 说书人卡牌
     */
    private async playMagicWandAnimation(masterCard: CardItem): Promise<void> {
        console.log('[CountingVoteDialog] 播放魔法棒动效')

        // 确保魔法棒节点存在
        if (!this.magicwand_spine || !this.magicwand_spine.isValid) {
            console.error('[CountingVoteDialog] 魔法棒节点不存在或无效')
            return
        }

        // 1. 激活魔法棒节点
        this.magicwand_spine.node.active = true

        // 2. 设置魔法棒初始位置（屏幕中央偏上）
        const initialPosition = v3(0, 200, 0)
        this.magicwand_spine.node.setPosition(initialPosition)

        // 3. 先播放admission动效
        return new Promise<void>((resolve) => {
            // 设置动画完成回调
            this.magicwand_spine.setCompleteListener(() => {
                // admission动效播放完成后，移动到说书人卡牌位置并播放idle动效
                this.playMagicWandIdleAnimation(masterCard, resolve)
            })

            // 播放admission动效
            this.magicwand_spine.setAnimation(0, 'admission', false)
            console.log('[CountingVoteDialog] 开始播放魔法棒admission动效')
        })
    }

    /**
     * 播放魔法棒idle动效和翻牌动效
     * @param masterCard 说书人卡牌
     * @param callback 动画完成后的回调函数
     */
    private async playMagicWandIdleAnimation(
        masterCard: CardItem,
        callback: () => void
    ): Promise<void> {
        // 获取说书人卡牌的世界坐标
        const cardWorldPos = masterCard.node.getWorldPosition()
        // 转换为本地坐标
        const localPos = this.node
            .getComponent(UITransform)!
            .convertToNodeSpaceAR(cardWorldPos)

        // 设置魔法棒和翻牌动画节点位置为说书人卡牌位置
        this.magicwand_spine.node.setPosition(localPos)

        // 设置魔法棒动画完成回调
        this.magicwand_spine.setCompleteListener(() => {
            // idle动效播放完成后隐藏魔法棒
            this.magicwand_spine.node.active = false
            console.log('[CountingVoteDialog] 魔法棒动效播放完成')

            // 标记魔法棒动画已完成
            this.magicWandAnimationCompleted = true
            // 检查翻牌动画是否也完成
            this.checkAnimationsComplete(callback)
        })

        // 播放魔法棒idle动效
        this.magicwand_spine.setAnimation(0, 'idle', false)

        await sleep(500)

        // 激活翻牌动画节点
        this.flipcard_spine.node.active = true
        this.flipcard_spine.node.setPosition(localPos)

        // 设置翻牌动画完成回调
        this.flipcard_spine.setCompleteListener(() => {
            // idle动效播放完成后隐藏翻牌动画
            this.flipcard_spine.node.active = false
            console.log('[CountingVoteDialog] 翻牌动效播放完成')

            // 标记翻牌动画已完成
            this.flipcardAnimationCompleted = true
            // 检查魔法棒动画是否也完成
            this.checkAnimationsComplete(callback)
        })
        this.flipcard_spine.setAnimation(0, 'idle', false)
        console.log('[CountingVoteDialog] 开始播放魔法棒和翻牌idle动效')
    }

    // 动画完成状态
    private magicWandAnimationCompleted: boolean = false
    private flipcardAnimationCompleted: boolean = false

    /**
     * 检查两个动画是否都完成，如果都完成则执行回调
     * @param callback 动画完成后的回调函数
     */
    private checkAnimationsComplete(callback: () => void): void {
        if (
            this.magicWandAnimationCompleted &&
            this.flipcardAnimationCompleted
        ) {
            // 重置状态
            this.magicWandAnimationCompleted = false
            this.flipcardAnimationCompleted = false
            // 执行回调
            callback()
        }
    }

    /**
     * 显示所有卡牌的出牌者标签
     * 依次设置每张卡片的showPlayerTag属性为true
     */
    private async showCardsPlayerTag() {
        console.log('[CountingVoteDialog] 显示所有卡牌的出牌者标签')

        // 创建一个数组来存储所有的Promise
        const showTagPromises: Promise<void>[] = []

        // 依次显示每张卡牌的玩家标签
        for (let i = 0; i < this.cardItems.length; i++) {
            const cardItem = this.cardItems[i]
            const promise = new Promise<void>((resolve) => {
                // 每张卡牌的显示有一定的延迟，以创造逐个显示的效果
                setTimeout(() => {
                    // 设置卡牌显示玩家标签
                    const currentProps = cardItem.props
                    cardItem.setUpdateProps({
                        ...currentProps,
                        showPlayerTag: true,
                    })

                    // 打印出牌者信息
                    const selectedCardData = cardItem.props?.selectedCardData
                    if (
                        selectedCardData &&
                        selectedCardData.selectedPlayerIndex !== undefined
                    ) {
                        const player = store.game.getPlayerByIndex(
                            selectedCardData.selectedPlayerIndex
                        )
                        console.log(
                            `[CountingVoteDialog] 卡牌 ${i} 的出牌者:`,
                            player
                                ? `玩家${player.index}(昵称: ${player.nickname})`
                                : `未知玩家${selectedCardData.selectedPlayerIndex}`
                        )
                    }

                    // 等待一定时间后解决Promise
                    setTimeout(() => resolve(), 300) // 等待标签动画完成
                }, i * 200) // 每张卡牌间隔200ms显示
            })

            showTagPromises.push(promise)
        }

        // 等待所有卡牌的玩家标签显示完成
        await Promise.all(showTagPromises)

        // 等待一段时间再进入下一环节
        await sleep(this.phaseInterval)
    }

    // 环节4逻辑
    private async executePhase4() {
        console.log('[CountingVoteDialog] 执行环节4')
        // 环节3到环节4时，从两行布局切换到两列布局(所有卡牌对应到其所有人头像旁边)
        if (this.isRowLayout) {
            await this.switchToColumnLayout()
        }

        // await sleep(this.phaseInterval)
    }

    // 环节5逻辑
    private async executePhase5() {
        console.log('[CountingVoteDialog] 执行环节5')

        // 这里实现环节5的具体逻辑
        // 计算针对说书人票型的分数，转移到指定玩家头像上

        // 1. 找出说书人的卡牌
        const masterCard = this.cardItems.find((cardItem) => {
            const selectedCardData = cardItem.props?.selectedCardData
            return selectedCardData && selectedCardData.isMasterCard
        })

        if (!masterCard) {
            console.error('[CountingVoteDialog] 未找到说书人卡牌')
            await sleep(this.phaseInterval)
            return
        }

        console.log(
            '[CountingVoteDialog] 找到说书人卡牌:',
            masterCard.props.selectedCardData
        )

        // 2. 获取说书人卡牌的分数和得分玩家列表
        const selectedCardData = masterCard.props.selectedCardData
        if (!selectedCardData) {
            console.log('[CountingVoteDialog] 说书人卡牌数据不存在')
            await sleep(this.phaseInterval)
            return
        }

        const score = selectedCardData.score || 0
        const scoredPlayerIndices = selectedCardData.scoredPlayerIndices || []

        console.log(
            `[CountingVoteDialog] 说书人卡牌分数: ${score}, 得分玩家数量: ${scoredPlayerIndices.length}`
        )
        this.playVoteAudioEffect(scoredPlayerIndices)

        if (score <= 0 || scoredPlayerIndices.length === 0) {
            console.log('[CountingVoteDialog] 没有分数或得分玩家')
            cat.gui.showToast({ title: '第一轮计分无人得分' })
            await sleep(2500)
            return
        }

        // 3. 使用封装的方法创建分数动画
        await this.createScoreAnimations(
            masterCard,
            scoredPlayerIndices,
            score,
            true // 说书人卡牌分数
        )

        // 打印存储的分数节点信息
        console.log(
            '[CountingVoteDialog] 存储的分数节点:',
            Array.from(this.scoreNodes.entries())
                .map(
                    ([playerIndex, nodes]) =>
                        `玩家${playerIndex}: ${nodes.length}个节点`
                )
                .join(', ')
        )

        await sleep(this.phaseInterval)
    }

    // 环节6逻辑
    private async executePhase6() {
        console.log('[CountingVoteDialog] 执行环节6')
        // 这里实现环节6的具体逻辑
        // 计算非说书人票型的分数，转移到指定玩家头像上

        // 1. 遍历所有卡牌，找到非说书人且有得分的卡牌
        const nonMasterScoredCards = this.cardItems.filter((cardItem) => {
            const selectedCardData = cardItem.props?.selectedCardData
            return (
                selectedCardData &&
                !selectedCardData.isMasterCard &&
                selectedCardData.score &&
                selectedCardData.score > 0 &&
                selectedCardData.scoredPlayerIndices &&
                selectedCardData.scoredPlayerIndices.length > 0
            )
        })

        console.log(
            `[CountingVoteDialog] 找到${nonMasterScoredCards.length}张非说书人且有得分的卡牌`
        )

        // 获取当前用户的索引
        const userIndex = store.user.userIndex

        // 找到用户自己打出的卡牌
        const userCard = nonMasterScoredCards.find((cardItem) => {
            const selectedCardData = cardItem.props?.selectedCardData
            return (
                selectedCardData &&
                selectedCardData.selectedPlayerIndex === userIndex
            )
        })

        // 如果找到了用户自己的卡牌，播放投票音效
        if (userCard) {
            const selectedCardData = userCard.props.selectedCardData!
            const scoredPlayerIndices =
                selectedCardData.scoredPlayerIndices || []

            console.log(
                `[CountingVoteDialog] 找到用户自己的卡牌，播放投票音效，得分玩家: ${scoredPlayerIndices.join(
                    ','
                )}`
            )

            this.playVoteAudioEffect(scoredPlayerIndices)
        } else {
            this.playVoteAudioEffect([])
        }

        if (nonMasterScoredCards.length === 0) {
            console.log('[CountingVoteDialog] 没有非说书人卡牌有得分')
            cat.gui.showToast({ title: '第二轮计分无人得分' })
            await sleep(2500)
            return
        }

        // 2. 为每张非说书人卡牌创建分数动画
        // 创建所有卡牌分数动画的Promise数组
        const cardScoreAnimationPromises: Promise<Node[]>[] = []

        for (const cardItem of nonMasterScoredCards) {
            const selectedCardData = cardItem.props?.selectedCardData!
            const score = selectedCardData.score || 0
            const scoredPlayerIndices =
                selectedCardData.scoredPlayerIndices || []

            console.log(
                `[CountingVoteDialog] 非说书人卡牌分数: ${score}, 得分玩家数量: ${scoredPlayerIndices.length}`
            )

            // 3. 使用封装的方法创建分数动画，不使用await，而是收集Promise
            const animationPromise = this.createScoreAnimations(
                cardItem,
                scoredPlayerIndices,
                score,
                false // 非说书人卡牌分数
            )

            cardScoreAnimationPromises.push(animationPromise)
        }

        // 等待所有卡牌的分数动画完成
        await Promise.all(cardScoreAnimationPromises)

        console.log('[CountingVoteDialog] 环节6完成，分数节点动效已添加')
        await sleep(this.phaseInterval)
    }

    // 环节7逻辑
    private async executePhase7() {
        console.log('[CountingVoteDialog] 执行环节7')
        // 这里实现环节7的具体逻辑
        // 所有分数移到计分板中，消失

        // 检查是否有分数节点
        if (this.scoreNodes.size === 0) {
            console.log('[CountingVoteDialog] 没有分数节点需要移动')
            cat.gui.showToast({ title: '本轮无人得分' })
            await sleep(2500)
            return
        }
        cat.audio.playEffect(AudioEffectConstant.SCORING)

        console.log('[CountingVoteDialog] 开始移动所有分数节点到计分板')

        // 创建一个数组来存储所有的Promise
        const animationPromises: Promise<void>[] = []

        // 遍历所有玩家的分数节点
        this.scoreNodes.forEach((nodes, playerIndex) => {
            console.log(
                `[CountingVoteDialog] 移动玩家 ${playerIndex} 的 ${nodes.length} 个分数节点`
            )

            // 遍历该玩家的所有分数节点
            nodes.forEach((scoreNode) => {
                // 创建动画的Promise
                const promise = new Promise<void>((resolve) => {
                    // 添加少量随机延迟，使得分数节点不会同时移动
                    const delay = Math.random() * 0.2

                    // 确保节点有UIOpacity组件
                    let uiOpacity = scoreNode.getComponent(UIOpacity)
                    if (!uiOpacity) {
                        uiOpacity = scoreNode.addComponent(UIOpacity)
                    }

                    // 创建位置和缩放的动画
                    const positionTween = tween(scoreNode)
                        .delay(delay)
                        .to(
                            0.8,
                            {
                                position: this.scoreBoardPosition,
                                scale: v3(0.5, 0.5, 0.5),
                            },
                            {
                                easing: 'cubicOut', // 使用缓动函数使动画更自然
                            }
                        )
                        .call(() => {
                            // 动画完成后移除节点
                            if (scoreNode.isValid) {
                                scoreNode.removeFromParent()
                                scoreNode.destroy()
                            }
                            resolve()
                        })

                    // 创建透明度的动画
                    const opacityTween = tween(uiOpacity)
                        .delay(delay)
                        .to(0.8, { opacity: 0 })

                    // 同时启动两个动画
                    positionTween.start()
                    opacityTween.start()
                })

                animationPromises.push(promise)
            })
        })

        // 等待所有动画完成
        await Promise.all(animationPromises)

        // 清空分数节点集合
        this.scoreNodes.clear()
        console.log('[CountingVoteDialog] 所有分数节点已移动到计分板并消失')

        await sleep(this.phaseInterval)
    }

    // 完成唱票环节
    private completeCountingPhase() {
        console.log('[CountingVoteDialog] 唱票环节完成')
        // 完成后关闭对话框
        this.close()
    }

    // 将卡牌排列为两行布局
    private arrangeCardsInRowLayout() {
        if (this.isAnimating) return
        this.isAnimating = true

        const totalCards = this.cardItems.length
        let cardsPerRow = 3

        // 根据卡牌数量确定每行卡牌数
        if (totalCards <= 4) {
            cardsPerRow = 2
        }

        const rows = Math.ceil(totalCards / cardsPerRow)
        const lastRowCards = totalCards % cardsPerRow || cardsPerRow
        const cardWidth = 220
        const cardHeight = 360

        // 为每张卡牌设置位置
        this.cardItems.forEach((cardItem, index) => {
            const row = Math.floor(index / cardsPerRow)
            const isLastRow = row === rows - 1
            const rowCards = isLastRow ? lastRowCards : cardsPerRow
            const col = index % cardsPerRow
            let targetX

            // 计算卡牌的X坐标，使其水平居中
            if (rowCards < cardsPerRow) {
                // 最后一行卡牌数量少于每行最大数量时，居中显示
                targetX = col * cardWidth - ((rowCards - 1) * cardWidth) / 2
            } else {
                targetX = col * cardWidth - ((cardsPerRow - 1) * cardWidth) / 2
            }

            const targetY = 150 - row * cardHeight
            const targetPos = v3(targetX, targetY, 0)

            // 设置卡牌位置
            cardItem.node.setPosition(targetPos)
        })

        this.isRowLayout = true
        this.isAnimating = false
    }

    // 将卡牌从两行布局切换到两列布局
    private async switchToColumnLayout() {
        if (this.isAnimating) return
        this.isAnimating = true

        const totalCards = this.cardItems.length
        const leftX = -220
        const rightX = 220
        const verticalSpacing = 388
        const verticalOffset = -160 // 整体向下移动120像素

        // 固定初始化六个坑位的位置
        const slotPositions = [
            // 左列三个坑位，从上到下
            v3(leftX, verticalSpacing + verticalOffset, 0), // 左上
            v3(leftX, 0 + verticalOffset, 0), // 左中
            v3(leftX, -verticalSpacing + verticalOffset, 0), // 左下
            // 右列三个坑位，从上到下
            v3(rightX, verticalSpacing + verticalOffset, 0), // 右上
            v3(rightX, 0 + verticalOffset, 0), // 右中
            v3(rightX, -verticalSpacing + verticalOffset, 0), // 右下
        ]

        const animationPromises: Promise<void>[] = []

        // 根据卡牌数量，将卡牌按顺序放入坑位
        this.cardItems.forEach((cardItem) => {
            const position = cardItem.props?.selectedCardData?.playerPosition!
            // 确保索引不超过坑位数量
            if (position < slotPositions.length) {
                const targetPos = slotPositions[position]

                // 创建动画
                const promise = new Promise<void>((resolve) => {
                    tween(cardItem.node)
                        .to(0.5, { position: targetPos })
                        .call(() => {
                            resolve()
                        })
                        .start()
                })

                animationPromises.push(promise)
            }
        })

        // 等待所有动画完成
        await Promise.all(animationPromises)

        this.isRowLayout = false
        this.isAnimating = false

        console.log(
            '[CountingVoteDialog] 切换到两列布局，卡牌数量:',
            totalCards,
            '坑位数量:',
            slotPositions.length
        )
    }

    /**
     * 从卡牌创建分数动画并飞向玩家头像
     * @param cardItem 卡牌组件
     * @param playerIndices 玩家索引数组
     * @param score 分数
     * @param isMasterCardScore 是否是说书人卡牌的分数，决定使用黄色还是蓝色
     * @param showInitialScoreAnimation 是否显示初始分数动画（默认为true）
     * @returns 分数节点数组
     */
    private async createScoreAnimations(
        cardItem: CardItem,
        playerIndices: number[],
        score: number,
        isMasterCardScore: boolean = true,
        showInitialScoreAnimation: boolean = true
    ): Promise<Node[]> {
        console.log(
            `[CountingVoteDialog] 从卡牌创建分数动画，玩家索引: ${playerIndices.join(
                ','
            )}, 分数: ${score}, 类型: ${
                isMasterCardScore ? '说书人卡牌' : '非说书人卡牌'
            }`
        )

        // 如果需要显示初始分数动画
        if (showInitialScoreAnimation) {
            // 创建分数标签
            const initialScoreLabel = new Node(
                isMasterCardScore ? 'MasterScoreLabel' : 'NonMasterScoreLabel'
            )
            initialScoreLabel.addComponent(UITransform).setContentSize(100, 50)
            const label = initialScoreLabel.addComponent(Label)
            this.applyScoreLabelStyle(label, score, isMasterCardScore)

            // 将分数标签添加到场景中
            initialScoreLabel.setParent(this.node)

            // 设置初始位置为卡牌位置
            const sourcePos = cardItem.node.getWorldPosition()
            const localPos = this.node
                .getComponent(UITransform)!
                .convertToNodeSpaceAR(sourcePos)
            initialScoreLabel.setPosition(localPos)

            // 先显示分数并停留0.5秒
            await new Promise<void>((resolve) => {
                tween(initialScoreLabel)
                    .to(0.2, { scale: v3(1.5, 1.5, 1.5) })
                    .delay(0.5)
                    .call(() => {
                        resolve()
                    })
                    .start()
            })

            // 删除原始分数标签
            initialScoreLabel.removeFromParent()
            initialScoreLabel.destroy()
        }

        // 创建分数动画的Promise数组
        const animationPromises: Promise<Node>[] = []

        // 为每个玩家创建分数动画
        for (const playerIndex of playerIndices) {
            console.log(
                `[CountingVoteDialog] 玩家 ${playerIndex} 得分 ${score}`
            )

            // 创建分数标签
            const scoreLabel = new Node('ScoreLabel')
            scoreLabel.addComponent(UITransform).setContentSize(100, 50)
            const label = scoreLabel.addComponent(Label)
            this.applyScoreLabelStyle(label, score, isMasterCardScore)

            // 将分数标签添加到场景中
            scoreLabel.setParent(this.node)

            // 设置初始位置为卡牌位置
            const sourcePos = cardItem.node.getWorldPosition()
            const localPos = this.node
                .getComponent(UITransform)!
                .convertToNodeSpaceAR(sourcePos)
            scoreLabel.setPosition(localPos)

            // 获取玩家头像位置
            let targetPosition: Vec3

            // 确保玩家索引在有效范围内
            if (playerIndex >= 0 && playerIndex < this.playerPositions.length) {
                // 获取基础位置
                const basePosition = this.playerPositions[playerIndex]

                // 检查该玩家是否已有分数节点，如果有则需要偏移
                let offset = v3(0, 0, 0)
                if (
                    this.scoreNodes.has(playerIndex) &&
                    this.scoreNodes.get(playerIndex)!.length > 0
                ) {
                    // 已有分数节点，向下偏移
                    offset = v3(0, -50, 0)
                }

                targetPosition = v3(
                    basePosition.x + offset.x,
                    basePosition.y + offset.y,
                    basePosition.z + offset.z
                )
            } else {
                // 如果玩家索引超出范围，使用默认位置
                console.warn(
                    `[CountingVoteDialog] 玩家索引 ${playerIndex} 超出预定义位置范围，使用默认位置`
                )
                targetPosition = v3(0, 300, 0)
            }

            // 创建动画的Promise
            const promise = new Promise<Node>((resolve) => {
                tween(scoreLabel)
                    .to(0.5, {
                        position: targetPosition,
                        scale: v3(1.5, 1.5, 1.5),
                    })
                    .delay(0.5)
                    // 不再缩小和销毁，而是保留节点
                    .call(() => {
                        // 将分数节点存储到Map中，以便后续添加动效
                        if (!this.scoreNodes.has(playerIndex)) {
                            this.scoreNodes.set(playerIndex, [])
                        }
                        this.scoreNodes.get(playerIndex)?.push(scoreLabel)

                        // 返回分数节点
                        resolve(scoreLabel)
                    })
                    .start()
            })

            animationPromises.push(promise)
        }

        // 等待所有动画完成
        const scoreNodes = await Promise.all(animationPromises)

        return scoreNodes
    }

    /**
     * 播放投票音效
     * 如果当前用户在得分玩家列表中，播放成功音效，否则播放失败音效
     * @param scoredPlayerIndices 得分玩家索引数组
     */
    private playVoteAudioEffect(scoredPlayerIndices: number[]): void {
        // 获取当前用户的索引
        const userIndex = store.user.userIndex

        console.log(
            `[CountingVoteDialog] 播放投票音效，当前用户索引: ${userIndex}, 得分玩家: ${scoredPlayerIndices.join(
                ','
            )}`
        )

        // 判断当前用户是否在得分玩家列表中
        const isUserScored = scoredPlayerIndices.includes(userIndex)

        // 根据结果播放对应音效
        if (isUserScored) {
            // 用户得分，播放成功音效
            cat.audio.playEffect(AudioEffectConstant.SUCCESS)
            console.log('[CountingVoteDialog] 播放成功音效')
        } else {
            // 用户未得分，播放失败音效
            cat.audio.playEffect(AudioEffectConstant.FAIL)
            console.log('[CountingVoteDialog] 播放失败音效')
        }
    }

    protected override onDestroy(): void {
        // 停止环节流转
        this.isPhaseFlowStopped = true
        console.log('[CountingVoteDialog] 停止环节流转')

        // 清理分数节点
        this.scoreNodes.forEach((nodes) => {
            nodes.forEach((node) => {
                if (node.isValid) {
                    node.removeFromParent()
                    node.destroy()
                }
            })
        })
        this.scoreNodes.clear()

        // 回收卡牌
        this.cardItems.forEach((cardItem) => {
            cat.cardPool.put(cardItem.node)
        })
    }
}
