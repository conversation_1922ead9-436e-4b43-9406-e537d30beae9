/**
* @describe 全局事件监听方法
* <AUTHOR>
* @date 2023-08-03 18:13:36
*/

export enum GlobalEventConstant {
    /** 游戏从后台进入 */
    EVENT_SHOW = 'GlobalEventConstant/EVENT_SHOW',
    /** 游戏切到后台 */
    EVENT_HIDE = 'GlobalEventConstant/EVENT_HIDE',
    /** 游戏画笔尺寸变化事件 */
    GAME_RESIZE = 'GlobalEventConstant/GAME_RESIZE',
    /**游戏关闭时间 */
    EVENT_CLOSE = 'GlobalEventConstant/EVENT_CLOSE',
    /**网络连接 */
    ONLINE = 'GlobalEventConstant/ONLINE',
    /**网络断开 */
    OFFLINE = 'GlobalEventConstant/OFFLINE',
}
