import { BaseComponent } from '@/core/manager/gui/base/BaseComponent'
import {
    _decorator,
    Component,
    error,
    EventTouch,
    instantiate,
    Label,
    Node,
    PageView,
    Prefab,
    Sprite,
    tween,
    UITransform,
    v3,
} from 'cc'
const { ccclass, property } = _decorator
import { audioEffect } from '@/core/business/hooks/Decorator'
import store from '@/core/business/store'
import {
    GameModelSinglePosMap,
    HeadNodeDefaultPosMap,
    Single,
} from '@/core/business/types/IGame'
import { cat } from '@/core/manager'
import { SocketEvent } from '@/core/business/ws'
import { GameInfoBroadcast } from '@/pb-generate/server/dixit/v1/game_pb'
import {
    Player,
    PlayerInfoMessage,
    PlayerSchema,
} from '@/pb-generate/server/dixit/v1/player_pb'
import { sleep } from '@/core/business/util/TimeUtils'
import {
    State as DeskState,
    State,
} from '@/pb-generate/server/dixit/v1/state_pb'
import { PlayerItem, PlayerState } from '../components/player/PlayerItem'
import { create } from '@bufbuild/protobuf'

import { BasePool } from '@/core/manager/gui/base/BasePool'

import {
    AudioEffectConstant,
    GameEventConstant,
} from '@/core/business/constant'

type DeskProps = {}

type DeskData = {}

declare module '@/core/manager' {
    interface Manager {
        cardPool: BasePool
    }
}

@ccclass('UIGame')
export class UIGame extends BaseComponent<DeskProps, DeskData> {
    @property({ type: Node, tooltip: '观战节点' })
    watch_node: Node = null!

    @property({ type: Node, tooltip: '玩家根节点' })
    playerRootNode: Node = null!

    @property({ type: Prefab, tooltip: '玩家预制体' })
    players_prefab: Prefab = null!

    @property({ type: Label, tooltip: '主题标签节点' })
    theme_label: Label = null!

    @property({ type: Label, tooltip: '积分榜标签节点' })
    round_label: Label = null!

    @property({ type: Node, tooltip: '标题节点' })
    main_title: Node = null!

    @property({ type: Prefab, tooltip: '卡牌预制体' })
    card_item_prefab: Prefab = null!

    @property({ type: Node, tooltip: '我的手牌btn' })
    my_handcard_btn: Node = null!

    @property({ type: Prefab, tooltip: '手牌弹框预制件' })
    hand_card_dialog_prefab: Prefab = null!

    @property({ type: Prefab, tooltip: '发牌弹框预制件' })
    deal_card_dialog_prefab: Prefab = null!

    gamePlayers: Node[] = []

    protected override initUI(): void {
        cat.cardPool = new BasePool(this.card_item_prefab)
    }

    protected override onEventListener(): void {
        cat.event
            .on<SocketEvent>(
                'MT_GAME_INFO_BROADCAST',
                this.onBroadcastStateSocketHandler,
                this
            )
            .on<SocketEvent>(
                'MT_STATE_CHANGED_BROADCAST',
                this.onBroadcastStateSocketHandler,
                this
            )
            .on<SocketEvent>(
                'MT_PLAYER_INFO_MESSAGE',
                this.onPlayerInfoBroadcastHandler,
                this
            )
            .on<SocketEvent>(
                'MT_PLAYER_STATUS_CHANGED_BROADCAST',
                this.onPlayerStatusChangedHandler,
                this
            )
    }

    protected override onAutoObserver(): void {
        this.addReaction(
            () => store.game.playerList,
            (players) => {
                players.forEach((player) => {
                    const find = this.gamePlayers.find(
                        (item) =>
                            item.getComponent(PlayerItem)?.props.player
                                .index === player.index
                    )
                    if (find) {
                        find.getComponent(PlayerItem)?.setUpdateProps({
                            player,
                        })
                    }
                })
            }
        )
    }

    /**初始化-阶段 */
    private async onPhaseInit() {
        window.ccLog('UIGame-->onPhaseInit')
        cat.audio.playEffect(AudioEffectConstant.GAME_START)
        cat.tracking.game.roomEnter()

        //1.分配座次
        await this.initPlayersDynamic()
    }

    private showMyHandCardBtn() {
        this.my_handcard_btn.active = !store.user.isAudience
    }

    private hideMyHandCardBtn() {
        this.my_handcard_btn.active = false
    }

    private async onBroadcastStateSocketHandler(data: GameInfoBroadcast) {
        const { stateInfo } = data

        window.ccLog('UIGame-->onBroadcastStateSocketHandler', stateInfo)
        // 新消息的时间戳小于当前时间戳则丢弃
        if (
            data.stateInfo?.serverTimestamp &&
            store.game.roomData?.stateInfo?.serverTimestamp &&
            data.stateInfo?.serverTimestamp <
                store.game.roomData?.stateInfo?.serverTimestamp
        ) {
            window.ccLog('UIGame-->onBroadcastStateSocketHandler 丢弃旧消息')
            return
        }

        store.game.roomData = data
        // 更新socket服务器时间
        if (data.stateInfo?.serverTimestamp) {
            const diff = Number(data.stateInfo?.serverTimestamp) - Date.now()
            store.global.ws_diff_server_timer = diff
            window.ccLog('ws时间差(ms)', diff)
        }

        if (stateInfo) {
            const { state } = stateInfo
        } else {
            window.ccLog('UIGame-->stateInfo为空，丢弃旧消息')
            return
        }

        switch (data.stateInfo?.state) {
            case DeskState.GAME_START:
                this.onPhaseInit()
                break
            default:
                break
        }
    }

    private onPlayerInfoBroadcastHandler(data: PlayerInfoMessage) {
        window.ccLog('UIGame-->onPlayerInfoBroadcastHandler', data)
        cat.gui.hideLoading()
        store.game.playerInfo = data
    }

    private onPlayerStatusChangedHandler(data: Player) {
        store.game.updatePlayerStatus(data)
    }

    async initPlayersDynamic() {
        window.ccLog('UIGame-->initPlayersDynamic')
        const { game, user } = store
        this.playerRootNode.removeAllChildren()

        const copy = [...store.game.playerList]
        if (!copy.length) return error('玩家列表不存在')

        const defaultPlayersPos = HeadNodeDefaultPosMap[6]

        //构建6个默认的玩家位置
        for (let i = 0; i < 6; i++) {
            const player_node = instantiate(this.players_prefab)
            const { x, y } = defaultPlayersPos[i]

            let tempPlayer = create(PlayerSchema)
            tempPlayer.index = i

            player_node
                .getComponent(PlayerItem)!
                .setPosition(v3(x, y, 0))
                .addToParent(this.playerRootNode, {
                    props: { player: { ...tempPlayer, position: i } },
                    data: { state: PlayerState.EMPTY },
                })

            this.gamePlayers.push(player_node)
        }

        const playersPos = GameModelSinglePosMap[copy.length as Single]
        // 游戏初始化时，实例化玩家，并且在一行上显示
        //定义一个数组，将循环中的player_node存储起来
        const scale = store.game.playerList.length > 5 ? 0.65 : 0.7
        let newPlayerNode: Node[] = []
        store.game.playerList.forEach((item, index) => {
            const player_node = instantiate(this.players_prefab)
            player_node.setScale(scale, scale, scale)

            const { x, y } = playersPos[index]
            player_node
                .getComponent(PlayerItem)!
                .setPosition(v3(x, y, 0))
                .addToParent(this.playerRootNode, { props: { player: item } })

            newPlayerNode.push(player_node)
        })

        await sleep(1000)
        // 头像飞到固定麦位上
        const headTweenPromise: Promise<void>[] = []
        newPlayerNode.forEach((node, index) => {
            window.ccLog(
                'UIGame-->initPlayersDynamic, position:',
                store.game.playerList[index].position
            )
            const { x, y } =
                this.gamePlayers[
                    store.game.playerList[index].position
                ].getPosition()
            headTweenPromise.push(
                new Promise((resolve) => {
                    tween(node)
                        .to(0.8, {
                            position: v3(x, y, 0),
                            scale: v3(1, 1, 1),
                        })
                        .delay(0.2 * index)
                        .call(() => {
                            this.gamePlayers[
                                store.game.playerList[index].position
                            ]
                                .getComponent(PlayerItem)!
                                .setUpdateProps({
                                    player: store.game.playerList[index],
                                })
                                .setUpdateData({
                                    state: PlayerState.NORMAL,
                                    basePlayer: store.game.getBasePlayerByUid(
                                        store.game.playerList[index].id
                                    ),
                                })
                            node.removeFromParent()
                            node.destroy()
                            resolve()
                        })
                        .start()
                })
            )
        })

        await Promise.all(headTweenPromise)
    }

    //游戏已经启动，直接显示玩家头像
    initPlayerStatic() {
        window.ccLog('UIGame-->initPlayerStatic')
        const { game, user } = store
        this.playerRootNode.removeAllChildren()

        const copy = [...store.game.playerList]
        if (!copy.length) return error('玩家列表不存在')

        const defaultPlayersPos = HeadNodeDefaultPosMap[6]

        copy.forEach((item, index) => {
            window.ccLog('UIGame-->initPlayerStatic, position:', item.position)
        })
        //构建6个默认的玩家位置
        for (let i = 0; i < 6; i++) {
            const player_node = instantiate(this.players_prefab)
            const { x, y } = defaultPlayersPos[i]

            let tempPlayer = create(PlayerSchema)
            let tempState = PlayerState.EMPTY
            tempPlayer.index = i

            copy.map((item) => {
                if (item.position === i) {
                    tempPlayer = item
                    tempState = PlayerState.NORMAL
                }
            })

            player_node
                .getComponent(PlayerItem)!
                .setPosition(v3(x, y, 0))
                .addToParent(this.playerRootNode, {
                    props: { player: { ...tempPlayer, position: i } },
                    data: { state: tempState },
                })

            this.gamePlayers.push(player_node)
        }
    }

    private onShowStoryTheme() {
        const theme_string = `(${store.game.roomData?.deckInfo?.themeName})`
        this.theme_label.node.active = true
        this.theme_label.string = theme_string || '故事书'
    }

    private showRoundInfo() {
        const { stateInfo } = store.game.roomData
        const { roundInfo } = store.game.roomData
        if (roundInfo && stateInfo) {
            const { roundCount, roundTotal } = roundInfo
            const { state } = stateInfo
            if (
                roundCount > 0 &&
                roundTotal > 0 &&
                state >= DeskState.ROUND_START
            ) {
                this.round_label.node.active = true
                this.round_label.string = `(第${roundCount}/${roundTotal}轮)`
            } else {
                this.round_label.node.active = false
            }
        }
    }

    //初始化手牌
    showInitCardDialog() {
        cat.event.dispatchEvent(GameEventConstant.CLOSE_COMMON_UI)
        const node = instantiate(this.deal_card_dialog_prefab)
        cat.gui.openUI<DealCardDialog>(node)
    }

    syncGameInfo() {
        window.ccLog('UIGame-->syncGameInfo')
        const stateInfo = store.game.roomData?.stateInfo
        if (
            stateInfo?.state !== undefined &&
            stateInfo.state > DeskState.ROUND_START
        ) {
            //如果当前状态大于Round_start，则执行确定说书人，以及显示轮次
            window.ccLog('UIGame-->syncGameInfo, onConfirmSpeaker()')
            this.onConfirmSpeaker()

            if (!store.game.isCurrentPlayerCursor) {
                if (
                    stateInfo.state === DeskState.PLAYER_POST ||
                    stateInfo.state === DeskState.PLAYER_TELLING
                ) {
                    this.showMyHandCardBtn()
                }
            }
        }
    }
}
