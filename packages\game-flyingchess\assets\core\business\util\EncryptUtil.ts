/**
 * @describe 加密工具
 * <AUTHOR>
 * @date 2023-01-30 17:20:24
 */
import CryptoES from 'crypto-es';

let _key: string = null!;
let _iv: CryptoES.lib.WordArray = null!;

/**
 * MD5加密
 * @param msg 加密信息
 */
export const md5 = (msg: string): string => {
    return CryptoES.MD5(msg).toString();
}

/** 初始化加密库 */
export const initCrypto = (key: string, iv: string) => {
    key = key;
    _iv = CryptoES.enc.Hex.parse(iv);
}

/**
 * AES 加密
 * @param msg 加密信息
 * @param key aes加密的key
 * @param iv  aes加密的iv
 */
export const aesEncrypt = (msg: string, key: string, iv: string): string => {
    return CryptoES.AES.encrypt(msg, key, {
        iv: _iv,
        format: JsonFormatter,
    }).toString();
}

/**
 * AES 解密
 * @param str 解密字符串
 * @param key aes加密的key
 * @param iv  aes加密的iv
 */
export const aesDecrypt = (str: string, key: string, iv: string): string => {
    const decrypted = CryptoES.AES.decrypt(str, key, {
        iv: _iv,
        format: JsonFormatter,
    });
    return decrypted.toString(CryptoES.enc.Utf8);
}

export const JsonFormatter = {
    stringify: (cipherParams: any) => {
        const jsonObj: any = { ct: cipherParams.ciphertext.toString(CryptoES.enc.Base64) };
        if (cipherParams.iv) {
            jsonObj.iv = cipherParams.iv.toString();
        }
        if (cipherParams.salt) {
            jsonObj.s = cipherParams.salt.toString();
        }
        return JSON.stringify(jsonObj);
    },
    parse: (jsonStr: string) => {
        const jsonObj = JSON.parse(jsonStr);
        const cipherParams = CryptoES.lib.CipherParams.create({ ciphertext: CryptoES.enc.Base64.parse(jsonObj.ct) });
        if (jsonObj.iv) {
            cipherParams.iv = CryptoES.enc.Hex.parse(jsonObj.iv);
        }
        if (jsonObj.s) {
            cipherParams.salt = CryptoES.enc.Hex.parse(jsonObj.s);
        }
        return cipherParams;
    },
};
