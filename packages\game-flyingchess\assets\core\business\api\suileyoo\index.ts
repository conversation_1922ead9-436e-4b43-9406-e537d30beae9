import bridge from "../../bridge"
import { Bridge } from "../../bridge/Bridge"
import { BaseAPI } from "../BaseAPI"
export class SuileyooAPI extends BaseAPI {
    serverLogin = () => {
        return this.cat.http<IBridge.EnterParam>({
            url: Bridge.EventName.APIName.ENTER_PARAM,
            method: 'POST',
        })
    }

    getAssets = () => {
        return this.cat.http<IBridge.UserAssetRes>({
            url: Bridge.EventName.APIName.ASSETS,
            method: 'POST',
            data: {}
        })
    }

    matchRoom = <T extends IBridge.MatchRoomRes>(params: IBridge.CreateRoomReq) => {
        return this.cat.http<T>({
            url: Bridge.EventName.APIName.MATCHING,
            method: 'POST',
            data: params
        })
    }

    searchRoom = <T extends IBridge.SearchRoomRes>(params: IBridge.SearchRoomReq) => {
        return this.cat.http<T>({
            url: Bridge.EventName.APIName.SEARCH,
            method: 'POST',
            data: params
        })
    }

    getUserInfo = (params: IBridge.UserGameDataReq) => {
        return this.cat.http<IBridge.UserGameDataRes>({
            url: Bridge.EventName.APIName.USER_GAME_DATA,
            method: 'POST',
            data: params
        })
    }

    getUserProfile = async <T extends IBridge.Profile[]>(params: IBridge.QueryUserInfoReq) => {
        const res = await this.cat.http<T>({
            url: Bridge.EventName.APIName.CHECK_USER_PROFILES,
            method: 'POST',
            data: params
        })
        bridge.nativeAPI.dispatchEvent(Bridge.EventName.APIName.CHECK_USER_PROFILES, {
            code: 0,
            data: res
        })
        return res
    }

    getMikeList = <T extends IBridge.MikeListRes>(params: IBridge.MikeListReq): Promise<T> => {
        return this.cat.http<T>({
            url: Bridge.EventName.APIName.MIKE_LIST,
            method: 'POST',
            data: params
        })
    }

    joinChangeMike = (params: IBridge.ChangeMikeReq): Promise<void> => {
        return this.cat.http({
            url: Bridge.EventName.APIName.MIKE_CHANGE_JOIN,
            method: 'POST',
            data: params
        })
    }
    ready = (params: IBridge.ReadyReq): Promise<void> => {
        return this.cat.http({
            url: Bridge.EventName.APIName.READY,
            method: 'POST',
            data: params
        })
    }

    unReady = (params: IBridge.UnReadyReq): Promise<void> => {
        return this.cat.http({
            url: Bridge.EventName.APIName.UNREADY,
            method: 'POST',
            data: params
        })
    }

    getRoomInfo = <T extends IBridge.RoomInfoRes>(params: IBridge.RoomInfoReq): Promise<T> => {
        return this.cat.http({
            url: Bridge.EventName.APIName.ROOM_INFO,
            method: 'POST',
            data: params
        })
    }

    exitRoom = (params: IBridge.ExitRoomReq): Promise<void> => {
        return this.cat.http({
            url: Bridge.EventName.APIName.EXIT,
            method: 'POST',
            data: params
        })
    }

    roomList = async <T extends IBridge.RoomListRes>(params: IBridge.RoomInfoReq): Promise<T> => {
        return this.cat.http({
            url: Bridge.EventName.APIV2Name.ROOM_LIST,
            method: 'POST',
            data: params
        })

    }

    roomExpertList = async <T extends IBridge.RoomListRes>(params: IBridge.RoomInfoReq): Promise<T> => {
        return await this.cat.http({
            url: Bridge.EventName.APIV2Name.ROOM_EXPERT_LIST,
            method: 'POST',
            data: params
        })

    }
    tierRank = async <T extends IBridge.TierRankRes>(params: IBridge.TierRankReq): Promise<T> => {
        return await this.cat.http({
            url: Bridge.EventName.APIV2Name.TIER_RANK,
            method: 'POST',
            data: params
        })

    }
    levelRank = async <T extends IBridge.LevelRankRes>(params: IBridge.LevelRankReq): Promise<T> => {
        return await this.cat.http({
            url: Bridge.EventName.APIV2Name.LEVEL_RANK,
            method: 'POST',
            data: params
        })

    }
    seasonSettle = async <T extends IBridge.SeasonSettleRes>(params: IBridge.SeasonSettleReq): Promise<T> => {
        return await this.cat.http({
            url: Bridge.EventName.APIV2Name.SEASON_SETTLE,
            method: 'POST',
            data: params
        })
    }

    createRoom = async <T extends IBridge.CreateRoomRes>(params: IBridge.CreateRoomReq): Promise<T> => {
        return await this.cat.http({
            url: Bridge.EventName.APIName.CREATE,
            method: 'POST',
            data: params
        })
    }
    longLink = async <T extends IBridge.LongLinkRes>(params: IBridge.LongLinkReq): Promise<T> => {
        return await this.cat.http({
            url: Bridge.EventName.APIV2Name.LONG_LINK,
            method: 'POST',
            data: params
        })
    }

    joinArena = async <T extends IBridge.JoinArenaRes>(params: IBridge.JoinArenaReq): Promise<T> => {
        return await this.cat.http({
            url: Bridge.EventName.APIV2Name.JOIN_ARENA,
            method: 'POST',
            data: params
        })
    }

    quickJoin = async <T extends IBridge.QuickJoinRes>(params: IBridge.QuickJoinReq): Promise<T> => {
        return await this.cat.http({
            url: Bridge.EventName.APIV2Name.QUICK_JOIN,
            method: 'POST',
            data: params
        })
    }

    cancelQuickJoin = async <T extends IBridge.CancelQuickJoinRes>(params: IBridge.CancelQuickJoinReq): Promise<T> => {
        return await this.cat.http({
            url: Bridge.EventName.APIV2Name.CANCEL_QUICK_JOIN,
            method: 'POST',
            data: params
        })
    }

    cancelJoinArena = async <T extends IBridge.CancelQuickJoinReq>(params: IBridge.CancelQuickJoinReq): Promise<T> => {
        return await this.cat.http({
            url: Bridge.EventName.APIV2Name.EXIT_ARENA,
            method: 'POST',
            data: params
        })
    }

    gameStateInfo = async <T extends IBridge.GameStateInfoRes>(params: IBridge.GameStateInfoReq): Promise<T> => {
        return await this.cat.http({
            url: Bridge.EventName.APIV2Name.GAME_STATE_INFO,
            method: 'POST',
            data: params
        })
    }
    taskReceive = async <T extends IBridge.TaskReceiveRes>(params: IBridge.TaskReceiveReq): Promise<T> => {
        return await this.cat.http({
            url: Bridge.EventName.APIV2Name.TASK_RECEIVE,
            method: 'POST',
            data: params
        })
    }

    task = async <T extends IBridge.TaskRes>(params: IBridge.TaskReq): Promise<T> => {
        return await this.cat.http({
            url: Bridge.EventName.APIV2Name.TASK,
            method: 'POST',
            data: params
        })
    }

    modifyJoinState = async <T extends IBridge.ModifyJoinStateRes>(params: IBridge.ModifyJoinStateReq): Promise<T> => {
        return await this.cat.http({
            url: Bridge.EventName.APIV2Name.MODIFY_JOIN_STATE,
            method: 'POST',
            data: params
        })
    }

}