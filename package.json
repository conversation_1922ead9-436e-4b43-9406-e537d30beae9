{"name": "shark", "version": "1.0.0", "description": "", "private": true, "scripts": {"generate-server:shark": "buf generate --template packages/game-shark/buf-server.gen.yaml --path ./node_modules/proto-api/shark/suileyoo", "generate-server:pirate": "buf generate --template packages/game-pirate/buf-server.gen.yaml --path ./node_modules/proto-api/pirate", "generate-server:dixit": "buf generate --template packages/game-dixit/buf-server.gen.yaml --path ./node_modules/proto-api/dixit", "generate-server:flyingchess": "buf generate --template packages/game-flyingchess/buf-server.gen.yaml --path ./node_modules/proto-api/flyingchess", "generate:shark": "yarn generate-server:shark && yarn workspace game-shark run generate:all", "generate:pirate": "yarn generate-server:pirate && yarn workspace game-pirate run generate:all", "generate:dixit": "yarn generate-server:dixit && yarn workspace game-dixit run generate:all", "generate:flyingchess": "yarn generate-server:flyingchess && yarn workspace game-flyingchess run generate:all", "generate:all": "yarn generate:shark && yarn generate:pirate && yarn generate:dixit && yarn generate:flyingchess", "build:dep": "yarn workspace pitayaclient run build && yarn workspace sgc run build", "generate": "yarn build:dep && yarn generate:all", "build": "node scripts/build.js", "release:shark": "node scripts/version.js shark", "release:pirate": "node scripts/version.js pirate", "release:dixit": "node scripts/version.js dixit", "release:flyingchess": "node scripts/version.js flyingchess", "release:all": "node scripts/version.js all"}, "devDependencies": {"@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-decorators": "^7.24.7", "@bufbuild/buf": "^1.41.0", "babel-loader": "^9.1.0", "clean-webpack-plugin": "^4.0.0", "cos-nodejs-sdk-v5": "^2.13.3", "declaration-bundler-webpack-plugin": "^1.0.3", "husky": "^8.0.1", "jest": "^28.1.3", "minigame-api-typings": "^3.8.3", "prettier": "2.8.7", "pretty-quick": "^3.1.3", "rimraf": "^6.0.1", "standard-version": "^9.5.0", "terser-webpack-plugin": "^5.3.3", "ts-jest": "^28.0.7", "ts-loader": "^9.3.1", "typescript": "^4.9.4", "webpack": "^5.73.0", "webpack-cli": "^5.0.1"}, "dependencies": {"@bufbuild/protobuf": "^2.0.0", "@socket.io/component-emitter": "3.1.0", "@types/pako": "^2.0.3", "aws-sdk": "^2.45.0", "cli": "1.2.42", "core-js": "3.39.0", "cos-js-sdk-v5": "^1.4.18", "crypto-es": "^2.0.4", "dsbridge": "3.1.3", "fastestsmallesttextencoderdecoder": "^1.0.22", "ky": "^1.7.2", "mobx-tsuki": "1.0.1", "pako": "^2.1.0", "pitayaclient": "0.0.1", "proto-api": "git+ssh://********************:party_game/proto-api.git#feat/dixit", "sgc": "0.0.1"}, "optionalDependencies": {"suileyoo-echo-proto": "git+ssh://********************:yunyouxi/suileyoo-echo-proto.git"}, "workspaces": ["packages/*"]}