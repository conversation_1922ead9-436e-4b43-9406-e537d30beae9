// @generated by protoc-gen-es v2.2.5 with parameter "target=ts,import_extension=none,json_types=true"
// @generated from file worker.proto (package protos, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import type { Any, AnyJson, EmptySchema, UInt64ValueSchema } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_any, file_google_protobuf_empty, file_google_protobuf_wrappers } from "@bufbuild/protobuf/wkt";
import type { BalanceNotification, BalanceNotificationJson } from "./account_pb";
import { file_account } from "./account_pb";
import type { RechargeNotification, RechargeNotificationJson } from "./recharge_pb";
import { file_recharge } from "./recharge_pb";
import type { ConnectOfflineNotification, ConnectOfflineNotificationJson, ConnectOnlineNotification, ConnectOnlineNotificationJson } from "./connect_pb";
import { file_connect } from "./connect_pb";
import type { UserInfoNotification, UserInfoNotificationJson } from "./user_pb";
import { file_user } from "./user_pb";
import type { NewMessageNotification, NewMessageNotificationJson } from "./new-message_pb";
import { file_new_message } from "./new-message_pb";
import type { Notification, NotificationJson } from "./notification_pb";
import { file_notification } from "./notification_pb";
import type { MemberInfoNotification, MemberInfoNotificationJson } from "./member_pb";
import { file_member } from "./member_pb";
import type { GrowthInfoNotification, GrowthInfoNotificationJson } from "./growth_pb";
import { file_growth } from "./growth_pb";
import type { MedalDoneNotification, MedalDoneNotificationJson } from "./medal_pb";
import { file_medal } from "./medal_pb";
import type { TaskAwardNotification, TaskAwardNotificationJson } from "./task_pb";
import { file_task } from "./task_pb";
import type { InviteAutoFollowNotification, InviteAutoFollowNotificationJson } from "./invite_pb";
import { file_invite } from "./invite_pb";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file worker.proto.
 */
export const file_worker: GenFile = /*@__PURE__*/
  fileDesc("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", [file_google_protobuf_any, file_google_protobuf_empty, file_google_protobuf_wrappers, file_account, file_recharge, file_connect, file_user, file_new_message, file_notification, file_member, file_growth, file_medal, file_task, file_invite]);

/**
 * 登录状态剔除
 *
 * @generated from message protos.KickOutRequest
 */
export type KickOutRequest = Message<"protos.KickOutRequest"> & {
  /**
   * @generated from field: uint64 user_id = 1;
   */
  userId: bigint;

  /**
   * @generated from field: string access_token = 2;
   */
  accessToken: string;
};

/**
 * 登录状态剔除
 *
 * @generated from message protos.KickOutRequest
 */
export type KickOutRequestJson = {
  /**
   * @generated from field: uint64 user_id = 1;
   */
  userId?: string;

  /**
   * @generated from field: string access_token = 2;
   */
  accessToken?: string;
};

/**
 * Describes the message protos.KickOutRequest.
 * Use `create(KickOutRequestSchema)` to create a new message.
 */
export const KickOutRequestSchema: GenMessage<KickOutRequest, KickOutRequestJson> = /*@__PURE__*/
  messageDesc(file_worker, 0);

/**
 * 用户封号
 *
 * @generated from message protos.BanRequest
 */
export type BanRequest = Message<"protos.BanRequest"> & {
  /**
   * @generated from field: uint64 user_id = 1;
   */
  userId: bigint;

  /**
   * @generated from field: google.protobuf.Any data = 2;
   */
  data?: Any;
};

/**
 * 用户封号
 *
 * @generated from message protos.BanRequest
 */
export type BanRequestJson = {
  /**
   * @generated from field: uint64 user_id = 1;
   */
  userId?: string;

  /**
   * @generated from field: google.protobuf.Any data = 2;
   */
  data?: AnyJson;
};

/**
 * Describes the message protos.BanRequest.
 * Use `create(BanRequestSchema)` to create a new message.
 */
export const BanRequestSchema: GenMessage<BanRequest, BanRequestJson> = /*@__PURE__*/
  messageDesc(file_worker, 1);

/**
 * 余额通知
 *
 * @generated from message protos.BalanceNotificationRequest
 */
export type BalanceNotificationRequest = Message<"protos.BalanceNotificationRequest"> & {
  /**
   * @generated from field: uint64 user_id = 1;
   */
  userId: bigint;

  /**
   * @generated from field: protos.BalanceNotification notification = 2;
   */
  notification?: BalanceNotification;
};

/**
 * 余额通知
 *
 * @generated from message protos.BalanceNotificationRequest
 */
export type BalanceNotificationRequestJson = {
  /**
   * @generated from field: uint64 user_id = 1;
   */
  userId?: string;

  /**
   * @generated from field: protos.BalanceNotification notification = 2;
   */
  notification?: BalanceNotificationJson;
};

/**
 * Describes the message protos.BalanceNotificationRequest.
 * Use `create(BalanceNotificationRequestSchema)` to create a new message.
 */
export const BalanceNotificationRequestSchema: GenMessage<BalanceNotificationRequest, BalanceNotificationRequestJson> = /*@__PURE__*/
  messageDesc(file_worker, 2);

/**
 * 充值成功
 *
 * @generated from message protos.RechargeNotificationRequest
 */
export type RechargeNotificationRequest = Message<"protos.RechargeNotificationRequest"> & {
  /**
   * @generated from field: uint64 user_id = 1;
   */
  userId: bigint;

  /**
   * @generated from field: protos.RechargeNotification notification = 2;
   */
  notification?: RechargeNotification;
};

/**
 * 充值成功
 *
 * @generated from message protos.RechargeNotificationRequest
 */
export type RechargeNotificationRequestJson = {
  /**
   * @generated from field: uint64 user_id = 1;
   */
  userId?: string;

  /**
   * @generated from field: protos.RechargeNotification notification = 2;
   */
  notification?: RechargeNotificationJson;
};

/**
 * Describes the message protos.RechargeNotificationRequest.
 * Use `create(RechargeNotificationRequestSchema)` to create a new message.
 */
export const RechargeNotificationRequestSchema: GenMessage<RechargeNotificationRequest, RechargeNotificationRequestJson> = /*@__PURE__*/
  messageDesc(file_worker, 3);

/**
 * 下机通知
 *
 * @generated from message protos.ConnectOfflineNotificationRequest
 */
export type ConnectOfflineNotificationRequest = Message<"protos.ConnectOfflineNotificationRequest"> & {
  /**
   * @generated from field: uint64 user_id = 1;
   */
  userId: bigint;

  /**
   * @generated from field: protos.ConnectOfflineNotification notification = 2;
   */
  notification?: ConnectOfflineNotification;
};

/**
 * 下机通知
 *
 * @generated from message protos.ConnectOfflineNotificationRequest
 */
export type ConnectOfflineNotificationRequestJson = {
  /**
   * @generated from field: uint64 user_id = 1;
   */
  userId?: string;

  /**
   * @generated from field: protos.ConnectOfflineNotification notification = 2;
   */
  notification?: ConnectOfflineNotificationJson;
};

/**
 * Describes the message protos.ConnectOfflineNotificationRequest.
 * Use `create(ConnectOfflineNotificationRequestSchema)` to create a new message.
 */
export const ConnectOfflineNotificationRequestSchema: GenMessage<ConnectOfflineNotificationRequest, ConnectOfflineNotificationRequestJson> = /*@__PURE__*/
  messageDesc(file_worker, 4);

/**
 * 上机成功通知
 *
 * @generated from message protos.ConnectOnlineNotificationRequest
 */
export type ConnectOnlineNotificationRequest = Message<"protos.ConnectOnlineNotificationRequest"> & {
  /**
   * @generated from field: uint64 user_id = 1;
   */
  userId: bigint;

  /**
   * @generated from field: protos.ConnectOnlineNotification notification = 2;
   */
  notification?: ConnectOnlineNotification;
};

/**
 * 上机成功通知
 *
 * @generated from message protos.ConnectOnlineNotificationRequest
 */
export type ConnectOnlineNotificationRequestJson = {
  /**
   * @generated from field: uint64 user_id = 1;
   */
  userId?: string;

  /**
   * @generated from field: protos.ConnectOnlineNotification notification = 2;
   */
  notification?: ConnectOnlineNotificationJson;
};

/**
 * Describes the message protos.ConnectOnlineNotificationRequest.
 * Use `create(ConnectOnlineNotificationRequestSchema)` to create a new message.
 */
export const ConnectOnlineNotificationRequestSchema: GenMessage<ConnectOnlineNotificationRequest, ConnectOnlineNotificationRequestJson> = /*@__PURE__*/
  messageDesc(file_worker, 5);

/**
 * 用户信息变更通知
 *
 * @generated from message protos.UserInfoNotificationRequest
 */
export type UserInfoNotificationRequest = Message<"protos.UserInfoNotificationRequest"> & {
  /**
   * @generated from field: uint64 user_id = 1;
   */
  userId: bigint;

  /**
   * @generated from field: protos.UserInfoNotification notification = 2;
   */
  notification?: UserInfoNotification;
};

/**
 * 用户信息变更通知
 *
 * @generated from message protos.UserInfoNotificationRequest
 */
export type UserInfoNotificationRequestJson = {
  /**
   * @generated from field: uint64 user_id = 1;
   */
  userId?: string;

  /**
   * @generated from field: protos.UserInfoNotification notification = 2;
   */
  notification?: UserInfoNotificationJson;
};

/**
 * Describes the message protos.UserInfoNotificationRequest.
 * Use `create(UserInfoNotificationRequestSchema)` to create a new message.
 */
export const UserInfoNotificationRequestSchema: GenMessage<UserInfoNotificationRequest, UserInfoNotificationRequestJson> = /*@__PURE__*/
  messageDesc(file_worker, 6);

/**
 * 会员信息变更通知
 *
 * @generated from message protos.MemberInfoNotificationRequest
 */
export type MemberInfoNotificationRequest = Message<"protos.MemberInfoNotificationRequest"> & {
  /**
   * @generated from field: uint64 user_id = 1;
   */
  userId: bigint;

  /**
   * @generated from field: protos.MemberInfoNotification notification = 2;
   */
  notification?: MemberInfoNotification;
};

/**
 * 会员信息变更通知
 *
 * @generated from message protos.MemberInfoNotificationRequest
 */
export type MemberInfoNotificationRequestJson = {
  /**
   * @generated from field: uint64 user_id = 1;
   */
  userId?: string;

  /**
   * @generated from field: protos.MemberInfoNotification notification = 2;
   */
  notification?: MemberInfoNotificationJson;
};

/**
 * Describes the message protos.MemberInfoNotificationRequest.
 * Use `create(MemberInfoNotificationRequestSchema)` to create a new message.
 */
export const MemberInfoNotificationRequestSchema: GenMessage<MemberInfoNotificationRequest, MemberInfoNotificationRequestJson> = /*@__PURE__*/
  messageDesc(file_worker, 7);

/**
 * 成长信息变更通知
 *
 * @generated from message protos.GrowthInfoNotificationRequest
 */
export type GrowthInfoNotificationRequest = Message<"protos.GrowthInfoNotificationRequest"> & {
  /**
   * @generated from field: uint64 user_id = 1;
   */
  userId: bigint;

  /**
   * @generated from field: protos.GrowthInfoNotification notification = 2;
   */
  notification?: GrowthInfoNotification;
};

/**
 * 成长信息变更通知
 *
 * @generated from message protos.GrowthInfoNotificationRequest
 */
export type GrowthInfoNotificationRequestJson = {
  /**
   * @generated from field: uint64 user_id = 1;
   */
  userId?: string;

  /**
   * @generated from field: protos.GrowthInfoNotification notification = 2;
   */
  notification?: GrowthInfoNotificationJson;
};

/**
 * Describes the message protos.GrowthInfoNotificationRequest.
 * Use `create(GrowthInfoNotificationRequestSchema)` to create a new message.
 */
export const GrowthInfoNotificationRequestSchema: GenMessage<GrowthInfoNotificationRequest, GrowthInfoNotificationRequestJson> = /*@__PURE__*/
  messageDesc(file_worker, 8);

/**
 * 个人勋章获得通知
 *
 * @generated from message protos.MedalDoneNotificationRequest
 */
export type MedalDoneNotificationRequest = Message<"protos.MedalDoneNotificationRequest"> & {
  /**
   * @generated from field: uint64 user_id = 1;
   */
  userId: bigint;

  /**
   * @generated from field: protos.MedalDoneNotification notification = 2;
   */
  notification?: MedalDoneNotification;
};

/**
 * 个人勋章获得通知
 *
 * @generated from message protos.MedalDoneNotificationRequest
 */
export type MedalDoneNotificationRequestJson = {
  /**
   * @generated from field: uint64 user_id = 1;
   */
  userId?: string;

  /**
   * @generated from field: protos.MedalDoneNotification notification = 2;
   */
  notification?: MedalDoneNotificationJson;
};

/**
 * Describes the message protos.MedalDoneNotificationRequest.
 * Use `create(MedalDoneNotificationRequestSchema)` to create a new message.
 */
export const MedalDoneNotificationRequestSchema: GenMessage<MedalDoneNotificationRequest, MedalDoneNotificationRequestJson> = /*@__PURE__*/
  messageDesc(file_worker, 9);

/**
 * 新消息通知
 *
 * @generated from message protos.NewMessageNotificationRequest
 */
export type NewMessageNotificationRequest = Message<"protos.NewMessageNotificationRequest"> & {
  /**
   * @generated from field: uint64 user_id = 1;
   */
  userId: bigint;

  /**
   * @generated from field: protos.NewMessageNotification notification = 2;
   */
  notification?: NewMessageNotification;
};

/**
 * 新消息通知
 *
 * @generated from message protos.NewMessageNotificationRequest
 */
export type NewMessageNotificationRequestJson = {
  /**
   * @generated from field: uint64 user_id = 1;
   */
  userId?: string;

  /**
   * @generated from field: protos.NewMessageNotification notification = 2;
   */
  notification?: NewMessageNotificationJson;
};

/**
 * Describes the message protos.NewMessageNotificationRequest.
 * Use `create(NewMessageNotificationRequestSchema)` to create a new message.
 */
export const NewMessageNotificationRequestSchema: GenMessage<NewMessageNotificationRequest, NewMessageNotificationRequestJson> = /*@__PURE__*/
  messageDesc(file_worker, 10);

/**
 * 成长信息变更通知
 *
 * @generated from message protos.TaskAwardNotificationRequest
 */
export type TaskAwardNotificationRequest = Message<"protos.TaskAwardNotificationRequest"> & {
  /**
   * @generated from field: uint64 user_id = 1;
   */
  userId: bigint;

  /**
   * @generated from field: protos.TaskAwardNotification notification = 2;
   */
  notification?: TaskAwardNotification;
};

/**
 * 成长信息变更通知
 *
 * @generated from message protos.TaskAwardNotificationRequest
 */
export type TaskAwardNotificationRequestJson = {
  /**
   * @generated from field: uint64 user_id = 1;
   */
  userId?: string;

  /**
   * @generated from field: protos.TaskAwardNotification notification = 2;
   */
  notification?: TaskAwardNotificationJson;
};

/**
 * Describes the message protos.TaskAwardNotificationRequest.
 * Use `create(TaskAwardNotificationRequestSchema)` to create a new message.
 */
export const TaskAwardNotificationRequestSchema: GenMessage<TaskAwardNotificationRequest, TaskAwardNotificationRequestJson> = /*@__PURE__*/
  messageDesc(file_worker, 11);

/**
 * 通用消息通知
 *
 * @generated from message protos.NotificationRequest
 */
export type NotificationRequest = Message<"protos.NotificationRequest"> & {
  /**
   * @generated from field: uint64 user_id = 1;
   */
  userId: bigint;

  /**
   * @generated from field: string client_type = 2;
   */
  clientType: string;

  /**
   * @generated from field: protos.Notification notification = 3;
   */
  notification?: Notification;
};

/**
 * 通用消息通知
 *
 * @generated from message protos.NotificationRequest
 */
export type NotificationRequestJson = {
  /**
   * @generated from field: uint64 user_id = 1;
   */
  userId?: string;

  /**
   * @generated from field: string client_type = 2;
   */
  clientType?: string;

  /**
   * @generated from field: protos.Notification notification = 3;
   */
  notification?: NotificationJson;
};

/**
 * Describes the message protos.NotificationRequest.
 * Use `create(NotificationRequestSchema)` to create a new message.
 */
export const NotificationRequestSchema: GenMessage<NotificationRequest, NotificationRequestJson> = /*@__PURE__*/
  messageDesc(file_worker, 12);

/**
 * 邀请的好友登录自动成为好友通知
 *
 * @generated from message protos.InviteAutoFollowNotificationRequest
 */
export type InviteAutoFollowNotificationRequest = Message<"protos.InviteAutoFollowNotificationRequest"> & {
  /**
   * @generated from field: uint64 user_id = 1;
   */
  userId: bigint;

  /**
   * @generated from field: protos.InviteAutoFollowNotification notification = 2;
   */
  notification?: InviteAutoFollowNotification;
};

/**
 * 邀请的好友登录自动成为好友通知
 *
 * @generated from message protos.InviteAutoFollowNotificationRequest
 */
export type InviteAutoFollowNotificationRequestJson = {
  /**
   * @generated from field: uint64 user_id = 1;
   */
  userId?: string;

  /**
   * @generated from field: protos.InviteAutoFollowNotification notification = 2;
   */
  notification?: InviteAutoFollowNotificationJson;
};

/**
 * Describes the message protos.InviteAutoFollowNotificationRequest.
 * Use `create(InviteAutoFollowNotificationRequestSchema)` to create a new message.
 */
export const InviteAutoFollowNotificationRequestSchema: GenMessage<InviteAutoFollowNotificationRequest, InviteAutoFollowNotificationRequestJson> = /*@__PURE__*/
  messageDesc(file_worker, 13);

/**
 * The greeting service definition.
 *
 * @generated from service protos.Worker
 */
export const Worker: GenService<{
  /**
   * Sends a greeting
   *
   * @generated from rpc protos.Worker.KickOut
   */
  kickOut: {
    methodKind: "unary";
    input: typeof KickOutRequestSchema;
    output: typeof EmptySchema;
  },
  /**
   * @generated from rpc protos.Worker.Expired
   */
  expired: {
    methodKind: "unary";
    input: typeof UInt64ValueSchema;
    output: typeof EmptySchema;
  },
  /**
   * @generated from rpc protos.Worker.Ban
   */
  ban: {
    methodKind: "unary";
    input: typeof BanRequestSchema;
    output: typeof EmptySchema;
  },
  /**
   * @generated from rpc protos.Worker.Destroyed
   */
  destroyed: {
    methodKind: "unary";
    input: typeof UInt64ValueSchema;
    output: typeof EmptySchema;
  },
  /**
   * @generated from rpc protos.Worker.SendBalanceNotificationTo
   */
  sendBalanceNotificationTo: {
    methodKind: "unary";
    input: typeof BalanceNotificationRequestSchema;
    output: typeof EmptySchema;
  },
  /**
   * @generated from rpc protos.Worker.SendRechargeNotificationTo
   */
  sendRechargeNotificationTo: {
    methodKind: "unary";
    input: typeof RechargeNotificationRequestSchema;
    output: typeof EmptySchema;
  },
  /**
   * @generated from rpc protos.Worker.SendConnectOfflineNotificationTo
   */
  sendConnectOfflineNotificationTo: {
    methodKind: "unary";
    input: typeof ConnectOfflineNotificationRequestSchema;
    output: typeof EmptySchema;
  },
  /**
   * @generated from rpc protos.Worker.SendConnectOnlineNotificationTo
   */
  sendConnectOnlineNotificationTo: {
    methodKind: "unary";
    input: typeof ConnectOnlineNotificationRequestSchema;
    output: typeof EmptySchema;
  },
  /**
   * @generated from rpc protos.Worker.SendUserInfoNotificationTo
   */
  sendUserInfoNotificationTo: {
    methodKind: "unary";
    input: typeof UserInfoNotificationRequestSchema;
    output: typeof EmptySchema;
  },
  /**
   * @generated from rpc protos.Worker.SendMemberInfoNotificationTo
   */
  sendMemberInfoNotificationTo: {
    methodKind: "unary";
    input: typeof MemberInfoNotificationRequestSchema;
    output: typeof EmptySchema;
  },
  /**
   * @generated from rpc protos.Worker.SendGrowthInfoNotificationTo
   */
  sendGrowthInfoNotificationTo: {
    methodKind: "unary";
    input: typeof GrowthInfoNotificationRequestSchema;
    output: typeof EmptySchema;
  },
  /**
   * @generated from rpc protos.Worker.SendMedalDoneNotificationTo
   */
  sendMedalDoneNotificationTo: {
    methodKind: "unary";
    input: typeof MedalDoneNotificationRequestSchema;
    output: typeof EmptySchema;
  },
  /**
   * @generated from rpc protos.Worker.SendNewMessageNotificationTo
   */
  sendNewMessageNotificationTo: {
    methodKind: "unary";
    input: typeof NewMessageNotificationRequestSchema;
    output: typeof EmptySchema;
  },
  /**
   * @generated from rpc protos.Worker.SendTaskAwardNotificationTo
   */
  sendTaskAwardNotificationTo: {
    methodKind: "unary";
    input: typeof TaskAwardNotificationRequestSchema;
    output: typeof EmptySchema;
  },
  /**
   * @generated from rpc protos.Worker.SendNotificationTo
   */
  sendNotificationTo: {
    methodKind: "unary";
    input: typeof NotificationRequestSchema;
    output: typeof EmptySchema;
  },
  /**
   * @generated from rpc protos.Worker.SendPartyGameNotificationTo
   */
  sendPartyGameNotificationTo: {
    methodKind: "unary";
    input: typeof NotificationRequestSchema;
    output: typeof EmptySchema;
  },
  /**
   * @generated from rpc protos.Worker.SendInviteAutoFollowNotificationTo
   */
  sendInviteAutoFollowNotificationTo: {
    methodKind: "unary";
    input: typeof InviteAutoFollowNotificationRequestSchema;
    output: typeof EmptySchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_worker, 0);

