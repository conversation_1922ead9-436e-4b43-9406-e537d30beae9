/**
 * @describe  音频管理
 * <AUTHOR>
 * @date 2023-08-03 17:54:31
 */
import { AudioSource, Component, director, log, Node } from 'cc'

import { AudioEffect } from './AudioEffect'
import { AudioMusic } from './AudioMusic'
import { EDITOR, EDITOR_NOT_IN_PREVIEW } from 'cc/env'
import { AudioEventConstant } from '@/core/business/constant'
import store from '@/core/business/store'
import { CustomPlatform } from '@/core/business/store/global'
import { Manager } from '../index'
import { BaseManager } from '../BaseManager'
import { autorun, makeAutoObservable } from 'mobx-tsuki'
import {
    AudioState,
    EnumJSBridgeWebView,
    JSBridgeClient,
    JSBridgeWebView,
} from '@/core/business/jsbridge/JSBridge'
import { GlobalEventConstant } from '../constant'

const LOCAL_STORE_KEY = 'game_audio'

/**
 * 音频管理
 */
export class AudioManager extends BaseManager {
    private local_data: any = {}

    private music: AudioMusic
    private effect: AudioEffect

    private _volume_music: number = 0.3
    private _volume_effect: number = 1
    private _switch_music: boolean = true
    private _switch_effect: boolean = true

    constructor(cat: Manager) {
        super(cat)

        var node = new Node('UIAudioManager')
        director.addPersistRootNode(node)

        var music = new Node('UIMusic')
        music.parent = node
        this.music = music.addComponent(AudioMusic).initAudio(cat)

        var effect = new Node('UIEffect')
        effect.parent = node
        this.effect = effect
            .addComponent(AudioEffect)
            .initAudio(cat)
            .preloadAll()

        if (store.global.customPlatform === CustomPlatform.SuiLeYoo) {
            JSBridgeWebView.on(
                EnumJSBridgeWebView.CHANGE_AUDIO_STATE,
                (res) => {
                    window.ccLog('SuiLeYoo音频状态改变', res)
                    this.switchEffect = this.switchMusic =
                        Number(res) === AudioState.ON
                },
                this
            )
        }

        cat.event.on(
            GlobalEventConstant.EVENT_HIDE,
            this.onHandleAppBackground,
            this
        )

        this.load()
    }

    onHandleAppBackground() {
        setTimeout(() => {
            if (!store.global.isSupportNativePlayAudio) {
                this.switchMusic && !this.music?.playing && this.music?.play()
                this.switchEffect &&
                    !this.effect?.playing &&
                    this.effect?.play()
            }
        }, 20)
    }

    /**
     * 设置背景音乐播放完成回调
     * @param callback 背景音乐播放完成回调
     */
    setMusicComplete(callback: Function | null = null) {
        this.music.onComplete = callback
    }

    /**
     * 播放背景音乐
     * @param url        资源地址
     * @param callback   音乐播放完成事件
     */
    playMusic(url: string, callback?: Function) {
        this.music.loop = true
        url &&
            this.music.load(url, () => {
                if (this._switch_music && this.music) {
                    if (
                        store.global.isSupportNativePlayAudio &&
                        this.music.clip
                    ) {
                        window.ccLog(
                            'AudioManager-->playMusic(), data:',
                            this.music.clip?.nativeUrl
                        )
                        JSBridgeClient.playMusic(this.music.clip.nativeUrl)
                    } else {
                        this.music?.play()
                    }
                }

                callback && callback()
            })
    }
    /**
     * 停止音乐
     */
    stopMusic() {
        window.ccLog('AudioManager-->stopMusic(),enter', this.music.state)
        if (!store.global.isSupportNativePlayAudio) {
            if (this.music.state != 0) {
                window.ccLog('AudioManager-->stopMusic(),music?.stop()')
                this.music?.stop()
            }
        }
    }
    /**暂停音乐 */
    pauseMusic() {
        window.ccLog('AudioManager-->pauseMusic(),enter', this.music.state)
        if (!store.global.isSupportNativePlayAudio) {
            if (this.music.state == 1) {
                window.ccLog('AudioManager-->pauseMusic(),music?.pause()')
                this.music?.pause()
            }
        }
    }
    /**恢复音乐 */
    resumeMusic() {
        window.ccLog('AudioManager-->resumeMusic(),enter', this.music.state)
        if (!store.global.isSupportNativePlayAudio) {
            if ([0, 2].includes(this.music.state)) {
                window.ccLog('AudioManager-->resumeMusic(),music?.play()')
                this.music?.play()
            }
        } else if (this.music.clip) {
            JSBridgeClient.playMusic(this.music.clip.nativeUrl)
        }
    }
    /**
     * 获取背景音乐播放进度
     */
    get progressMusic(): number {
        return this.music.progress
    }
    /**
     * 设置背景乐播放进度
     * @param value     播放进度值
     */
    set progressMusic(value: number) {
        this.music.progress = value
    }

    /**
     * 获取背景音乐音量
     */
    get volumeMusic(): number {
        return this._volume_music
    }
    /**
     * 设置背景音乐音量
     * @param value     音乐音量值
     */
    set volumeMusic(value: number) {
        this._volume_music = value
        this.music.volume = value
    }

    /**
     * 获取背景音乐开关值
     */
    get switchMusic(): boolean {
        return this._switch_music
    }
    /**
     * 设置背景音乐开关值
     * @param value     开关值
     */
    set switchMusic(value: boolean) {
        window.ccLog('AudioManager-->switchMusic():', value, this._switch_music)
        if (value == this._switch_music) return
        this._switch_music = value
        value ? this.resumeMusic() : this.pauseMusic()
        const nonce = value
            ? AudioEventConstant.MUSIC_ON
            : AudioEventConstant.MUSIC_OFF
        if (this.cat.event.has(nonce)) this.cat.event.dispatchEvent(nonce)
        this.save()
    }

    /**
     * 播放音效
     * @param url  资源地址
     */
    async playEffect(url: string): Promise<void> {
        if (!this._switch_effect || !url) {
            return
        }
        await this.effect.loadAndPlay(url)
    }
    /**
     * 停止音效
     */
    stopEffect() {
        this.effect?.stop()
    }

    /**
     * 获取音效音量
     */
    get volumeEffect(): number {
        return this._volume_effect
    }
    /**
     * 设置获取音效音量
     * @param value     音效音量值
     */
    set volumeEffect(value: number) {
        this._volume_effect = value
        this.effect.volume = value
    }

    /**
     * 获取音效开关值
     */
    get switchEffect(): boolean {
        return this._switch_effect
    }
    /**
     * 设置音效开关值
     * @param value     音效开关值
     */
    set switchEffect(value: boolean) {
        if (value == this._switch_effect) return
        this._switch_effect = value
        value ? this.effect?.play() : this.effect?.stop()

        const nonce = value
            ? AudioEventConstant.EFFECT_ON
            : AudioEventConstant.EFFECT_OFF
        if (this.cat.event.has(nonce)) this.cat.event.dispatchEvent(nonce)

        this.save()
    }

    /** 保存音乐音效的音量、开关配置数据到本地 */
    save() {
        this.local_data.volume_music = this._volume_music
        this.local_data.volume_effect = this._volume_effect
        this.local_data.switch_music = this._switch_music
        this.local_data.switch_effect = this._switch_effect

        if (store.global.customPlatform === CustomPlatform.SuiLeYoo) return
        let data = JSON.stringify(this.local_data)
        this.cat.storage.set(LOCAL_STORE_KEY, data)
    }

    /** 本地加载音乐音效的音量、开关配置数据并设置到游戏中 */
    load() {
        if (store.global.customPlatform === CustomPlatform.SuiLeYoo) {
            JSBridgeClient.getAudioState().then((res) => {
                window.ccLog('SuiLeYoo音频状态', res)
                this.switchEffect = this.switchMusic =
                    Number(res) === AudioState.ON
            })
            if (this.music) {
                this.music.volume = this._volume_music
            }
            if (this.effect) {
                this.effect.volume = this._volume_effect
            }
        } else {
            try {
                let data = this.cat.storage.get(LOCAL_STORE_KEY)
                this.local_data = JSON.parse(data)
                this._volume_music = this.local_data.volume_music
                this._volume_effect = this.local_data.volume_effect
                this._switch_music = this.local_data.switch_music
                this._switch_effect = this.local_data.switch_effect
            } catch (e) {
                this.local_data = {}
                this._volume_music = 0.6
                this._volume_effect = 1.0
                this._switch_music = true
                this._switch_effect = true
            }

            if (this.music) this.music.volume = this._volume_music
            if (this.effect) this.effect.volume = this._volume_effect
        }
    }
}
