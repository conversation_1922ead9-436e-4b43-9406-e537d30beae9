import { _decorator, Component, Label, log, Node, ProgressBar } from 'cc'
import { cat } from '@/core/manager'
import { GameEventConstant } from '@/core/business/constant'
import { BaseComponent } from '@/core/manager/gui/base/BaseComponent'

const { ccclass, property } = _decorator

type TimeCountProps = { time: number; progress: number }

@ccclass('TimeCount')
export class TimeCount extends BaseComponent<TimeCountProps> {
    @property({ type: ProgressBar, tooltip: '剩余时间进度' })
    time_progres: ProgressBar

    @property({ type: Label, tooltip: '剩余时间' })
    time_count: Label

    protected override onLoad(): void {}

    override onDestroy(): void {}

    protected override onEventListener(): void {
        cat.event.on(
            GameEventConstant.DRAW_SCOUT_TIME_OUT,
            (data: TimeCountProps) => {
                this.updateTime(data)
            },
            this
        )
    }

    updateTime(data: TimeCountProps) {
        this.time_progres.progress = data.progress
        this.time_count.string = `${data.time}s`
    }
}
