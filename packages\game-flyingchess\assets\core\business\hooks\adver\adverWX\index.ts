import { error, log } from 'cc'
import { cat } from '@/core/manager'

export default class AdverWX implements IAdver {
    interstitialAd: Map<number, WechatMinigame.InterstitialAd>

    rewardedVideoAd: WechatMinigame.RewardedVideoAd

    private interstitialAdCount: number = 1

    getRewardedVideoAdInstance() {
        return this.rewardedVideoAd
    }

    /**显示激励视频 */
    showRewardedVideoAd() {
        return new Promise<void>((resolve, reject) => {
            if (!this.rewardedVideoAd) {
                this.rewardedVideoAd = wx.createRewardedVideoAd({
                    adUnitId: 'adunit-76394f0e8cf4f036',
                    multiton: true,
                })
            }
            this.rewardedVideoAd.onLoad(() => {
                resolve()
            })
            this.rewardedVideoAd.onError((err) => {
                error(err)
                reject()
            })
            this.rewardedVideoAd.onClose((res) => {
                // 用户点击了【关闭广告】按钮
                if (res && res.isEnded) {
                    window.ccLog('正常播放结束')
                    // 正常播放结束，可以下发游戏奖励
                } else {
                    window.ccLog('播放中途退出')
                    cat.gui.showToast({
                        title: '需要完整观看视频才能获得奖励哦！',
                    })
                    // 播放中途退出，不下发游戏奖励
                }
            })
            this.rewardedVideoAd?.load()
        })
    }
    /**隐藏激励视频 */
    hideRewardedVideoAd() {
        this.rewardedVideoAd?.destroy()
    }
    /**显示插屏广告 */
    showInterstitialAd() {
        return new Promise<number>((resolve, reject) => {
            const _interstitialAd = wx.createInterstitialAd({
                adUnitId: 'adunit-363fbc552a67bdb6',
            })
            this.interstitialAdCount += 1
            this.interstitialAd.set(this.interstitialAdCount, _interstitialAd)
            _interstitialAd.onError((err) => {
                error(err)
                reject()
            })
            _interstitialAd.onClose((res) => {
                _interstitialAd.destroy()
            })
            _interstitialAd.onLoad(() => {
                resolve(this.interstitialAdCount)
            })
            _interstitialAd.load()
        })
    }
    hideInterstitialAd = (tag?: number) => {
        if (!tag) return
        const _interstitialAd = this.interstitialAd.get(tag)
        if (!tag) {
            this.interstitialAd.forEach((item) => {
                item?.destroy()
            })
        }
        _interstitialAd?.destroy()
    }
}
