{"__version__": "1.0.10", "macroConfig": {"ENABLE_MULTI_TOUCH": false}, "modules": {"cache": {"base": {"_value": true}, "gfx-webgl": {"_value": true}, "gfx-webgl2": {"_value": false}, "gfx-webgpu": {"_value": false}, "animation": {"_value": true}, "skeletal-animation": {"_value": true}, "3d": {"_value": true}, "meshopt": {"_value": false}, "2d": {"_value": true}, "xr": {"_value": false}, "ui": {"_value": true}, "particle": {"_value": false}, "physics": {"_value": true, "_option": "physics-ammo", "_flags": {"physics-ammo": {"LOAD_BULLET_MANUALLY": false}, "physics-physx": {"LOAD_PHYSX_MANUALLY": false}}}, "physics-ammo": {"_value": true, "_flags": {"physics-ammo": {"LOAD_BULLET_MANUALLY": false}}}, "physics-cannon": {"_value": false}, "physics-physx": {"_value": false, "_flags": {"physics-physx": {"LOAD_PHYSX_MANUALLY": false}}}, "physics-builtin": {"_value": false}, "physics-2d": {"_value": false, "_option": "physics-2d-box2d", "_flags": {"physics-2d-box2d-wasm": {"LOAD_BOX2D_MANUALLY": false}}}, "physics-2d-box2d": {"_value": true}, "physics-2d-box2d-wasm": {"_value": false, "_flags": {"physics-2d-box2d-wasm": {"LOAD_BOX2D_MANUALLY": false}}}, "physics-2d-builtin": {"_value": false}, "intersection-2d": {"_value": true}, "primitive": {"_value": true}, "profiler": {"_value": true}, "occlusion-query": {"_value": false}, "geometry-renderer": {"_value": false}, "debug-renderer": {"_value": false}, "particle-2d": {"_value": true}, "audio": {"_value": true}, "video": {"_value": false}, "webview": {"_value": false}, "tween": {"_value": true}, "websocket": {"_value": true}, "websocket-server": {"_value": false}, "terrain": {"_value": false}, "light-probe": {"_value": false}, "tiled-map": {"_value": false}, "vendor-google": {"_value": false}, "spine": {"_value": true, "_flags": {"spine": {"LOAD_SPINE_MANUALLY": false}}}, "dragon-bones": {"_value": false}, "marionette": {"_value": true}, "procedural-animation": {"_value": false}, "custom-pipeline-post-process": {"_value": false}, "render-pipeline": {"_value": true, "_option": "custom-pipeline"}, "custom-pipeline": {"_value": true}, "legacy-pipeline": {"_value": false}}, "flags": {}, "includeModules": ["2d", "3d", "animation", "audio", "base", "custom-pipeline", "gfx-webgl", "intersection-2d", "marionette", "particle-2d", "physics-ammo", "primitive", "profiler", "skeletal-animation", "spine", "tween", "ui", "websocket"], "noDeprecatedFeatures": {"value": false, "version": ""}}}