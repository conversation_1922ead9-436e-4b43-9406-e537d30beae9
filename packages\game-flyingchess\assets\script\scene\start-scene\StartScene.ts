import { _decorator, Component, Node } from 'cc'
import { cat } from '@/core/manager'
import InitialScene from '@/core/manager/gui/scene/InitialScene'

const { ccclass, property } = _decorator

/**
 * @describe 框架加载的第一个场景
 * <AUTHOR>
 * @date 2024-09-12 11:48:45
 */

@ccclass('StartScene')
export class StartScene extends InitialScene {
    protected override onLoaded(): void {
        console.log('loading')
        cat.gui.loadScene('loading')
    }
}
