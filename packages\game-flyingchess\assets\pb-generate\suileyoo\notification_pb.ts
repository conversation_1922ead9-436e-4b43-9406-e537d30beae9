// @generated by protoc-gen-es v2.2.5 with parameter "target=ts,import_extension=none,json_types=true"
// @generated from file notification.proto (package protos, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv1";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file notification.proto.
 */
export const file_notification: GenFile = /*@__PURE__*/
  fileDesc("ChJub3RpZmljYXRpb24ucHJvdG8SBnByb3RvcyI7CgxOb3RpZmljYXRpb24SDAoEQ29kZRgBIAEoDRIPCgdDb250ZW50GAIgASgJEgwKBERhdGEYAyABKAlCMAokY29tLnN0bnRzLmNsb3VkLnN1aWxleW9vLmVjaG8ucHJvdG9zWgguL3Byb3Rvc2IGcHJvdG8z");

/**
 * 通用通知消息
 *
 * @generated from message protos.Notification
 */
export type Notification = Message<"protos.Notification"> & {
  /**
   * 通知类型
   *
   * @generated from field: uint32 Code = 1;
   */
  Code: number;

  /**
   * 通知内容
   *
   * @generated from field: string Content = 2;
   */
  Content: string;

  /**
   * 通知数据
   *
   * @generated from field: string Data = 3;
   */
  Data: string;
};

/**
 * 通用通知消息
 *
 * @generated from message protos.Notification
 */
export type NotificationJson = {
  /**
   * 通知类型
   *
   * @generated from field: uint32 Code = 1;
   */
  Code?: number;

  /**
   * 通知内容
   *
   * @generated from field: string Content = 2;
   */
  Content?: string;

  /**
   * 通知数据
   *
   * @generated from field: string Data = 3;
   */
  Data?: string;
};

/**
 * Describes the message protos.Notification.
 * Use `create(NotificationSchema)` to create a new message.
 */
export const NotificationSchema: GenMessage<Notification, NotificationJson> = /*@__PURE__*/
  messageDesc(file_notification, 0);

