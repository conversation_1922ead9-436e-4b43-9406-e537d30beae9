import {
    _decorator,
    Component,
    Sprite,
    Sprite<PERSON>rame,
    ImageAsset,
    TextAsset,
    Asset,
} from 'cc'
import { plistMgr } from './plistMgr'
const { ccclass, property } = _decorator

@ccclass('plistImage')
export class plistImage extends Component {
    @property
    loop: boolean = false

    @property
    frameRate: number = 0.03

    @property({ type: ImageAsset, tooltip: 'PNG 图片资源' })
    pngAsset: ImageAsset = null!

    @property({ type: Asset, tooltip: 'Plist 文本资源' })
    plistAsset: TextAsset = null!

    @property
    autoPlay: boolean = false

    private frameIdx: number = 0
    public frames: (SpriteFrame | null)[] = []

    public remoteUrl: string = ''
    public localPngPath: string = ''
    public localPlistPath: string = ''

    private completeCallback: (() => void) | null = null

    protected override onLoad(): void {
        // 如果设置了自动播放且资源已设置，则自动加载并播放
        if (this.autoPlay && this.pngAsset && this.plistAsset) {
            this.loadDirectAssets(
                this.pngAsset,
                this.plistAsset,
                this.loop,
                this.frameRate
            )
        }
    }

    /**
     * 直接加载已设置的资源
     * @param pngAsset 图片资源
     * @param plistAsset plist资源
     * @param loop 是否循环播放
     * @param frameRate 帧率，默认为 0.03 秒/帧
     */
    public loadDirectAssets(
        pngAsset: ImageAsset,
        plistAsset: TextAsset,
        loop: boolean = false,
        frameRate?: number
    ): void {
        try {
            this.pngAsset = pngAsset
            this.plistAsset = plistAsset
            this.loop = loop

            if (frameRate !== undefined) {
                this.frameRate = frameRate
            }

            // 使用直接加载方法
            const atlas = plistMgr
                .getInstance()
                .loadAssetsDirectly(pngAsset, plistAsset)
            this.frames = atlas.spAtlas.getSpriteFrames()
            this.stop()
            this.play()
        } catch (error) {
            console.error('直接加载资源失败:', error)
        }
    }

    /**
     * 加载远程 plist 资源
     * @param url 远程资源 URL（不含扩展名）
     * @param loop 是否循环播放
     */
    async loadUrl(url: string, loop: boolean = false): Promise<void> {
        try {
            this.remoteUrl = url
            this.loop = loop
            const atlas = await plistMgr.getInstance().loadRemoteAssets(url)
            this.frames = atlas.spAtlas.getSpriteFrames()
            this.stop()
            this.play()
        } catch (error) {
            this.node.destroy()
            console.error('远程动画加载失败:', error)
        }
    }

    /**
     * 设置动画播放完成的回调函数
     * @param callback 播放完成时调用的回调函数
     */
    public setCompleteListener(callback: () => void): void {
        this.completeCallback = callback
    }

    /**
     * 播放动画
     */
    play(): void {
        if (this.frames.length) {
            if (this.frameIdx >= this.frames.length) {
                this.frameIdx = 0
                if (!this.loop) {
                    // 如果设置了完成回调，则调用回调
                    if (this.completeCallback) {
                        this.completeCallback()
                    }
                    this.node.destroy()
                    return
                }
            }
            const sprite = this.getComponent(Sprite)
            if (sprite) {
                sprite.spriteFrame = this.frames[this.frameIdx]
            }
            this.scheduleOnce(() => {
                this.play()
            }, this.frameRate)
            this.frameIdx++
        }
    }

    stop(): void {
        this.frameIdx = 0
        this.unscheduleAllCallbacks()
    }
}
