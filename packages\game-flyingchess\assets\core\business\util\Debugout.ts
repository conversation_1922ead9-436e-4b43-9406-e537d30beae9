import { safeStringify } from './StringUtil'

var __console__log = console.log
var __console__error = console.error
var __console__info = console.info
var __console__warn = console.warn

var debugoutDefaults: DebugoutOptions = {
    realTimeLoggingOn: true,
    useTimestamps: true,
    includeSessionMetadata: true,
    useLocalStorage: false,
    recordLogs: true,
    autoTrim: true,
    maxLines: 1000000,
    tailNumLines: 25,
    maxDepth: 20,
    logFilename: 'debugout.txt',
    localStorageKey: 'debugout.js',
    indent: '  ',
    quoteStrings: true,
}

interface DebugoutOptions {
    realTimeLoggingOn?: boolean
    useTimestamps?: boolean
    includeSessionMetadata?: boolean
    useLocalStorage?: boolean
    recordLogs?: boolean
    autoTrim?: boolean
    maxLines?: number
    tailNumLines?: number
    maxDepth?: number
    logFilename?: string
    localStorageKey?: string
    indent?: string
    quoteStrings?: boolean
}

interface SaveObject {
    startTime: Date
    log: string
    lastLog: Date
}

class Debugout {
    indent: string
    tailNumLines: number
    output: string
    realTimeLoggingOn: boolean
    useTimestamps: boolean
    includeSessionMetadata: boolean
    useLocalStorage: boolean
    recordLogs: boolean
    autoTrim: boolean
    maxLines: number
    maxDepth: number
    logFilename: string
    localStorageKey: string
    quoteStrings: boolean
    startTime: Date

    constructor(options: DebugoutOptions = {}) {
        var _this = this
        this.indent = '  '
        this.tailNumLines = 25
        this.output = '' // holds all logs

        // set options from defaults and passed options.
        var settings: DebugoutOptions = Object.assign(debugoutDefaults, options)
        for (var prop in settings) {
            if (settings[prop as keyof DebugoutOptions] !== undefined) {
                ;(this as any)[prop as keyof DebugoutOptions] =
                    settings[prop as keyof DebugoutOptions]
            }
        }
        // START/RESUME LOG
        if (this.useLocalStorage && window && !!window.localStorage) {
            var stored = this.load()
            if (stored) {
                this.output = stored.log
                this.startTime = new Date(stored.startTime)
                var end = new Date(stored.lastLog)
                this.logMetadata('Last session end: ' + stored.lastLog)
                this.logMetadata(
                    'Last ' + this.formatSessionDuration(this.startTime, end)
                )
                this.startLog()
            } else {
                this.startLog()
            }
        } else {
            this.useLocalStorage = false
            this.startLog()
        }
    }

    indentsForDepth(depth: number): string {
        return this.indent.repeat(Math.max(depth, 0))
    }

    startLog() {
        this.startTime = new Date()
        this.logMetadata('Session started: ' + this.formatDate(this.startTime))
    }
    // records a log
    recordLog(...args: any[]) {
        var _this = this
        // record log
        if (this.useTimestamps) {
            this.output += this.formatDate() + ' '
        }
        this.output += args
            .map(function (obj) {
                return _this.stringify(obj)
            })
            .join(' ')
        this.output += '#end#\n'
        if (this.autoTrim) this.output = this.trimLog(this.maxLines)
        if (this.useLocalStorage) {
            var saveObject = {
                startTime: this.startTime,
                log: this.output,
                lastLog: new Date(),
            }
            window.localStorage.setItem(
                this.localStorageKey,
                JSON.stringify(saveObject)
            )
        }
    }
    logMetadata(msg: string) {
        if (this.includeSessionMetadata)
            this.output += '---- ' + msg + ' ----\n'
    }
    // USER METHODS
    log(...args: any[]) {
        if (this.realTimeLoggingOn) __console__log.apply(console, args)
        if (this.recordLogs) this.recordLog.apply(this, args)
    }
    info(...args: any[]) {
        // tslint:disable-next-line:no-console
        if (this.realTimeLoggingOn) __console__info.apply(console, args)
        if (this.recordLogs) {
            this.output += '[INFO] '
            this.recordLog.apply(this, args)
        }
    }
    warn(...args: any[]) {
        if (this.realTimeLoggingOn) __console__warn.apply(console, args)
        if (this.recordLogs) {
            this.output += '[WARN] '
            this.recordLog.apply(this, args)
        }
    }
    error(...args: any[]) {
        if (this.realTimeLoggingOn) __console__error.apply(console, args)
        if (this.recordLogs) {
            this.output += '[ERROR] '
            this.recordLog.apply(this, args)
        }
    }
    debug(...args: any[]) {
        if (this.realTimeLoggingOn) console.debug.apply(console, args)
        if (this.recordLogs) {
            this.output += '[DEBUG] '
            this.recordLog.apply(this, args)
        }
    }
    getLog(): string {
        var retrievalTime = new Date()
        // if recording is off, so dev knows why they don't have any logs
        if (!this.recordLogs) {
            this.info('Log recording is off')
        }
        // if using local storage, get values
        if (this.useLocalStorage && window && window.localStorage) {
            var stored = this.load()
            if (stored) {
                this.startTime = new Date(stored.startTime)
                this.output = stored.log
            }
        }
        if (this.includeSessionMetadata) {
            return (
                this.output +
                ('---- ' +
                    this.formatSessionDuration(this.startTime, retrievalTime) +
                    ' ----\n')
            )
        }
        return this.output
    }
    // clears the log
    clear() {
        this.output = ''
        this.logMetadata('Session started: ' + this.formatDate(this.startTime))
        this.logMetadata('Log cleared ' + this.formatDate())
        if (this.useLocalStorage) this.save()
    }
    // gets last X number of lines
    tail(numLines?: number): string {
        var lines = numLines || this.tailNumLines
        return this.trimLog(lines)
    }
    // find occurences of your search term in the log
    search(term: string): string {
        var rgx = new RegExp(term, 'ig')
        var lines = this.output.split('\n')
        var matched: string[] = []
        // can't use a simple filter & map here because we need to add the line number
        for (var i = 0; i < lines.length; i++) {
            var addr = '[' + i + '] '
            if (lines[i].match(rgx)) {
                matched.push(addr + lines[i].trim())
            }
        }
        var result = matched.join('\n')
        if (!result.length) result = 'Nothing found for "' + term + '".'
        return result
    }
    // retrieve a section of the log. Works the same as js slice
    slice(...args: any[]): string {
        return this.output
            .split('\n')
            .slice(...args)
            .join('\n')
    }
    // downloads the log - for browser use
    downloadLog() {
        if (!!window) {
            var logFile = this.getLog()
            var blob = new Blob([logFile], {
                type: 'data:text/plain;charset=utf-8',
            })
            var a = document.createElement('a')
            a.href = window.URL.createObjectURL(blob)
            a.target = '_blank'
            a.download = this.logFilename
            document.body.appendChild(a)
            a.click()
            document.body.removeChild(a)
            window.URL.revokeObjectURL(a.href)
        } else {
            __console__error('downloadLog only works in the browser')
        }
    }
    // METHODS FOR CONSTRUCTING THE LOG
    save() {
        var saveObject = {
            startTime: this.startTime,
            log: this.output,
            lastLog: new Date(),
        }
        window.localStorage.setItem(
            this.localStorageKey,
            JSON.stringify(saveObject)
        )
    }
    load(): SaveObject | null {
        var saved = window.localStorage.getItem(this.localStorageKey)
        if (saved) {
            return JSON.parse(saved)
        }
        return null
    }
    determineType(object: any): string {
        if (object === null) {
            return 'null'
        } else if (object === undefined) {
            return 'undefined'
        } else {
            var type: string = typeof object
            if (type === 'object') {
                if (Array.isArray(object)) {
                    type = 'Array'
                } else {
                    if (object instanceof Date) {
                        type = 'Date'
                    } else if (object instanceof RegExp) {
                        type = 'RegExp'
                    } else if (object instanceof Debugout) {
                        type = 'Debugout'
                    } else {
                        type = 'Object'
                    }
                }
            }
            return type
        }
    }
    // recursively stringify object
    stringifyObject(obj: any, startingDepth: number = 0): string {
        if (obj instanceof Error) {
            return obj.toString()
        }
        try {
            return safeStringify(obj, this.indent)
        } catch (e) {
            return '[Circular Reference]'
        }
        // return JSON.stringify(obj, null, this.indent); // can't control depth/line-breaks/quotes
        var result = '{'
        var depth = startingDepth
        if (this.objectSize(obj) > 0) {
            result += '\n'
            depth++
            var i = 0
            for (var prop in obj) {
                result += this.indentsForDepth(depth)
                result += prop + ': '
                var subresult = this.stringify(obj[prop], depth)
                if (subresult) {
                    result += subresult
                }
                if (i < this.objectSize(obj) - 1) result += ','
                result += '\n'
                i++
            }
            depth--
            result += this.indentsForDepth(depth)
        }
        result += '}'
        return result
    }
    // recursively stringify array
    stringifyArray(arr: any[], startingDepth: number = 0): string {
        // return JSON.stringify(arr, null, this.indent); // can't control depth/line-breaks/quotes
        var result = '['
        var depth = startingDepth
        var lastLineNeedsNewLine = false
        if (arr.length > 0) {
            depth++
            for (var i = 0; i < arr.length; i++) {
                var subtype = this.determineType(arr[i])
                var needsNewLine = false
                if (subtype === 'Object' && this.objectSize(arr[i]) > 0)
                    needsNewLine = true
                if (subtype === 'Array' && arr[i].length > 0)
                    needsNewLine = true
                if (!lastLineNeedsNewLine && needsNewLine) result += '\n'
                var subresult = this.stringify(arr[i], depth)
                if (subresult) {
                    if (needsNewLine) result += this.indentsForDepth(depth)
                    result += subresult
                    if (i < arr.length - 1) result += ', '
                    if (needsNewLine) result += '\n'
                }
                lastLineNeedsNewLine = needsNewLine
            }
            depth--
        }
        result += ']'
        return result
    }
    // pretty-printing functions is a lib unto itself - this simply prints with indents
    stringifyFunction(fn: Function, startingDepth: number = 0): string {
        var _this = this
        var depth = startingDepth
        return String(fn)
            .split('\n')
            .map(function (line) {
                if (line.match(/\}/)) depth--
                var val = _this.indentsForDepth(depth) + line.trim()
                if (line.match(/\{/)) depth++
                return val
            })
            .join('\n')
    }
    // stringify any data
    stringify(obj: any, depth: number = 0): string {
        if (depth >= this.maxDepth) {
            return '... (max-depth reached)'
        }
        var type = this.determineType(obj)
        switch (type) {
            case 'Object':
                return this.stringifyObject(obj, depth)
            case 'Array':
                return this.stringifyArray(obj, depth)
            case 'function':
                return this.stringifyFunction(obj, depth)
            case 'RegExp':
                return '/' + obj.source + '/' + obj.flags
            case 'Date':
            case 'string':
                return this.quoteStrings ? '"' + obj + '"' : obj + ''
            case 'boolean':
                return obj ? 'true' : 'false'
            case 'number':
                return obj + ''
            case 'null':
            case 'undefined':
                return type
            case 'Debugout':
                return '... (Debugout)' // prevent endless loop
            default:
                return '?'
        }
    }
    trimLog(maxLines: number): string {
        var lines = this.output.split('\n')
        lines.pop()
        if (lines.length > maxLines) {
            lines = lines.slice(lines.length - maxLines)
        }
        return lines.join('\n') + '\n'
    }
    // no type args: typescript doesn't think dates can be subtracted but they can
    formatSessionDuration(startTime: Date, endTime: Date): string {
        var msec = endTime.getTime() - startTime.getTime()
        var hh = Math.floor(msec / 1000 / 60 / 60)
        var hrs = ('0' + hh).slice(-2)
        msec -= hh * 1000 * 60 * 60
        var mm = Math.floor(msec / 1000 / 60)
        var mins = ('0' + mm).slice(-2)
        msec -= mm * 1000 * 60
        var ss = Math.floor(msec / 1000)
        var secs = ('0' + ss).slice(-2)
        msec -= ss * 1000
        return 'Session duration: ' + hrs + ':' + mins + ':' + secs
    }
    formatDate(ts: Date = new Date()): string {
        return '[' + ts.toISOString() + ']'
    }
    objectSize(obj: any): number {
        var size = 0
        try {
            for (var key in obj) {
                if (obj.hasOwnProperty(key)) size++
            }
        } catch (e) {}
        return size
    }

    report(logName: string) {
        const content = this.getLog()

        fetch('https://cocos-game-dev.mityoo.com/log', {
            mode: 'cors',
            headers: {
                Accept: 'application/json',
                'Content-Type': 'application/json',
            },
            method: 'POST',
            body: JSON.stringify({
                fileName: logName,
                content,
            }),
        })
            .catch((e) => {})
            .finally(() => {})
    }
    request(type: string, url: string, params: any): Promise<any> {
        return new Promise((resolve) => {
            var httpRequest = new XMLHttpRequest() //第一步：创建需要的对象
            httpRequest.open(type, url, true) //第二步：打开连接
            httpRequest.timeout = 10000
            // httpRequest.setRequestHeader("Content-type","application/json");//设置请求头 注：post方式必须设置请求头（在建立连接后设置请求头）

            // httpRequest.send('acid='+ that.acid + '&data={serial:'+ that.serial + '}&timestamp='+ new Date().getTime());//发送请求 将情头体写在send中
            httpRequest.send(JSON.stringify(params)) //发送请求 将情头体写在send中

            //     dataType:"json",
            //
            //     data: JSON.stringify(game_data),
            /**
             * 获取数据后的处理程序
             */
            httpRequest.onreadystatechange = function () {
                //请求后的回调接口，可将请求成功后要执行的程序写在其中

                if (httpRequest.readyState == 4 && httpRequest.status == 200) {
                    //验证请求是否发送成功
                    var json = httpRequest.responseText //获取到服务端返回的数据

                    if (json == 'ERROR_PUT_SUCCESS') {
                        resolve(json)
                    } else {
                        resolve(JSON.parse(json))
                    }
                } else {
                    // reject(httpRequest.status)
                }
            }
        })
    }
}

export default Debugout
