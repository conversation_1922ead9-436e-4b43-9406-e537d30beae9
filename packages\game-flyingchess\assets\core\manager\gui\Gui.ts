/**
 * @describe GUI组件类
 * <AUTHOR>
 * @date 2024-09-12 11:46:22
 */

import {
    _decorator,
    asset<PERSON><PERSON><PERSON>,
    <PERSON><PERSON>,
    Component,
    Director,
    director,
    error,
    instantiate,
    Layers,
    Node,
    Prefab,
    Scene,
    SceneAsset,
    warn,
} from 'cc'
import { BaseComponent, IUIOption } from './base/BaseComponent'
import SceneLayer from './scene/SceneLayer'
import UILayer, { ICloseOptions, IOpenOptions } from './layer/UILayer'
import { Toast, ToastProps, ToastType } from '../../components/toast/Toast'
import { GlobalEventConstant } from '../constant'
import { UIContainer } from './components/UIContainer'
import { Manager } from '../index'
import {
    ShowLoading,
    ShowLoadingProps,
} from '../../ui/show-loading/ShowLoading'
import {
    Reconnection,
    ReconnectPrompt,
} from '../../ui/reconnection/Reconnection'
import { Notice, NoticeProps } from '../../ui/notice/Notice'
import store from '@/core/business/store'
import { exportLog } from './exportLog'

const { ccclass, property } = _decorator

type UIComponentType<T> = T extends UILayer<infer U, infer K>
    ? { props: U; data: K }
    : { props: object; data: object }

type PropsType<T> = UIComponentType<T> extends {
    props: infer U extends object
    data: infer K extends object
}
    ? U
    : never
type DataType<T> = UIComponentType<T> extends {
    props: infer U extends object
    data: infer K extends object
}
    ? K
    : never

export enum LayerType {
    UI,
    /**加载 */
    LOADING,
    /**提示 */
    TOAST,
    /**断线重连 */
    RECONNECTTION,
    /**系统服务通知(停机维护) */
    NOTICE,
}

/**Gui层 */
@ccclass('Gui')
export class Gui extends BaseComponent {
    @property({ type: Prefab, tooltip: '断线重连UI预制体' })
    reconnection_ui_prefab: Prefab

    @property({ type: Prefab, tooltip: '提示UI预制体' })
    toast_ui_prefab: Prefab

    @property({ type: Prefab, tooltip: '加载UI预制体' })
    loading_ui_prefab: Prefab

    @property({ type: Prefab, tooltip: '公告UI预制体' })
    notice_ui_prefab: Prefab

    @property({ type: Prefab, tooltip: 'UI层预制体' })
    ui_prefab: Prefab

    @property({ type: Prefab, tooltip: '导出日志预制体' })
    exportLogEntry_prefab: Prefab

    @property({ type: Node, tooltip: 'root-UI层' })
    root_ui: Node

    @property({ type: Node, tooltip: 'root-组件层' })
    root_toast: Node

    @property({ type: Node, tooltip: 'log调试组件层' })
    root_log: Node

    @property({ type: Node, tooltip: 'root-mask' })
    root_mask: Node

    private cat: Manager

    /**场景中的UI层 */
    private ui_container_component: UIContainer | null

    /**断线重连UI弹窗 */
    private reconnection_ui_component: Reconnection | null

    /**公告UI弹窗组件 */
    private notice_ui_component: Notice

    /**加载UI弹窗 */
    private loading_ui_component: ShowLoading | null

    /**提示UI弹窗 */
    private toast_ui_component: Toast

    /**当前场景对象名 */
    public currentScene: string

    protected override onEventListener(): void {
        this.cat.event.on(
            GlobalEventConstant.ROOT_MASK_UPDATE,
            this.onRootUpdate,
            this
        )
    }

    init(cat: Manager) {
        this.cat = cat
        const scene = director.getScene()

        this.root_log.active = store.global.isDebugOrTestEnv

        window.ccLog('init scene')
        scene && this.changeScene(scene)
        return this
    }

    protected override start(): void {
        this.onRootUpdate()
    }

    /**显示提示 */
    showToast({
        title = '',
        type = ToastType.SLIDE,
        fixed_time = 2.0,
    }: ToastProps) {
        const node = instantiate(this.toast_ui_prefab)
        if (!title) debugger
        this.toast_ui_component = node
            .getComponent(Toast)!
            .addToParent(this.root_toast, {
                props: {
                    title,
                    type,
                    fixed_time,
                },
            })
        return this
    }

    /**隐藏提示 */
    hideToast() {
        this.toast_ui_component?.removeAndDestroy()
        return this
    }

    /**显示Loading */
    showLoading({
        title = '',
        mask = true,
        black = true,
    }: ShowLoadingProps = {}) {
        if (this.loading_ui_component) {
            this.loading_ui_component.setOptions({
                props: {
                    title,
                    mask,
                    black,
                },
            })
        } else {
            const node = instantiate(this.loading_ui_prefab)
            this.loading_ui_component = node
                .getComponent(ShowLoading)!
                .addToParent(this.root_ui, {
                    props: {
                        title,
                        mask,
                        black,
                    },
                })
        }
        return this
    }

    /**隐藏Loading */
    hideLoading() {
        this.loading_ui_component?.removeAndDestroy()
        this.loading_ui_component = null
        return this
    }

    /**
     * 显示断线重连
     * @param option 状态提示文案
     */
    showReconnect(option: ReconnectPrompt) {
        if (!this.reconnection_ui_component) {
            const node = instantiate(this.reconnection_ui_prefab)
            this.reconnection_ui_component = node
                .getComponent(Reconnection)!
                .addToParent(this.root_ui, {
                    props: {
                        content: option,
                    },
                })
        } else {
            this.reconnection_ui_component.setUpdateProps({
                content: option,
            })
        }
        return this
    }

    /**隐藏断线重连 */
    hideReconnect() {
        this.reconnection_ui_component?.removeAndDestroy()
        this.reconnection_ui_component = null
        return this
    }

    /**
     * 显示公告
     * @param option 参数
     */
    showNotice(option: NoticeProps) {
        const node = instantiate(this.notice_ui_prefab)
        this.notice_ui_component = node
            .getComponent(Notice)!
            .addToParent(this.root_ui, { props: option })
        return this
    }

    /**隐藏公告 */
    hideNotice() {
        this.notice_ui_component?.removeAndDestroy()
        return this
    }

    /**加载场景 */
    loadScene<T>(sceneName: string): Promise<void>
    loadScene<T>(
        sceneName: string,
        options: IUIOption<PropsType<T>, DataType<T>>
    ): Promise<void>
    loadScene<T>(sceneName: string, isReload: boolean): Promise<void>
    loadScene<T>(
        sceneName: string,
        options: IUIOption<PropsType<T>, DataType<T>>,
        isReload: boolean
    ): Promise<void>
    // 方法实现
    loadScene<T>(
        sceneName: string,
        options?: IUIOption<PropsType<T>, DataType<T>> | boolean,
        isReload?: boolean
    ) {
        return new Promise<void>((resolve, reject) => {
            window.ccLog('加载场景', sceneName, options, isReload)
            let _options: IUIOption<PropsType<T>, DataType<T>> | undefined = {}
            let _isReload: boolean = isReload ?? false
            if (typeof options === 'boolean') {
                _isReload = options
            } else {
                _options = options ?? {}
            }

            window.ccLog(director.getScene()?.uuid, director.getScene()?.name)
            director.once(
                Director.EVENT_BEFORE_SCENE_LAUNCH,
                (scene: Scene) => {
                    window.ccLog('Director.EVENT_BEFORE_SCENE_LAUNCH', scene)
                    this.changeScene(scene, _options, _isReload)
                    resolve()
                }
            )

            director.loadScene(
                sceneName,
                (err: null | Error, scene?: Scene) => {
                    if (err || !scene) {
                        reject(err)
                    }
                }
            )
        })
    }

    /**重置场景(清除当前默认当前场景) */
    resetScene(sceneName: string = '') {
        sceneName = sceneName || this.currentScene
        return this.loadScene(sceneName)
    }

    /**清除场景 */
    async cleanScene() {
        // 清理全局UI
        await this.resetScene()
        this.hideLoading().hideNotice().hideReconnect().hideToast()
    }

    /**
     * 场景变化
     * @param scene 场景
     * @param options 选项
     * @param isReload 是否重新加载
     */
    changeScene<T>(
        scene: Scene,
        options?: IUIOption<PropsType<T>, DataType<T>>,
        isReload?: boolean
    ) {
        this.currentScene = scene.name

        // 创建UI层
        this.createUILayer(scene)

        // 给场景传递属性值
        const crrentScene = scene.getComponentInChildren(SceneLayer)

        if (crrentScene) {
            crrentScene.isReload = isReload ?? false
        }

        // 更新场景props
        crrentScene?.setOptions(options)
    }

    private createUILayer(scene: Scene) {
        this.ui_container_component?.removeAndDestroy()
        this.ui_container_component = null

        const node = instantiate(this.ui_prefab)
        this.ui_container_component = node
            .getComponent(UIContainer)!
            .setGui(this)!
            .addToParent(scene)
    }

    /**重新加载当前场景 */
    reloadScene() {
        this.loadScene(this.currentScene, true)
    }

    /**关闭UI */
    async closeUI<T extends UILayer<object>, U extends object>(
        ui: Node | T,
        options?: ICloseOptions<T, U>
    ) {
        window.ccLog('closeUI', ui.name)
        const { component } = this.findUIBaseLayer(ui, false)
        if (component) {
            component.setOptions({
                ...(options?.hook ?? {}),
                ...(options?.props ?? {}),
            })
            // 调用uiNode组件下的showTween方法
            await component.hideTween<T, U>(options || { isMotion: false })
            // 移除层级遮罩
            this.ui_container_component?.subMask()
        }
    }

    /**
     * 打开ui层
     * @param ui 界面的节点或组件
     * @param options 动画参数选项
     */

    async openUI<T extends UILayer>(
        ui: Node,
        options?: IOpenOptions<PropsType<T>, DataType<T>>
    ) {
        // 查询根节点
        const { rootNode, component } = this.findUIBaseLayer(ui, true)
        // 获取根组件
        const uilayer = rootNode.getComponent(UILayer)
        // 配置选项
        component?.setOptions(options)

        // 动画
        if (rootNode) {
            // 添加层级遮罩
            this.ui_container_component?.addNodeToTween(rootNode)
            // 始终添加遮罩，但传入noMask参数控制透明度
            this.ui_container_component?.addMask(options?.noMask)
        }

        if (component) {
            // 调用uiNode组件下的showTween方法
            await component.showTween(options || {})
        }
        // 运动结束之后，添加界面到UI层
        this.ui_container_component?.ui_container &&
            component?.addToParent(this.ui_container_component.ui_container)
    }

    /**根据节点或组件 查询根UI层(继承自UILayer的)节点或组件 */
    private findUIBaseLayer<T extends UILayer>(
        ui: Node | T,
        isOpen: boolean
    ): { rootNode: Node; component: T | null } {
        let rootNode: Node
        let component: T | null = null
        if (ui instanceof Node) {
            // ui为节点
            rootNode = ui
            // 获取组件
            const extendsBaseLayer = ui.components.filter((item) => {
                return item instanceof UILayer
            }) as T[]
            if (extendsBaseLayer.length) {
                if (extendsBaseLayer.length == 1) {
                    component = extendsBaseLayer[0]
                } else {
                    warn(`${ui.name}节点存在多个继承自BaseLayer的组件`)
                }
            } else {
                error(`${ui.name}节点未找到继承自BaseLayer的组件`)
                return { rootNode, component }
            }
        } else {
            // ui为T组件
            // 定义一个变量用于保存根节点
            rootNode = ui.node
            component = ui
            if (isOpen) {
                // 循环向上查找根节点，直到找到没有父节点的节点
                while (rootNode.parent) {
                    rootNode = rootNode.parent
                }
                component.root = rootNode
            } else {
                rootNode = ui.root
            }
        }
        return { rootNode, component }
    }

    /**根层的变化 */
    private onRootUpdate() {
        window.ccLog('onRootUpdate')
        // 如果根ui节点 没有子节点 则隐藏根遮罩
        const len = this.root_ui.children.length
        this.root_mask.active = len > 1
        if (len > 1) {
            // 更新根遮罩层级
            const last = this.root_ui.children[len - 2]
            this.root_mask.setSiblingIndex(last.getSiblingIndex())
        }
        // 通知ui层级变化
        this.cat.event.dispatchEvent(GlobalEventConstant.ROOT_MASK_CHANGE)
    }
}
