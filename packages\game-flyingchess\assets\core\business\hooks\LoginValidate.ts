/**登录验证 */

import { log } from 'cc'
import store from '../store'
import { cat } from '@/core/manager'

export function LoginValidation() {
    return function (target: any, key: string, descriptor: PropertyDescriptor) {
        const originalMethod = descriptor.value

        descriptor.value = function (...args: any[]) {
            const { login } = store
            if (login.isLogin) {
                // 用户已登录，执行原始方法
                return originalMethod.apply(this, args)
            } else {
                // 用户未登录，可以选择执行其他操作或不执行任何操作
                window.ccLog(
                    'User is not logged in. Skipping method execution.'
                )
                cat.gui.showToast({ title: '请先登录' })
            }
        }

        return descriptor
    }
}
