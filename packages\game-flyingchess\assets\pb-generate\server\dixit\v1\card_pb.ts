// @generated by protoc-gen-es v2.4.0 with parameter "target=ts,json_types=true"
// @generated from file dixit/v1/card.proto (package dixit.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv1";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file dixit/v1/card.proto.
 */
export const file_dixit_v1_card: GenFile = /*@__PURE__*/
  fileDesc("ChNkaXhpdC92MS9jYXJkLnByb3RvEghkaXhpdC52MSIjCghDYXJkSW5mbxIKCgJpZBgBIAEoCRILCgN1cmwYAiABKAliBnByb3RvMw");

/**
 * CardInfo 卡牌信息
 *
 * @generated from message dixit.v1.CardInfo
 */
export type CardInfo = Message<"dixit.v1.CardInfo"> & {
  /**
   * 卡牌ID
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: string url = 2;
   */
  url: string;
};

/**
 * CardInfo 卡牌信息
 *
 * @generated from message dixit.v1.CardInfo
 */
export type CardInfoJson = {
  /**
   * 卡牌ID
   *
   * @generated from field: string id = 1;
   */
  id?: string;

  /**
   * @generated from field: string url = 2;
   */
  url?: string;
};

/**
 * Describes the message dixit.v1.CardInfo.
 * Use `create(CardInfoSchema)` to create a new message.
 */
export const CardInfoSchema: GenMessage<CardInfo, CardInfoJson> = /*@__PURE__*/
  messageDesc(file_dixit_v1_card, 0);

