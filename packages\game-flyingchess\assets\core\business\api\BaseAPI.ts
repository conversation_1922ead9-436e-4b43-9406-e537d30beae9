/**
 * @describe
 * <AUTHOR>
 * @date 2024-09-12 11:35:02
 */

import ky, { HTTPError, KyInstance, Options } from 'ky'
import { Manager } from '@/core/manager'
import { getCommonSign } from '../hooks/api-sign/CommonSign'
import { attachSign } from '../hooks/api-sign/SuileyooSign'
import { error, log } from 'cc'

interface IResponse<T> {
    code: number // 请求消息码
    message: string // 错误信息
    data: T // 输出数据
    success: boolean // 请求成功标识
}

export class BaseAPI {
    protected cat: Manager

    protected base: KyInstance

    constructor(cat: Manager) {
        this.cat = cat

        this.base = ky.create({
            prefixUrl: this.cat.env.http_base_url,
            retry: {
                limit: 10,
                backoffLimit: 3000,
            },
            hooks: {
                beforeRequest: [
                    async (request) => {
                        const body = await request.json()
                        const new_body = attachSign(body ?? {}) as Record<
                            string,
                            string
                        >
                        request.headers.set(
                            'Content-type',
                            'application/x-www-form-urlencoded'
                        )
                        window.ccLog(
                            `%c HTTP请求地址 %c ${request.method} %c ${request.url}`,
                            'background:#35495e ; padding: 1px; border-radius: 3px 0 0 3px;  color: #fff',
                            'background:#41b883 ; padding: 1px; border-radius: 0 3px 3px 0;  color: #fff',
                            'background:transparent'
                        )
                        return new Request(request, {
                            body: new URLSearchParams(new_body),
                        })
                    },
                ],
                afterResponse: [
                    async (_request, _options, response) => {
                        window.ccLog(_request, _options, response)
                        if (response.ok) {
                            const data =
                                (await response.json()) as IResponse<void>
                            if (data.code !== 0 && data?.message) {
                                this.cat.gui.showToast({
                                    title: `${data?.message}`,
                                })
                            }
                        }
                    },
                ],
                beforeError: [
                    (error) => {
                        const { response } = error
                        if (response && response.body) {
                            window.ccLog('err', response)
                            this.cat.gui.showToast({
                                title: `REQUEST ERROR:${response.status}${
                                    response.statusText ?? ''
                                }`,
                            })
                        }
                        return error
                    },
                ],
            },
        })
    }

    async api<T>(url: string, options: Options) {
        // try {
        const res = (await this.base
            .extend(options)(url)
            .json()) as IResponse<T>
        return res.data
        // } catch (err) {
        //     if (err instanceof HTTPError) {
        //         if (err.name === 'HTTPError') {
        //             const errorJson = await err.response.json();
        //             error(errorJson)
        //             this.cat.gui.showToast({
        //                 title: `${errorJson.status}:${errorJson.error}${errorJson.statusText ?? ''}`,
        //             });
        //         }
        //     }
        // }
    }
}
