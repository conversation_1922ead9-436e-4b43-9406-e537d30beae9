import { cat } from '@/core/manager'
import {
    _decorator,
    tween,
    v3,
    Node,
    Button,
    EditBox,
    Sprite,
    SpriteFrame,
    Label,
} from 'cc'
import { sleep } from '@/core/business/util/TimeUtils'
import store from '@/core/business/store'
import { CommonDialog } from '../CommonDialog/CommonDialog'
import { CardItem, cardSizeMap } from '../../components/card/CardItem'
import { audioEffect, buttonLock } from '@/core/business/hooks/Decorator'
import { GameEventConstant } from '@/core/business/constant'

const { ccclass, property } = _decorator

export interface StorytellingDialogProps {}

@ccclass('StorytellingDialog')
export class StorytellingDialog extends CommonDialog<StorytellingDialogProps> {
    @property({ type: Node, tooltip: '卡牌容器' })
    card_container: Node = null!

    @property({ type: Node, tooltip: '输入框节点' })
    input_node: Node = null!

    @property({ type: EditBox, tooltip: '输入框' })
    input_editbox: EditBox

    @property({ type: Button, tooltip: '确定按钮' })
    btn_confirm: Button

    @property({ type: Sprite, tooltip: '点击卡牌放大' })
    enlarge_sprite: Sprite

    @property({ type: [SpriteFrame], tooltip: '放大缩小精灵图集' })
    sprite_frame: SpriteFrame[] = []

    @property({ type: Label, tooltip: 'AI讲故事' })
    ai_label: Label

    @property({ type: Label, tooltip: '统计输入字数' })
    count_label: Label

    private cardItem: CardItem | null = null
    private isSmallMode: boolean = true // 是否小图模式
    private usedAiCount: number = 0 //是否用过ai
    private MAX_AI_COUNT = 3
    private aiTypingTimerId: number | null = null // AI打字动画的定时器ID
    private isDestroyed: boolean = false // 组件是否已销毁的标志
    private aiStorys: string[] //从服务器获取

    override props: StorytellingDialogProps = {}

    protected override initUI(): void {
        super.initUI()
        this.initCards()
        this.initEvents()
    }

    protected override onAutoObserver(): void {}

    /**
     * 组件销毁时清除定时器
     */
    protected override onDestroy(): void {
        // 设置销毁标志，用于终止 typeNextChar 的调用
        this.isDestroyed = true
        this.usedAiCount = 0

        this.terminateAIInput()
    }

    private onTextChanged() {
        const storyString = this.input_editbox.string
        this.count_label.string = `${storyString.length}/60`
    }

    //清除AI打字动画定时器
    private terminateAIInput() {
        if (this.aiTypingTimerId !== null) {
            clearTimeout(this.aiTypingTimerId)
            this.aiTypingTimerId = null
            console.log('StorytellingDialog--> 清除AI打字动画定时器')
        }
    }

    private initCards(): void {
        const selectedCardId = store.game.speakerSelectedCard
        console.log('StorytellingDialog--> selectedCardId:', selectedCardId)

        const cardNode = cat.cardPool.get()
        this.cardItem = cardNode.getComponent(CardItem)

        if (this.cardItem) {
            this.cardItem.setOptions({
                props: {
                    cardId: selectedCardId!,
                    isSwitch: false,
                    isSelected: false,
                },
            })

            cardNode.setParent(this.card_container)
            cardNode.setScale(cardSizeMap.M)
            cardNode.setPosition(v3(0, 0, 0))
        }
    }

    private initEvents() {
        if (!this.cardItem) return

        this.cardItem.node.on(Node.EventType.TOUCH_START, () => {
            this.onCardClick()
        })

        // 添加确认按钮点击事件
        this.btn_confirm.node.on(Node.EventType.TOUCH_START, () => {
            this.onConfirm()
        })

        this.ai_label.node.on(Node.EventType.TOUCH_START, () => {
            this.onAiClick()
        })

        this.input_editbox.node.on('text-changed', this.onTextChanged, this)
    }

    @audioEffect()
    private onCardClick() {
        console.log(
            'StorytellingDialog--> onCardClick,isSmallMode',
            this.isSmallMode
        )
        if (this.isSmallMode) {
            //当前是小图模式，点击后放大，并且显示输入框和确定按钮
            const card_item_y = this.cardItem?.node.position.y || 0

            tween(this.cardItem?.node)
                .to(0.2, {
                    scale: cardSizeMap.XXL,
                    position: v3(0, card_item_y - 150, 0),
                })
                .start()

            const enlarge_sprite_y = this.enlarge_sprite.node.position.y
            this.enlarge_sprite.node.setPosition(
                v3(0, enlarge_sprite_y - 350, 0)
            )
            this.enlarge_sprite.spriteFrame = this.sprite_frame[1]

            this.input_node.active = false
            this.btn_confirm.node.active = false

            this.isSmallMode = false
        } else {
            //当前是大图模式，点击后缩小，并且隐藏输入框和确定按钮
            const card_item_y = this.cardItem?.node.position.y || 0
            tween(this.cardItem?.node)
                .to(0.2, {
                    scale: cardSizeMap.M,
                    position: v3(0, card_item_y + 150, 0),
                })
                .call(() => {
                    this.input_node.active = true
                    this.btn_confirm.node.active = true
                })
                .start()

            const enlarge_sprite_y = this.enlarge_sprite.node.position.y
            this.enlarge_sprite.node.setPosition(
                v3(0, enlarge_sprite_y + 350, 0)
            )
            this.enlarge_sprite.spriteFrame = this.sprite_frame[0]

            this.isSmallMode = true
        }
    }

    @audioEffect()
    @buttonLock(0.5)
    private onConfirm() {
        // 获取输入的故事并去除前后空格
        this.terminateAIInput()
        const storyString = this.input_editbox.string

        // 判断去除空格后的长度
        // if (storyString.length < 1) {
        //     cat.gui.showToast({ title: '故事长度不能少于1个字符！' })
        //     return
        // }

        console.log('storytellingDialog-->onConfirm,story:', storyString)

        // 提交故事
        cat.ws
            .postStory({
                story: storyString, // 使用去除空格后的故事
                type: 0,
            })
            .then((res) => {
                console.log('storytellingDialog--> 讲故事提交成功', res)
                // this.close()
            })
    }

    @audioEffect()
    @buttonLock(0.5)
    private async onAiClick() {
        console.log('storytellingDialog-->onAiClick')
        this.usedAiCount++

        if (this.usedAiCount >= this.MAX_AI_COUNT) {
            this.ai_label.node.active = false
        }

        if (!this.aiStorys) {
            const response = await cat.ws.genStory({})
            this.aiStorys = [...response.stories].sort(
                () => Math.random() - 0.5
            )
            console.log(
                'storytellingDialog-->onAiClick,this.aiStorys:',
                this.aiStorys
            )
        }

        // 为了演示，我们使用一个示例文本
        // const ai_result =
        //     '这是一个神奇的故事，讲述了一个勇敢的小鸭子如何战胜困难，找到了自己的家。'

        // 调用模拟打字效果方法
        const ai_result = this.aiStorys[this.usedAiCount - 1]
        if (ai_result) {
            this.simulateAIInput(ai_result)
        } else {
            console.log(
                'storytellingDialog-->onAiClick,t his.aiStorys[this.usedAiCount - 1] is empty'
            )
        }
    }

    /**
     * 模拟用户打字效果，一个字一个字地显示 AI 生成的文本
     * @param ai_result AI 生成的文本结果
     */
    private simulateAIInput(ai_result: string) {
        // 清空当前输入框内容
        this.input_editbox.string = ''

        // 如果 AI 结果为空，直接返回
        if (!ai_result || ai_result.length === 0) {
            return
        }

        // 禁用输入框，防止用户在AI模拟打字时输入
        this.input_editbox.enabled = false

        // 计算每个字符的显示间隔时间
        // // 目标是在 3-5 秒内完成整个文本的显示
        // const totalDuration = 3000 + Math.random() * 2000 // 3000-5000ms
        //const intervalPerChar = totalDuration / ai_result.length

        let intervalPerChar = 0
        if (ai_result.length < 25) {
            intervalPerChar = 1000 / 8
        } else if (ai_result.length < 40) {
            intervalPerChar = 1000 / 10
        } else {
            intervalPerChar = 1000 / 12
        }

        // 当前已显示的字符索引
        let currentIndex = 0

        // 清除可能存在的之前的定时器
        if (this.aiTypingTimerId !== null) {
            clearTimeout(this.aiTypingTimerId)
            this.aiTypingTimerId = null
        }

        // 创建一个函数来逐字显示文本
        const typeNextChar = () => {
            // 如果组件已销毁，则终止执行并重新启用输入框
            if (this.isDestroyed) {
                this.input_editbox.enabled = true
                console.log(
                    'StorytellingDialog--> 组件已销毁，终止 AI 输入模拟'
                )
                return
            }

            if (currentIndex < ai_result.length) {
                // 添加下一个字符
                this.input_editbox.string += ai_result.charAt(currentIndex)
                currentIndex++

                // 设置下一个字符的显示定时器
                this.aiTypingTimerId = setTimeout(typeNextChar, intervalPerChar)
            } else {
                // 重新启用输入框
                this.input_editbox.enabled = true
            }
            this.count_label.string = `${currentIndex}/60`
        }

        // 开始逐字显示
        typeNextChar()
    }
}
