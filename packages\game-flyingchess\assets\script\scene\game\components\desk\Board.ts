import { _decorator, Component, Node, Vec3, UITransform } from 'cc'
import { BaseComponent } from '@/core/manager/gui/base/BaseComponent'

const { ccclass, property } = _decorator

// 玩家位置枚举
export enum PlayerPosition {
    YELLOW = 0, // 黄色玩家（左上角）
    BLUE = 1, // 蓝色玩家（右上角）
    RED = 2, // 红色玩家（右下角）
    GREEN = 3, // 绿色玩家（左下角）
}

// 棋盘坐标数据结构
export interface BoardCoordinates {
    // 四个角的用户头像位置
    playerAvatarPositions: Vec3[]
    // 每个用户起飞区的四个棋子位置
    playerStartPositions: Vec3[][]
    // 棋盘格子位置（按顺序排列，从每个玩家的起点开始）
    boardGridPositions: Vec3[]
    // 中心安全区位置
    safeZonePositions: Vec3[][]
}

@ccclass('Board')
export class Board extends BaseComponent {
    private boardSize: number = 0
    private gridSize: number = 0
    private coordinates: BoardCoordinates | null = null

    protected override onLoad(): void {
        this.initializeBoardCoordinates()
    }

    override onDestroy(): void {}

    /**
     * 初始化棋盘坐标
     * 在棋盘初始化时调用此方法来动态计算所有关键元素的坐标
     */
    public initializeBoardCoordinates(): void {
        const transform = this.getComponent(UITransform)
        if (!transform) {
            console.error('Board组件缺少UITransform组件')
            return
        }

        this.boardSize = Math.min(transform.width, transform.height)
        this.gridSize = this.boardSize / 15 // 棋盘15x15网格

        this.coordinates = {
            playerAvatarPositions: this.calculatePlayerAvatarPositions(),
            playerStartPositions: this.calculatePlayerStartPositions(),
            boardGridPositions: this.calculateBoardGridPositions(),
            safeZonePositions: this.calculateSafeZonePositions(),
        }

        console.log('棋盘坐标初始化完成', this.coordinates)
    }

    /**
     * 计算四个角的用户头像位置
     */
    private calculatePlayerAvatarPositions(): Vec3[] {
        const positions: Vec3[] = []
        const offset = this.boardSize * 0.4 // 头像距离中心的偏移量

        // 黄色玩家（左上角）
        positions[PlayerPosition.YELLOW] = new Vec3(-offset, offset, 0)
        // 蓝色玩家（右上角）
        positions[PlayerPosition.BLUE] = new Vec3(offset, offset, 0)
        // 红色玩家（右下角）
        positions[PlayerPosition.RED] = new Vec3(offset, -offset, 0)
        // 绿色玩家（左下角）
        positions[PlayerPosition.GREEN] = new Vec3(-offset, -offset, 0)

        return positions
    }

    /**
     * 计算每个用户起飞区的四个棋子位置
     */
    private calculatePlayerStartPositions(): Vec3[][] {
        const allStartPositions: Vec3[][] = []
        const startAreaOffset = this.boardSize * 0.35
        const pieceSpacing = this.gridSize * 0.8

        for (let player = 0; player < 4; player++) {
            const playerPositions: Vec3[] = []
            let baseX = 0,
                baseY = 0

            // 根据玩家位置确定起飞区基础坐标
            switch (player) {
                case PlayerPosition.YELLOW: // 左上角
                    baseX = -startAreaOffset
                    baseY = startAreaOffset
                    break
                case PlayerPosition.BLUE: // 右上角
                    baseX = startAreaOffset
                    baseY = startAreaOffset
                    break
                case PlayerPosition.RED: // 右下角
                    baseX = startAreaOffset
                    baseY = -startAreaOffset
                    break
                case PlayerPosition.GREEN: // 左下角
                    baseX = -startAreaOffset
                    baseY = -startAreaOffset
                    break
            }

            // 计算2x2排列的四个棋子位置
            for (let i = 0; i < 4; i++) {
                const row = Math.floor(i / 2)
                const col = i % 2
                const x = baseX + (col - 0.5) * pieceSpacing
                const y = baseY - (row - 0.5) * pieceSpacing
                playerPositions.push(new Vec3(x, y, 0))
            }

            allStartPositions.push(playerPositions)
        }

        return allStartPositions
    }

    /**
     * 计算棋盘格子位置
     * 按照飞行棋规则，每个玩家有52个可移动格子
     */
    private calculateBoardGridPositions(): Vec3[] {
        const positions: Vec3[] = []
        const centerOffset = this.boardSize * 0.2
        const gridSpacing = this.gridSize

        // 外圈轨道坐标计算
        // 从黄色玩家起点开始，顺时针计算52个格子

        // 黄色玩家起点到蓝色玩家起点（右上方向）
        for (let i = 0; i < 6; i++) {
            positions.push(
                new Vec3(-centerOffset + i * gridSpacing, centerOffset, 0)
            )
        }

        // 蓝色区域右侧向下
        for (let i = 1; i < 6; i++) {
            positions.push(
                new Vec3(centerOffset, centerOffset - i * gridSpacing, 0)
            )
        }

        // 蓝色玩家起点到红色玩家起点（右下方向）
        for (let i = 1; i < 6; i++) {
            positions.push(
                new Vec3(centerOffset - i * gridSpacing, -centerOffset, 0)
            )
        }

        // 红色区域下侧向左
        for (let i = 1; i < 6; i++) {
            positions.push(
                new Vec3(-centerOffset, -centerOffset + i * gridSpacing, 0)
            )
        }

        // 红色玩家起点到绿色玩家起点（左下方向）
        for (let i = 1; i < 6; i++) {
            positions.push(
                new Vec3(-centerOffset + i * gridSpacing, centerOffset, 0)
            )
        }

        // 继续完成整个外圈...
        // 这里简化处理，实际需要完整的52个格子坐标

        return positions
    }

    /**
     * 计算中心安全区位置
     */
    private calculateSafeZonePositions(): Vec3[][] {
        const allSafeZones: Vec3[][] = []
        const centerGridSpacing = this.gridSize * 0.8

        for (let player = 0; player < 4; player++) {
            const safeZone: Vec3[] = []

            // 每个玩家有5个安全区格子，从外圈通向中心
            for (let i = 0; i < 5; i++) {
                let x = 0,
                    y = 0

                switch (player) {
                    case PlayerPosition.YELLOW:
                        x = -centerGridSpacing * (4 - i)
                        y = 0
                        break
                    case PlayerPosition.BLUE:
                        x = 0
                        y = centerGridSpacing * (4 - i)
                        break
                    case PlayerPosition.RED:
                        x = centerGridSpacing * (4 - i)
                        y = 0
                        break
                    case PlayerPosition.GREEN:
                        x = 0
                        y = -centerGridSpacing * (4 - i)
                        break
                }

                safeZone.push(new Vec3(x, y, 0))
            }

            allSafeZones.push(safeZone)
        }

        return allSafeZones
    }

    /**
     * 获取棋盘坐标数据
     */
    public getCoordinates(): BoardCoordinates | null {
        return this.coordinates
    }

    /**
     * 获取指定玩家的头像位置
     */
    public getPlayerAvatarPosition(player: PlayerPosition): Vec3 | null {
        return this.coordinates?.playerAvatarPositions[player] || null
    }

    /**
     * 获取指定玩家的起飞区棋子位置
     */
    public getPlayerStartPositions(player: PlayerPosition): Vec3[] | null {
        return this.coordinates?.playerStartPositions[player] || null
    }

    /**
     * 获取棋盘格子位置
     */
    public getBoardGridPosition(index: number): Vec3 | null {
        return this.coordinates?.boardGridPositions[index] || null
    }

    /**
     * 获取指定玩家的安全区位置
     */
    public getPlayerSafeZonePositions(player: PlayerPosition): Vec3[] | null {
        return this.coordinates?.safeZonePositions[player] || null
    }
}
