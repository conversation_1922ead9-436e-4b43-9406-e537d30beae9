// @generated by protoc-gen-es v2.2.5 with parameter "target=ts,import_extension=none,json_types=true"
// @generated from file member.proto (package protos, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv1";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file member.proto.
 */
export const file_member: GenFile = /*@__PURE__*/
  fileDesc("CgxtZW1iZXIucHJvdG8SBnByb3RvcyIyCg5NZW1iZXJJbmZvSXRlbRIMCgR0eXBlGAEgASgJEhIKCmV4cGlyZWRfYXQYAiABKAkiTgoWTWVtYmVySW5mb05vdGlmaWNhdGlvbhINCgV1c2luZxgBIAEoCRIlCgVJdGVtcxgCIAMoCzIWLnByb3Rvcy5NZW1iZXJJbmZvSXRlbUIwCiRjb20uc3RudHMuY2xvdWQuc3VpbGV5b28uZWNoby5wcm90b3NaCC4vcHJvdG9zYgZwcm90bzM");

/**
 * @generated from message protos.MemberInfoItem
 */
export type MemberInfoItem = Message<"protos.MemberInfoItem"> & {
  /**
   * 会员类型
   *
   * @generated from field: string type = 1;
   */
  type: string;

  /**
   * 过期时间
   *
   * @generated from field: string expired_at = 2;
   */
  expiredAt: string;
};

/**
 * @generated from message protos.MemberInfoItem
 */
export type MemberInfoItemJson = {
  /**
   * 会员类型
   *
   * @generated from field: string type = 1;
   */
  type?: string;

  /**
   * 过期时间
   *
   * @generated from field: string expired_at = 2;
   */
  expiredAt?: string;
};

/**
 * Describes the message protos.MemberInfoItem.
 * Use `create(MemberInfoItemSchema)` to create a new message.
 */
export const MemberInfoItemSchema: GenMessage<MemberInfoItem, MemberInfoItemJson> = /*@__PURE__*/
  messageDesc(file_member, 0);

/**
 * 账户资产变动通知
 *
 * @generated from message protos.MemberInfoNotification
 */
export type MemberInfoNotification = Message<"protos.MemberInfoNotification"> & {
  /**
   * 当前会员类型，vip | 标准会员，hvip | 高级会员，"" | 非会员
   *
   * @generated from field: string using = 1;
   */
  using: string;

  /**
   * 会员身份列表
   *
   * @generated from field: repeated protos.MemberInfoItem Items = 2;
   */
  Items: MemberInfoItem[];
};

/**
 * 账户资产变动通知
 *
 * @generated from message protos.MemberInfoNotification
 */
export type MemberInfoNotificationJson = {
  /**
   * 当前会员类型，vip | 标准会员，hvip | 高级会员，"" | 非会员
   *
   * @generated from field: string using = 1;
   */
  using?: string;

  /**
   * 会员身份列表
   *
   * @generated from field: repeated protos.MemberInfoItem Items = 2;
   */
  Items?: MemberInfoItemJson[];
};

/**
 * Describes the message protos.MemberInfoNotification.
 * Use `create(MemberInfoNotificationSchema)` to create a new message.
 */
export const MemberInfoNotificationSchema: GenMessage<MemberInfoNotification, MemberInfoNotificationJson> = /*@__PURE__*/
  messageDesc(file_member, 1);

