import { _decorator, Button, Label, log, tween } from 'cc'

import store from '@/core/business/store'
import UILayer from '@/core/manager/gui/layer/UILayer'

import { audioEffect } from '@/core/business/hooks/Decorator'
import { cat } from '@/core/manager'
import RootUILayer from '@/core/manager/gui/layer/RootUILayer'

const { ccclass, property } = _decorator

export enum ReconnectPrompt {
    RECONNECTED = '重连成功',
    RECONNECTING = '正在重连',
    MAX_RECONNECT = '重连次数超出限制,请检查网络',
    RECONNECTED_ERROR = '重连失败,请检查网络',
    CONNECT_PARAM_ERROR = '游戏参数错误,请重新加载',
    KICK = '账号已下线',
    OFFLINE = '网络已断开',
    ONLINE = '网络已连接',
    GAME_ERROR = '连接游戏服错误',
}

type ReconnectionProps = {
    content: ReconnectPrompt | null
}

/**
 * @describe 重连框
 * <AUTHOR>
 * @date 2024-09-12 11:49:51
 */

@ccclass('Reconnection')
export class Reconnection extends RootUILayer<ReconnectionProps> {
    @property({ type: Label, tooltip: '通用提示文本' })
    common_prompt_text: Label

    @property({ type: Button, tooltip: '确定按钮' })
    btn_confirm: Button

    private is_close: boolean = false

    override props: ReconnectionProps = {
        content: null,
    }

    protected override onLoad(): void {
        this.btn_confirm.node.on(
            Button.EventType.CLICK,
            this.onConfirmHandler,
            this
        )

        this.addAutorun([
            () => {
                this.common_prompt_text.string = this.props.content ?? ''
            },
        ])
    }

    override onDestroy(): void {
        this.unscheduleAllCallbacks()
        this.is_close && cat.platform.back()
    }

    updateProps(props: ReconnectPrompt): void {
        this.updatePromptText(props, props == ReconnectPrompt.RECONNECTING)
    }

    /**更新提示文案 */
    protected updatePromptText(text: ReconnectPrompt, isAnim: boolean = false) {
        // 停止之前的动画效果
        this.unscheduleAllCallbacks()
        this.props.content = text
        window.ccLog('更新提示文案:', text)

        // 如果需要应用动画效果
        if (isAnim) {
            let index = 0
            const updateText = () => {
                index = (index + 1) % 4
                this.common_prompt_text.string =
                    text + ['', '·', '··', '···'][index]
            }
            this.schedule(updateText, 0.5)
        }
    }

    @audioEffect()
    private onConfirmHandler() {
        this.is_close = true
        // 销毁ws
        // cat.ws.destroy()
        // gui.close(this)
        cat.gui.hideReconnect()
    }
}
