import { Vec2, v2 } from 'cc'

type Without<T, U> = { [P in Exclude<keyof T, keyof U>]?: never }

export type XOR<T, U> = (Without<T, U> & U) | (Without<U, T> & T)

/**手牌显示状态 */
export enum HandCardStatus {
    SHOW,
    HIDE,
}

export type Single = 4 | 5 | 6

const head_y_pos = -100

/**玩家初始位置， 在一行上 */
export const GameModelSinglePosMap: Record<Single, Vec2[]> = {
    4: [
        v2(-195, head_y_pos),
        v2(-65, head_y_pos),
        v2(65, head_y_pos),
        v2(195, head_y_pos),
    ],
    5: [
        v2(-260, head_y_pos),
        v2(-130, head_y_pos),
        v2(0, head_y_pos),
        v2(130, head_y_pos),
        v2(260, head_y_pos),
    ],
    6: [
        v2(-320, head_y_pos),
        v2(-195, head_y_pos),
        v2(-65, head_y_pos),
        v2(65, head_y_pos),
        v2(195, head_y_pos),
        v2(320, head_y_pos),
    ],
}

//头像默认位置
export const HeadNodeDefaultPosMap: Record<6, Vec2[]> = {
    6: [
        v2(-440, 260),
        v2(-440, -80),
        v2(-440, -420),
        v2(440, 260),
        v2(440, -80),
        v2(440, -420),
    ],
}

//卡牌位置， 假设卡牌200*300
const card_top_y_pos = 830
const card_bot_y_pos = 500
export const CardNodeSinglePosMap: Record<6, Vec2[]> = {
    //横向间隔40，纵向间隔30
    6: [
        v2(-240, card_top_y_pos),
        v2(0, card_top_y_pos),
        v2(240, card_top_y_pos),
        v2(-240, card_bot_y_pos),
        v2(0, card_bot_y_pos),
        v2(240, card_bot_y_pos),
    ],
}

//卡牌缩略图时的位置
export const CardNodeThumbSinglePosMap: Record<6, Vec2[]> = {
    //横向间隔40，纵向间隔30
    6: [
        v2(-363, 0),
        v2(-218, 0),
        v2(-73, 0),
        v2(73, 0),
        v2(218, 0),
        v2(363, 0),
    ],
}

/**单人模式 */
export const GameSingle: Record<Single, string> = {
    4: '1V1V1V1',
    5: '1V1V1V1V1',
    6: '1V1V1V1V1V1',
}

export type ServerType = 'Game' | 'Tourist' | null

/**游戏关闭类型 */
export enum GameCloseType {
    /**加入游戏超时 */
    JoinOverTime = 'join_over_time',
    /**结束 */
    GameOver = 'game_over',
}
