import { _decorator, CCFloat, CCInteger, Component, Enum, Label, Node, Sprite } from 'cc';

const { ccclass, property } = _decorator;

/**
 *  row（默认值）：主轴为水平方向，起点在左端。
    row-reverse：主轴为水平方向，起点在右端。
    column：主轴为垂直方向，起点在上沿。
    column-reverse：主轴为垂直方向，起点在下沿。
 */
enum FlexDirectionEnum {
    None = 0,
    Row,
    Row_Reverse,
    Column,
    Column_Reverse,
}

enum FlexWrapEnum {
    None = 0,
    Nowrap,
    Wrap,
    WrapReverse
}

/**
 *  flex-start（默认值）：左对齐
    flex-end：右对齐
    center： 居中
    space-between：两端对齐，项目之间的间隔都相等。
    space-around：每个项目两侧的间隔相等。所以，项目之间的间隔比项目与边框的间隔大一倍。
 */
enum JustifyContentEnum {
    None = 0,
    FlexStart,
    FlexEnd,
    Center,
    SpaceBetween,
    SpaceAround
}

/**
 *  flex-start：与交叉轴的起点对齐。
    flex-end：与交叉轴的终点对齐。
    center：与交叉轴的中点对齐。
    space-between：与交叉轴两端对齐，轴线之间的间隔平均分布。
    space-around：每根轴线两侧的间隔都相等。所以，轴线之间的间隔比轴线与边框的间隔大一倍。
    stretch（默认值）：轴线占满整个交叉轴
 */
enum AlignContentEnum {
    None = 0,
    FlexStart,
    FlexEnd,
    Center,
    SpaceBetween,
    SpaceAround,
    Stretch
}

/**
 *  flex-start：交叉轴的起点对齐。
    flex-end：交叉轴的终点对齐。
    center：交叉轴的中点对齐。
    baseline: 项目的第一行文字的基线对齐。
    stretch（默认值）：如果项目未设置高度或设为auto，将占满整个容器的高度。
 */
enum AlignItemEnum {
    None = 0,
    FlexStart,
    FlexEnd,
    Center,
    Baseline,
    Stretch
}

@ccclass('Flex')
export class Flex extends Component {

    @property({
        group: {
            name: 'container', id: '1', displayOrder: 1, style: 'section',
        }, tooltip: `[TODO] 属性决定主轴的方向（即项目的排列方向）。
        row（默认值）：主轴为水平方向，起点在左端。
        row- reverse：主轴为水平方向，起点在右端。
        column：主轴为垂直方向，起点在上沿。
        column - reverse：主轴为垂直方向，起点在下沿。`, type: Enum(FlexDirectionEnum)
    })
    flex_direction: FlexDirectionEnum = FlexDirectionEnum.None;

    @property({
        group: {
            name: 'container', id: '1',
        }, tooltip: `[TODO] 默认情况下，项目都排在一条线（又称"轴线"）上。flex-wrap属性定义，如果一条轴线排不下，如何换行。
        nowrap（默认）：不换行。
        wrap：换行，第一行在上方。
        wrap-reverse：换行，第一行在下方。`, type: Enum(FlexWrapEnum)
    })
    flex_wrap: FlexWrapEnum = FlexWrapEnum.None;

    @property({
        group: {
            name: 'container', id: '1',
        }, tooltip: `[TODO]属性定义了项目在主轴上的对齐方式。
         flex-start（默认值）：左对齐
         flex-end：右对齐
         center： 居中
         space-between：两端对齐，项目之间的间隔都相等。
         space-around：每个项目两侧的间隔相等。所以，项目之间的间隔比项目与边框的间隔大一倍。`, type: Enum(JustifyContentEnum)
    })
    justify_content: JustifyContentEnum = JustifyContentEnum.None;

    @property({
        group: {
            name: 'container', id: '1',
        }, tooltip: `[TODO] 属性定义了多根轴线的对齐方式。如果项目只有一根轴线，该属性不起作用。
            flex-start：与交叉轴的起点对齐。
            flex-end：与交叉轴的终点对齐。
            center：与交叉轴的中点对齐。
            space-between：与交叉轴两端对齐，轴线之间的间隔平均分布。
            space-around：每根轴线两侧的间隔都相等。所以，轴线之间的间隔比轴线与边框的间隔大一倍。
            stretch（默认值）：轴线占满整个交叉轴。`
        , type: Enum(AlignContentEnum)
    })
    align_content: AlignContentEnum = AlignContentEnum.None;



    @property({
        group: {
            name: 'container', id: '1',
        }, tooltip: `[TODO] 属性定义了多根轴线的对齐方式。如果项目只有一根轴线，该属性不起作用。
            flex-start：交叉轴的起点对齐。
            flex-end：交叉轴的终点对齐。
            center：交叉轴的中点对齐。
            baseline: 项目的第一行文字的基线对齐。
            stretch（默认值）：如果项目未设置高度或设为auto，将占满整个容器的高度。`
        , type: Enum(AlignItemEnum)
    })
    align_items: AlignItemEnum = AlignItemEnum.None;

    @property({
        group: {
            name: 'container', id: '1',
        }, tooltip: `上内边距`
        , type: CCFloat
    })
    Padding_top = 0
    @property({
        group: {
            name: 'container', id: '1',
        }, tooltip: `下内边距`
        , type: CCFloat
    })
    Padding_bottom = 0
    @property({
        group: {
            name: 'container', id: '1',
        }, tooltip: `左内边距`
        , type: CCFloat
    })
    Padding_left = 0

    @property({
        group: {
            name: 'container', id: '1',
        }, tooltip: `右内边距`
        , type: CCFloat
    })
    Padding_right = 0



    @property({ group: { name: 'item', id: '2', style: 'section' }, type: CCInteger })
    order = null!;

    protected override onLoad(): void {
    }

    override onDestroy(): void {

    }


}


