// @generated by protoc-gen-es v2.2.5 with parameter "target=ts,json_types=true"
// @generated from file pirate/v1/message.proto (package pirate.v1, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile } from "@bufbuild/protobuf/codegenv1";
import { enumDesc, fileDesc } from "@bufbuild/protobuf/codegenv1";

/**
 * Describes the file pirate/v1/message.proto.
 */
export const file_pirate_v1_message: GenFile = /*@__PURE__*/
  fileDesc("ChdwaXJhdGUvdjEvbWVzc2FnZS5wcm90bxIJcGlyYXRlLnYxKtkICgtNZXNzYWdlVHlwZRISCg5NVF9VTlNQRUNJRklFRBAAEhoKFk1UX0dBTUVfSU5GT19CUk9BRENBU1QQARIeChpNVF9TVEFURV9DSEFOR0VEX0JST0FEQ0FTVBACEhgKFE1UX1JBTktJTkdfQlJPQURDQVNUEAMSHAoYTVRfQ0xJRU5UX0RBVEFfQlJPQURDQVNUEAQSGgoWTVRfR0FNRV9PVkVSX0JST0FEQ0FTVBAFEhwKGE1UX1NFUlZFUl9EQVRBX0JST0FEQ0FTVBAGEhoKFk1UX1BMQVlFUl9JTkZPX01FU1NBR0UQChIhCh1NVF9QTEFZRVJfRVhJVF9HQU1FX0JST0FEQ0FTVBAMEioKJk1UX1BMQVlFUl9DT05ORUNUSU9OX0NIQU5HRURfQlJPQURDQVNUEA0SIAocTVRfUExBWUVSX0RSQVdOX0NBUkRfTUVTU0FHRRAVEiMKH01UX1BMQVlFUl9QT1NURURfQ0FSRF9CUk9BRENBU1QQFhIrCidNVF9QTEFZRVJfQUZURVJfU0VMRUNUX1RBUkdFVF9CUk9BRENBU1QQFxIoCiRNVF9QTEFZRVJfU0VMRUNUQUJMRV9UQVJHRVRTX01FU1NBR0UQGBIiCh5NVF9QTEFZX0hPU1RJTkdfRFJBV19CUk9BRENBU1QQGxIaChZNVF9DQVJEX1BFRUtfQlJPQURDQVNUEB8SGwoXTVRfQ0FSRF9TVEVBTF9CUk9BRENBU1QQIBIgChxNVF9DQVJEX1NUUkVOR1RIRU5fQlJPQURDQVNUECESHQoZTVRfQ0FSRF9ERUZFTlNFX0JST0FEQ0FTVBAiEiAKHE1UX0NBUkRfU1VCU1RJVFVURV9CUk9BRENBU1QQIxIcChhNVF9DQVJEX1dFQUtFTl9CUk9BRENBU1QQJBIcChhNVF9DQVJEX0ZSRUVaRV9CUk9BRENBU1QQJRIaChZNVF9DQVJEX1NXQVBfQlJPQURDQVNUECYSHgoaTVRfQ0FSRF9BRlRFUl9QRUVLX01FU1NBR0UQKRIfChtNVF9DQVJEX0FGVEVSX1NURUFMX01FU1NBR0UQKhIiCh5NVF9DQVJEX0FGVEVSX0ZSRUVaRV9CUk9BRENBU1QQKxIcChhNVF9DQVJEX1JFVklWRV9CUk9BRENBU1QQLBIkCiBNVF9DQVJEX0FDVElWRV9ERUZFTlNFX0JST0FEQ0FTVBAtEicKI01UX0NBUkRfQUNUSVZFX1NVQlNUSVRVVEVfQlJPQURDQVNUEC4SKAokTVRfQ0FSRF9TVFJFTkdUSEVOX0lOVkFMSURfQlJPQURDQVNUEC8SHgoaTVRfQ0FSRF9BRlRFUl9TV0FQX01FU1NBR0UQMBIoCiRNVF9DQVJEX1NUUkVOR1RIRU5fU1VDQ0VTU19CUk9BRENBU1QQMRIhCh1NVF9DQVJEX0RSQVdOX0RFQVRIX0JST0FEQ0FTVBAyYgZwcm90bzM");

/**
 * - Broadcast 表示所有玩家都是同样的消息
 * - Message 表示每个玩家的消息有部分数据不一样
 *
 * @generated from enum pirate.v1.MessageType
 */
export enum MessageType {
  /**
   * buf:lint:ignore ENUM_VALUE_UPPER_SNAKE_CASE
   * buf:lint:ignore ENUM_VALUE_PREFIX
   * 占位 要默认加个0，否则前端有问题
   *
   * @generated from enum value: MT_UNSPECIFIED = 0;
   */
  MT_UNSPECIFIED = 0,

  /**
   * ----- 游戏基础相关消息 -----
   * 游戏所有信息，广播：GameInfoBroadcast
   *
   * @generated from enum value: MT_GAME_INFO_BROADCAST = 1;
   */
  MT_GAME_INFO_BROADCAST = 1,

  /**
   * 游戏状态机改变，广播：GameInfoBroadcast
   *
   * @generated from enum value: MT_STATE_CHANGED_BROADCAST = 2;
   */
  MT_STATE_CHANGED_BROADCAST = 2,

  /**
   * 排行榜，广播：RankingBroadcast
   *
   * @generated from enum value: MT_RANKING_BROADCAST = 3;
   */
  MT_RANKING_BROADCAST = 3,

  /**
   * 客户端自定义的数据广播，广播：ClientDataBroadcast
   *
   * @generated from enum value: MT_CLIENT_DATA_BROADCAST = 4;
   */
  MT_CLIENT_DATA_BROADCAST = 4,

  /**
   * 游戏结束，广播：GameOverBroadcast
   *
   * @generated from enum value: MT_GAME_OVER_BROADCAST = 5;
   */
  MT_GAME_OVER_BROADCAST = 5,

  /**
   * 服务端自定义的数据广播，广播：ServerDataBroadcast
   *
   * @generated from enum value: MT_SERVER_DATA_BROADCAST = 6;
   */
  MT_SERVER_DATA_BROADCAST = 6,

  /**
   * ----- 玩家状态相关消息 -----
   * 玩家信息，消息：PlayerInfoMessage
   *
   * @generated from enum value: MT_PLAYER_INFO_MESSAGE = 10;
   */
  MT_PLAYER_INFO_MESSAGE = 10,

  /**
   * 玩家退出游戏，广播：PlayerStatusChangedBroadcast
   *
   * @generated from enum value: MT_PLAYER_EXIT_GAME_BROADCAST = 12;
   */
  MT_PLAYER_EXIT_GAME_BROADCAST = 12,

  /**
   * 玩家上下线广播，广播：PlayerStatusChangedBroadcast
   *
   * @generated from enum value: MT_PLAYER_CONNECTION_CHANGED_BROADCAST = 13;
   */
  MT_PLAYER_CONNECTION_CHANGED_BROADCAST = 13,

  /**
   * ----- 玩家主动操作相关消息 -----
   * 玩家摸了牌的消息，消息：PlayerDrawnCardMessage
   *
   * @generated from enum value: MT_PLAYER_DRAWN_CARD_MESSAGE = 21;
   */
  MT_PLAYER_DRAWN_CARD_MESSAGE = 21,

  /**
   * 玩家出了牌的消息，消息：PlayerPostedCardBroadcast
   *
   * @generated from enum value: MT_PLAYER_POSTED_CARD_BROADCAST = 22;
   */
  MT_PLAYER_POSTED_CARD_BROADCAST = 22,

  /**
   * 玩家选择了目标的消息，消息：PlayerAfterSelectTargetBroadcast
   *
   * @generated from enum value: MT_PLAYER_AFTER_SELECT_TARGET_BROADCAST = 23;
   */
  MT_PLAYER_AFTER_SELECT_TARGET_BROADCAST = 23,

  /**
   * 玩家可选的目标玩家列表的消息，消息：PlayerSelectableTargetsMessage
   *
   * @generated from enum value: MT_PLAYER_SELECTABLE_TARGETS_MESSAGE = 24;
   */
  MT_PLAYER_SELECTABLE_TARGETS_MESSAGE = 24,

  /**
   * 玩家托管状态下摸牌的广播，广播：PlayerHostingDrawnCardMessage
   *
   * @generated from enum value: MT_PLAY_HOSTING_DRAW_BROADCAST = 27;
   */
  MT_PLAY_HOSTING_DRAW_BROADCAST = 27,

  /**
   * ----- 玩家主动出牌的消息（暂时不用） -----
   * 玩家出【透视】的广播，消息：CardBroadcast
   *
   * @generated from enum value: MT_CARD_PEEK_BROADCAST = 31;
   */
  MT_CARD_PEEK_BROADCAST = 31,

  /**
   * 玩家出【索取】的广播，消息：CardBroadcast
   *
   * @generated from enum value: MT_CARD_STEAL_BROADCAST = 32;
   */
  MT_CARD_STEAL_BROADCAST = 32,

  /**
   * 玩家出【强化】的广播，消息：CardBroadcast
   *
   * @generated from enum value: MT_CARD_STRENGTHEN_BROADCAST = 33;
   */
  MT_CARD_STRENGTHEN_BROADCAST = 33,

  /**
   * 玩家出【防御】的广播，消息：CardBroadcast
   *
   * @generated from enum value: MT_CARD_DEFENSE_BROADCAST = 34;
   */
  MT_CARD_DEFENSE_BROADCAST = 34,

  /**
   * 玩家出【替身】的广播，消息：CardBroadcast
   *
   * @generated from enum value: MT_CARD_SUBSTITUTE_BROADCAST = 35;
   */
  MT_CARD_SUBSTITUTE_BROADCAST = 35,

  /**
   * 玩家出【虚弱】的广播，消息：CardBroadcast
   *
   * @generated from enum value: MT_CARD_WEAKEN_BROADCAST = 36;
   */
  MT_CARD_WEAKEN_BROADCAST = 36,

  /**
   * 玩家出【冻结】的广播，消息：CardBroadcast
   *
   * @generated from enum value: MT_CARD_FREEZE_BROADCAST = 37;
   */
  MT_CARD_FREEZE_BROADCAST = 37,

  /**
   * 玩家出【交换】的广播，消息：CardBroadcast
   *
   * @generated from enum value: MT_CARD_SWAP_BROADCAST = 38;
   */
  MT_CARD_SWAP_BROADCAST = 38,

  /**
   * ----- 玩家出牌之后的被动消息 -----
   * 【透视】了之后的消息，消息：CardAfterPeekMessage
   *
   * @generated from enum value: MT_CARD_AFTER_PEEK_MESSAGE = 41;
   */
  MT_CARD_AFTER_PEEK_MESSAGE = 41,

  /**
   * 【索取】的之后的消息，消息：CardAfterStealMessage
   *
   * @generated from enum value: MT_CARD_AFTER_STEAL_MESSAGE = 42;
   */
  MT_CARD_AFTER_STEAL_MESSAGE = 42,

  /**
   * 【冻结】之后的广播，消息：CardAfterFreezeBroadcast
   *
   * @generated from enum value: MT_CARD_AFTER_FREEZE_BROADCAST = 43;
   */
  MT_CARD_AFTER_FREEZE_BROADCAST = 43,

  /**
   * 被动触发【绷带】海盗的广播，消息：CardReviveBroadcast
   *
   * @generated from enum value: MT_CARD_REVIVE_BROADCAST = 44;
   */
  MT_CARD_REVIVE_BROADCAST = 44,

  /**
   * 玩家激活【防御】的广播，消息：CardActiveDefenseBroadcast
   *
   * @generated from enum value: MT_CARD_ACTIVE_DEFENSE_BROADCAST = 45;
   */
  MT_CARD_ACTIVE_DEFENSE_BROADCAST = 45,

  /**
   * 玩家激活【替身】的广播，消息：CardActiveSubstituteBroadcast
   *
   * @generated from enum value: MT_CARD_ACTIVE_SUBSTITUTE_BROADCAST = 46;
   */
  MT_CARD_ACTIVE_SUBSTITUTE_BROADCAST = 46,

  /**
   * 【强化】无效的广播，消息：CardStrengthenInvalidBroadcast
   *
   * @generated from enum value: MT_CARD_STRENGTHEN_INVALID_BROADCAST = 47;
   */
  MT_CARD_STRENGTHEN_INVALID_BROADCAST = 47,

  /**
   * 【交换】之后的广播，消息：CardAfterSwapMessage
   *
   * @generated from enum value: MT_CARD_AFTER_SWAP_MESSAGE = 48;
   */
  MT_CARD_AFTER_SWAP_MESSAGE = 48,

  /**
   * 【强化】成功的广播，消息：CardStrengthenSuccessBroadcast
   *
   * @generated from enum value: MT_CARD_STRENGTHEN_SUCCESS_BROADCAST = 49;
   */
  MT_CARD_STRENGTHEN_SUCCESS_BROADCAST = 49,

  /**
   * 【弱点】击中的广播，消息：PlayerDrawnDeathBroadcast
   *
   * @generated from enum value: MT_CARD_DRAWN_DEATH_BROADCAST = 50;
   */
  MT_CARD_DRAWN_DEATH_BROADCAST = 50,
}

/**
 * - Broadcast 表示所有玩家都是同样的消息
 * - Message 表示每个玩家的消息有部分数据不一样
 *
 * @generated from enum pirate.v1.MessageType
 */
export type MessageTypeJson = "MT_UNSPECIFIED" | "MT_GAME_INFO_BROADCAST" | "MT_STATE_CHANGED_BROADCAST" | "MT_RANKING_BROADCAST" | "MT_CLIENT_DATA_BROADCAST" | "MT_GAME_OVER_BROADCAST" | "MT_SERVER_DATA_BROADCAST" | "MT_PLAYER_INFO_MESSAGE" | "MT_PLAYER_EXIT_GAME_BROADCAST" | "MT_PLAYER_CONNECTION_CHANGED_BROADCAST" | "MT_PLAYER_DRAWN_CARD_MESSAGE" | "MT_PLAYER_POSTED_CARD_BROADCAST" | "MT_PLAYER_AFTER_SELECT_TARGET_BROADCAST" | "MT_PLAYER_SELECTABLE_TARGETS_MESSAGE" | "MT_PLAY_HOSTING_DRAW_BROADCAST" | "MT_CARD_PEEK_BROADCAST" | "MT_CARD_STEAL_BROADCAST" | "MT_CARD_STRENGTHEN_BROADCAST" | "MT_CARD_DEFENSE_BROADCAST" | "MT_CARD_SUBSTITUTE_BROADCAST" | "MT_CARD_WEAKEN_BROADCAST" | "MT_CARD_FREEZE_BROADCAST" | "MT_CARD_SWAP_BROADCAST" | "MT_CARD_AFTER_PEEK_MESSAGE" | "MT_CARD_AFTER_STEAL_MESSAGE" | "MT_CARD_AFTER_FREEZE_BROADCAST" | "MT_CARD_REVIVE_BROADCAST" | "MT_CARD_ACTIVE_DEFENSE_BROADCAST" | "MT_CARD_ACTIVE_SUBSTITUTE_BROADCAST" | "MT_CARD_STRENGTHEN_INVALID_BROADCAST" | "MT_CARD_AFTER_SWAP_MESSAGE" | "MT_CARD_STRENGTHEN_SUCCESS_BROADCAST" | "MT_CARD_DRAWN_DEATH_BROADCAST";

/**
 * Describes the enum pirate.v1.MessageType.
 */
export const MessageTypeSchema: GenEnum<MessageType, MessageTypeJson> = /*@__PURE__*/
  enumDesc(file_pirate_v1_message, 0);

