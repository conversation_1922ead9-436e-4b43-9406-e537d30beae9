// @generated by protoc-gen-es v2.4.0 with parameter "target=ts,json_types=true"
// @generated from file dixit/v1/player.proto (package dixit.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv1";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file dixit/v1/player.proto.
 */
export const file_dixit_v1_player: GenFile = /*@__PURE__*/
  fileDesc("ChVkaXhpdC92MS9wbGF5ZXIucHJvdG8SCGRpeGl0LnYxIvwBCgZQbGF5ZXISCgoCaWQYASABKAkSEAoIbmlja25hbWUYAiABKAkSEQoJY292ZXJfdXJsGAMgASgJEhEKCWdhbWVfb3ZlchgEIAEoCBIPCgdob3N0aW5nGAUgASgIEg0KBWluZGV4GAYgASgNEg8KB3RlYW1faWQYByABKAkSDgoGZXhpdGVkGAggASgIEg0KBXNjb3JlGAkgASgFEhIKCmhhbmRfY2FyZHMYCyADKAkSEAoIc2VsZWN0ZWQYDSABKAgSFQoNc2VsZWN0ZWRfY2FyZBgOIAEoCRINCgV2b3RlZBgPIAEoCBISCgp2b3RlZF9jYXJkGBAgASgJIlsKBFRlYW0SCgoCaWQYASABKAkSFgoOcGxheWVyX2luZGljZXMYAyADKA0SDgoGZXhpdGVkGAQgASgIEhEKCWdhbWVfb3ZlchgFIAEoCBIMCgRyYW5rGAYgASgNIkAKHFBsYXllclN0YXR1c0NoYW5nZWRCcm9hZGNhc3QSIAoGcGxheWVyGAEgASgLMhAuZGl4aXQudjEuUGxheWVyImsKEVBsYXllckluZm9NZXNzYWdlEh0KBXRlYW1zGAEgAygLMg4uZGl4aXQudjEuVGVhbRIhCgdwbGF5ZXJzGAIgAygLMhAuZGl4aXQudjEuUGxheWVyEhQKDHBsYXllcl9pbmRleBgDIAEoBSJhChZQbGF5ZXJEcmF3bkNhcmRNZXNzYWdlEhQKDHBsYXllcl9pbmRleBgBIAEoBRISCgpkcmF3bl9jYXJkGAIgASgJEh0KFXJlbWFpbmluZ19kcmF3bl9jYXJkcxgDIAEoBSI1Ch1QbGF5ZXJIb3N0aW5nRHJhd25DYXJkTWVzc2FnZRIUCgxwbGF5ZXJfaW5kZXgYASABKAUidwoZUGxheWVyUG9zdGVkQ2FyZEJyb2FkY2FzdBIUCgxwbGF5ZXJfaW5kZXgYASABKAUSEgoKaGFuZF9jYXJkcxgCIAMoCRIUCgxwb3N0ZWRfY2FyZHMYAyADKAkSGgoSZGVja19kaXNjYXJkX2NhcmRzGAQgAygJIl4KHlBsYXllclNlbGVjdGFibGVUYXJnZXRzTWVzc2FnZRIZChFmcm9tX3BsYXllcl9pbmRleBgBIAEoBRIhChlzZWxlY3RhYmxlX3BsYXllcl9pbmRpY2VzGAIgAygFIloKIFBsYXllckFmdGVyU2VsZWN0VGFyZ2V0QnJvYWRjYXN0EhkKEWZyb21fcGxheWVyX2luZGV4GAEgASgFEhsKE3RhcmdldF9wbGF5ZXJfaW5kZXgYAiABKAViBnByb3RvMw");

/**
 * 用户信息
 *
 * @generated from message dixit.v1.Player
 */
export type Player = Message<"dixit.v1.Player"> & {
  /**
   * 用户id
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * 用户昵称
   *
   * @generated from field: string nickname = 2;
   */
  nickname: string;

  /**
   * 用户头像
   *
   * @generated from field: string cover_url = 3;
   */
  coverUrl: string;

  /**
   * 是否 Game over
   *
   * @generated from field: bool game_over = 4;
   */
  gameOver: boolean;

  /**
   * 是否系统托管
   *
   * @generated from field: bool hosting = 5;
   */
  hosting: boolean;

  /**
   * 玩家在战局中的索引
   *
   * @generated from field: uint32 index = 6;
   */
  index: number;

  /**
   * 组队id
   *
   * @generated from field: string team_id = 7;
   */
  teamId: string;

  /**
   * 是否退出
   *
   * @generated from field: bool exited = 8;
   */
  exited: boolean;

  /**
   * 累计得分
   *
   * @generated from field: int32 score = 9;
   */
  score: number;

  /**
   * 手牌，广播时，此项无
   *
   * @generated from field: repeated string hand_cards = 11;
   */
  handCards: string[];

  /**
   * 是否已选牌
   *
   * @generated from field: bool selected = 13;
   */
  selected: boolean;

  /**
   * 已经选的牌，只有自己有
   *
   * @generated from field: string selected_card = 14;
   */
  selectedCard: string;

  /**
   * 是否已投票
   *
   * @generated from field: bool voted = 15;
   */
  voted: boolean;

  /**
   * 已投票的牌，只有自己有
   *
   * @generated from field: string voted_card = 16;
   */
  votedCard: string;
};

/**
 * 用户信息
 *
 * @generated from message dixit.v1.Player
 */
export type PlayerJson = {
  /**
   * 用户id
   *
   * @generated from field: string id = 1;
   */
  id?: string;

  /**
   * 用户昵称
   *
   * @generated from field: string nickname = 2;
   */
  nickname?: string;

  /**
   * 用户头像
   *
   * @generated from field: string cover_url = 3;
   */
  coverUrl?: string;

  /**
   * 是否 Game over
   *
   * @generated from field: bool game_over = 4;
   */
  gameOver?: boolean;

  /**
   * 是否系统托管
   *
   * @generated from field: bool hosting = 5;
   */
  hosting?: boolean;

  /**
   * 玩家在战局中的索引
   *
   * @generated from field: uint32 index = 6;
   */
  index?: number;

  /**
   * 组队id
   *
   * @generated from field: string team_id = 7;
   */
  teamId?: string;

  /**
   * 是否退出
   *
   * @generated from field: bool exited = 8;
   */
  exited?: boolean;

  /**
   * 累计得分
   *
   * @generated from field: int32 score = 9;
   */
  score?: number;

  /**
   * 手牌，广播时，此项无
   *
   * @generated from field: repeated string hand_cards = 11;
   */
  handCards?: string[];

  /**
   * 是否已选牌
   *
   * @generated from field: bool selected = 13;
   */
  selected?: boolean;

  /**
   * 已经选的牌，只有自己有
   *
   * @generated from field: string selected_card = 14;
   */
  selectedCard?: string;

  /**
   * 是否已投票
   *
   * @generated from field: bool voted = 15;
   */
  voted?: boolean;

  /**
   * 已投票的牌，只有自己有
   *
   * @generated from field: string voted_card = 16;
   */
  votedCard?: string;
};

/**
 * Describes the message dixit.v1.Player.
 * Use `create(PlayerSchema)` to create a new message.
 */
export const PlayerSchema: GenMessage<Player, PlayerJson> = /*@__PURE__*/
  messageDesc(file_dixit_v1_player, 0);

/**
 * @generated from message dixit.v1.Team
 */
export type Team = Message<"dixit.v1.Team"> & {
  /**
   * 队伍id
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * 玩家索引列表
   *
   * @generated from field: repeated uint32 player_indices = 3;
   */
  playerIndices: number[];

  /**
   * 是否全部退出
   *
   * @generated from field: bool exited = 4;
   */
  exited: boolean;

  /**
   * 是否全部 Game over
   *
   * @generated from field: bool game_over = 5;
   */
  gameOver: boolean;

  /**
   * 排名
   *
   * @generated from field: uint32 rank = 6;
   */
  rank: number;
};

/**
 * @generated from message dixit.v1.Team
 */
export type TeamJson = {
  /**
   * 队伍id
   *
   * @generated from field: string id = 1;
   */
  id?: string;

  /**
   * 玩家索引列表
   *
   * @generated from field: repeated uint32 player_indices = 3;
   */
  playerIndices?: number[];

  /**
   * 是否全部退出
   *
   * @generated from field: bool exited = 4;
   */
  exited?: boolean;

  /**
   * 是否全部 Game over
   *
   * @generated from field: bool game_over = 5;
   */
  gameOver?: boolean;

  /**
   * 排名
   *
   * @generated from field: uint32 rank = 6;
   */
  rank?: number;
};

/**
 * Describes the message dixit.v1.Team.
 * Use `create(TeamSchema)` to create a new message.
 */
export const TeamSchema: GenMessage<Team, TeamJson> = /*@__PURE__*/
  messageDesc(file_dixit_v1_player, 1);

/**
 * 玩家在线状态改变、出局、退出的广播
 *
 * @generated from message dixit.v1.PlayerStatusChangedBroadcast
 */
export type PlayerStatusChangedBroadcast = Message<"dixit.v1.PlayerStatusChangedBroadcast"> & {
  /**
   * @generated from field: dixit.v1.Player player = 1;
   */
  player?: Player;
};

/**
 * 玩家在线状态改变、出局、退出的广播
 *
 * @generated from message dixit.v1.PlayerStatusChangedBroadcast
 */
export type PlayerStatusChangedBroadcastJson = {
  /**
   * @generated from field: dixit.v1.Player player = 1;
   */
  player?: PlayerJson;
};

/**
 * Describes the message dixit.v1.PlayerStatusChangedBroadcast.
 * Use `create(PlayerStatusChangedBroadcastSchema)` to create a new message.
 */
export const PlayerStatusChangedBroadcastSchema: GenMessage<PlayerStatusChangedBroadcast, PlayerStatusChangedBroadcastJson> = /*@__PURE__*/
  messageDesc(file_dixit_v1_player, 2);

/**
 * 玩家信息
 *
 * @generated from message dixit.v1.PlayerInfoMessage
 */
export type PlayerInfoMessage = Message<"dixit.v1.PlayerInfoMessage"> & {
  /**
   * 玩家分组信息
   *
   * @generated from field: repeated dixit.v1.Team teams = 1;
   */
  teams: Team[];

  /**
   * 玩家信息（当前玩家下，会有hand cards的数据）
   *
   * @generated from field: repeated dixit.v1.Player players = 2;
   */
  players: Player[];

  /**
   * 当前玩家索引，广播用户会返回-1
   *
   * @generated from field: int32 player_index = 3;
   */
  playerIndex: number;
};

/**
 * 玩家信息
 *
 * @generated from message dixit.v1.PlayerInfoMessage
 */
export type PlayerInfoMessageJson = {
  /**
   * 玩家分组信息
   *
   * @generated from field: repeated dixit.v1.Team teams = 1;
   */
  teams?: TeamJson[];

  /**
   * 玩家信息（当前玩家下，会有hand cards的数据）
   *
   * @generated from field: repeated dixit.v1.Player players = 2;
   */
  players?: PlayerJson[];

  /**
   * 当前玩家索引，广播用户会返回-1
   *
   * @generated from field: int32 player_index = 3;
   */
  playerIndex?: number;
};

/**
 * Describes the message dixit.v1.PlayerInfoMessage.
 * Use `create(PlayerInfoMessageSchema)` to create a new message.
 */
export const PlayerInfoMessageSchema: GenMessage<PlayerInfoMessage, PlayerInfoMessageJson> = /*@__PURE__*/
  messageDesc(file_dixit_v1_player, 3);

/**
 * 玩家摸牌的消息
 *
 * @generated from message dixit.v1.PlayerDrawnCardMessage
 */
export type PlayerDrawnCardMessage = Message<"dixit.v1.PlayerDrawnCardMessage"> & {
  /**
   * 玩家索引
   *
   * @generated from field: int32 player_index = 1;
   */
  playerIndex: number;

  /**
   * 玩家卡牌（只有自己有数据）
   *
   * @generated from field: string drawn_card = 2;
   */
  drawnCard: string;

  /**
   * 剩余的摸牌消费数量
   *
   * @generated from field: int32 remaining_drawn_cards = 3;
   */
  remainingDrawnCards: number;
};

/**
 * 玩家摸牌的消息
 *
 * @generated from message dixit.v1.PlayerDrawnCardMessage
 */
export type PlayerDrawnCardMessageJson = {
  /**
   * 玩家索引
   *
   * @generated from field: int32 player_index = 1;
   */
  playerIndex?: number;

  /**
   * 玩家卡牌（只有自己有数据）
   *
   * @generated from field: string drawn_card = 2;
   */
  drawnCard?: string;

  /**
   * 剩余的摸牌消费数量
   *
   * @generated from field: int32 remaining_drawn_cards = 3;
   */
  remainingDrawnCards?: number;
};

/**
 * Describes the message dixit.v1.PlayerDrawnCardMessage.
 * Use `create(PlayerDrawnCardMessageSchema)` to create a new message.
 */
export const PlayerDrawnCardMessageSchema: GenMessage<PlayerDrawnCardMessage, PlayerDrawnCardMessageJson> = /*@__PURE__*/
  messageDesc(file_dixit_v1_player, 4);

/**
 * @generated from message dixit.v1.PlayerHostingDrawnCardMessage
 */
export type PlayerHostingDrawnCardMessage = Message<"dixit.v1.PlayerHostingDrawnCardMessage"> & {
  /**
   * 玩家索引
   *
   * @generated from field: int32 player_index = 1;
   */
  playerIndex: number;
};

/**
 * @generated from message dixit.v1.PlayerHostingDrawnCardMessage
 */
export type PlayerHostingDrawnCardMessageJson = {
  /**
   * 玩家索引
   *
   * @generated from field: int32 player_index = 1;
   */
  playerIndex?: number;
};

/**
 * Describes the message dixit.v1.PlayerHostingDrawnCardMessage.
 * Use `create(PlayerHostingDrawnCardMessageSchema)` to create a new message.
 */
export const PlayerHostingDrawnCardMessageSchema: GenMessage<PlayerHostingDrawnCardMessage, PlayerHostingDrawnCardMessageJson> = /*@__PURE__*/
  messageDesc(file_dixit_v1_player, 5);

/**
 * 玩家出牌的广播
 *
 * @generated from message dixit.v1.PlayerPostedCardBroadcast
 */
export type PlayerPostedCardBroadcast = Message<"dixit.v1.PlayerPostedCardBroadcast"> & {
  /**
   * 玩家索引
   *
   * @generated from field: int32 player_index = 1;
   */
  playerIndex: number;

  /**
   * 玩家手牌出牌的索引
   *
   * @generated from field: repeated string hand_cards = 2;
   */
  handCards: string[];

  /**
   * 玩家出的牌
   *
   * @generated from field: repeated string posted_cards = 3;
   */
  postedCards: string[];

  /**
   * 桌面所有出的牌
   *
   * @generated from field: repeated string deck_discard_cards = 4;
   */
  deckDiscardCards: string[];
};

/**
 * 玩家出牌的广播
 *
 * @generated from message dixit.v1.PlayerPostedCardBroadcast
 */
export type PlayerPostedCardBroadcastJson = {
  /**
   * 玩家索引
   *
   * @generated from field: int32 player_index = 1;
   */
  playerIndex?: number;

  /**
   * 玩家手牌出牌的索引
   *
   * @generated from field: repeated string hand_cards = 2;
   */
  handCards?: string[];

  /**
   * 玩家出的牌
   *
   * @generated from field: repeated string posted_cards = 3;
   */
  postedCards?: string[];

  /**
   * 桌面所有出的牌
   *
   * @generated from field: repeated string deck_discard_cards = 4;
   */
  deckDiscardCards?: string[];
};

/**
 * Describes the message dixit.v1.PlayerPostedCardBroadcast.
 * Use `create(PlayerPostedCardBroadcastSchema)` to create a new message.
 */
export const PlayerPostedCardBroadcastSchema: GenMessage<PlayerPostedCardBroadcast, PlayerPostedCardBroadcastJson> = /*@__PURE__*/
  messageDesc(file_dixit_v1_player, 6);

/**
 * @generated from message dixit.v1.PlayerSelectableTargetsMessage
 */
export type PlayerSelectableTargetsMessage = Message<"dixit.v1.PlayerSelectableTargetsMessage"> & {
  /**
   * 发起选择的玩家索引
   *
   * @generated from field: int32 from_player_index = 1;
   */
  fromPlayerIndex: number;

  /**
   * 可选择的玩家索引
   *
   * @generated from field: repeated int32 selectable_player_indices = 2;
   */
  selectablePlayerIndices: number[];
};

/**
 * @generated from message dixit.v1.PlayerSelectableTargetsMessage
 */
export type PlayerSelectableTargetsMessageJson = {
  /**
   * 发起选择的玩家索引
   *
   * @generated from field: int32 from_player_index = 1;
   */
  fromPlayerIndex?: number;

  /**
   * 可选择的玩家索引
   *
   * @generated from field: repeated int32 selectable_player_indices = 2;
   */
  selectablePlayerIndices?: number[];
};

/**
 * Describes the message dixit.v1.PlayerSelectableTargetsMessage.
 * Use `create(PlayerSelectableTargetsMessageSchema)` to create a new message.
 */
export const PlayerSelectableTargetsMessageSchema: GenMessage<PlayerSelectableTargetsMessage, PlayerSelectableTargetsMessageJson> = /*@__PURE__*/
  messageDesc(file_dixit_v1_player, 7);

/**
 * @generated from message dixit.v1.PlayerAfterSelectTargetBroadcast
 */
export type PlayerAfterSelectTargetBroadcast = Message<"dixit.v1.PlayerAfterSelectTargetBroadcast"> & {
  /**
   * 发起选择的玩家索引
   *
   * @generated from field: int32 from_player_index = 1;
   */
  fromPlayerIndex: number;

  /**
   * 玩家选择的目标索引
   *
   * @generated from field: int32 target_player_index = 2;
   */
  targetPlayerIndex: number;
};

/**
 * @generated from message dixit.v1.PlayerAfterSelectTargetBroadcast
 */
export type PlayerAfterSelectTargetBroadcastJson = {
  /**
   * 发起选择的玩家索引
   *
   * @generated from field: int32 from_player_index = 1;
   */
  fromPlayerIndex?: number;

  /**
   * 玩家选择的目标索引
   *
   * @generated from field: int32 target_player_index = 2;
   */
  targetPlayerIndex?: number;
};

/**
 * Describes the message dixit.v1.PlayerAfterSelectTargetBroadcast.
 * Use `create(PlayerAfterSelectTargetBroadcastSchema)` to create a new message.
 */
export const PlayerAfterSelectTargetBroadcastSchema: GenMessage<PlayerAfterSelectTargetBroadcast, PlayerAfterSelectTargetBroadcastJson> = /*@__PURE__*/
  messageDesc(file_dixit_v1_player, 8);

