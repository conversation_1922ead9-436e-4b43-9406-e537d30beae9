import { BaseTracking } from "../BaseTracking"




/**系统事件属性 */
export enum TrackingSystemEvent {
    /**规则说明按钮点击 */
    GUIDE_CLICK = 'guide_click',
    /**音效开关: 切换游戏音效&音乐按钮开关时上报 */
    SOUND_CLICK = 'sound_click',
    /**聊天输入框点击 */
    CHAT_CLICK = 'chat_click',
    /**礼物图标点击 */
    GIFT_CLICK = 'gift_click',
    /**消息图标点击 */
    MESSAGE_CLICK = 'message_click',
    /**设置图标点击 */
    SETTING_CLICK = 'setting_click',
    /**查看其他人的名片：结算页点击他人卡片时上报 */
    BUSINESS_CARD = 'business_card',
}

export class TrackingSystem extends BaseTracking {
    guideClick = () => {
        this.tracking.upload({
            event_type: TrackingSystemEvent.GUIDE_CLICK,
        })
    }

    soundClick = (bgm: "on" | "off", effect: "on" | "off") => {
        this.tracking.upload({
            event_type: TrackingSystemEvent.SOUND_CLICK,
            value: { bgm, effect }
        })
    }

}