// @generated by protoc-gen-es v2.2.5 with parameter "target=ts,import_extension=none,json_types=true"
// @generated from file new-message.proto (package protos, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv1";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file new-message.proto.
 */
export const file_new_message: GenFile = /*@__PURE__*/
  fileDesc("ChFuZXctbWVzc2FnZS5wcm90bxIGcHJvdG9zIhgKFk5ld01lc3NhZ2VOb3RpZmljYXRpb25CMAokY29tLnN0bnRzLmNsb3VkLnN1aWxleW9vLmVjaG8ucHJvdG9zWgguL3Byb3Rvc2IGcHJvdG8z");

/**
 * 新消息的通知
 *
 * @generated from message protos.NewMessageNotification
 */
export type NewMessageNotification = Message<"protos.NewMessageNotification"> & {
};

/**
 * 新消息的通知
 *
 * @generated from message protos.NewMessageNotification
 */
export type NewMessageNotificationJson = {
};

/**
 * Describes the message protos.NewMessageNotification.
 * Use `create(NewMessageNotificationSchema)` to create a new message.
 */
export const NewMessageNotificationSchema: GenMessage<NewMessageNotification, NewMessageNotificationJson> = /*@__PURE__*/
  messageDesc(file_new_message, 0);

