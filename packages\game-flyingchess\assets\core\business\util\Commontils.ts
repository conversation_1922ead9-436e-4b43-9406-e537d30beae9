/**
* @describe 公共工具
* <AUTHOR>
* @date 2024-01-23 19:34:52
*/

import { error } from "cc";

/**重试函数 */
export const retryRequest = async (
    requestFunction: () => Promise<void>,
    maxRetries: number = 3,
    retryInterval: number = 1000 // milliseconds
) => {
    let retries = 0;

    const makeRequest = async () => {
        try {
            await requestFunction();
        } catch (err) {
            console.error(`Request failed. Retries left: ${maxRetries - retries}`);
            if (retries < maxRetries) {
                retries++;
                // Wait for the specified interval before retrying
                await new Promise(resolve => setTimeout(resolve, retryInterval));
                await makeRequest();
            } else {
                error(`Max retries reached. Unable to complete the request.`);
                throw (err)
            }
        }
    };

    await makeRequest();
};
