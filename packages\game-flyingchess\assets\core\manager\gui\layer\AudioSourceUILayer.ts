import {
    math,
    view,
    Widget,
    Vec3,
    v3,
    tween,
    __private,
    Node,
    CCBoolean,
    AudioSource,
    _decorator,
    Enum,
    AudioClip,
} from 'cc'
import { AudioTypeEnum } from '../base/AudioSourceBaseComponent'
import { cat } from '../../index'
import { GameVisiable } from '@/core/business/store/global'
import { AudioEventConstant } from '@/core/business/constant'
import UILayer from './UILayer'
import store from '@/core/business/store'
import { JSBridgeClient } from '@/core/business/jsbridge/JSBridge'

const { ccclass, property } = _decorator

/**带有音频通道的UIlayer组件 */

export default class AudioSourceUILayer<
    T extends object,
    U extends object = {}
> extends UILayer<T, U> {
    @property({
        tooltip: `类型:
        EFFECT 音效
        BGM    音乐`,
        type: Enum(AudioTypeEnum),
    })
    type = AudioTypeEnum.EFFECT

    @property({ tooltip: '音源', type: AudioClip })
    clip = null!

    @property({ tooltip: '循环' })
    loop = false

    @property({ tooltip: '音量' })
    volume = 1

    @property({ tooltip: '是否启用自动播放' })
    playOnAwake = false

    audioSource: AudioSource = new AudioSource()

    override onEnable() {
        super.onEnable()
        cat.event
            .on(AudioEventConstant.EFFECT_ON, this.onPlayEffectHandler, this)
            .on(AudioEventConstant.EFFECT_OFF, this.onStopEffectHandler, this)
            .on(AudioEventConstant.MUSIC_ON, this.onPlayMusicHandler, this)
            .on(AudioEventConstant.MUSIC_OFF, this.onStopMusicHandler, this)
    }

    override __preload() {
        this.clip && (this.audioSource.clip = this.clip)

        this.audioSource.loop = this.loop
        this.audioSource.volume = this.volume
        this.audioSource.playOnAwake = this.playOnAwake

        super.__preload()
        const { volumeEffect, volumeMusic } = cat.audio
        this.audioSource.volume =
            this.type === AudioTypeEnum.BGM ? volumeMusic : volumeEffect
    }

    protected stopAudio() {
        this.audioSource.stop()
    }

    protected playAudio() {
        const { switchEffect, switchMusic } = cat.audio
        if (
            !this.audioSource.playing &&
            (this.type === AudioTypeEnum.BGM ? switchMusic : switchEffect)
            // && store.global.gameVisiable == GameVisiable.SHOW
        ) {
            if (
                store.global.isSupportNativePlayAudio &&
                this.audioSource.clip
            ) {
                JSBridgeClient.playAudio(this.audioSource.clip.nativeUrl)
            } else {
                this.audioSource.play()
            }
        }
    }

    protected onPlayEffectHandler() {}
    protected onStopEffectHandler() {
        this.stopAudio()
    }

    protected onPlayMusicHandler() {}

    protected onStopMusicHandler() {
        this.stopAudio()
    }

    override _onPreDestroy() {
        this.onStopMusicHandler()
        super._onPreDestroy()
    }
}
