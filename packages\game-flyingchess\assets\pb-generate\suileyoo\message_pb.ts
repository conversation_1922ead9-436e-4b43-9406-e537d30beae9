// @generated by protoc-gen-es v2.2.5 with parameter "target=ts,import_extension=none,json_types=true"
// @generated from file message.proto (package protos, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage } from "@bufbuild/protobuf/codegenv1";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv1";
import type { Message as Message$1 } from "@bufbuild/protobuf";

/**
 * Describes the file message.proto.
 */
export const file_message: GenFile = /*@__PURE__*/
  fileDesc("Cg1tZXNzYWdlLnByb3RvEgZwcm90b3MimAYKB01lc3NhZ2USIgoEdHlwZRgBIAEoDjIULnByb3Rvcy5NZXNzYWdlLlR5cGUSDwoHY29udGVudBgCIAEoDCLXBQoEVHlwZRILCgdSRVNFUlZFEAASCQoEUElORxCRThIJCgRQT05HEJJOEhEKDE5PVElGSUNBVElPThD0ThIcChdQQVJUWV9HQU1FX05PVElGSUNBVElPThD1ThIfChpBVVRIT1JJWkFUSU9OX05PVElGSUNBVElPThD4VRIZChRCQUxBTkNFX05PVElGSUNBVElPThD5VRIaChVSRUNIQVJHRV9OT1RJRklDQVRJT04Q+lUSGwoWVVNFUl9JTkZPX05PVElGSUNBVElPThD7VRIdChhNRU1CRVJfSU5GT19OT1RJRklDQVRJT04Q/FUSHQoYR1JPV1RIX0lORk9fTk9USUZJQ0FUSU9OEP1VEhwKF01FREFMX0RPTkVfTk9USUZJQ0FUSU9OEP5VEiAKG0NPTk5FQ1RfT05MSU5FX05PVElGSUNBVElPThDhXRIhChxDT05ORUNUX09GRkxJTkVfTk9USUZJQ0FUSU9OEOJdEh0KGE5FV19NRVNTQUdFX05PVElGSUNBVElPThDJZRIcChdUQVNLX0FXQVJEX05PVElGSUNBVElPThDKZRIkCh9JTlZJVEVfQVVUT19GT0xMT1dfTk9USUZJQ0FUSU9OEMtlEhcKEVVTRVJfSU5GT19SRVFVRVNUELiOAxIZChJVU0VSX0lORk9fUkVTUE9OU0UQweWoGBIcChZDT05ORUNUX1BBUkFNU19SRVFVRVNUEKCWAxIeChdDT05ORUNUX1BBUkFNU19SRVNQT05TRRCB6uUYEhwKFkNPTk5FQ1RfU1RBVFVTX1JFUVVFU1QQoZYDEh4KF0NPTk5FQ1RfU1RBVFVTX1JFU1BPTlNFEOnx5RgSJwohQ09OTkVDVF9BVVRIX0FDQ0VTU19UT0tFTl9SRVFVRVNUEKKWAxIpCiJDT05ORUNUX0FVVEhfQUNDRVNTX1RPS0VOX1JFU1BPTlNFENH55RhCMAokY29tLnN0bnRzLmNsb3VkLnN1aWxleW9vLmVjaG8ucHJvdG9zWgguL3Byb3Rvc2IGcHJvdG8z");

/**
 * 消息体
 *
 * @generated from message protos.Message
 */
export type Message = Message$1<"protos.Message"> & {
  /**
   * 消息类型
   *
   * @generated from field: protos.Message.Type type = 1;
   */
  type: Message_Type;

  /**
   * 消息内容
   *
   * @generated from field: bytes content = 2;
   */
  content: Uint8Array;
};

/**
 * 消息体
 *
 * @generated from message protos.Message
 */
export type MessageJson = {
  /**
   * 消息类型
   *
   * @generated from field: protos.Message.Type type = 1;
   */
  type?: Message_TypeJson;

  /**
   * 消息内容
   *
   * @generated from field: bytes content = 2;
   */
  content?: string;
};

/**
 * Describes the message protos.Message.
 * Use `create(MessageSchema)` to create a new message.
 */
export const MessageSchema: GenMessage<Message, MessageJson> = /*@__PURE__*/
  messageDesc(file_message, 0);

/**
 * @generated from enum protos.Message.Type
 */
export enum Message_Type {
  /**
   * @generated from enum value: RESERVE = 0;
   */
  RESERVE = 0,

  /**
   * @generated from enum value: PING = 10001;
   */
  PING = 10001,

  /**
   * @generated from enum value: PONG = 10002;
   */
  PONG = 10002,

  /**
   * 普通通知消息
   *
   * @generated from enum value: NOTIFICATION = 10100;
   */
  NOTIFICATION = 10100,

  /**
   * 派对消息通知
   *
   * @generated from enum value: PARTY_GAME_NOTIFICATION = 10101;
   */
  PARTY_GAME_NOTIFICATION = 10101,

  /**
   * 认证消息
   *
   * @generated from enum value: AUTHORIZATION_NOTIFICATION = 11000;
   */
  AUTHORIZATION_NOTIFICATION = 11000,

  /**
   * 账户资产变动通知
   *
   * @generated from enum value: BALANCE_NOTIFICATION = 11001;
   */
  BALANCE_NOTIFICATION = 11001,

  /**
   * 充值成功通知
   *
   * @generated from enum value: RECHARGE_NOTIFICATION = 11002;
   */
  RECHARGE_NOTIFICATION = 11002,

  /**
   * 用户信息变更通知
   *
   * @generated from enum value: USER_INFO_NOTIFICATION = 11003;
   */
  USER_INFO_NOTIFICATION = 11003,

  /**
   * 会员信息变更通知
   *
   * @generated from enum value: MEMBER_INFO_NOTIFICATION = 11004;
   */
  MEMBER_INFO_NOTIFICATION = 11004,

  /**
   * 成长信息变更通知
   *
   * @generated from enum value: GROWTH_INFO_NOTIFICATION = 11005;
   */
  GROWTH_INFO_NOTIFICATION = 11005,

  /**
   * 个人勋章获得
   *
   * @generated from enum value: MEDAL_DONE_NOTIFICATION = 11006;
   */
  MEDAL_DONE_NOTIFICATION = 11006,

  /**
   * 上机通知
   *
   * @generated from enum value: CONNECT_ONLINE_NOTIFICATION = 12001;
   */
  CONNECT_ONLINE_NOTIFICATION = 12001,

  /**
   * 下机通知
   *
   * @generated from enum value: CONNECT_OFFLINE_NOTIFICATION = 12002;
   */
  CONNECT_OFFLINE_NOTIFICATION = 12002,

  /**
   * 新消息通知
   *
   * @generated from enum value: NEW_MESSAGE_NOTIFICATION = 13001;
   */
  NEW_MESSAGE_NOTIFICATION = 13001,

  /**
   * 任务奖励
   *
   * @generated from enum value: TASK_AWARD_NOTIFICATION = 13002;
   */
  TASK_AWARD_NOTIFICATION = 13002,

  /**
   * 邀请你的用户自动成为是你的好友
   *
   * @generated from enum value: INVITE_AUTO_FOLLOW_NOTIFICATION = 13003;
   */
  INVITE_AUTO_FOLLOW_NOTIFICATION = 13003,

  /**
   * 用户基本信息请求（Archived）
   *
   * @generated from enum value: USER_INFO_REQUEST = 51000;
   */
  USER_INFO_REQUEST = 51000,

  /**
   * 用户基本信息响应（Archived）
   *
   * @generated from enum value: USER_INFO_RESPONSE = 51000001;
   */
  USER_INFO_RESPONSE = 51000001,

  /**
   * 获取连接必要参数的请求（Archived）
   *
   * @generated from enum value: CONNECT_PARAMS_REQUEST = 52000;
   */
  CONNECT_PARAMS_REQUEST = 52000,

  /**
   * 获取连接必要参数的响应（Archived）
   *
   * @generated from enum value: CONNECT_PARAMS_RESPONSE = 52000001;
   */
  CONNECT_PARAMS_RESPONSE = 52000001,

  /**
   * 获取用户当前连接状态的请求，频率参考：1次/60s（Archived）
   *
   * @generated from enum value: CONNECT_STATUS_REQUEST = 52001;
   */
  CONNECT_STATUS_REQUEST = 52001,

  /**
   * 获取用户当前连接状态的响应（Archived）
   *
   * @generated from enum value: CONNECT_STATUS_RESPONSE = 52001001;
   */
  CONNECT_STATUS_RESPONSE = 52001001,

  /**
   * 获取SDK认证的access_token的请求（Archived）
   *
   * @generated from enum value: CONNECT_AUTH_ACCESS_TOKEN_REQUEST = 52002;
   */
  CONNECT_AUTH_ACCESS_TOKEN_REQUEST = 52002,

  /**
   * 获取SDK认证的access_token的响应（Archived）
   *
   * @generated from enum value: CONNECT_AUTH_ACCESS_TOKEN_RESPONSE = 52002001;
   */
  CONNECT_AUTH_ACCESS_TOKEN_RESPONSE = 52002001,
}

/**
 * @generated from enum protos.Message.Type
 */
export type Message_TypeJson = "RESERVE" | "PING" | "PONG" | "NOTIFICATION" | "PARTY_GAME_NOTIFICATION" | "AUTHORIZATION_NOTIFICATION" | "BALANCE_NOTIFICATION" | "RECHARGE_NOTIFICATION" | "USER_INFO_NOTIFICATION" | "MEMBER_INFO_NOTIFICATION" | "GROWTH_INFO_NOTIFICATION" | "MEDAL_DONE_NOTIFICATION" | "CONNECT_ONLINE_NOTIFICATION" | "CONNECT_OFFLINE_NOTIFICATION" | "NEW_MESSAGE_NOTIFICATION" | "TASK_AWARD_NOTIFICATION" | "INVITE_AUTO_FOLLOW_NOTIFICATION" | "USER_INFO_REQUEST" | "USER_INFO_RESPONSE" | "CONNECT_PARAMS_REQUEST" | "CONNECT_PARAMS_RESPONSE" | "CONNECT_STATUS_REQUEST" | "CONNECT_STATUS_RESPONSE" | "CONNECT_AUTH_ACCESS_TOKEN_REQUEST" | "CONNECT_AUTH_ACCESS_TOKEN_RESPONSE";

/**
 * Describes the enum protos.Message.Type.
 */
export const Message_TypeSchema: GenEnum<Message_Type, Message_TypeJson> = /*@__PURE__*/
  enumDesc(file_message, 0, 0);

