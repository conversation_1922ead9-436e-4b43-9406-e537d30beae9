/**
 * @describe 装饰器
 * <AUTHOR>
 * @date 2023-08-03 18:06:18
 */

import { log } from 'cc'
import { cat } from '@/core/manager'
import { AudioEffectConstant } from '../constant'
import store from '../store'
import { safeStringify } from '../util/StringUtil'

export const buttonLock = (lockTime: number = 1, callBackFun?: Function) => {
    return function (
        target: any,
        propertyKey: string,
        descriptor: PropertyDescriptor
    ) {
        let oldFun: Function = descriptor.value
        let isLock: boolean = false
        descriptor.value = function (...args: any[]) {
            if (isLock) {
                if (callBackFun) {
                    callBackFun()
                }
                return
            }
            isLock = true
            setTimeout(() => {
                isLock = false
            }, lockTime * 1000)
            oldFun.apply(this, args)
        }
        return descriptor
    }
}

export const audioEffect = (
    effect: string = AudioEffectConstant.CLICK,
    callBackFun?: Function
) => {
    return (
        target: any,
        propertyKey: string,
        descriptor: PropertyDescriptor
    ) => {
        let oldFun: Function = descriptor.value
        descriptor.value = function (...args: any[]) {
            cat.audio.playEffect(effect)
            if (callBackFun) {
                callBackFun()
            }
            oldFun.apply(this, args)
        }
        return descriptor
    }
}

/**观战 */
export const watchUser = (callBackFun?: Function) => {
    return (
        _target: any,
        _propertyKey: string,
        descriptor: PropertyDescriptor
    ) => {
        let oldFun: Function = descriptor.value
        descriptor.value = function (...args: any[]) {
            if (callBackFun) {
                callBackFun()
            }
            window.ccLog('观战', store.user.isAudience)
            if (store.user.isAudience) return
            oldFun.apply(this, args)
        }
        return descriptor
    }
}

/**
 * 方法装饰器，用于打印方法名、输入参数和返回值
 * @param target 类的原型
 * @param propertyKey 方法名
 * @param descriptor 方法的属性描述符
 */
export function LogMethod(
    target: any,
    propertyKey: string,
    descriptor: PropertyDescriptor
) {
    const originalMethod = descriptor.value
    descriptor.value = function (...args: any[]) {
        // 打印方法名和输入参数
        window.ccLog(`Method: ${propertyKey}`)
        window.ccLog(`Arguments: ${safeStringify(args)}`)

        // 调用原始方法
        const result = originalMethod.apply(this, args)

        // 打印返回值
        window.ccLog(`Return: ${safeStringify(result)}`)

        return result
    }

    return descriptor
}

/**
 * 游戏可见性检查装饰器
 * @description 用于检查游戏当前是否可见，如果不可见则不执行原方法
 * @param callBackFun 可选的回调函数，当游戏不可见时会被调用
 * @returns 装饰器函数
 * @example
 * ```typescript
 * @gameVisibleCheck()
 * public someMethod() {
 *   // 只有在游戏可见时才会执行
 * }
 * ```
 */
export const gameVisibleCheck = (callBackFun?: Function) => {
    return (
        _target: any,
        _propertyKey: string,
        descriptor: PropertyDescriptor
    ) => {
        let oldFun: Function = descriptor.value
        descriptor.value = function (...args: any[]) {
            if (!store.global.isGameVisiable) {
                if (callBackFun) {
                    callBackFun()
                }
                return
            }
            return oldFun.apply(this, args)
        }
        return descriptor
    }
}
