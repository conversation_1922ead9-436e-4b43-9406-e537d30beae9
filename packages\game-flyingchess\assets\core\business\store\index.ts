/**
 * @describe 业务数据存储
 * <AUTHOR>
 * @date 2023-08-02 20:10:07
 */

import UserStore from './user'
import GlobalStore from './global'
import GameStore from './game'
import Login from './login'
import Lobby from './lobby'

export class Store {
    user = new UserStore(this)
    global = new GlobalStore(this)
    game = new GameStore(this)
    login = new Login(this)
    lobby = new Lobby(this)

    reset() {
        this.user = new UserStore(this)
        this.global = new GlobalStore(this)
        this.game = new GameStore(this)
        this.login = new Login(this)
        this.lobby = new Lobby(this)
    }
}

export default new Store()
