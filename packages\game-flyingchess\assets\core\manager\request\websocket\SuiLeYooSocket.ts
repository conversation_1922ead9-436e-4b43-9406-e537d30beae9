import { error, log, warn } from 'cc'

import { Emitter } from 'pitayaclient'
import {
    create,
    fromBinary,
    Message as ProtobufMessage,
    toBinary,
} from '@bufbuild/protobuf'
import store from '@/core/business/store'
import { cat } from '../../index'

import { reflect, ReflectMessage } from '@bufbuild/protobuf/reflect'
import { EmptySchema } from '@bufbuild/protobuf/wkt'
import { Message_Type, MessageSchema } from '@/pb-generate/suileyoo/message_pb'
import {
    AuthorizationNotification_Status,
    AuthorizationNotificationSchema,
} from '@/pb-generate/suileyoo/authorization_pb'
import { NotificationSchema } from '@/pb-generate/suileyoo/notification_pb'

export interface Events {
    /**重连成功 */
    reconnected: () => void
    /**链接成功 */
    connected: () => void
    /**心跳超时 */
    timeout: () => void
    /**ws报错 */
    error: (ev: Event | string) => void
    /**ws关闭 */
    close: (ev: Event) => void
    /**ws断开 */
    disconnect: (ev: Event) => void
    /**超出最大重连 */
    maxReconnect: () => void
    /**正在重连 */
    reconnecting: () => void
    /**重连倒计时 */
    reconnectTimeout: () => void
    /**参数错误 */
    paramError: () => void
    /**被踢 */
    kick: () => void
}

/**
 * @describe 随乐游socket基础类
 * <AUTHOR>
 * @date 2024-09-12 11:47:51
 */

export class SuiLeYooSocket extends Emitter.Emitter<Events, Events> {
    private socket: WebSocket | null
    private url: string = ''
    private port: string = ''
    /**重连最大上限次数 */
    private reconnectMaxTimes: number = 3
    /**重连的时间间隔 */
    private reconnectTimeGap: number = 3

    /**重连间隔当前时间计时 */
    private reconnectCurTime: number = 0
    /**当前重连次数 */
    private reconnectCurTimes: number = 0
    /**心跳计时 */
    private heartBeatTime: number = 0
    /**心跳发送时间 */
    private heartBeatReconnectTime: number = 30

    /**心跳计时器 */
    private heartBeatIntervalID: number = 0
    /**重连计时器 */
    private reconnectIntervalID: number | null = null

    /**主动关闭 防止断线重连 */
    private isActiveClose: boolean = false

    /**连接成功回调 */
    private connectSuccessCB: () => void

    /**连接成功 */
    get isSocketConnected() {
        return this.socket?.readyState === WebSocket.OPEN
    }

    /**正在重连 */
    get isReConnecting(): boolean {
        return this.reconnectIntervalID !== null
    }

    constructor(url: string, port: string = '') {
        super()
        this.url = url
        this.port = port
        this.resetReconnectConfig()
    }

    resetReconnectConfig() {
        this.reconnectMaxTimes = 3
        this.reconnectTimeGap = 3

        this.reconnectCurTime = 0
        this.reconnectCurTimes = 0
        this.heartBeatTime = 0
    }

    /**连接 */
    connect(): Promise<this> {
        console.log('连接随乐游 ws', this.url)
        this.socket = new WebSocket(this.url)
        this.socket.binaryType = 'arraybuffer'

        return new Promise<this>((resolve, reject) => {
            /**连接失败就会触发重连 */
            this.connectSuccessCB = () => {
                if (this.isReConnecting) {
                    cat.gui.showToast({ title: '重连成功' })
                    cat.gui.reloadScene()
                }
                console.log('随乐游ws 连接成功')
                resolve(this)
            }

            this.socket!.onmessage = this.onReceiveMessage.bind(this)
            this.socket!.onclose = this.onSocketClose.bind(this)
            this.socket!.onerror = this.onSocketError.bind(this)
            this.socket!.onopen = this.onSocketOpen.bind(this)
        })
    }

    onSocketOpen(ev: Event) {
        window.ccLog('socket onopen', ev)
        this.connectSuccessCB?.()
        this.setHeartbeat()
        this.resetReconnectConfig()
    }

    onSocketClose(ev: CloseEvent) {
        warn('socket close', ev)
        this.socket?.close()
        // 重连
        this.startReconnect()
    }

    onSocketError(ev: Event) {
        error('socket close', ev)
    }

    onReceiveMessage(eventdata: MessageEvent) {
        const { data } = eventdata
        const u8a: Uint8Array =
            data instanceof ArrayBuffer ? new Uint8Array(data) : data
        const message = fromBinary(MessageSchema, u8a)
        const { type, content } = message

        window.ccLog(
            `%c 随乐游ws响应消息:${+Date.now()} %c type:${type} %c`,
            'background:#E748CE ; padding: 1px; border-radius: 3px 0 0 3px;  color: #fff',
            'background:#E71172 ; padding: 1px; border-radius: 0 3px 3px 0;  color: #fff',
            'background:transparent',
            message
        )

        switch (type) {
            // 心跳接收
            case Message_Type.PONG:
                this.resetHeartbeat()
                break
            case Message_Type.AUTHORIZATION_NOTIFICATION: {
                const { status, data } = fromBinary(
                    AuthorizationNotificationSchema,
                    content
                )
                window.ccLog('AUTHORIZATION_NOTIFICATION', data, status)
                if (status != AuthorizationNotification_Status.SUCCESS) {
                    // cat.platform.longLink({ gameId: store.match.gameId }).then((res: IBridge.LongLinkRes) => {
                    //     this.url = res.url;
                    // });
                }
                break
            }
            case Message_Type.PARTY_GAME_NOTIFICATION:
                {
                    const data = fromBinary(NotificationSchema, content)
                    const { Code, Data, Content } = data
                    window.ccLog('PARTY_GAME_NOTIFICATION', data)
                    //  PARTY_GAME_QUICK_JOIN_ERROR(132185, "未匹配到用户"),
                    //  PARTY_GAME_QUICK_JOIN_SUCCESS(132186, "匹配成功"),
                    //  PARTY_GAME_ARENA_MATCH_SUCCESS(132187, "竞技场匹配成功"),
                    //  PARTY_GAME_BEGIN_START(132188, "即将开启游戏，请前往房间进行准备"),
                    //  PARTY_GAME_UNREADY_KICK_OUT(132189, "由于你没有准备游戏，已被系统踢出房间/踢下麦");
                    cat.event.dispatchEvent(Code)
                    switch (Code) {
                        case 132185: //未匹配到用户
                            break
                        case 132186: //匹配成功
                            // cat.platform.gameStateInfo({ gameId: store.match.gameId }).then(async (res) => {
                            // window.ccLog('匹配成功', JSON.stringify(res))
                            // if ([1, 2].includes(res.gameSate)) {
                            //     store.match.roomInfo = res.roomInfo
                            //     const { ddRid, roomSerial, imUsername, impassword, swToken, partyMode } = res.roomInfo
                            //     store.lobby.matchRoom = {
                            //         ddRid,
                            //         roomSerial,
                            //         imUsername,
                            //         impassword,
                            //         swToken,
                            //         userId: store.user.userGameData.userId,
                            //         partyMode,
                            //     }
                            // }
                            // cat.gui.loadScene('match')

                            // })

                            break
                        case 132187: //竞技场匹配成功
                            break
                        case 132188: //即将开启游戏，请前往房间进行准备
                            break
                        case 132189: //由于你没有准备游戏，已被系统踢出房间
                            break

                        default:
                            break
                    }
                }
                break

            default:
                break
        }
    }

    private sendMsg(type: Message_Type, content: Uint8Array) {
        if (this.socket?.readyState === WebSocket.OPEN) {
            let message = toBinary(
                MessageSchema,
                create(MessageSchema, {
                    type,
                    content,
                })
            )

            this.socket.send(message)
        } else {
            console.warn(
                'socket is not open: readyState ' +
                    (this.socket?.readyState ?? -1)
            )
        }
    }

    /**请求 */
    request(type: Message_Type, req: ReflectMessage) {
        this.sendMsg(type, toBinary(req.desc, req.message))
        window.ccLog(
            `%c 随乐游ws请求消息:${+Date.now()} %c type:${type} %c`,
            'background:#AA48CE ; padding: 1px; border-radius: 3px 0 0 3px;  color: #fff',
            'background:#AA1172 ; padding: 1px; border-radius: 0 3px 3px 0;  color: #fff',
            'background:transparent',
            req
        )
    }

    /**设置心跳 */
    setHeartbeat() {
        this.clearHeartbeat().clearReconnect()
        this.heartBeatIntervalID = setInterval(
            this.updateHeartbeat.bind(this),
            1000
        )
        return this
    }

    /**清除心跳 */
    clearHeartbeat() {
        this.resetHeartbeat()
        clearInterval(this.heartBeatIntervalID)
        return this
    }

    /**重置心跳 */
    resetHeartbeat() {
        this.heartBeatTime = 0
    }

    /**更新心跳 */
    updateHeartbeat() {
        if (this.heartBeatTime > this.heartBeatReconnectTime) {
            this.startReconnect()
            return
        } else if (this.heartBeatTime == this.heartBeatReconnectTime) {
            this.request(Message_Type.PING, reflect(EmptySchema))
        }
        this.heartBeatTime++
    }

    /**重连更新 */
    onReconnectUpdate() {
        if (this.reconnectCurTimes >= this.reconnectMaxTimes) {
            this.clearReconnect()
        } else {
            if (this.reconnectCurTime > this.reconnectTimeGap) {
                this.reconnectCurTimes++
                this.reconnectCurTime = 0
                this.connect()
            } else {
                this.reconnectCurTime++
            }
        }
    }

    /**重连 */
    startReconnect() {
        this.clearReconnect().clearHeartbeat().resetReconnectConfig()
        if (this.isActiveClose || this.isReConnecting) return
        window.ccLog('正在重连随乐游')
        cat.gui.showToast({ title: '网络已断开,正在重连' })
        this.reconnectIntervalID = setInterval(() => {
            this.onReconnectUpdate()
        }, 1000)
    }

    /**清除重连 */
    clearReconnect() {
        clearInterval(this.reconnectIntervalID!)
        this.reconnectIntervalID = null
        return this
    }

    /**销毁ws */
    destroy() {
        this.isActiveClose = true
        window.ccLog('销毁suileyoo ws')
        if (this.socket) {
            this.socket?.close()
            this.socket = null
        }
        this.clearReconnect().clearHeartbeat().resetReconnectConfig()
    }
}
