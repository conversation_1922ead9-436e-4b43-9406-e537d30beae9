// @generated by protoc-gen-es v2.4.0 with parameter "target=ts,json_types=true"
// @generated from file dixit/v1/state.proto (package dixit.v1, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile } from "@bufbuild/protobuf/codegenv1";
import { enumDesc, fileDesc } from "@bufbuild/protobuf/codegenv1";

/**
 * Describes the file dixit/v1/state.proto.
 */
export const file_dixit_v1_state: GenFile = /*@__PURE__*/
  fileDesc("ChRkaXhpdC92MS9zdGF0ZS5wcm90bxIIZGl4aXQudjEqvQMKBVN0YXRlEhUKEVNUQVRFX1VOU1BFQ0lGSUVEEAASFAoQU1RBVEVfR0FNRV9TVEFSVBABEhMKD1NUQVRFX0dBTUVfT1ZFUhACEhgKFFNUQVRFX0dBTUVfRklOSVNISU5HEAMSFwoTU1RBVEVfR0FNRV9GSU5JU0hFRBAEEhQKEFNUQVRFX0lOSVRfVEhFTUUQCxIYChRTVEFURV9JTklUX0hBTkRfQ0FSRBAMEhUKEVNUQVRFX1JPVU5EX1NUQVJUEA0SFQoRU1RBVEVfUExBWUVSX1BPU1QQDhIXChNTVEFURV9QTEFZRVJfUE9TVEVEEA8SGAoUU1RBVEVfUExBWUVSX1RFTExJTkcQEBIeChpTVEFURV9QTEFZRVJfQUZURVJfVEVMTElORxAREh0KGVNUQVRFX09USEVSX1BMQVlFUl9TRUxFQ1QQEhIjCh9TVEFURV9PVEhFUl9QTEFZRVJfQUZURVJfU0VMRUNUEBMSFQoRU1RBVEVfUExBWUVSX1ZPVEUQFBIbChdTVEFURV9QTEFZRVJfVk9URV9DT1VOVBAVEhYKElNUQVRFX1BMQVlFUl9EUkFXThAWYgZwcm90bzM");

/**
 * @generated from enum dixit.v1.State
 */
export enum State {
  /**
   * @generated from enum value: STATE_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * ---- 游戏状态 ----
   *
   * @generated from enum value: STATE_GAME_START = 1;
   */
  GAME_START = 1,

  /**
   * @generated from enum value: STATE_GAME_OVER = 2;
   */
  GAME_OVER = 2,

  /**
   * @generated from enum value: STATE_GAME_FINISHING = 3;
   */
  GAME_FINISHING = 3,

  /**
   * @generated from enum value: STATE_GAME_FINISHED = 4;
   */
  GAME_FINISHED = 4,

  /**
   * ---- 玩家状态 ----
   * 确定主题
   *
   * @generated from enum value: STATE_INIT_THEME = 11;
   */
  INIT_THEME = 11,

  /**
   * 初始化手牌
   *
   * @generated from enum value: STATE_INIT_HAND_CARD = 12;
   */
  INIT_HAND_CARD = 12,

  /**
   * 回合开始，确定说书人
   *
   * @generated from enum value: STATE_ROUND_START = 13;
   */
  ROUND_START = 13,

  /**
   * 说书人需要出牌
   *
   * @generated from enum value: STATE_PLAYER_POST = 14;
   */
  PLAYER_POST = 14,

  /**
   * 说书人已出牌
   *
   * @generated from enum value: STATE_PLAYER_POSTED = 15;
   */
  PLAYER_POSTED = 15,

  /**
   * 说书人需要讲故事
   *
   * @generated from enum value: STATE_PLAYER_TELLING = 16;
   */
  PLAYER_TELLING = 16,

  /**
   * 说书人讲完故事，展示故事内容
   *
   * @generated from enum value: STATE_PLAYER_AFTER_TELLING = 17;
   */
  PLAYER_AFTER_TELLING = 17,

  /**
   * 其他玩家选择卡牌
   *
   * @generated from enum value: STATE_OTHER_PLAYER_SELECT = 18;
   */
  OTHER_PLAYER_SELECT = 18,

  /**
   * 其他玩家完成选择卡牌
   *
   * @generated from enum value: STATE_OTHER_PLAYER_AFTER_SELECT = 19;
   */
  OTHER_PLAYER_AFTER_SELECT = 19,

  /**
   * 玩家投票环节
   *
   * @generated from enum value: STATE_PLAYER_VOTE = 20;
   */
  PLAYER_VOTE = 20,

  /**
   * 玩家投票完成，进入唱票环节
   *
   * @generated from enum value: STATE_PLAYER_VOTE_COUNT = 21;
   */
  PLAYER_VOTE_COUNT = 21,

  /**
   * 补牌环节
   *
   * @generated from enum value: STATE_PLAYER_DRAWN = 22;
   */
  PLAYER_DRAWN = 22,
}

/**
 * @generated from enum dixit.v1.State
 */
export type StateJson = "STATE_UNSPECIFIED" | "STATE_GAME_START" | "STATE_GAME_OVER" | "STATE_GAME_FINISHING" | "STATE_GAME_FINISHED" | "STATE_INIT_THEME" | "STATE_INIT_HAND_CARD" | "STATE_ROUND_START" | "STATE_PLAYER_POST" | "STATE_PLAYER_POSTED" | "STATE_PLAYER_TELLING" | "STATE_PLAYER_AFTER_TELLING" | "STATE_OTHER_PLAYER_SELECT" | "STATE_OTHER_PLAYER_AFTER_SELECT" | "STATE_PLAYER_VOTE" | "STATE_PLAYER_VOTE_COUNT" | "STATE_PLAYER_DRAWN";

/**
 * Describes the enum dixit.v1.State.
 */
export const StateSchema: GenEnum<State, StateJson> = /*@__PURE__*/
  enumDesc(file_dixit_v1_state, 0);

