import { error, game, log } from 'cc'
import { GlobalEventConstant } from '../constant'
import { BaseManager } from '../BaseManager'
import { Manager } from '../index'

/**
 * @describe 时间管理
 * <AUTHOR>
 * @date 2024-09-12 11:49:19
 */

enum TimerType {
    Delay,
    Interval,
}
type TimerCallback = (executionTime: number) => void

class TimerEntry {
    /** 唯一标识符 */
    public readonly tag: any
    /** 计时器类型（计时器或延时器） */
    public readonly type: TimerType
    /** 回调函数 */
    public readonly callback: TimerCallback
    /** 触发剩余时间（毫秒）*/
    public remainingTime: number
    /** 执行时间（毫秒）*/
    public executionTime: number = 0
    /** 初始时间（仅适用于计时器）  */
    public initialTime: number
    /**
     * 创建一个新的计时器或延时器条目
     * @param tag 唯一标识符
     * @param type 计时器类型（计时器或延时器）
     * @param time 剩余时间（毫秒）
     * @param callback 回调函数
     * @param initialTime 初始间隔时间（仅适用于计时器）
     */
    constructor(
        tag: any,
        type: TimerType,
        time: number,
        callback: TimerCallback
    ) {
        this.tag = tag
        this.type = type
        this.remainingTime = time
        this.initialTime = time // 新增字段，仅适用于计时器
        this.callback = callback
    }
}

export class TimerManager extends BaseManager {
    private timers: Map<any, TimerEntry> = new Map<string, TimerEntry>()
    private lastUpdateTime: number = Date.now()

    constructor(cat: Manager) {
        super(cat)
        cat.event.on(
            GlobalEventConstant.EVENT_HIDE,
            this.onHandleAppBackground,
            this
        )
        cat.event.on(
            GlobalEventConstant.EVENT_SHOW,
            this.onHandleAppForeground,
            this
        )
        // 在构造函数中启动一个定时器，以每帧调用 update 方法
        setInterval(this.update.bind(this), 1000)
    }

    private onHandleAppBackground() {
        this.lastUpdateTime = Date.now()
    }

    private onHandleAppForeground() {
        const currentTime = Date.now()
        const elapsedTime = currentTime - this.lastUpdateTime
        window.ccLog('elapsedTime', elapsedTime)
        for (const entry of this.timers.values()) {
            if (entry.remainingTime > 0) {
                entry.remainingTime -= elapsedTime
                entry.executionTime += elapsedTime
            }
        }

        this.lastUpdateTime = currentTime
    }

    /**
     * 注册一个计时器
     * @param tag 注册计时器的对象
     * @param interval 间隔时间（毫秒）
     * @param callback 计时器回调函数
     * @returns 计时器的唯一标识
     */
    registerInterval(tag: any, interval: number, callback: TimerCallback) {
        if (this.has(tag)) {
            return error(`${tag}定时器已存在，请勿重复注册`)
        }
        const timerEntry = new TimerEntry(
            tag,
            TimerType.Interval,
            interval,
            callback
        )
        this.timers.set(tag, timerEntry)
    }

    /**
     * 注册一个延时器
     * @param tag 注册延时器的对象
     * @param delay 延时时间（毫秒）
     * @param callback 延时器回调函数
     * @returns 延时器的唯一标识
     */
    registerDelay(tag: any, delay: number, callback: TimerCallback) {
        if (this.has(tag)) {
            return error(`${tag}延时器已存在，请勿重复注册`)
        }
        const timerEntry = new TimerEntry(tag, TimerType.Delay, delay, callback)
        this.timers.set(tag, timerEntry)
    }

    /**
     * 取消一个计时器或延时器
     * @param tag 计时器或延时器的唯一标识
     */
    unregister(tag: any) {
        if (this.has(tag)) {
            this.timers.delete(tag)
        }
    }

    /**是否拥有定时器 */
    has(object: any) {
        return this.timers.has(object)
    }

    /**
     * 更新计时器状态
     * @param dt 间隔时间（毫秒）
     */
    private update() {
        const entriesToRemove: string[] = []

        for (const [id, entry] of this.timers.entries()) {
            entry.remainingTime -= 1000

            if (entry.remainingTime <= 0) {
                if (entry.type === TimerType.Interval) {
                    entry.executionTime += entry.initialTime
                }
                // 触发回调
                entry.callback(entry.executionTime)

                // 如果是计时器，重置剩余时间
                if (entry.type === TimerType.Interval) {
                    entry.remainingTime = entry.initialTime // 重置为初始间隔时间
                } else {
                    // 如果是延时器，标记为待删除
                    entriesToRemove.push(id)
                }
            }
        }

        // 删除已完成的延时器
        for (const idToRemove of entriesToRemove) {
            this.timers.delete(idToRemove)
        }
    }
}
