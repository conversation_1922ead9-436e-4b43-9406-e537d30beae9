/**
 * @describe
 * <AUTHOR>
 * @date 2024-09-12 11:35:02
 */

import { reflect } from '@bufbuild/protobuf/reflect'
import { cat } from '@/core/manager'
import UILayer from '@/core/manager/gui/layer/UILayer'
import { API } from '../api'
import { Environment } from '../environment'
import { Platform } from '../platform'
import { Tracking } from '../tracking'
import { createProxySocket } from '../ws'
import { createProxySuileyooSocket } from '../ws/SuileyooWS'

import { log } from 'cc'
import { EDITOR } from 'cc/env'

/**loading加载组件基类 */
export default class BaseLoading extends UILayer {
    override async start() {
        await this.businessInit()
        // loading 初始化
        this.init()
    }

    /**业务类初始化 */
    protected async businessInit() {
        // 平台
        cat.platform = new Platform(cat).platform
        window.ccLog('platform initializationed')
        // 环境
        cat.env = await new Environment().init()
        window.ccLog('env initializationed')
        // 统计
        cat.tracking = new Tracking(cat)
        window.ccLog('tracking initializationed')

        // 接口请求
        if (!EDITOR) {
            cat.api = new API(cat)
            window.ccLog('api initializationed')
        }

        // ws创建
        cat.ws = createProxySocket(cat)
        window.ccLog('ws initializationed')
        // ws随乐游创建
        cat.suiLeYooSocket = createProxySuileyooSocket(cat)
        window.ccLog('suiLeYooSocket initializationed')
        // 扩展... TODO
    }

    /**loading初始化 */
    protected init() {}
}
