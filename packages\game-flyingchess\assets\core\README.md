# Core 模块依赖关系图

## 模块概览

```mermaid
graph TD
    A[manager] --> B[business]
    A --> C[components]
    A --> D[ui]
    B --> D
    B --> C
    C --> B
```

## 详细依赖关系

### manager 模块

-   **核心管理层**，是其他模块的基础依赖
-   主要包含以下子模块：
    -   audio: 音频管理，依赖 business/constant, business/store
    -   event: 事件系统，被 business/jsbridge 依赖
    -   gui: GUI 管理，依赖 business/store，被 components 和 ui 模块依赖
    -   request: 网络请求管理，依赖 business/store, business/hooks

### business 模块

-   **业务逻辑层**，依赖 manager 模块的基础功能
-   主要包含以下子模块：
    -   api: API 调用，依赖 manager
    -   store: 状态管理，被多个模块依赖
    -   ws: WebSocket 通信，依赖 manager/request
    -   hooks: 业务钩子，依赖 manager
    -   jsbridge: 桥接层，依赖 manager/event
    -   constant: 常量定义，被多个模块依赖
    -   util: 工具函数，依赖 manager
    -   watch: 监听系统，依赖 manager

### components 模块

-   **UI 组件库**，依赖 manager/gui 和 business 模块
-   主要组件：
    -   BaseComponent: 基础组件，依赖 manager/gui
    -   BreakHosting: 依赖 manager, business/constant
    -   FitCamera: 依赖 manager
    -   SafeArea: 依赖 business/store
    -   Toast: 依赖 manager/gui

### ui 模块

-   **界面实现层**，依赖 manager 和 business 模块
-   主要界面：
    -   modal: 弹窗，依赖 manager/gui, business/hooks
    -   notice: 通知，依赖 manager/gui
    -   reconnection: 重连，依赖 manager/gui, business/store
    -   uploadLog: 日志上传，依赖 manager/gui

## 循环依赖分析

存在以下循环依赖需要注意：

1. components 和 business 之间：components 依赖 business/store，而 business/hooks 中的装饰器被 components 使用
2. manager 和 business 之间：manager/request 依赖 business/hooks，而 business 模块普遍依赖 manager

## 重构建议

1. 解决循环依赖：

    - 将 business/hooks 中的装饰器移至 components 模块
    - 将 business/store 拆分，核心状态放在 manager 中

2. 模块解耦：

    - manager 模块应该只包含核心功能，移除对 business 的依赖
    - business 模块应该通过接口而不是直接引用来使用 manager 的功能

3. 统一依赖方向：
    - 确保依赖关系是单向的：manager <- business <- components/ui
    - 将跨层级的依赖通过事件或接口解耦
