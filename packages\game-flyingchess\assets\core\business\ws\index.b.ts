/**
 * @describe 社交游戏socket类
 * <AUTHOR>
 * @date 2024-09-12 11:41:27
 */

import { ClientOption } from 'pitayaclient'
import {
    BattleInitialData,
    JoinBattleResponse,
    Player as BasePlayer,
    SocialGameClientOption,
    Event as SGCEvent,
    WatchBroadcastMessage,
    BattleInitialDataSchema,
    PlayerSchema as BasePlayerSchema,
    WatchBroadcastMessageSchema,
} from 'sgc'
import { DescMessage, Message } from '@bufbuild/protobuf'
import { WrapperSocialGameClient } from '@/core/manager/request/websocket'
import { error, Game, game, log } from 'cc'

import { BaseManager } from '@/core/manager/BaseManager'
import { Manager } from '@/core/manager'
import { BaseWebSocket } from './BaseWebSocket'

import store from '../store'
import {
    DataBroadcastResponseSchema,
    PirateService,
    Route,
} from '@/pb-generate/server/dixit/v1/handler_pb'
import { MessageType } from '@/pb-generate/server/dixit/v1/message_pb'
import {
    ClientDataBroadcastSchema,
    GameInfoBroadcastSchema,
    GameOverBroadcastSchema,
    RankingBroadcastSchema,
    ServerDataBroadcastSchema,
} from '@/pb-generate/server/dixit/v1/game_pb'
import {
    PlayerAfterSelectTargetBroadcastSchema,
    PlayerDrawnCardMessageSchema,
    PlayerHostingDrawnCardMessageSchema,
    PlayerInfoMessageSchema,
    PlayerPostedCardBroadcastSchema,
    PlayerSchema,
    PlayerSelectableTargetsMessageSchema,
    PlayerStatusChangedBroadcastSchema,
} from '@/pb-generate/server/dixit/v1/player_pb'

import { DataBroadcastContentSchema } from '@/pb-generate/client/game/game_pb'

/**基础事件 */
export type BsseSocketEvent = keyof typeof SGCEvent

/**业务事件 */
export type BusinessSocketEvent = keyof typeof MessageType

/**异步路由 */
export type ASYNCSocketRoute = keyof typeof Route

/**路由 */
export type SocketRoute = keyof typeof Route

export type SocketEvent =
    | BusinessSocketEvent
    | BsseSocketEvent
    | ASYNCSocketRoute

// 事件响应类型
type EventResponsePair<T extends SocketEvent> = {
    event: T
    responseType: DescMessage
    cb?: () => void
}

export const businessEventResponsePairs: EventResponsePair<
    BusinessSocketEvent | ASYNCSocketRoute
>[] = [
    // 业务事件对
    // assets\pb-generate\server\dixit\v1\message_pb.ts
    {
        event: 'MT_GAME_INFO_BROADCAST',
        responseType: GameInfoBroadcastSchema,
    },
    {
        event: 'MT_STATE_CHANGED_BROADCAST',
        responseType: GameInfoBroadcastSchema,
    },

    {
        event: 'MT_CLIENT_DATA_BROADCAST',
        responseType: ClientDataBroadcastSchema,
    },
    {
        event: 'MT_SERVER_DATA_BROADCAST',
        responseType: ServerDataBroadcastSchema,
    },
    {
        event: 'MT_GAME_OVER_BROADCAST',
        responseType: GameOverBroadcastSchema,
    },
    {
        event: 'MT_PLAYER_INFO_MESSAGE',
        responseType: PlayerInfoMessageSchema,
    },
    {
        event: 'MT_PLAYER_STATUS_CHANGED_BROADCAST',
        responseType: PlayerSchema,
    },
]

declare module '@/core/manager' {
    interface Manager {
        ws: WrapperSocialGameClient & SocialGameBusinessSocket
    }
}

/**
 * 处理ws业务
 */

export class SocialGameBusinessSocket extends BaseWebSocket<WrapperSocialGameClient> {
    constructor(cat: Manager) {
        super(cat)
    }

    /**创建 */
    create = (opts: SocialGameClientOption, clientOption: ClientOption) => {
        if (this.ins !== null) {
            throw new Error('[ws] is alreay exist! please invoke destroy first')
        }
        this.ins = new WrapperSocialGameClient(opts, {
            ...clientOption,
            ...{ isAutoConnect: false },
        })
        window.ccLog('------创建')
        this.#registerServerListen()
        this.#registerPirateServiceMethods()
        window.ccLog('------注册')
        return this.cat.ws
    }

    /**注册PirateService方法 */
    #registerPirateServiceMethods = () => {
        if (!this.ins) {
            return
        }

        // 注册PirateService方法
        this.ins?.registerService(PirateService.methods)
    }

    /**注册监听 */
    #registerServerListen = () => {
        ;[
            ...this.#baseEventResponsePairs,
            ...this.#businessEventResponsePairs,
        ].forEach((pair) => {
            this.ins?.listen(pair.event, pair.responseType, pair?.cb)
        })
    }

    /**基础事件对 */
    #baseEventResponsePairs: EventResponsePair<BsseSocketEvent>[] = [
        // 战局初始化- 广播，协议名称：BattleInitialize，返回参数：EmptyResponse
        { event: 'BattleInitialize', responseType: BattleInitialDataSchema },

        // 玩家状态变更推送- 广播，协议名称：PlayerStatusChanged，返回参数：EmptyResponse
        { event: 'PlayerStatusChanged', responseType: BasePlayerSchema },

        // 观战 - 广播，协议名称：WatchBroadcast，返回参数：WatchBroadcastMessage
        { event: 'WatchBroadcast', responseType: WatchBroadcastMessageSchema },
    ]

    /**业务事件对 */
    #businessEventResponsePairs: EventResponsePair<
        BusinessSocketEvent | ASYNCSocketRoute
    >[] = businessEventResponsePairs
}

const handler: ProxyHandler<
    SocialGameBusinessSocket & WrapperSocialGameClient
> = {
    get(target, prop: PropertyKey, receiver: any) {
        if (target.hasOwnProperty(prop)) {
            return Reflect.get(
                target,
                prop,
                receiver
            ) as SocialGameBusinessSocket
        } else if (target.ins) {
            // 对于其他属性，返回 WrapperSocialGameClient 类型
            return (...args: any[]) =>
                Reflect.get(target.ins!, prop, receiver).apply(
                    target.ins,
                    args
                ) as WrapperSocialGameClient
        } else {
            const err = new Error(
                '[ws] is not available, please invoke [create]'
            )
            error(err)
            throw err
        }
    },

    set(target, prop: PropertyKey, value: any) {
        if (typeof prop === 'string') {
            // 使用类型断言将 prop 断言为 keyof SocialGameBusinessSocket
            ;(target as any)[prop] = value
            window.ccLog(`Setting property ${prop} to ${value}`)
            return true
        }
        return false
    },
}

export const createProxySocket = (cat: Manager) => {
    const instance = new SocialGameBusinessSocket(cat)
    window.ccLog('ws instance')
    const proxy = new Proxy(instance, handler) as SocialGameBusinessSocket &
        WrapperSocialGameClient
    window.ccLog('ws proxy')
    return proxy
}
