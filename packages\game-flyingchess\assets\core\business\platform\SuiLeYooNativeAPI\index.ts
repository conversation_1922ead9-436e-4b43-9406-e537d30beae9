/**
 * @describe
 * <AUTHOR>
 * @date 2024-09-12 11:35:02
 */

import { error, log } from 'cc'
import bridge from '../../bridge'
import { Bridge } from '../../bridge/Bridge'
import store from '../../store'
import { H5API } from '../H5API'
import { ClientBootParam, ClientBootParamSchema } from 'sgc'

import { RoomEventConstant } from '../../constant'
import { cat } from '@/core/manager'
import { fromJson } from '@bufbuild/protobuf'

export class SuiLeYooNativeAPI extends H5API {
    protected override netWorkStatusListener() {
        bridge.nativeAPI.on<Bridge.EventName.CommonName>(
            Bridge.EventName.CommonName.NETWORK_AVAILABLE,
            (statu: boolean) => {
                statu ? this.online() : this.offline()
            },
            this
        )
    }

    override getEnvironmentAsync(): Promise<string> {
        // 获取运行环境
        return new Promise((resolve, _reject) => {
            const cb = (res: IBridge.ResCompleteRes) => {
                // const { match } = store
                // match.gameId = res.gameId
                // match.micState = res.micState
                // match.voiceState = res.voiceState
                // match.musicState = res.musicState
                // match.giftState = res?.giftState ?? 1
                // match.invitePlay = res.invitePlay
                // match.gameState = res.gameState
                // match.banner = res.bannerSpecial
                // match.appType = res.appType
                // const env = ['pro', 'pre'].includes(res.gameEnv) ? 'release' : 'trial'
                // window.ccLog('获取运行环境', env)
                // resolve(env)
            }
            bridge.nativeAPI.once<Bridge.EventName.CommonName>(
                Bridge.EventName.CommonName.RES_COMPLETE,
                cb,
                Symbol()
            )
            bridge.cocosAPI.callStaticMethod(
                Bridge.MethodName.CocosBridge.LOAD_COMMON_REQUEST,
                Bridge.EventName.CommonName.RES_COMPLETE
            )
        })
    }

    override async serverLogin(): Promise<void> {
        return new Promise((resolve, _reject) => {
            const cb = (res: IBridge.IResponse<IBridge.EnterParam>) => {
                const authParams = fromJson(ClientBootParamSchema, res.data)
                store.user.auth = authParams
                window.ccLog('authParams', JSON.stringify(authParams))
                resolve()
            }
            bridge.nativeAPI.once(
                Bridge.EventName.APIName.ENTER_PARAM,
                cb,
                Symbol()
            )
            bridge.cocosAPI.callStaticMethod(
                Bridge.MethodName.CocosBridge.LOAD_API_REQUEST,
                Bridge.EventName.APIName.ENTER_PARAM
            )
        })
    }

    override share(option: IBridge.ShareOption = { type: 1 }): void {
        bridge.cocosAPI.callStaticMethod(
            Bridge.MethodName.CocosBridge.LOAD_BRIDGE_REQUEST,
            Bridge.EventName.BridgeName.SHARE,
            option
        )
    }

    override report(): void {
        bridge.cocosAPI.callStaticMethod(
            Bridge.MethodName.CocosBridge.LOAD_BRIDGE_REQUEST,
            Bridge.EventName.BridgeName.REPORT
        )
    }

    override copyText(text: string): void {
        bridge.cocosAPI.callStaticMethod<
            Bridge.MethodName.CocosBridge.LOAD_COMMON_REQUEST,
            string
        >(
            Bridge.MethodName.CocosBridge.LOAD_COMMON_REQUEST,
            Bridge.EventName.CommonName.COPY_TEXT,
            text
        )
    }

    override loginChatRoom(params: IBridge.MatchRoomRes) {
        return new Promise<void>((resolve, _reject) => {
            // this.roomOpenTime(store.match.roomInfo.openTime)
            bridge.nativeAPI.once<Bridge.EventName.ChatRoomName>(
                Bridge.EventName.ChatRoomName.LOGIN_CHAT_ROOM,
                () => {
                    window.ccLog('登录聊天室成功')
                    resolve()
                },
                Symbol()
            )
            bridge.cocosAPI.callStaticMethod(
                Bridge.MethodName.CocosBridge.LOAD_CHATROOM_REQUEST,
                Bridge.EventName.ChatRoomName.LOGIN_CHAT_ROOM,
                params
            )
        })
    }

    override logoutChatRoom(): this {
        bridge.cocosAPI.callStaticMethod(
            Bridge.MethodName.CocosBridge.LOAD_CHATROOM_REQUEST,
            Bridge.EventName.ChatRoomName.LOGOUT_CHAT_ROOM
        )
        return this
    }
    override back(): void {
        bridge.cocosAPI.callStaticMethod(
            Bridge.MethodName.CocosBridge.LOAD_BRIDGE_REQUEST,
            Bridge.EventName.BridgeName.BACK
        )
    }
    override task(): void {
        bridge.cocosAPI.callStaticMethod(
            Bridge.MethodName.CocosBridge.LOAD_BRIDGE_REQUEST,
            Bridge.EventName.BridgeName.TASK
        )
    }
    override mall(): void {
        bridge.cocosAPI.callStaticMethod(
            Bridge.MethodName.CocosBridge.LOAD_BRIDGE_REQUEST,
            Bridge.EventName.BridgeName.MALL
        )
    }

    override audience(): void {
        bridge.cocosAPI.callStaticMethod(
            Bridge.MethodName.CocosBridge.LOAD_BRIDGE_REQUEST,
            Bridge.EventName.BridgeName.AUDIENCE
        )
    }

    override matchRoom<T extends IBridge.MatchRoomRes>(
        params: IBridge.CreateRoomReq
    ): Promise<T> {
        return new Promise<T>((resolve, reject) => {
            bridge.nativeAPI.once<Bridge.EventName.APIName>(
                Bridge.EventName.APIName.MATCHING,
                (res: IBridge.IResponse<T>) => {
                    if ('errorCode' in res) {
                        reject()
                    } else {
                        resolve(res.data)
                    }
                },
                Symbol()
            )
            bridge.cocosAPI.callStaticMethod<
                Bridge.MethodName.CocosBridge.LOAD_API_REQUEST,
                IBridge.MatchRoomReq
            >(
                Bridge.MethodName.CocosBridge.LOAD_API_REQUEST,
                Bridge.EventName.APIName.MATCHING,
                params
            )
        })
    }
    override getAssets<T extends IBridge.UserAssetRes>(): Promise<void> {
        return new Promise<void>((resolve, reject) => {
            bridge.nativeAPI.once(
                Bridge.EventName.APIName.ASSETS,
                (res: IBridge.IResponse<T>) => {
                    if ('errorCode' in res) {
                        reject()
                    } else {
                        store.user.assets = res.data.point
                        resolve()
                    }
                },
                Symbol()
            )
            bridge.cocosAPI.callStaticMethod(
                Bridge.MethodName.CocosBridge.LOAD_API_REQUEST,
                Bridge.EventName.APIName.ASSETS
            )
        })
    }
    override searchRoom<T extends IBridge.SearchRoomRes>(
        params: IBridge.SearchRoomReq
    ): Promise<T> {
        return new Promise((resolve, reject) => {
            bridge.nativeAPI.once(
                Bridge.EventName.APIName.SEARCH,
                (res: IBridge.IResponse<T>) => {
                    if ('errorCode' in res) {
                        reject()
                    } else {
                        store.lobby.matchRoom = res.data
                        resolve(res.data)
                    }
                },
                Symbol()
            )
            bridge.cocosAPI.callStaticMethod(
                Bridge.MethodName.CocosBridge.LOAD_API_REQUEST,
                Bridge.EventName.APIName.SEARCH,
                params
            )
        })
    }
    override createRoom<T extends IBridge.CreateRoomRes>(
        params: IBridge.CreateRoomReq
    ): Promise<T> {
        return new Promise<T>((resolve, reject) => {
            bridge.nativeAPI.once(
                Bridge.EventName.APIName.CREATE,
                (res: IBridge.IResponse<T>) => {
                    if ('errorCode' in res) {
                        reject()
                    } else {
                        resolve(res.data)
                    }
                },
                Symbol()
            )
            bridge.cocosAPI.callStaticMethod(
                Bridge.MethodName.CocosBridge.LOAD_API_REQUEST,
                Bridge.EventName.APIName.CREATE,
                params
            )
        })
    }
    override recharge(): void {
        // const type: IBridge.AssetsType = store.match.appType
        // bridge.cocosAPI.callStaticMethod(Bridge.MethodName.CocosBridge.LOAD_COMMON_REQUEST, Bridge.EventName.CommonName.RECHARGE, type)
    }

    override getUserInfo<T extends IBridge.UserGameDataRes>(
        params: IBridge.UserGameDataReq
    ): Promise<void> {
        return new Promise((resolve, reject) => {
            bridge.nativeAPI.once(
                Bridge.EventName.APIName.USER_GAME_DATA,
                (res: IBridge.IResponse<T>) => {
                    if ('errorCode' in res) {
                        reject()
                    } else {
                        store.user.userGameData = res.data
                        resolve()
                    }
                },
                Symbol()
            )
            bridge.cocosAPI.callStaticMethod(
                Bridge.MethodName.CocosBridge.LOAD_API_REQUEST,
                Bridge.EventName.APIName.USER_GAME_DATA,
                params
            )
        })
    }

    override getUserProfile<T extends IBridge.Profile[]>(
        params: IBridge.QueryUserInfoReq
    ): Promise<T> {
        return new Promise<T>((resolve, reject) => {
            bridge.nativeAPI.once(
                Bridge.EventName.APIName.CHECK_USER_PROFILES,
                (res: IBridge.IResponse<T>) => {
                    window.ccLog('res', JSON.stringify(res))
                    if ('errorCode' in res) {
                        reject()
                    } else {
                        resolve(res.data)
                    }
                },
                Symbol()
            )
            bridge.cocosAPI.callStaticMethod(
                Bridge.MethodName.CocosBridge.LOAD_API_REQUEST,
                Bridge.EventName.APIName.CHECK_USER_PROFILES,
                params
            )
        })
    }

    override getMikeList<T extends IBridge.MikeListRes>(
        room_id: IBridge.MikeListReq
    ): Promise<T> {
        return new Promise<T>((resolve, reject) => {
            bridge.nativeAPI.once(
                Bridge.EventName.APIName.MIKE_LIST,
                (res: IBridge.IResponse<T>) => {
                    if ('errorCode' in res) {
                        reject()
                    } else {
                        // store.match.mikeUserList = res.data.mikeList
                        // store.match.roomOwner = res.data.tempHownerId
                        resolve(res.data)
                    }
                },
                Symbol()
            )
            bridge.cocosAPI.callStaticMethod(
                Bridge.MethodName.CocosBridge.LOAD_API_REQUEST,
                Bridge.EventName.APIName.MIKE_LIST,
                room_id
            )
        })
    }

    override joinChangeMike<T>(params: IBridge.ChangeMikeReq) {
        return new Promise<void>((resolve, reject) => {
            bridge.nativeAPI.once(
                Bridge.EventName.APIName.MIKE_CHANGE_JOIN,
                (res: IBridge.IResponse<T>) => {
                    if ('errorCode' in res) {
                        reject()
                    } else {
                        if (cat.event.has(RoomEventConstant.MIKE_CHANGE)) {
                            cat.event.dispatchEvent(
                                RoomEventConstant.MIKE_CHANGE,
                                params.mikeId
                            )
                        }
                        resolve()
                    }
                },
                Symbol()
            )
            bridge.cocosAPI.callStaticMethod(
                Bridge.MethodName.CocosBridge.LOAD_API_REQUEST,
                Bridge.EventName.APIName.MIKE_CHANGE_JOIN,
                params
            )
        })
    }

    override openProfile(params: { userId: number }): void {
        bridge.cocosAPI.callStaticMethod(
            Bridge.MethodName.CocosBridge.LOAD_BRIDGE_REQUEST,
            Bridge.EventName.BridgeName.AVATAR_CARD,
            params
        )
    }

    override invite(): void {
        bridge.cocosAPI.callStaticMethod(
            Bridge.MethodName.CocosBridge.LOAD_BRIDGE_REQUEST,
            Bridge.EventName.BridgeName.INVITE
        )
    }

    override ready<T extends void>(params: IBridge.ReadyReq): Promise<void> {
        return new Promise<void>((resolve, reject) => {
            bridge.nativeAPI.once(
                Bridge.EventName.APIName.READY,
                (res: IBridge.IResponse<T>) => {
                    if ('errorCode' in res) {
                        reject()
                    } else {
                        resolve()
                    }
                },
                Symbol()
            )
            bridge.cocosAPI.callStaticMethod(
                Bridge.MethodName.CocosBridge.LOAD_API_REQUEST,
                Bridge.EventName.APIName.READY,
                params
            )
        })
    }

    override unReady<T extends void>(params: IBridge.ReadyReq): Promise<void> {
        return new Promise<void>((resolve, reject) => {
            bridge.nativeAPI.once(
                Bridge.EventName.APIName.UNREADY,
                (res: IBridge.IResponse<T>) => {
                    if ('errorCode' in res) {
                        reject()
                    } else {
                        resolve()
                    }
                },
                Symbol()
            )
            bridge.cocosAPI.callStaticMethod(
                Bridge.MethodName.CocosBridge.LOAD_API_REQUEST,
                Bridge.EventName.APIName.UNREADY,
                params
            )
        })
    }

    override banner(params: IBridge.BannerReq) {
        return new Promise<void>((resolve, reject) => {
            bridge.nativeAPI.once(
                Bridge.EventName.BridgeName.BANNER_SPECIAL,
                (res: IBridge.BlockBanner) => {
                    resolve()
                },
                Symbol()
            )
            bridge.cocosAPI.callStaticMethod(
                Bridge.MethodName.CocosBridge.LOAD_BRIDGE_REQUEST,
                Bridge.EventName.BridgeName.BANNER_SPECIAL,
                params
            )
        })
    }

    /**获取房间信息 */
    override getRoomInfo<T extends IBridge.RoomInfoRes>(
        params: IBridge.RoomInfoReq
    ): Promise<T> {
        return new Promise<T>((resolve, reject) => {
            bridge.nativeAPI.once(
                Bridge.EventName.APIName.ROOM_INFO,
                (res: IBridge.IResponse<T>) => {
                    if ('errorCode' in res) {
                        reject()
                    } else {
                        // store.match.roomInfo = res.data
                        resolve(res.data)
                    }
                },
                Symbol()
            )
            bridge.cocosAPI.callStaticMethod(
                Bridge.MethodName.CocosBridge.LOAD_API_REQUEST,
                Bridge.EventName.APIName.ROOM_INFO,
                params
            )
        })
    }

    override openGift(): void {
        if (store.lobby.matchRoom?.ddRid) {
            this.getMikeList({ ddRid: store.lobby.matchRoom.ddRid }).then(
                (res) => {
                    this.updateMike(res)
                    bridge.cocosAPI.callStaticMethod(
                        Bridge.MethodName.CocosBridge.LOAD_BRIDGE_REQUEST,
                        Bridge.EventName.BridgeName.GIFT
                    )
                }
            )
        }
    }

    override voiceState(params: IBridge.MusicState): Promise<void> {
        return new Promise<void>((resolve, reject) => {
            bridge.nativeAPI.once(
                Bridge.EventName.CommonName.VOICE_STATE,
                (res: IBridge.VoiceState) => {
                    resolve()
                },
                Symbol()
            )
            bridge.cocosAPI.callStaticMethod(
                Bridge.MethodName.CocosBridge.LOAD_COMMON_REQUEST,
                Bridge.EventName.CommonName.VOICE_STATE,
                params
            )
        })
    }

    override micState(params: IBridge.VoiceState): Promise<void> {
        return new Promise<void>((resolve, _reject) => {
            bridge.nativeAPI.once(
                Bridge.EventName.CommonName.MIC_STATE,
                (res: IBridge.MicState) => {
                    resolve()
                },
                Symbol()
            )
            bridge.cocosAPI.callStaticMethod(
                Bridge.MethodName.CocosBridge.LOAD_COMMON_REQUEST,
                Bridge.EventName.CommonName.MIC_STATE,
                params
            )
        })
    }

    override musicState(params: IBridge.VoiceState): Promise<void> {
        return new Promise<void>((resolve, _reject) => {
            bridge.nativeAPI.once(
                Bridge.EventName.CommonName.MUSIC_STATE,
                (res: IBridge.MusicState) => {
                    resolve()
                },
                Symbol()
            )
            bridge.cocosAPI.callStaticMethod(
                Bridge.MethodName.CocosBridge.LOAD_COMMON_REQUEST,
                Bridge.EventName.CommonName.MIC_STATE,
                params
            )
        })
    }

    /**发送聊天消息 */
    override sendMessage(text: string): void {
        bridge.cocosAPI.callStaticMethod(
            Bridge.MethodName.CocosBridge.LOAD_CHATROOM_REQUEST,
            Bridge.EventName.ChatRoomName.SEND_MESSAGE,
            text
        )
    }

    override inputMode(mode: IBridge.InputMode): void {
        bridge.cocosAPI.callStaticMethod(
            Bridge.MethodName.CocosBridge.LOAD_COMMON_REQUEST,
            Bridge.EventName.CommonName.UPDATE_INPUTMODE,
            mode
        )
    }

    override follow<T extends void>(params: IBridge.FollowReq): Promise<void> {
        return new Promise<void>((resolve, reject) => {
            bridge.nativeAPI.once(
                Bridge.EventName.APIName.FOLLOW,
                (res: IBridge.IResponse<T>) => {
                    if ('errorCode' in res) {
                        reject()
                    } else {
                        resolve()
                    }
                },
                Symbol()
            )
            bridge.cocosAPI.callStaticMethod(
                Bridge.MethodName.CocosBridge.LOAD_API_REQUEST,
                Bridge.EventName.APIName.FOLLOW,
                params
            )
        })
    }

    override checkFollowedUser<T extends IBridge.QueryFollowedUserRes>(
        params: IBridge.QueryFollowedUserReq
    ): Promise<T> {
        return new Promise((resolve, reject) => {
            bridge.nativeAPI.once(
                Bridge.EventName.APIName.CHECK_FOLLOWED,
                (res: IBridge.IResponse<T>) => {
                    if ('errorCode' in res) {
                        reject()
                    } else {
                        resolve(res.data)
                    }
                },
                Symbol()
            )
            bridge.cocosAPI.callStaticMethod(
                Bridge.MethodName.CocosBridge.LOAD_API_REQUEST,
                Bridge.EventName.APIName.CHECK_FOLLOWED,
                params
            )
        })
    }

    override exitRoom<T extends void>(params: IBridge.ExitRoomReq): Promise<T> {
        return new Promise((resolve, reject) => {
            bridge.nativeAPI.once(
                Bridge.EventName.APIName.EXIT,
                (res: IBridge.IResponse<T>) => {
                    if ('errorCode' in res) {
                        reject()
                    } else {
                        resolve(res.data)
                    }
                },
                Symbol()
            )
            bridge.cocosAPI.callStaticMethod(
                Bridge.MethodName.CocosBridge.LOAD_API_REQUEST,
                Bridge.EventName.APIName.EXIT,
                params
            )
        })
    }
    override updateMike(params: IBridge.MikeListRes) {
        bridge.cocosAPI.callStaticMethod(
            Bridge.MethodName.CocosBridge.LOAD_COMMON_REQUEST,
            Bridge.EventName.CommonName.UPDATE_MIKE,
            params
        )
    }

    override moreDialog(): void {
        bridge.cocosAPI.callStaticMethod(
            Bridge.MethodName.CocosBridge.LOAD_BRIDGE_REQUEST,
            Bridge.EventName.BridgeName.MORE_DIALOG
        )
    }

    rule(): void {
        bridge.cocosAPI.callStaticMethod(
            Bridge.MethodName.CocosBridge.LOAD_BRIDGE_REQUEST,
            Bridge.EventName.BridgeName.RULE
        )
    }

    exit(): void {
        bridge.cocosAPI.callStaticMethod(
            Bridge.MethodName.CocosBridge.LOAD_BRIDGE_REQUEST,
            Bridge.EventName.BridgeName.EXIT
        )
    }

    override openPrivateChat(params: { userId: number }): void {
        bridge.cocosAPI.callStaticMethod(
            Bridge.MethodName.CocosBridge.LOAD_BRIDGE_REQUEST,
            Bridge.EventName.BridgeName.PRIVATE_CHAT,
            params
        )
    }

    override roomOpenTime(params: string): Promise<void> {
        return new Promise<void>((resolve, reject) => {
            bridge.nativeAPI.once(
                Bridge.EventName.CommonName.ROOM_OPEN_TIME,
                (res: IBridge.IResponse<void>) => {
                    if ('errorCode' in res) {
                        reject()
                    } else {
                        resolve(res.data)
                    }
                },
                Symbol()
            )
            bridge.cocosAPI.callStaticMethod(
                Bridge.MethodName.CocosBridge.LOAD_COMMON_REQUEST,
                Bridge.EventName.CommonName.ROOM_OPEN_TIME,
                params
            )
        })
    }

    override getAudienceList<T = IBridge.AudienceListRes>(
        params: IBridge.AudienceListReq
    ): Promise<T> {
        return new Promise<T>((resolve, reject) => {
            bridge.nativeAPI.once(
                Bridge.EventName.APIName.VIEW_LIST,
                (res: IBridge.IResponse<T>) => {
                    if ('errorCode' in res) {
                        reject()
                    } else {
                        resolve(res.data)
                    }
                },
                Symbol()
            )
            bridge.cocosAPI.callStaticMethod(
                Bridge.MethodName.CocosBridge.LOAD_API_REQUEST,
                Bridge.EventName.APIName.VIEW_LIST,
                params
            )
        })
    }

    override mikeUp<T = void>(ddRid: string) {
        return new Promise<T>((resolve, reject) => {
            bridge.nativeAPI.once(
                Bridge.EventName.APIName.MIKE_UP,
                (res: IBridge.IResponse<T>) => {
                    if ('errorCode' in res) {
                        reject()
                    } else {
                        resolve(res.data)
                    }
                },
                Symbol()
            )
            bridge.cocosAPI.callStaticMethod(
                Bridge.MethodName.CocosBridge.LOAD_API_REQUEST,
                Bridge.EventName.APIName.MIKE_UP,
                { ddRid }
            )
        })
    }

    override readyDown<T = void>(ddRid: string) {
        return new Promise<T>((resolve, reject) => {
            bridge.nativeAPI.once(
                Bridge.EventName.APIName.READY_DOWN,
                (res: IBridge.IResponse<T>) => {
                    if ('errorCode' in res) {
                        reject()
                    } else {
                        resolve(res.data)
                    }
                },
                Symbol()
            )
            bridge.cocosAPI.callStaticMethod(
                Bridge.MethodName.CocosBridge.LOAD_API_REQUEST,
                Bridge.EventName.APIName.READY_DOWN,
                { ddRid }
            )
        })
    }

    override ban<T = void>(params: IBridge.BanReq) {
        return new Promise<T>((resolve, reject) => {
            bridge.nativeAPI.once(
                Bridge.EventName.APIName.BAN,
                (res: IBridge.IResponse<T>) => {
                    if ('errorCode' in res) {
                        reject()
                    } else {
                        resolve(res.data)
                    }
                },
                Symbol()
            )
            bridge.cocosAPI.callStaticMethod(
                Bridge.MethodName.CocosBridge.LOAD_API_REQUEST,
                Bridge.EventName.APIName.BAN,
                params
            )
        })
    }
    override mikeLock<T = void>(params: IBridge.MikeLockReq) {
        return new Promise<T>((resolve, reject) => {
            bridge.nativeAPI.once(
                Bridge.EventName.APIName.MIKE_LOCK,
                (res: IBridge.IResponse<T>) => {
                    if ('errorCode' in res) {
                        reject()
                    } else {
                        resolve(res.data)
                    }
                },
                Symbol()
            )
            bridge.cocosAPI.callStaticMethod(
                Bridge.MethodName.CocosBridge.LOAD_API_REQUEST,
                Bridge.EventName.APIName.MIKE_LOCK,
                params
            )
        })
    }
    override mikeUnlock<T = void>(params: IBridge.MikeUnLockReq) {
        return new Promise<T>((resolve, reject) => {
            bridge.nativeAPI.once(
                Bridge.EventName.APIName.MIKE_UNLOCK,
                (res: IBridge.IResponse<T>) => {
                    if ('errorCode' in res) {
                        reject()
                    } else {
                        resolve(res.data)
                    }
                },
                Symbol()
            )
            bridge.cocosAPI.callStaticMethod(
                Bridge.MethodName.CocosBridge.LOAD_API_REQUEST,
                Bridge.EventName.APIName.MIKE_UNLOCK,
                params
            )
        })
    }
    override startGame<T = void>(ddRid: string) {
        return new Promise<T>((resolve, reject) => {
            bridge.nativeAPI.once(
                Bridge.EventName.APIName.START_GAME,
                (res: IBridge.IResponse<T>) => {
                    if ('errorCode' in res) {
                        reject(res)
                    } else {
                        resolve(res.data)
                    }
                },
                Symbol()
            )
            bridge.cocosAPI.callStaticMethod(
                Bridge.MethodName.CocosBridge.LOAD_API_REQUEST,
                Bridge.EventName.APIName.START_GAME,
                { ddRid }
            )
        })
    }

    override asrConnect(state: boolean): void {
        bridge.cocosAPI.callStaticMethod(
            Bridge.MethodName.CocosBridge.LOAD_COMMON_REQUEST,
            Bridge.EventName.CommonName.ASRCONNECT,
            state
        )
    }
    override asrOpen(state: boolean): void {
        bridge.cocosAPI.callStaticMethod(
            Bridge.MethodName.CocosBridge.LOAD_COMMON_REQUEST,
            Bridge.EventName.CommonName.ASROPEN,
            state
        )
    }

    override clickEvent(params: IBridge.ClickEvent): void {
        bridge.cocosAPI.callStaticMethod(
            Bridge.MethodName.CocosBridge.LOAD_BRIDGE_REQUEST,
            Bridge.EventName.BridgeName.CLICK_EVENT,
            params
        )
    }

    override joinArena<T = IBridge.JoinArenaRes>(
        params: IBridge.JoinArenaReq
    ): Promise<T> {
        return new Promise<T>((resolve, reject) => {
            bridge.nativeAPI.once(
                Bridge.EventName.APIV2Name.JOIN_ARENA,
                (res: IBridge.IResponse<T>) => {
                    if ('errorCode' in res) {
                        reject(res)
                    } else {
                        resolve(res.data)
                    }
                },
                Symbol()
            )
            bridge.cocosAPI.callStaticMethod(
                Bridge.MethodName.CocosBridge.LOAD_API_REQUEST,
                Bridge.EventName.APIV2Name.JOIN_ARENA,
                params
            )
        })
    }
    override cancelJoinArena<T = IBridge.CancelArenaRes>(
        params: IBridge.CancelArenaReq
    ): Promise<T> {
        return new Promise<T>((resolve, reject) => {
            bridge.nativeAPI.once(
                Bridge.EventName.APIV2Name.EXIT_ARENA,
                (res: IBridge.IResponse<T>) => {
                    if ('errorCode' in res) {
                        reject(res)
                    } else {
                        resolve(res.data)
                    }
                },
                Symbol()
            )
            bridge.cocosAPI.callStaticMethod(
                Bridge.MethodName.CocosBridge.LOAD_API_REQUEST,
                Bridge.EventName.APIV2Name.EXIT_ARENA,
                params
            )
        })
    }

    override tierRank<T = IBridge.TierRankRes>(
        params: IBridge.TierRankReq
    ): Promise<T> {
        return new Promise<T>((resolve, reject) => {
            bridge.nativeAPI.once(
                Bridge.EventName.APIV2Name.TIER_RANK,
                (res: IBridge.IResponse<T>) => {
                    if ('errorCode' in res) {
                        reject(res)
                    } else {
                        resolve(res.data)
                    }
                },
                Symbol()
            )
            bridge.cocosAPI.callStaticMethod(
                Bridge.MethodName.CocosBridge.LOAD_API_REQUEST,
                Bridge.EventName.APIV2Name.TIER_RANK,
                params
            )
        })
    }
    override levelRank<T = IBridge.LevelRankRes>(
        params: IBridge.LevelRankReq
    ): Promise<T> {
        return new Promise<T>((resolve, reject) => {
            bridge.nativeAPI.once(
                Bridge.EventName.APIV2Name.LEVEL_RANK,
                (res: IBridge.IResponse<T>) => {
                    if ('errorCode' in res) {
                        reject(res)
                    } else {
                        resolve(res.data)
                    }
                },
                Symbol()
            )
            bridge.cocosAPI.callStaticMethod(
                Bridge.MethodName.CocosBridge.LOAD_API_REQUEST,
                Bridge.EventName.APIV2Name.LEVEL_RANK,
                params
            )
        })
    }
    override roomList<T = IBridge.RoomInfoRes>(
        params: IBridge.RoomInfoReq
    ): Promise<T> {
        return new Promise<T>((resolve, reject) => {
            bridge.nativeAPI.once(
                Bridge.EventName.APIV2Name.ROOM_LIST,
                (res: IBridge.IResponse<T>) => {
                    if ('errorCode' in res) {
                        reject(res)
                    } else {
                        resolve(res.data)
                    }
                },
                Symbol()
            )
            bridge.cocosAPI.callStaticMethod(
                Bridge.MethodName.CocosBridge.LOAD_API_REQUEST,
                Bridge.EventName.APIV2Name.ROOM_LIST,
                params
            )
        })
    }
    override roomExpertList<T = IBridge.GameExpertRes>(
        params: IBridge.GameExpertReq
    ): Promise<T> {
        return new Promise<T>((resolve, reject) => {
            bridge.nativeAPI.once(
                Bridge.EventName.APIV2Name.ROOM_EXPERT_LIST,
                (res: IBridge.IResponse<T>) => {
                    if ('errorCode' in res) {
                        reject(res)
                    } else {
                        resolve(res.data)
                    }
                },
                Symbol()
            )
            bridge.cocosAPI.callStaticMethod(
                Bridge.MethodName.CocosBridge.LOAD_API_REQUEST,
                Bridge.EventName.APIV2Name.ROOM_EXPERT_LIST,
                params
            )
        })
    }
    override quickJoin<T = void>(params: IBridge.QuickJoinReq): Promise<T> {
        return new Promise<T>((resolve, reject) => {
            bridge.nativeAPI.once(
                Bridge.EventName.APIV2Name.QUICK_JOIN,
                (res: IBridge.IResponse<T>) => {
                    if ('errorCode' in res) {
                        reject(res)
                    } else {
                        resolve(res.data)
                    }
                },
                Symbol()
            )
            bridge.cocosAPI.callStaticMethod(
                Bridge.MethodName.CocosBridge.LOAD_API_REQUEST,
                Bridge.EventName.APIV2Name.QUICK_JOIN,
                params
            )
        })
    }
    override cancelQuickJoin<T = void>(
        params: IBridge.CancelQuickJoinReq
    ): Promise<T> {
        return new Promise<T>((resolve, reject) => {
            bridge.nativeAPI.once(
                Bridge.EventName.APIV2Name.CANCEL_QUICK_JOIN,
                (res: IBridge.IResponse<T>) => {
                    if ('errorCode' in res) {
                        reject(res)
                    } else {
                        resolve(res.data)
                    }
                },
                Symbol()
            )
            bridge.cocosAPI.callStaticMethod(
                Bridge.MethodName.CocosBridge.LOAD_API_REQUEST,
                Bridge.EventName.APIV2Name.CANCEL_QUICK_JOIN,
                params
            )
        })
    }

    override seasonSettle<T = IBridge.SeasonSettleRes>(
        params: IBridge.SeasonSettleReq
    ): Promise<T> {
        return new Promise<T>((resolve, reject) => {
            bridge.nativeAPI.once(
                Bridge.EventName.APIV2Name.SEASON_SETTLE,
                (res: IBridge.IResponse<T>) => {
                    if ('errorCode' in res) {
                        reject(res)
                    } else {
                        resolve(res.data)
                    }
                },
                Symbol()
            )
            bridge.cocosAPI.callStaticMethod(
                Bridge.MethodName.CocosBridge.LOAD_API_REQUEST,
                Bridge.EventName.APIV2Name.SEASON_SETTLE,
                params
            )
        })
    }
    override like<T = void>(params: IBridge.LikeReq): Promise<T> {
        return new Promise<T>((resolve, reject) => {
            bridge.nativeAPI.once(
                Bridge.EventName.APIV2Name.LIKE,
                (res: IBridge.IResponse<T>) => {
                    if ('errorCode' in res) {
                        reject(res)
                    } else {
                        resolve(res.data)
                    }
                },
                Symbol()
            )
            bridge.cocosAPI.callStaticMethod(
                Bridge.MethodName.CocosBridge.LOAD_API_REQUEST,
                Bridge.EventName.APIV2Name.LIKE,
                params
            )
        })
    }

    override hangUpGame() {
        bridge.cocosAPI.callStaticMethod(
            Bridge.MethodName.CocosBridge.LOAD_BRIDGE_REQUEST,
            Bridge.EventName.BridgeName.HANG_UP_GAME
        )
    }

    override longLink<T = IBridge.LongLinkRes>(
        params: IBridge.LongLinkReq
    ): Promise<T> {
        return new Promise<T>((resolve, reject) => {
            bridge.nativeAPI.once(
                Bridge.EventName.APIV2Name.LONG_LINK,
                (res: IBridge.IResponse<T>) => {
                    if ('errorCode' in res) {
                        reject(res)
                    } else {
                        resolve(res.data)
                    }
                },
                Symbol()
            )
            bridge.cocosAPI.callStaticMethod(
                Bridge.MethodName.CocosBridge.LOAD_API_REQUEST,
                Bridge.EventName.APIV2Name.LONG_LINK,
                params
            )
        })
    }

    override modifyJoinState<T = IBridge.ModifyJoinStateRes>(
        params: IBridge.ModifyJoinStateReq
    ): Promise<T> {
        return new Promise<T>((resolve, reject) => {
            bridge.nativeAPI.once(
                Bridge.EventName.APIV2Name.MODIFY_JOIN_STATE,
                (res: IBridge.IResponse<T>) => {
                    if ('errorCode' in res) {
                        reject(res)
                    } else {
                        resolve(res.data)
                    }
                },
                Symbol()
            )
            bridge.cocosAPI.callStaticMethod(
                Bridge.MethodName.CocosBridge.LOAD_API_REQUEST,
                Bridge.EventName.APIV2Name.MODIFY_JOIN_STATE,
                params
            )
        })
    }
    override sitDown<T = IBridge.SitDownRes>(
        params: IBridge.SitDownReq
    ): Promise<T> {
        return new Promise<T>((resolve, reject) => {
            bridge.nativeAPI.once(
                Bridge.EventName.APIV2Name.SIT_DOWN,
                (res: IBridge.IResponse<T>) => {
                    if ('errorCode' in res) {
                        reject(res)
                    } else {
                        resolve(res.data)
                    }
                },
                Symbol()
            )
            bridge.cocosAPI.callStaticMethod(
                Bridge.MethodName.CocosBridge.LOAD_API_REQUEST,
                Bridge.EventName.APIV2Name.SIT_DOWN,
                params
            )
        })
    }

    override gameStateInfo<T = IBridge.GameStateInfoRes>(
        params: IBridge.GameStateInfoReq
    ): Promise<T> {
        return new Promise<T>((resolve, reject) => {
            bridge.nativeAPI.once(
                Bridge.EventName.APIV2Name.GAME_STATE_INFO,
                (res: IBridge.IResponse<T>) => {
                    if ('errorCode' in res) {
                        reject(res)
                    } else {
                        resolve(res.data)
                    }
                },
                Symbol()
            )
            bridge.cocosAPI.callStaticMethod(
                Bridge.MethodName.CocosBridge.LOAD_API_REQUEST,
                Bridge.EventName.APIV2Name.GAME_STATE_INFO,
                params
            )
        })
    }

    override taskReceive<T = IBridge.TaskReceiveRes>(
        params: IBridge.TaskReceiveReq
    ): Promise<T> {
        return new Promise<T>((resolve, reject) => {
            bridge.nativeAPI.once(
                Bridge.EventName.APIV2Name.TASK_RECEIVE,
                (res: IBridge.IResponse<T>) => {
                    if ('errorCode' in res) {
                        reject(res)
                    } else {
                        resolve(res.data)
                    }
                },
                Symbol()
            )
            bridge.cocosAPI.callStaticMethod(
                Bridge.MethodName.CocosBridge.LOAD_API_REQUEST,
                Bridge.EventName.APIV2Name.TASK_RECEIVE,
                params
            )
        })
    }

    override taskList<T = IBridge.TaskRes>(
        params: IBridge.TaskReq
    ): Promise<T> {
        return new Promise<T>((resolve, reject) => {
            bridge.nativeAPI.once(
                Bridge.EventName.APIV2Name.TASK,
                (res: IBridge.IResponse<T>) => {
                    if ('errorCode' in res) {
                        reject(res)
                    } else {
                        resolve(res.data)
                    }
                },
                Symbol()
            )
            bridge.cocosAPI.callStaticMethod(
                Bridge.MethodName.CocosBridge.LOAD_API_REQUEST,
                Bridge.EventName.APIV2Name.TASK,
                params
            )
        })
    }

    override inviteAccept<T = void>(params: IBridge.InviteAcceptReq) {
        return new Promise<T>((resolve, reject) => {
            bridge.nativeAPI.once(
                Bridge.EventName.APIV2Name.InviteAccept,
                (res: IBridge.IResponse<T>) => {
                    if ('errorCode' in res) {
                        reject(res)
                    } else {
                        resolve(res.data)
                    }
                },
                Symbol()
            )
            bridge.cocosAPI.callStaticMethod(
                Bridge.MethodName.CocosBridge.LOAD_API_REQUEST,
                Bridge.EventName.APIV2Name.InviteAccept,
                params
            )
        })
    }

    override inviteRefuse<T = void>(params: IBridge.InviteRefuseReq) {
        return new Promise<T>((resolve, reject) => {
            bridge.nativeAPI.once(
                Bridge.EventName.APIV2Name.InviteRefuse,
                (res: IBridge.IResponse<T>) => {
                    if ('errorCode' in res) {
                        reject(res)
                    } else {
                        resolve(res.data)
                    }
                },
                Symbol()
            )
            bridge.cocosAPI.callStaticMethod(
                Bridge.MethodName.CocosBridge.LOAD_API_REQUEST,
                Bridge.EventName.APIV2Name.InviteRefuse,
                params
            )
        })
    }
}
