/**
 * @describe 根级别的UI层  所有的ROOT UI层需继承自该自组件
 * <AUTHOR>
 * @date 2023-08-04 10:42:26
 */

import {
    Node,
    Vec3,
    Widget,
    __private,
    director,
    log,
    math,
    tween,
    v3,
    view,
} from 'cc'
import { BaseComponent } from '../base/BaseComponent'
import UILayer from './UILayer'
import { cat } from '../../index'
import { GlobalEventConstant } from '../../constant'

export default class RootUILayer<T extends object> extends UILayer<T> {
    protected override onEnable(): void {
        super.onEnable()
        window.ccLog('---------onEnable', this.node.name)
        this.updateMask()
    }

    protected override onDisable(): void {
        super.onDisable()
        window.ccLog('---------onDisable', this.node.name)
        this.updateMask()
    }

    private updateMask() {
        cat.event.dispatchEvent(GlobalEventConstant.ROOT_MASK_UPDATE)
    }
}
