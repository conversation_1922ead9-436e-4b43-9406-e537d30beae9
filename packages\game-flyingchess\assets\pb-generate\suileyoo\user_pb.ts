// @generated by protoc-gen-es v2.2.5 with parameter "target=ts,import_extension=none,json_types=true"
// @generated from file user.proto (package protos, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage } from "@bufbuild/protobuf/codegenv1";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv1";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file user.proto.
 */
export const file_user: GenFile = /*@__PURE__*/
  fileDesc("Cgp1c2VyLnByb3RvEgZwcm90b3MigAEKBFVzZXIaSwoMQmluZGluZ1N0YXRlEgoKAlFRGAEgASgIEg4KBndlY2hhdBgCIAEoCBINCgVwaG9uZRgDIAEoCBIQCghwYXNzd29yZBgEIAEoCCIrCgZHZW5kZXISCwoHTk9UX1NFVBAAEggKBE1BTEUQARIKCgZGRU1BTEUQAiKAAwoQVXNlckluZm9SZXNwb25zZRILCgN1aWQYASABKAQSDgoGYXZhdGFyGAIgASgJEhEKCWNlbGxwaG9uZRgDIAEoCRIQCghiaXJ0aGRheRgEIAEoCRIUCgxkaXNwbGF5X25hbWUYBSABKAkSFwoPaW52aXRhdGlvbl9jb2RlGAYgASgJEhAKCG5pY2tuYW1lGAcgASgJEiMKBmdlbmRlchgIIAEoDjITLnByb3Rvcy5Vc2VyLkdlbmRlchIMCgRzaWduGAkgASgIEhUKDXJlZ2lzdGVyZWRfYXQYCiABKAMSDQoFbGV2ZWwYCyABKAQSDwoHYmFsYW5jZRgMIAEoCRINCgVwb2ludBgNIAEoBBIWCg5wbGF5X3RpbWVfcGFpZBgOIAEoBBIWCg5wbGF5X3RpbWVfZnJlZRgPIAEoBBIwCg1iaW5kaW5nX3N0YXRlGBAgASgLMhkucHJvdG9zLlVzZXIuQmluZGluZ1N0YXRlEg4KBnN0YXR1cxgRIAEoDSKYAQoUVXNlckluZm9Ob3RpZmljYXRpb24SDgoGYXZhdGFyGAEgASgJEhEKCWNlbGxwaG9uZRgCIAEoCRIQCghiaXJ0aGRheRgDIAEoCRIUCgxkaXNwbGF5X25hbWUYBCABKAkSEAoIbmlja25hbWUYBSABKAkSIwoGZ2VuZGVyGAYgASgOMhMucHJvdG9zLlVzZXIuR2VuZGVyQjAKJGNvbS5zdG50cy5jbG91ZC5zdWlsZXlvby5lY2hvLnByb3Rvc1oILi9wcm90b3NiBnByb3RvMw");

/**
 * @generated from message protos.User
 */
export type User = Message<"protos.User"> & {
};

/**
 * @generated from message protos.User
 */
export type UserJson = {
};

/**
 * Describes the message protos.User.
 * Use `create(UserSchema)` to create a new message.
 */
export const UserSchema: GenMessage<User, UserJson> = /*@__PURE__*/
  messageDesc(file_user, 0);

/**
 * @generated from message protos.User.BindingState
 */
export type User_BindingState = Message<"protos.User.BindingState"> & {
  /**
   * @generated from field: bool QQ = 1;
   */
  QQ: boolean;

  /**
   * @generated from field: bool wechat = 2;
   */
  wechat: boolean;

  /**
   * @generated from field: bool phone = 3;
   */
  phone: boolean;

  /**
   * @generated from field: bool password = 4;
   */
  password: boolean;
};

/**
 * @generated from message protos.User.BindingState
 */
export type User_BindingStateJson = {
  /**
   * @generated from field: bool QQ = 1;
   */
  QQ?: boolean;

  /**
   * @generated from field: bool wechat = 2;
   */
  wechat?: boolean;

  /**
   * @generated from field: bool phone = 3;
   */
  phone?: boolean;

  /**
   * @generated from field: bool password = 4;
   */
  password?: boolean;
};

/**
 * Describes the message protos.User.BindingState.
 * Use `create(User_BindingStateSchema)` to create a new message.
 */
export const User_BindingStateSchema: GenMessage<User_BindingState, User_BindingStateJson> = /*@__PURE__*/
  messageDesc(file_user, 0, 0);

/**
 * @generated from enum protos.User.Gender
 */
export enum User_Gender {
  /**
   * 未设置
   *
   * @generated from enum value: NOT_SET = 0;
   */
  NOT_SET = 0,

  /**
   * 男
   *
   * @generated from enum value: MALE = 1;
   */
  MALE = 1,

  /**
   * 女
   *
   * @generated from enum value: FEMALE = 2;
   */
  FEMALE = 2,
}

/**
 * @generated from enum protos.User.Gender
 */
export type User_GenderJson = "NOT_SET" | "MALE" | "FEMALE";

/**
 * Describes the enum protos.User.Gender.
 */
export const User_GenderSchema: GenEnum<User_Gender, User_GenderJson> = /*@__PURE__*/
  enumDesc(file_user, 0, 0);

/**
 * 用户基本信息的响应
 *
 * @generated from message protos.UserInfoResponse
 */
export type UserInfoResponse = Message<"protos.UserInfoResponse"> & {
  /**
   * 用户ID
   *
   * @generated from field: uint64 uid = 1;
   */
  uid: bigint;

  /**
   * 用户头像
   *
   * @generated from field: string avatar = 2;
   */
  avatar: string;

  /**
   * 绑定的手机号
   *
   * @generated from field: string cellphone = 3;
   */
  cellphone: string;

  /**
   * 出生日期, 为""表示未填写
   *
   * @generated from field: string birthday = 4;
   */
  birthday: string;

  /**
   * 显示的昵称
   *
   * @generated from field: string display_name = 5;
   */
  displayName: string;

  /**
   * 邀请码
   *
   * @generated from field: string invitation_code = 6;
   */
  invitationCode: string;

  /**
   * 用户昵称
   *
   * @generated from field: string nickname = 7;
   */
  nickname: string;

  /**
   * 性别
   *
   * @generated from field: protos.User.Gender gender = 8;
   */
  gender: User_Gender;

  /**
   * 今日是否签到
   *
   * @generated from field: bool sign = 9;
   */
  sign: boolean;

  /**
   * 注册时间
   *
   * @generated from field: int64 registered_at = 10;
   */
  registeredAt: bigint;

  /**
   * vip等级，0 表示普通用户
   *
   * @generated from field: uint64 level = 11;
   */
  level: bigint;

  /**
   * 云币余额
   *
   * @generated from field: string balance = 12;
   */
  balance: string;

  /**
   * 积分
   *
   * @generated from field: uint64 point = 13;
   */
  point: bigint;

  /**
   * 付费时长，单位：秒
   *
   * @generated from field: uint64 play_time_paid = 14;
   */
  playTimePaid: bigint;

  /**
   * 免费体验时长，单位：秒
   *
   * @generated from field: uint64 play_time_free = 15;
   */
  playTimeFree: bigint;

  /**
   * 绑定状态
   *
   * @generated from field: protos.User.BindingState binding_state = 16;
   */
  bindingState?: User_BindingState;

  /**
   * 用户状态，2|内侧用户，9|管理员
   *
   * @generated from field: uint32 status = 17;
   */
  status: number;
};

/**
 * 用户基本信息的响应
 *
 * @generated from message protos.UserInfoResponse
 */
export type UserInfoResponseJson = {
  /**
   * 用户ID
   *
   * @generated from field: uint64 uid = 1;
   */
  uid?: string;

  /**
   * 用户头像
   *
   * @generated from field: string avatar = 2;
   */
  avatar?: string;

  /**
   * 绑定的手机号
   *
   * @generated from field: string cellphone = 3;
   */
  cellphone?: string;

  /**
   * 出生日期, 为""表示未填写
   *
   * @generated from field: string birthday = 4;
   */
  birthday?: string;

  /**
   * 显示的昵称
   *
   * @generated from field: string display_name = 5;
   */
  displayName?: string;

  /**
   * 邀请码
   *
   * @generated from field: string invitation_code = 6;
   */
  invitationCode?: string;

  /**
   * 用户昵称
   *
   * @generated from field: string nickname = 7;
   */
  nickname?: string;

  /**
   * 性别
   *
   * @generated from field: protos.User.Gender gender = 8;
   */
  gender?: User_GenderJson;

  /**
   * 今日是否签到
   *
   * @generated from field: bool sign = 9;
   */
  sign?: boolean;

  /**
   * 注册时间
   *
   * @generated from field: int64 registered_at = 10;
   */
  registeredAt?: string;

  /**
   * vip等级，0 表示普通用户
   *
   * @generated from field: uint64 level = 11;
   */
  level?: string;

  /**
   * 云币余额
   *
   * @generated from field: string balance = 12;
   */
  balance?: string;

  /**
   * 积分
   *
   * @generated from field: uint64 point = 13;
   */
  point?: string;

  /**
   * 付费时长，单位：秒
   *
   * @generated from field: uint64 play_time_paid = 14;
   */
  playTimePaid?: string;

  /**
   * 免费体验时长，单位：秒
   *
   * @generated from field: uint64 play_time_free = 15;
   */
  playTimeFree?: string;

  /**
   * 绑定状态
   *
   * @generated from field: protos.User.BindingState binding_state = 16;
   */
  bindingState?: User_BindingStateJson;

  /**
   * 用户状态，2|内侧用户，9|管理员
   *
   * @generated from field: uint32 status = 17;
   */
  status?: number;
};

/**
 * Describes the message protos.UserInfoResponse.
 * Use `create(UserInfoResponseSchema)` to create a new message.
 */
export const UserInfoResponseSchema: GenMessage<UserInfoResponse, UserInfoResponseJson> = /*@__PURE__*/
  messageDesc(file_user, 1);

/**
 * 用户信息的通知
 *
 * @generated from message protos.UserInfoNotification
 */
export type UserInfoNotification = Message<"protos.UserInfoNotification"> & {
  /**
   * 用户头像
   *
   * @generated from field: string avatar = 1;
   */
  avatar: string;

  /**
   * 绑定的手机号
   *
   * @generated from field: string cellphone = 2;
   */
  cellphone: string;

  /**
   * 出生日期, 为""表示未填写
   *
   * @generated from field: string birthday = 3;
   */
  birthday: string;

  /**
   * 显示的昵称
   *
   * @generated from field: string display_name = 4;
   */
  displayName: string;

  /**
   * 用户昵称
   *
   * @generated from field: string nickname = 5;
   */
  nickname: string;

  /**
   * 性别
   *
   * @generated from field: protos.User.Gender gender = 6;
   */
  gender: User_Gender;
};

/**
 * 用户信息的通知
 *
 * @generated from message protos.UserInfoNotification
 */
export type UserInfoNotificationJson = {
  /**
   * 用户头像
   *
   * @generated from field: string avatar = 1;
   */
  avatar?: string;

  /**
   * 绑定的手机号
   *
   * @generated from field: string cellphone = 2;
   */
  cellphone?: string;

  /**
   * 出生日期, 为""表示未填写
   *
   * @generated from field: string birthday = 3;
   */
  birthday?: string;

  /**
   * 显示的昵称
   *
   * @generated from field: string display_name = 4;
   */
  displayName?: string;

  /**
   * 用户昵称
   *
   * @generated from field: string nickname = 5;
   */
  nickname?: string;

  /**
   * 性别
   *
   * @generated from field: protos.User.Gender gender = 6;
   */
  gender?: User_GenderJson;
};

/**
 * Describes the message protos.UserInfoNotification.
 * Use `create(UserInfoNotificationSchema)` to create a new message.
 */
export const UserInfoNotificationSchema: GenMessage<UserInfoNotification, UserInfoNotificationJson> = /*@__PURE__*/
  messageDesc(file_user, 2);

