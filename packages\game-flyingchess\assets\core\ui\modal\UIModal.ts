import {
    _decorator,
    Button,
    Component,
    Label,
    log,
    Node,
    Sprite,
    SpriteFrame,
} from 'cc'
import UILayer from '@/core/manager/gui/layer/UILayer'
import { audioEffect } from '@/core/business/hooks/Decorator'
import store from '@/core/business/store'
import { cat } from '@/core/manager'
const { ccclass, property } = _decorator

type UIModalProps = {
    content: string | SpriteFrame | null
    confirmCB: () => void
    cancelCB: () => void
    onDestroy?: () => void
    title?: SpriteFrame
    style?: {
        confirm?: SpriteFrame
        cancel?: SpriteFrame
    } | null
}

/**
 * @describe 模态框
 * <AUTHOR>
 * @date 2024-09-12 11:49:51
 */

@ccclass('UIModal')
export class UIModal extends UILayer<UIModalProps> {
    @property({ type: SpriteFrame, tooltip: '默认标题' })
    default_title: SpriteFrame = null!

    @property({ type: Sprite, tooltip: '标题节点' })
    title: Sprite

    @property({ type: Label, tooltip: '字符串内容按钮' })
    prompt_content_str: Label

    @property({ type: Sprite, tooltip: '图片精灵内容按钮' })
    prompt_content_spriteFrame: Sprite

    @property({ type: Button, tooltip: '确认按钮' })
    btn_confirm: Button

    @property({ type: SpriteFrame, tooltip: '确认按钮精灵图' })
    confirm_spriteFrame: SpriteFrame

    @property({ type: Button, tooltip: '取消按钮' })
    btn_cancel: Button

    @property({ type: SpriteFrame, tooltip: '取消按钮精灵图' })
    cancel_spriteFrame: SpriteFrame

    @property({ type: Button, tooltip: '关闭按钮' })
    btn_close: Button

    private isConfirm: boolean = false

    override props: UIModalProps = {
        title: this.default_title,
        content: null,
        confirmCB: () => {},
        cancelCB: () => {},
        style: null,
    }

    protected override onLoad(): void {
        this.btn_cancel.node.on(
            Button.EventType.CLICK,
            this.onCancelHandler,
            this
        )
        this.btn_confirm.node.on(
            Button.EventType.CLICK,
            this.onConfirmHandler,
            this
        )
        this.btn_close.node.on(
            Button.EventType.CLICK,
            this.onCloseHandler,
            this
        )

        this.addAutorun([
            () => {
                this.title.spriteFrame = this.props?.title || this.default_title
            },
            () => {
                if (this.props.content instanceof SpriteFrame) {
                    //图片
                    this.prompt_content_spriteFrame.spriteFrame =
                        this.props.content
                } else if (typeof this.props.content === 'string') {
                    this.prompt_content_str.string = this.props.content
                } else {
                    console.error('未知类型的【UIModal】内容')
                }
            },
            () => {
                if (this.props?.style?.confirm === null) {
                    this.btn_confirm.node.active = false
                } else {
                    this.btn_confirm.node.active = true
                    this.btn_confirm.getComponent(Sprite)!.spriteFrame =
                        this.props.style?.confirm || this.confirm_spriteFrame
                }

                if (this.props?.style?.cancel === null) {
                    this.btn_cancel.node.active = false
                } else {
                    this.btn_cancel.node.active = true
                    this.btn_cancel.getComponent(Sprite)!.spriteFrame =
                        this.props.style?.cancel || this.cancel_spriteFrame
                }
            },
        ])
    }

    private close() {
        this.props.cancelCB?.()
        cat.gui.closeUI(this)
    }

    @audioEffect()
    private onCancelHandler() {
        this.close()
    }

    @audioEffect()
    private onConfirmHandler() {
        this.props.confirmCB?.()
    }

    override onDestroy(): void {
        if (this.isConfirm) this.props.onDestroy?.()
        window.ccLog('-----onDestroy')
    }

    @audioEffect()
    private onCloseHandler() {
        this.close()
    }
}
