/**
 * @describe UI层  所有的UI层需继承自该自组件
 * <AUTHOR>
 * @date 2023-08-04 10:42:26
 */

import {
    Node,
    Vec3,
    Widget,
    __private,
    director,
    math,
    tween,
    v3,
    view,
} from 'cc'
import { BaseComponent, IUIOption } from '../base/BaseComponent'

/**
 * left    从左往右
 * right   从右往左
 * top     从上往下
 * bot     从下往上
 * center  中间
 */
export type IDirection = 'left' | 'right' | 'top' | 'bot' | 'center'

/**开启动画参数选项 */
export interface IOpenOptions<T extends object, U extends object>
    extends IUIOption<T, U> {
    /**动画 */
    isMotion?: boolean
    /**动画时间 */
    duration?: number
    /**方向 */
    direction?: IDirection
    /**是否回弹 */
    isBounce?: boolean
    /**回弹时间 */
    bounceDuration?: number
    noMask?: boolean
}

/**关闭动画参数选项 */
export interface ICloseOptions<T extends object, U extends object>
    extends IUIOption<T, U> {
    /**动画 */
    isMotion?: boolean
    /**动画时间 */
    duration?: number
    /**方向 */
    direction?: IDirection
}

export default class UILayer<
    T extends object = {},
    U extends object = {}
> extends BaseComponent<T, U> {
    /**上次进入的方向 */
    lastEnterDirection: IDirection = 'center'

    /**屏幕尺寸 */
    screen: math.Size

    /**是否正在关闭 */
    isClosing: boolean = false

    /**组件所在的UI最顶层(不包含UI) */
    public root: Node

    constructor() {
        super()
        this._init()
    }

    private _init() {
        this.root = this.node
        this.screen = view.getVisibleSize()
    }

    // protected onLoad() {
    //     // TODO 预留做弹窗动画
    //     this.node.addComponent(BlockInputEvents)
    // }

    /**显示动画 */
    public async showTween<T extends object, U extends object = {}>({
        isMotion = true,
        direction = 'center',
        duration = 0.1,
        isBounce = true,
        bounceDuration = 0.05,
    }: IOpenOptions<T, U>) {
        this.lastEnterDirection = direction
        if (isMotion) {
            const widget = this.node.getComponent(Widget)

            if (widget) {
                widget.updateAlignment()
                widget.enabled = false
            }
            // 起点坐标
            let start: Vec3
            // 终点坐标
            let end: Vec3
            // 回弹坐标 TODO
            const bounce = v3(1.1, 1.1, 1)

            if (direction == 'center') {
                // 从中间放大
                start = v3(0.01, 0.01, 0.01)
                end = v3(1, 1, 1)
            } else {
                if (direction == 'left') {
                    start = v3(-this.screen.width, 0, 0)
                } else if (direction == 'right') {
                    start = v3(this.screen.width, 0, 0)
                } else if (direction == 'top') {
                    start = v3(0, this.screen.height, 0)
                } else {
                    start = v3(0, -this.screen.height, 0)
                }
                end = v3(0, 0, 0)
            }

            await this.handle(direction, duration, start, end, isBounce)
            if (widget) widget.enabled = true
        }
    }

    /**隐藏动画 */
    public async hideTween<T extends object, U extends object>({
        isMotion = true,
        direction,
        duration = 0.1,
    }: ICloseOptions<T, U>) {
        direction = direction || this.lastEnterDirection
        // 避免重复点击关闭
        if (this.isClosing) return
        this.isClosing = true
        // 停止当前动画
        tween(this.node).removeSelf()
        if (isMotion) {
            const widget = this.node.getComponent(Widget)
            if (widget) widget.enabled = false
            // 设置起始坐标
            // 起点坐标
            let start: Vec3
            // 终点坐标
            let end: Vec3
            if (direction == 'center') {
                // 从中间缩小
                start = this.node.scale
                end = v3(0, 0, 0)
            } else {
                if (direction == 'left') {
                    end = v3(-this.screen.width, 0, 0)
                } else if (direction == 'right') {
                    end = v3(this.screen.width, 0, 0)
                } else if (direction == 'top') {
                    end = v3(0, this.screen.height, 0)
                } else {
                    end = v3(0, -this.screen.height, 0)
                }
                start = this.node.getPosition()
            }
            await this.handle(direction, duration, start, end, false)
        }
        this.removeAndDestroy()
    }

    private async handle(
        direction: IDirection,
        duration: number,
        start: Vec3,
        end: Vec3,
        isBounce: boolean
    ) {
        if (direction == 'center') {
            // 从中间放大
            this.node.setScale(start)
        } else {
            this.node.setPosition(start)
        }
        await this.deftween<Node>(
            duration,
            { [direction == 'center' ? 'scale' : 'position']: end },
            isBounce
        )
    }

    private deftween<T>(
        duration: number,
        props: __private._cocos_tween_tween__ConstructorType<T>,
        isBounce: boolean,
        cb?: Function
    ): Promise<void> {
        return new Promise((resolve, reject) => {
            const tween_node = tween(this.node)
                .to(duration, props)
                .call(() => {
                    // tween_node.removeSelf();
                    resolve()
                })
                .start()

            // if (options.isBounce) {
            //     this.node.scale = start;
            //     const tween_node = tween(this.node)
            //         .to(options.duration, { scale: bounce })
            //         .to(options.bounceDuration, { scale: end })
            //         .call(() => {
            //             tween_node.removeSelf()
            //         })
            //         .start();
            // }
        })
    }
}
