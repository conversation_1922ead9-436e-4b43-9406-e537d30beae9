import {
    _decorator,
    Component,
    instantiate,
    isValid,
    Label,
    log,
    Node,
    Prefab,
    Animation,
    Skeleton,
    sp,
    Sprite,
    SpriteFrame,
    tween,
    Vec3,
} from 'cc'
import { BaseComponent } from '@/core/manager/gui/base/BaseComponent'
import { AudioEffectConstant } from '@/core/business/constant'
import { cat } from '@/core/manager'
import { create } from '@bufbuild/protobuf'
import store from '@/core/business/store'
import { Player, PlayerSchema } from '@/pb-generate/server/pirate/v1/player_pb'

import { PlayerItem } from './PlayerItem'

import { State as DeskState } from '@/pb-generate/server/pirate/v1/state_pb'

const { ccclass, property } = _decorator

@ccclass('Score')
export class Score extends BaseComponent {
    @property({ type: Node, tooltip: '积分图标' })
    score_icon: Node

    @property({ type: Label, tooltip: '积分' })
    score_count: Label = null!

    @property({ type: Label, tooltip: '积分变化' })
    score_change: Label = null!

    override props = {
        player: create(PlayerSchema),
    }

    protected override initUI(): void {
        this.setScore()
    }

    protected override onLoad(): void {}

    protected override onAutoObserver(): void {
        this.addReaction(
            () => this.props.player.score,
            () => {
                this.updateScore()
            }
        )
    }

    override onDestroy(): void {}

    /**更新积分 */
    updateScore() {
        this.node.active = true

        const duration = 0.1
        tween(this.node)
            .to(duration, { scale: new Vec3(1, 0, 0) })
            .call(() => {
                this.score_change.node.active = true
                this.score_change.string = `+${this.props.player.scoreChange}`
                this.score_count.node.active = false
                this.score_icon.active = false
            })
            .to(duration, { scale: new Vec3(1, -1, 0) })
            .delay(0.5)
            .to(duration, { scale: new Vec3(1, 0, 0) })
            .call(() => {
                this.score_count.node.active = true
                this.score_icon.active = true

                this.score_change.node.active = false
                this.setScore()
            })
            .to(duration, { scale: Vec3.ONE })
            .start()
    }

    setScore() {
        this.score_count.string = this.props.player.score.toString()
    }

    hide() {
        this.node.active = false
    }
}
