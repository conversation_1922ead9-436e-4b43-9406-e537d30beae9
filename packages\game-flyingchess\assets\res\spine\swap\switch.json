{"skeleton": {"hash": "H2EZcU0AlO0baBBIbaxqBzomhCc", "spine": "3.8.85", "x": -328.02, "y": -261.06, "width": 621.6, "height": 456.21, "images": "./images/", "audio": "E:/XM/商业策划部/海盗桶/13-交换"}, "bones": [{"name": "root"}, {"name": "00", "parent": "root"}, {"name": "jh_1", "parent": "00", "x": -37.93, "y": -40.47}, {"name": "jh_2", "parent": "00", "x": 42.02, "y": 45.4}, {"name": "01", "parent": "root"}, {"name": "jh_3", "parent": "01", "x": 111.08, "y": -7.34, "color": "21ff00ff"}, {"name": "jh_4", "parent": "01", "x": -39.46, "y": 7.62, "color": "21ff00ff"}, {"name": "jh_7", "parent": "01", "rotation": 26.58, "x": -123.04, "y": -23.42, "scaleX": 0.7703, "scaleY": 0.882, "color": "21ff00ff"}], "slots": [{"name": "jh_7", "bone": "jh_7", "attachment": "jh_3"}, {"name": "jh_4", "bone": "jh_4", "attachment": "jh_4"}, {"name": "jh_3", "bone": "jh_3", "attachment": "jh_3"}, {"name": "jh_2", "bone": "jh_2", "attachment": "jh_2"}, {"name": "jh_1", "bone": "jh_1", "attachment": "jh_1"}], "skins": [{"name": "default", "attachments": {"jh_1": {"jh_1": {"x": -11.31, "y": -1, "width": 132, "height": 123}}, "jh_2": {"jh_2": {"x": 7.74, "y": -2.87, "width": 132, "height": 123}}, "jh_3": {"jh_3": {"x": -0.99, "y": -11.66, "width": 367, "height": 418}}, "jh_4": {"jh_4": {"x": 16.55, "y": -8.62, "width": 295, "height": 366}}, "jh_7": {"jh_3": {"x": -0.99, "y": -11.66, "width": 367, "height": 418}}}}], "animations": {"idle": {"bones": {"root": {"rotate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 2}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 2}]}, "jh_4": {"rotate": [{"time": 2}], "translate": [{"time": 2}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 1.05, "y": 1.05, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": "stepped"}, {"time": 2}], "shear": [{"time": 2}]}, "01": {"rotate": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 2}], "translate": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 2}]}, "jh_2": {"rotate": [{"time": 2}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 84.84, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": "stepped"}, {"time": 2}], "scale": [{"time": 2}], "shear": [{"time": 2}]}, "jh_1": {"rotate": [{"time": 2}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -85, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": "stepped"}, {"time": 2}], "scale": [{"time": 2}], "shear": [{"time": 2}]}, "jh_3": {"rotate": [{"curve": "stepped"}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 27.56, "curve": "stepped"}, {"time": 0.6667, "angle": 27.56, "curve": "stepped"}, {"time": 2, "angle": 27.56}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 144.43, "curve": "stepped"}, {"time": 0.4333, "x": 144.43, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -219.8, "y": -5.96, "curve": "stepped"}, {"time": 2, "x": -219.8, "y": -5.96}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 1.268, "y": 1.268, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": 0.88, "y": 0.88, "curve": "stepped"}, {"time": 2, "x": 0.88, "y": 0.88}], "shear": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 2}]}, "jh_7": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -12.21, "curve": "stepped"}, {"time": 0.4333, "angle": -12.21, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -26.58, "curve": "stepped"}, {"time": 2, "angle": -26.58}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": -215.22, "curve": "stepped"}, {"time": 0.4333, "x": -215.22, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 234.12, "y": 16.07, "curve": "stepped"}, {"time": 2, "x": 234.12, "y": 16.07}], "scale": [{"curve": "stepped"}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": 1.33, "y": 1.33, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 1.298, "y": 1.134, "curve": "stepped"}, {"time": 2, "x": 1.298, "y": 1.134}], "shear": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.6667, "curve": "stepped"}, {"time": 2}]}, "00": {"rotate": [{"time": 2}], "translate": [{"time": 2}], "scale": [{"time": 2}], "shear": [{"time": 2}]}}, "drawOrder": [{"time": 0.2333}, {"time": 0.4333, "offsets": [{"slot": "jh_7", "offset": 2}, {"slot": "jh_4", "offset": 0}]}]}}}