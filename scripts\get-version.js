/**
 * Monorepo 版本号获取脚本
 *
 * 优先级：
 * 1. version-result.json中的游戏版本 (由版本管理job生成)
 * 2. 游戏package.json中的version
 * 3. git tag中的最新版本
 * 4. 默认版本 0.1.0
 *
 * 使用方法:
 * node scripts/get-version.js [game-name] [--format=short|full|tag]
 *
 * game-name: shark, pirate, dixit (必需)
 * --format选项:
 * - short: 只返回版本号 (默认)
 * - full: 返回完整的版本信息JSON
 * - tag: 返回适合git tag的格式 (game-shark@1.0.0)
 */

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

// 获取命令行参数
const args = process.argv.slice(2)
const gameName = args.find((arg) => !arg.startsWith('--'))
const formatArg = args.find((arg) => arg.startsWith('--format='))
const format = formatArg ? formatArg.split('=')[1] : 'short'

// 动态获取游戏配置
function getGameConfigs() {
    const packagesDir = path.join(process.cwd(), 'packages')
    const games = {}

    try {
        const entries = fs.readdirSync(packagesDir, { withFileTypes: true })

        for (const entry of entries) {
            if (entry.isDirectory() && entry.name.startsWith('game-')) {
                const gamePath = path.join(packagesDir, entry.name)
                const packageJsonPath = path.join(gamePath, 'package.json')

                // 从目录名提取游戏简称 (去掉 'game-' 前缀)
                const gameKey = entry.name.replace('game-', '')

                let displayName = gameKey // 默认显示名称

                // 尝试从 package.json 读取显示名称
                try {
                    if (fs.existsSync(packageJsonPath)) {
                        const packageJson = JSON.parse(
                            fs.readFileSync(packageJsonPath, 'utf8')
                        )
                        displayName =
                            packageJson.displayName ||
                            packageJson.description ||
                            gameKey
                    }
                } catch (error) {
                    console.warn(
                        `警告: 无法读取 ${entry.name}/package.json:`,
                        error.message
                    )
                }

                games[gameKey] = {
                    name: entry.name,
                    displayName: displayName,
                    path: `packages/${entry.name}`,
                    tagPrefix: `${entry.name}@`,
                }
            }
        }
    } catch (error) {
        console.error('错误: 无法读取 packages 目录:', error.message)
        process.exit(1)
    }

    return games
}

// 游戏配置
const GAMES = getGameConfigs()

// 验证游戏名称
if (!gameName || !GAMES[gameName]) {
    console.error(`错误: 请指定有效的游戏名称`)
    console.error(`可用的游戏: ${Object.keys(GAMES).join(', ')}`)
    console.error(
        `使用方法: node scripts/get-version.js [game-name] [--format=short|full|tag]`
    )
    process.exit(1)
}

const gameConfig = GAMES[gameName]

function getVersionFromPackageJson() {
    try {
        const packageFile = path.join(gameConfig.path, 'package.json')
        if (fs.existsSync(packageFile)) {
            const pkg = JSON.parse(fs.readFileSync(packageFile, 'utf8'))
            return pkg.version
        }
    } catch (error) {
        // 忽略错误
    }
    return null
}

function getVersionFromGitTag() {
    try {
        // 获取该游戏的最新tag
        const tags = execSync(
            `git tag -l "${gameConfig.tagPrefix}*" --sort=-version:refname`,
            {
                encoding: 'utf8',
                stdio: 'pipe',
            }
        ).trim()

        if (tags) {
            const latestTag = tags.split('\n')[0]
            // 移除tag前缀
            return latestTag.replace(gameConfig.tagPrefix, '')
        }
    } catch (error) {
        // 忽略错误
    }
    return null
}

function getCommitSha() {
    try {
        return execSync('git rev-parse --short HEAD', {
            encoding: 'utf8',
            stdio: 'pipe',
        }).trim()
    } catch (error) {
        return 'unknown'
    }
}

// 主函数
function getVersion() {
    const sources = [
        { name: 'package.json', version: getVersionFromPackageJson() },
        { name: 'git-tag', version: getVersionFromGitTag() },
        { name: 'default', version: '0.1.0' },
    ]

    // 找到第一个有效的版本号
    const validSource = sources.find((source) => source.version)
    const version = validSource.version
    const source = validSource.name

    const commitSha = getCommitSha()

    const versionInfo = {
        game: gameConfig.name,
        displayName: gameConfig.displayName,
        version: version,
        source: source,
        commitSha: commitSha,
        timestamp: new Date().toISOString(),
    }

    // 根据format参数返回不同格式
    switch (format) {
        case 'full':
            return JSON.stringify(versionInfo, null, 2)
        case 'tag':
            return `${gameConfig.tagPrefix}${version}`
        case 'short':
        default:
            return version
    }
}

// 输出结果
console.log(getVersion())
