// @generated by protoc-gen-es v2.2.5 with parameter "target=ts,json_types=true"
// @generated from file pirate/v1/state.proto (package pirate.v1, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile } from "@bufbuild/protobuf/codegenv1";
import { enumDesc, fileDesc } from "@bufbuild/protobuf/codegenv1";

/**
 * Describes the file pirate/v1/state.proto.
 */
export const file_pirate_v1_state: GenFile = /*@__PURE__*/
  fileDesc("ChVwaXJhdGUvdjEvc3RhdGUucHJvdG8SCXBpcmF0ZS52MSrABQoFU3RhdGUSFQoRU1RBVEVfVU5TUEVDSUZJRUQQABIUChBTVEFURV9HQU1FX1NUQVJUEAESEwoPU1RBVEVfR0FNRV9PVkVSEAISGAoUU1RBVEVfR0FNRV9GSU5JU0hJTkcQAxIXChNTVEFURV9HQU1FX0ZJTklTSEVEEAQSHQoZU1RBVEVfUExBWUVSX0RSQVdfT1JfUE9TVBALEhYKElNUQVRFX1BMQVlFUl9EUkFXThAMEicKI1NUQVRFX1BMQVlFUl9QUkVfTkVYVF9DT05TVU1FX0RSQVdOEB4SIwofU1RBVEVfUExBWUVSX05FWFRfQ09OU1VNRV9EUkFXThAfEhcKE1NUQVRFX1BMQVlFUl9QT1NURUQQDRIYChRTVEFURV9QTEFZRVJfSE9TVElORxAOEhcKE1NUQVRFX1BMQVlFUl9QRUVLRUQQDxIWChJTVEFURV9QTEFZRVJfU1RPTEUQEBIeChpTVEFURV9QTEFZRVJfU0VMRUNUX1RBUkdFVBAREiQKIFNUQVRFX1BMQVlFUl9BRlRFUl9TRUxFQ1RfVEFSR0VUEBISFQoRU1RBVEVfQ0FSRF9SRVZJVkUQFBIgChxTVEFURV9DQVJEX0FDVElWRV9TVUJTVElUVVRFEBUSFAoQU1RBVEVfQ0FSRF9TVEVBTBAWEhoKFlNUQVRFX0NBUkRfQUZURVJfU1RFQUwQFxIZChVTVEFURV9DQVJEX0FGVEVSX1NXQVAQGBIhCh1TVEFURV9DQVJEX1NUUkVOR1RIRU5fSU5WQUxJRBAZEh0KGVNUQVRFX0NBUkRfQUNUSVZFX0RFRkVOU0UQGhITCg9TVEFURV9DQVJEX1BFRUsQGxIZChVTVEFURV9DQVJEX0FGVEVSX1BFRUsQHBIbChdTVEFURV9DQVJEX0FGVEVSX0ZSRUVaRRAdYgZwcm90bzM");

/**
 * @generated from enum pirate.v1.State
 */
export enum State {
  /**
   * @generated from enum value: STATE_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * ---- 游戏状态 ----
   *
   * @generated from enum value: STATE_GAME_START = 1;
   */
  GAME_START = 1,

  /**
   * @generated from enum value: STATE_GAME_OVER = 2;
   */
  GAME_OVER = 2,

  /**
   * @generated from enum value: STATE_GAME_FINISHING = 3;
   */
  GAME_FINISHING = 3,

  /**
   * @generated from enum value: STATE_GAME_FINISHED = 4;
   */
  GAME_FINISHED = 4,

  /**
   * ---- 玩家状态 ----
   * 玩家需要摸牌、出牌
   *
   * @generated from enum value: STATE_PLAYER_DRAW_OR_POST = 11;
   */
  PLAYER_DRAW_OR_POST = 11,

  /**
   * 玩家已经摸牌
   *
   * @generated from enum value: STATE_PLAYER_DRAWN = 12;
   */
  PLAYER_DRAWN = 12,

  /**
   * 玩家一张（或多张牌），需要进入预消费队列，因为FSM无法从A状态到A状态，所以需要一个中间状态
   *
   * @generated from enum value: STATE_PLAYER_PRE_NEXT_CONSUME_DRAWN = 30;
   */
  PLAYER_PRE_NEXT_CONSUME_DRAWN = 30,

  /**
   * 玩家一张（或多张牌），进入消费队列
   *
   * @generated from enum value: STATE_PLAYER_NEXT_CONSUME_DRAWN = 31;
   */
  PLAYER_NEXT_CONSUME_DRAWN = 31,

  /**
   * 玩家已经出牌
   *
   * @generated from enum value: STATE_PLAYER_POSTED = 13;
   */
  PLAYER_POSTED = 13,

  /**
   * 玩家没有摸牌、出牌，进入托管状态
   *
   * @generated from enum value: STATE_PLAYER_HOSTING = 14;
   */
  PLAYER_HOSTING = 14,

  /**
   * 玩家已经透视
   *
   * @generated from enum value: STATE_PLAYER_PEEKED = 15;
   */
  PLAYER_PEEKED = 15,

  /**
   * 玩家已经索取
   *
   * @generated from enum value: STATE_PLAYER_STOLE = 16;
   */
  PLAYER_STOLE = 16,

  /**
   * 玩家选择目标
   *
   * @generated from enum value: STATE_PLAYER_SELECT_TARGET = 17;
   */
  PLAYER_SELECT_TARGET = 17,

  /**
   * 玩家已经选择目标
   *
   * @generated from enum value: STATE_PLAYER_AFTER_SELECT_TARGET = 18;
   */
  PLAYER_AFTER_SELECT_TARGET = 18,

  /**
   * ---- 玩家操作 ----
   * 复活【绷带】被打出
   *
   * @generated from enum value: STATE_CARD_REVIVE = 20;
   */
  CARD_REVIVE = 20,

  /**
   * 【替身】被激活
   *
   * @generated from enum value: STATE_CARD_ACTIVE_SUBSTITUTE = 21;
   */
  CARD_ACTIVE_SUBSTITUTE = 21,

  /**
   * 【索取】玩家的牌（选择玩家手牌）
   *
   * @generated from enum value: STATE_CARD_STEAL = 22;
   */
  CARD_STEAL = 22,

  /**
   * 【索取】玩家的牌之后
   *
   * @generated from enum value: STATE_CARD_AFTER_STEAL = 23;
   */
  CARD_AFTER_STEAL = 23,

  /**
   * 【交换】玩家的牌
   *
   * @generated from enum value: STATE_CARD_AFTER_SWAP = 24;
   */
  CARD_AFTER_SWAP = 24,

  /**
   * 【强化】被无效
   *
   * @generated from enum value: STATE_CARD_STRENGTHEN_INVALID = 25;
   */
  CARD_STRENGTHEN_INVALID = 25,

  /**
   * 【防御】被激活
   *
   * @generated from enum value: STATE_CARD_ACTIVE_DEFENSE = 26;
   */
  CARD_ACTIVE_DEFENSE = 26,

  /**
   * 【透视】选择裂缝
   *
   * @generated from enum value: STATE_CARD_PEEK = 27;
   */
  CARD_PEEK = 27,

  /**
   * 【透视】选择裂缝之后
   *
   * @generated from enum value: STATE_CARD_AFTER_PEEK = 28;
   */
  CARD_AFTER_PEEK = 28,

  /**
   * 【冻结】之后
   *
   * @generated from enum value: STATE_CARD_AFTER_FREEZE = 29;
   */
  CARD_AFTER_FREEZE = 29,
}

/**
 * @generated from enum pirate.v1.State
 */
export type StateJson = "STATE_UNSPECIFIED" | "STATE_GAME_START" | "STATE_GAME_OVER" | "STATE_GAME_FINISHING" | "STATE_GAME_FINISHED" | "STATE_PLAYER_DRAW_OR_POST" | "STATE_PLAYER_DRAWN" | "STATE_PLAYER_PRE_NEXT_CONSUME_DRAWN" | "STATE_PLAYER_NEXT_CONSUME_DRAWN" | "STATE_PLAYER_POSTED" | "STATE_PLAYER_HOSTING" | "STATE_PLAYER_PEEKED" | "STATE_PLAYER_STOLE" | "STATE_PLAYER_SELECT_TARGET" | "STATE_PLAYER_AFTER_SELECT_TARGET" | "STATE_CARD_REVIVE" | "STATE_CARD_ACTIVE_SUBSTITUTE" | "STATE_CARD_STEAL" | "STATE_CARD_AFTER_STEAL" | "STATE_CARD_AFTER_SWAP" | "STATE_CARD_STRENGTHEN_INVALID" | "STATE_CARD_ACTIVE_DEFENSE" | "STATE_CARD_PEEK" | "STATE_CARD_AFTER_PEEK" | "STATE_CARD_AFTER_FREEZE";

/**
 * Describes the enum pirate.v1.State.
 */
export const StateSchema: GenEnum<State, StateJson> = /*@__PURE__*/
  enumDesc(file_pirate_v1_state, 0);

