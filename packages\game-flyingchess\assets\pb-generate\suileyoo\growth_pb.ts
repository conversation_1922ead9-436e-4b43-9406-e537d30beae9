// @generated by protoc-gen-es v2.2.5 with parameter "target=ts,import_extension=none,json_types=true"
// @generated from file growth.proto (package protos, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv1";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file growth.proto.
 */
export const file_growth: GenFile = /*@__PURE__*/
  fileDesc("Cgxncm93dGgucHJvdG8SBnByb3RvcyJEChZHcm93dGhJbmZvTm90aWZpY2F0aW9uEhQKDGdyb3d0aF92YWx1ZRgBIAEoBBIUCgxncm93dGhfbGV2ZWwYAiABKA1CMAokY29tLnN0bnRzLmNsb3VkLnN1aWxleW9vLmVjaG8ucHJvdG9zWgguL3Byb3Rvc2IGcHJvdG8z");

/**
 * 成长信息通知
 *
 * @generated from message protos.GrowthInfoNotification
 */
export type GrowthInfoNotification = Message<"protos.GrowthInfoNotification"> & {
  /**
   * 成长值
   *
   * @generated from field: uint64 growth_value = 1;
   */
  growthValue: bigint;

  /**
   * 成长等级
   *
   * @generated from field: uint32 growth_level = 2;
   */
  growthLevel: number;
};

/**
 * 成长信息通知
 *
 * @generated from message protos.GrowthInfoNotification
 */
export type GrowthInfoNotificationJson = {
  /**
   * 成长值
   *
   * @generated from field: uint64 growth_value = 1;
   */
  growthValue?: string;

  /**
   * 成长等级
   *
   * @generated from field: uint32 growth_level = 2;
   */
  growthLevel?: number;
};

/**
 * Describes the message protos.GrowthInfoNotification.
 * Use `create(GrowthInfoNotificationSchema)` to create a new message.
 */
export const GrowthInfoNotificationSchema: GenMessage<GrowthInfoNotification, GrowthInfoNotificationJson> = /*@__PURE__*/
  messageDesc(file_growth, 0);

