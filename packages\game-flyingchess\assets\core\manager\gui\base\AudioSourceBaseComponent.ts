/**
 * @describe 音频组件基类
 * <AUTHOR>
 * @date 2023-12-22 16:20:20
 */

import {
    AudioClip,
    AudioSource,
    CCBoolean,
    Component,
    Enum,
    Node,
    Prefab,
    Vec3,
    _decorator,
    instantiate,
    log,
} from 'cc'
import { AudioEventConstant } from '@/core/business/constant'

import store from '@/core/business/store'
import { GameVisiable } from '@/core/business/store/global'
import { BaseComponent } from './BaseComponent'
import { cat } from '../../index'
import { JSBridgeClient } from '@/core/business/jsbridge/JSBridge'

const { ccclass, property } = _decorator

/**
 * EFFECT 音效
 * BGM    音乐
 */
export enum AudioTypeEnum {
    EFFECT = 0,
    BGM,
}

export class AudioSourceBaseComponent<
    T extends object = {},
    K extends object = {}
> extends BaseComponent<T, K> {
    @property({
        tooltip: `类型:
        EFFECT 音效
        BGM    音乐`,
        type: Enum(AudioTypeEnum),
    })
    type = AudioTypeEnum.EFFECT

    @property({ tooltip: '音源', type: AudioClip })
    clip = null!

    @property({ tooltip: '循环' })
    loop = false

    @property({ tooltip: '音量' })
    volume = 1

    @property({ tooltip: '是否启用自动播放' })
    playOnAwake = false

    audioSource: AudioSource = new AudioSource()

    override onEnable() {
        super.onEnable()
        this.clip && (this.audioSource.clip = this.clip)
        this.audioSource.loop = this.loop
        this.audioSource.volume = this.volume
        this.audioSource.playOnAwake = this.playOnAwake
        cat.event
            .on(AudioEventConstant.EFFECT_ON, this.onPlayEffectHandler, this)
            .on(AudioEventConstant.EFFECT_OFF, this.onStopEffectHandler, this)
            .on(AudioEventConstant.MUSIC_ON, this.onPlayMusicHandler, this)
            .on(AudioEventConstant.MUSIC_OFF, this.onStopMusicHandler, this)
    }

    override __preload() {
        super.__preload()
        const { volumeEffect, volumeMusic } = cat.audio
        this.audioSource.volume =
            this.type === AudioTypeEnum.BGM ? volumeMusic : volumeEffect
    }

    protected override start() {}

    protected stopAudio() {
        this.audioSource.stop()
    }

    protected playAudio() {
        const { switchEffect, switchMusic } = cat.audio
        if (
            !this.audioSource.playing &&
            (this.type === AudioTypeEnum.BGM ? switchMusic : switchEffect)
            // && store.global.gameVisiable == GameVisiable.SHOW
        ) {
            if (
                store.global.isSupportNativePlayAudio &&
                this.audioSource.clip
            ) {
                JSBridgeClient.playAudio(this.audioSource.clip.nativeUrl)
            } else {
                this.audioSource.play()
            }
        }
    }

    protected onPlayEffectHandler() {}
    protected onStopEffectHandler() {
        this.stopAudio()
    }

    protected onPlayMusicHandler() {}

    protected onStopMusicHandler() {
        this.stopAudio()
    }

    override _onPreDestroy() {
        this.onStopMusicHandler()
        super._onPreDestroy()
    }
}
