import {
    _decorator,
    Component,
    error,
    ImageAsset,
    Input,
    instantiate,
    isValid,
    Label,
    log,
    math,
    Node,
    Prefab,
    sp,
    Sprite,
    Sprite<PERSON>rame,
    Tween,
    tween,
    v2,
    v3,
    Vec3,
} from 'cc'
import { BaseComponent } from '@/core/manager/gui/base/BaseComponent'
import { cat } from '@/core/manager'
import {
    AudioEffectConstant,
    GameEventConstant,
} from '@/core/business/constant'
import { SocketEvent } from '@/core/business/ws'
import { create } from '@bufbuild/protobuf'
import store from '@/core/business/store'
import { Card } from '@/pb-generate/server/pirate/v1/card_pb'
import { State as DeskState } from '@/pb-generate/server/pirate/v1/state_pb'
import { Player, PlayerSchema } from '@/pb-generate/server/pirate/v1/player_pb'
import {
    GameOverBroadcast,
    GameInfoBroadcast,
} from '@/pb-generate/server/pirate/v1/game_pb'

// import { ThrowMine } from './ThrowMine'
// import { Curse } from './Curse'
import {
    BattleMode,
    Player as BasePlayer,
    PlayerSchema as BasePlayerSchema,
} from 'sgc'
import GetTimeDifferenceFromServer from '@/core/business/hooks/GetTimeDifferenceFromServer'
import { Strengthen } from './Strengthen'
import { Freeze } from './Freeze'
import { Score } from './score'
import { Defer } from './Defer'
import { Countdown } from './Countdown'
import { Dress } from '@/core/components/Dress'

const { ccclass, property } = _decorator

export type PlayerItemProps = {
    player: Player
    showCurseCountImmediately?: boolean
}

export type PlayerItemData = {
    state?: PlayerState
    basePlayer?: BasePlayer
    show_select?: boolean
    is_selected?: boolean
}

/**玩家状态 */
export enum PlayerState {
    /**正常 */
    NORMAL = 0,
    /**托管 */
    AUTO,
    /**出局 */
    OUT,
    /**离开 */
    LEAVE,
    /**观战 */
    WATCH,
}

@ccclass('Player')
export class PlayerItem extends BaseComponent<PlayerItemProps, PlayerItemData> {
    @property({ type: Sprite, tooltip: '玩家节点' })
    player_node: Sprite = null!

    @property({ type: Label, tooltip: '昵称节点' })
    nick_name_node: Label = null!

    @property({ type: Sprite, tooltip: '头像节点' })
    avatar_node: Sprite = null!

    @property({ type: Label, tooltip: '卡牌数量节点' })
    card_count_node: Label = null!

    @property({ type: Node, tooltip: '选择箭头节点' })
    select_arrow_node: Node = null!

    @property({ type: Node, tooltip: '选中箭头节点' })
    selected_node: Node = null!

    @property({ type: Countdown, tooltip: '操作倒计时节点' })
    countdown: Countdown = null!

    @property({ type: Node, tooltip: '标识节点' })
    tag_node: Node = null!

    @property({ type: Node, tooltip: '托管标识节点' })
    auto_node: Node = null!

    @property({ type: Node, tooltip: '出局标识节点' })
    out_node: Node = null!

    @property({ type: Node, tooltip: '离开标识节点' })
    leave_node: Node = null!

    @property({ type: Node, tooltip: '观战标识节点' })
    watch_node: Node = null!

    @property({ type: Node, tooltip: '摸雷标识节点' })
    draw_mine_node: Node = null!

    @property({ type: Score, tooltip: '积分组件' })
    score: Score

    @property({ type: Strengthen, tooltip: '强化状态组件' })
    strengthen: Strengthen

    @property({ type: Freeze, tooltip: '冻结状态组件' })
    freeze: Freeze

    @property({ type: Defer, tooltip: '延时状态组件' })
    defer: Defer

    // @property({ type: Curse, tooltip: '诅咒状态和动画组件' })
    // curse: Curse

    @property(Prefab)
    dressPrefab: Prefab = null!

    @property(Node)
    dress_container: Node = null!

    private dress: Dress | null = null

    /**状态对应节点 */
    stateNodeMap: Map<PlayerState, Node | null>

    override props: PlayerItemProps = {
        player: create(PlayerSchema),
    }

    override data: PlayerItemData = {
        state: PlayerState.NORMAL,
        basePlayer: create(BasePlayerSchema),
        show_select: false,
    }

    protected override onLoad(): void {
        this.node.on(Input.EventType.TOUCH_END, this.onClickPlayerHandler, this)
    }

    protected override start(): void {}

    protected override initUI(): void {
        // this.strengthen.node.active = this.select_arrow_node.active = false

        const { player } = this.props
        const { user, game } = store

        const base = game.getBasePlayerByUid(player.id)
        base && (this.data.basePlayer = base)

        this.nick_name_node.string = `${cat.util.stringUtil.sub(
            base?.nickname ?? '',
            10
        )}`
        if (base?.avatar) {
            cat.res.loadRemote(
                base?.avatar,
                (err: Error, imageAsset: ImageAsset) => {
                    if (err) return error(err)
                    if (isValid(this.avatar_node)) {
                        const spriteFrame =
                            SpriteFrame.createWithImage(imageAsset)
                        spriteFrame.packable = false
                        this.avatar_node.spriteFrame = spriteFrame
                    }
                }
            )
        } else {
            this.avatar_node.customMaterial = null
        }

        // 初始化 Dress 组件
        if (this.dressPrefab && this.dress_container) {
            // 清空容器
            this.dress_container.destroyAllChildren()

            // 实例化 Dress 预制体
            const dressNode = instantiate(this.dressPrefab)
            this.dress_container.addChild(dressNode)

            // 获取 Dress 组件
            this.dress = dressNode.getComponent(Dress)

            // 设置属性
            if (this.dress) {
                this.dress.setUpdateProps({
                    playerId: player.id,
                })
            }
        }

        // // 玩家自己不显示手牌数量
        // if (this.props.player.index == user.userIndex) this.card_count_node.node.parent!.active = false
    }

    protected override onAutoObserver(): void {
        this.addAutorun([
            () => {
                if (this.data.state === PlayerState.AUTO) {
                    //进入托管
                    window.ccLog('用户进入托管')
                    cat.tracking.game.gameEntrust('on')
                } else {
                    //解除托管
                    window.ccLog('用户解除托管')
                    cat.tracking.game.gameEntrust('off')
                }

                window.ccLog('更新状态', this.data.state)
                this.auto_node.active =
                    this.leave_node.active =
                    this.watch_node.active =
                    this.out_node.active =
                        false
                cat.event.dispatchEvent(GameEventConstant.UPDATE_PLAYER_STATE)
                switch (this.data.state) {
                    case PlayerState.LEAVE:
                        this.leave_node.active = true
                        break
                    case PlayerState.WATCH:
                        this.watch_node.active = true
                        break
                    case PlayerState.OUT:
                        cat.util.nodeUtils.setNodeGray(this.node)
                        this.out_node.active = true
                        this.score.hide()
                        this.strengthen.hide()
                        this.freeze.hide()
                        this.defer.hide()
                        break
                    case PlayerState.AUTO:
                        this.auto_node.active = true
                        break
                    case PlayerState.NORMAL:

                    default:
                        break
                }
                this.tag_node.active =
                    this.leave_node.active ||
                    this.watch_node.active ||
                    this.out_node.active ||
                    this.auto_node.active
            },
            () => {
                this.updatePalyerInfoByPlayer()
            },
            () => {
                // 手牌数量
                this.card_count_node.string = `${this.props.player.handCardCount}`
            },

            () => {
                this.select_arrow_node.active = !!this.data.show_select
                this.selected_node.active = !!this.data.is_selected
            },
            () => {
                if (!this.props) {
                    return
                }
                this.score.setUpdateProps(this.props)
                this.strengthen.setUpdateProps(this.props)
                this.freeze.setUpdateProps(this.props)
                this.defer.setUpdateProps(this.props)
                this.countdown.setUpdateProps(this.props)
            },
        ])

        // this.addReaction(
        //     () =>
        //         store.game.roomData.stateInfo?.state ===
        //             DeskState.DESK_STATE_REMOVE_LANDMINE &&
        //         this.isCurrentPlayer(store.game.roomData.cursor),
        //     (is_playe_draw_mine) => {
        //         is_playe_draw_mine
        //             ? this.playEffectDrawMine()
        //             : this.stopEffectDrawMine()
        //     },
        //     {
        //         fireImmediately: true,
        //     }
        // )
    }

    /**点击玩家 */
    onClickPlayerHandler() {
        if (cat.event.has(GameEventConstant.SELECT_PLAYER)) {
            cat.event.dispatchEvent(
                GameEventConstant.SELECT_PLAYER,
                this.props.player
            )
        } else {
            cat.platform.openProfile({ userId: Number(this.props.player.id) })
        }
    }

    /**更新玩家信息 */
    private updatePalyerInfoByPlayer() {
        // window.ccLog('更新玩家信息', this.props.player)
        const data = this.props.player
        // 状态
        if (data.exited || !this.data.basePlayer!.online) {
            this.data.state = PlayerState.LEAVE
        } else if (data.gameOver) {
            this.data.state = PlayerState.OUT
        } else if (data.hosting) {
            this.data.state = PlayerState.AUTO
        } else {
            this.data.state = PlayerState.NORMAL
        }
    }

    /**播放爆炸特效 */
    private playEffectBoom() {
        // cat.audio.playEffect(AudioEffectConstant.MINE_BOMB)
    }

    /**播放摸雷特效 */
    private playEffectDrawMine() {
        this.draw_mine_node.active = true
    }

    /**隐藏摸雷 */
    private stopEffectDrawMine() {
        this.draw_mine_node.active = false
    }

    override onDestroy(): void {
        Tween.stopAllByTarget(this.player_node.node)
        this.unscheduleAllCallbacks()
    }

    //#region 监听广播处理

    //#endregion
}
