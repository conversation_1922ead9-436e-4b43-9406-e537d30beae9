import { BaseTracking } from "../BaseTracking"


/**随乐游事件属性 */
export enum TrackingSuileyooEvent {
    RETURN_CLICK = 'return_click',
    SEARCH_ROOM = 'SearchRoom',
    CONFIRM_SEARCH = 'ConfirmSearch',
    RULE_CLICK = 'rule_click',
    COMPETITIVE_MODE = 'CompetitiveMode',
    CANCELCOMPETITIVE = 'CancelCompetitive',
    RANKING_CLICK = 'Ranking_Click',
    TASK_CLICK = 'task_click',
    MALL_CLICK = 'mall_click',
    HOME_LEISUREMODE = 'Home_LeisureMode',
    RANKING_LV_CLICK = 'Ranking_Lv_Click',
    CLAIM_REWARD = 'CLAIM_REWARD',
    CLICK_ON_THE_ROOM_LIST = 'ClickOnTheRoomList',
    JOIN_LEISUREMODE = 'Join_LeisureMode',
    CREATE_ROOM = 'CreateRoom',
    SUCCESSFULLY_CREATED_ROOM = 'SuccessfullyCreatedRoom',
    GAME_PREPARE = 'game_prepare',
    GAME_SIT_DOWN = 'game_sit_down',
    GAME_STAND = 'game_stand',
    GAME_INVITE = 'game_invite',
    ENTER_THE_BATTLE = 'EnterTheBattle',
    SMALLSETTLEMENT = 'SmallSettlement',
    GAME_OVER = 'game_over',
    ANOTHER_ROUND = 'AnotherRound',
    Leave = 'leave',
    ADD_FRIEND = 'add_friend',
    MESSAGE_SEND = 'message_send',
    GIFT_TOUCH = 'gift_touch',
    MIKE_STATE = 'mike_state',
}

export class TrackingSuileyoo extends BaseTracking {
    /**点击主页左上角返回 */
    returnClick = () => {
        this.tracking.upload({
            event_type: TrackingSuileyooEvent.RETURN_CLICK,
        })
    }

    /**右上角搜房 */
    searchRoom = () => {
        this.tracking.upload({
            event_type: TrackingSuileyooEvent.SEARCH_ROOM,
        })
    }

    /**
     * 确认搜房
     * @param roomid 查找房间的id
     */
    confirmSearch = (roomid: string) => {
        this.tracking.upload({
            event_type: TrackingSuileyooEvent.CONFIRM_SEARCH,
            value: { roomid }
        })
    }

    /**点击竞技场右上角规则 */
    ruleClick = () => {
        this.tracking.upload({
            event_type: TrackingSuileyooEvent.RULE_CLICK,
        })
    }

    /**点击竞技场模式 */
    competitiveMode = () => {
        this.tracking.upload({
            event_type: TrackingSuileyooEvent.COMPETITIVE_MODE,
        })
    }

    /**竞技场模式取消匹配 */
    cancelCompetitive = () => {
        this.tracking.upload({
            event_type: TrackingSuileyooEvent.CANCELCOMPETITIVE,
        })
    }

    /**点击排行榜 */
    rankingClick = () => {
        this.tracking.upload({
            event_type: TrackingSuileyooEvent.RANKING_CLICK,
        })
    }

    /**点击主页任务按钮 */
    taskClick = () => {
        this.tracking.upload({
            event_type: TrackingSuileyooEvent.TASK_CLICK,
        })
    }

    /**点击主页商城 */
    mallClick = () => {
        this.tracking.upload({
            event_type: TrackingSuileyooEvent.MALL_CLICK,
        })
    }

    /**点击休闲场按钮 */
    homeLeisureMode = () => {
        this.tracking.upload({
            event_type: TrackingSuileyooEvent.HOME_LEISUREMODE,
        })
    }

    /**点击达人排行榜 */
    rankingLvClick = () => {
        this.tracking.upload({
            event_type: TrackingSuileyooEvent.RANKING_LV_CLICK,
        })
    }

    /**领取任务奖励 */
    claimReward = (taskname: string) => {
        this.tracking.upload({
            event_type: TrackingSuileyooEvent.CLAIM_REWARD,
            value: { taskname }
        })
    }

    /**点击首页房间列表里的房间 */
    clickOnTheRoomList = (roomid: string) => {
        this.tracking.upload({
            event_type: TrackingSuileyooEvent.CLICK_ON_THE_ROOM_LIST,
            value: { roomid }
        })
    }

    /**休闲场-快速加入 */
    joinLeisureMode = () => {
        this.tracking.upload({
            event_type: TrackingSuileyooEvent.JOIN_LEISUREMODE,
        })
    }

    /**休闲场-创建房间 */
    createRoom = () => {
        this.tracking.upload({
            event_type: TrackingSuileyooEvent.CREATE_ROOM,
        })
    }

    /**
     * 成功创建房间
     * @param mode 创建的房间人数 2 | 2v2
     */
    successfullyCreatedRoom = (mode: string) => {
        this.tracking.upload({
            event_type: TrackingSuileyooEvent.SUCCESSFULLY_CREATED_ROOM,
            value: { mode }
        })
    }

    /**点击准备：点击准备按钮时上报 */
    gamePrepare = () => {
        this.tracking.upload({
            event_type: TrackingSuileyooEvent.GAME_PREPARE,
        })
    }

    /**坐下：点击坐下按钮后上报 */
    gameSitDown = () => {
        this.tracking.upload({
            event_type: TrackingSuileyooEvent.GAME_SIT_DOWN,
        })
    }

    /**站起：点击站起按钮后上报 */
    gameStand = () => {
        this.tracking.upload({
            event_type: TrackingSuileyooEvent.GAME_STAND,
        })
    }

    /**邀请：点击邀请按钮后上报 */
    gameInvite = () => {
        this.tracking.upload({
            event_type: TrackingSuileyooEvent.GAME_INVITE,
        })
    }
    /**进入对战时上报 */
    enterBattle = () => {
        this.tracking.upload({
            event_type: TrackingSuileyooEvent.ENTER_THE_BATTLE,
        })
    }
    /**
     * 进入小结算时上报
     * @param rank 统计玩家的最终排名
     */
    smallSettlement = (rank: number) => {
        this.tracking.upload({
            event_type: TrackingSuileyooEvent.SMALLSETTLEMENT,
            value: { rank }
        })
    }
    /**
     * 进入大结算时上报
     * @param rank 统计玩家的最终排名
     */
    gameOver = (rank: number) => {
        this.tracking.upload({
            event_type: TrackingSuileyooEvent.GAME_OVER,
            value: { rank }
        })
    }

    /**结算页点击再来一局 */
    anotherRound = () => {
        this.tracking.upload({
            event_type: TrackingSuileyooEvent.ANOTHER_ROUND,
        })
    }

    /**结算页点击离开 */
    leave = () => {
        this.tracking.upload({
            event_type: TrackingSuileyooEvent.Leave,
        })
    }

    /**添加好友/关注好友：点击结算页的关注或添加好友后上报 */
    addFriend = () => {
        this.tracking.upload({
            event_type: TrackingSuileyooEvent.ADD_FRIEND,
        })
    }

    /**发送文字消息：文字消息点击发送时上报 */
    messageSend = () => {
        this.tracking.upload({
            event_type: TrackingSuileyooEvent.MESSAGE_SEND,
        })
    }

    /**点击礼物按钮时上报 */
    giftTouch = () => {
        this.tracking.upload({
            event_type: TrackingSuileyooEvent.GIFT_TOUCH,
        })
    }

    /**主动关闭或者打开麦克风时上报 */
    mikeState = (type: 'close' | 'open') => {
        this.tracking.upload({
            event_type: TrackingSuileyooEvent.MIKE_STATE,
            value: { type }
        })
    }


}