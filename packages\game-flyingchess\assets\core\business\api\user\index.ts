
import store from "../../store"
import { BaseAPI } from "../BaseAPI"

export class UserAPI extends BaseAPI {

    private async get_user_info() {
        // const res = await cat.http<web.v1.GetUserInfoResponse>({
        //     url: '/v1/user/info',
        //     method: 'GET',

        // })
        const { user } = store
    }

}


/**获取用户信息 */
// export const get_user_info = async () => {
//     const res = await request.http<web.v1.GetUserInfoResponse>({
//         url: '/v1/user/info',
//         method: 'GET',

//     })
//     const { user } = store

// }

// /**更新用户信息 */
// export const update_user_info = (data: web.v1.UpdateUserInfoRequest) => {
//     return request.http<void>({
//         url: '/v1/user/update',
//         method: 'POST',
//         data
//     })
// }