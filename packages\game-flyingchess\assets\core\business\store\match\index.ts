/**
 * @describe 匹配数据
 * <AUTHOR>
 * @date 2024-09-12 11:35:02
 */

import { log } from 'cc'
import { ChatRoomUser } from '../../bridge/types/Chat'
import { cat } from '@/core/manager'
import { BaseStore } from '../BaseStore'
import { Store } from '../index'
import { makeAutoObservable } from 'mobx-tsuki'

/**匹配 */
export default class Match extends BaseStore {
    constructor(rootStore: Store) {
        super(rootStore)
        makeAutoObservable(this)
    }

    /**(随乐游)游戏ID */
    gameId: number = 1017

    /**麦克风 */
    micState: IBridge.MicState = 0

    /**聊天声音 */
    private _voiceState: IBridge.VoiceState = 0

    public set voiceState(v: IBridge.VoiceState) {
        this._voiceState = v
        cat.storage.set('crazy_watermelon_voiceState', v)
    }

    public get voiceState(): IBridge.VoiceState {
        return this._voiceState
    }

    /**礼物状态 */
    giftState: IBridge.GiftState = 0

    /**音效声音 */
    _musicState: IBridge.MusicState = 0

    public get musicState(): IBridge.MusicState {
        return this._musicState
    }

    public set musicState(v: IBridge.MusicState) {
        this._musicState = v
        // TODO
        // cat.audio.switchMusic = cat.audio.switchEffect = this.rootStore.match.musicState == 1
    }

    /**邀请 */
    invitePlay: boolean = false

    /**app类型*/
    appType: IBridge.AppType = 0

    /**游戏状态 0: 未开始 1: 待开始或已开始*/
    gameState: 0 | 1 = 0

    /**全局横幅特效 0：不屏蔽 1：屏蔽*/
    banner: IBridge.BlockBanner = 1

    /**
     * 匹配房间的所有成员个人信息列表(观众+麦位玩家+聊天历史用户)
     * key: uid:stirng
     */
    private roomUserlist: Map<number, ChatRoomUser> = new Map()

    /**新增成员 */
    addList(data: ChatRoomUser) {
        window.ccLog('添加成员', data)
        data.id && this.roomUserlist.set(data.id, data)
    }

    /**移除成员 */
    removeList(uids: number[]) {
        window.ccLog('移除成员')
        uids.forEach((item) => {
            this.roomUserlist.delete(item)
        })
    }

    /**清除成员 */
    clearList() {
        window.ccLog('清除成员')
        this.roomUserlist.clear()
    }

    /**获取房间成员信息*/
    getRoomUserById(id: number) {
        return this.roomUserlist.get(id)
    }

    hasRoomUser(id: number | undefined | null) {
        return typeof id === 'number' ? this.roomUserlist.has(id) : false
    }

    /**个人信息预请求处理(用于请求房间的所有用户的个人信息) */
    fetchProfiles: Map<number, ((profile: IBridge.Profile) => void)[]> =
        new Map()

    /**麦位用户列表 */
    mikeUserList: IBridge.MikeItem[] = []

    /**根据Key获取信息 */
    getMikeUserByKey<
        T extends IBridge.MikeItem,
        K extends keyof IBridge.MikeItem
    >(key: K, value: T[K]) {
        return this.mikeUserList.find((item) => item[key] === value)
    }

    /**玩家自己的麦位 */
    get selfMick() {
        return this.mikeUserList.find(
            (item) => item.userId === this.rootStore.lobby.matchRoom.userId
        )
    }

    /**房主UID */
    roomOwner: number = -1

    /**判断玩家是否为房主 */
    get isRoomOwner(): boolean | undefined {
        return this.selfMick && this.selfMick.userId === this.roomOwner
    }

    /**房间信息 */
    roomInfo: IBridge.RoomInfoRes = {
        /**房间ID */
        ddRid: '',
        /**房间流水号 */
        roomSerial: '',
        /**房间名称 */
        roomName: '',
        /**0未知 1-个人赛(2-5人场) 2-组队赛（2V2） */
        partyMode: 0,
        /**房间公告 */
        announcement: '',
        /**房间游戏状态 0未开始 1准备倒计时 2游戏中*/
        readyStatus: 0,
        /**剩余倒计时(s) */
        duration: 0,
        /**云信IM账号 */
        imUsername: '',
        /**云信IM密码 */
        impassword: '',
        /**声网token */
        swToken: '',
        /**房间开启时间。格式：yyyy-MM-dd HH:mm:ss */
        openTime: '',
        joinState: 0,
        superManager: 0,
    }

    /**重置match */
    resetMatch(): void {
        this.roomOwner = -1
        this.mikeUserList = []
    }
}
