{"skeleton": {"hash": "8j4OjhTLqmq5YJG5WyLzQRxmZ1w", "spine": "3.8.85", "x": -236.75, "y": -238.01, "width": 555.88, "height": 562.85, "images": "./images/", "audio": ""}, "bones": [{"name": "root"}, {"name": "tx/b0", "parent": "root", "length": 371.36, "rotation": 90, "scaleX": 2.1011, "scaleY": 1.7646}, {"name": "bone2", "parent": "root"}, {"name": "bengdai2", "parent": "bone2", "length": 306.83, "rotation": 7.81, "x": -155.88, "y": -23.85, "scaleX": 1.2, "scaleY": 1.2}, {"name": "bone", "parent": "bone2", "rotation": 32.2, "x": -14.22, "y": 178.7, "scaleX": -1}, {"name": "bd1", "parent": "bengdai2", "length": 107.92, "rotation": -89.99, "x": 119.18, "y": 59.57}, {"name": "bd2", "parent": "bengdai2", "length": 107.36, "rotation": -88.57, "x": 161.86, "y": 63.06}, {"name": "bengdai3", "parent": "bone2", "length": 306.83, "rotation": 7.81, "x": -123.05, "y": -84.97, "scaleX": 1.2, "scaleY": 1.2}], "slots": [{"name": "bengdai3", "bone": "bengdai3", "color": "00000027", "attachment": "bengdai2"}, {"name": "tx/b0", "bone": "tx/b0"}, {"name": "bengdai2", "bone": "bengdai2", "attachment": "bengdai2"}, {"name": "bd1", "bone": "bd1", "attachment": "bd1"}, {"name": "bd2", "bone": "bd2", "attachment": "bd2"}, {"name": "bengdai1", "bone": "bone", "attachment": "bengdai1"}], "skins": [{"name": "default", "attachments": {"bd1": {"bd1": {"type": "mesh", "uvs": [1, 1, 0.8, 1, 0.6, 1, 0.4, 1, 0.2, 1, 0, 1, 0, 0.9, 0, 0.8, 0, 0.7, 0, 0.6, 0, 0.5, 0, 0.4, 0, 0.3, 0, 0.2, 0, 0.1, 0, 0, 0.2, 0, 0.4, 0, 0.6, 0, 0.8, 0, 1, 0, 1, 0.1, 1, 0.2, 1, 0.3, 1, 0.4, 1, 0.5, 1, 0.6, 1, 0.7, 1, 0.8, 1, 0.9, 0.8, 0.9, 0.6, 0.9, 0.4, 0.9, 0.2, 0.9, 0.8, 0.8, 0.6, 0.8, 0.4, 0.8, 0.2, 0.8, 0.8, 0.7, 0.6, 0.7, 0.4, 0.7, 0.2, 0.7, 0.8, 0.6, 0.6, 0.6, 0.4, 0.6, 0.2, 0.6, 0.8, 0.5, 0.6, 0.5, 0.4, 0.5, 0.2, 0.5, 0.8, 0.4, 0.6, 0.4, 0.4, 0.4, 0.2, 0.4, 0.8, 0.3, 0.6, 0.3, 0.4, 0.3, 0.2, 0.3, 0.8, 0.2, 0.6, 0.2, 0.4, 0.2, 0.2, 0.2, 0.8, 0.1, 0.6, 0.1, 0.4, 0.1, 0.2, 0.1], "triangles": [0, 1, 29, 1, 30, 29, 29, 30, 28, 30, 34, 28, 28, 34, 27, 34, 38, 27, 27, 38, 26, 38, 42, 26, 26, 42, 25, 42, 46, 25, 25, 46, 24, 46, 50, 24, 24, 50, 23, 50, 54, 23, 23, 54, 22, 54, 58, 22, 22, 58, 21, 58, 62, 21, 21, 62, 20, 62, 19, 20, 1, 2, 30, 2, 31, 30, 30, 31, 34, 31, 35, 34, 34, 35, 38, 35, 39, 38, 38, 39, 42, 39, 43, 42, 42, 43, 46, 43, 47, 46, 46, 47, 50, 47, 51, 50, 50, 51, 54, 51, 55, 54, 54, 55, 58, 55, 59, 58, 58, 59, 62, 59, 63, 62, 62, 63, 19, 63, 18, 19, 2, 3, 31, 3, 32, 31, 31, 32, 35, 32, 36, 35, 35, 36, 39, 36, 40, 39, 39, 40, 43, 40, 44, 43, 43, 44, 47, 44, 48, 47, 47, 48, 51, 48, 52, 51, 51, 52, 55, 52, 56, 55, 55, 56, 59, 56, 60, 59, 59, 60, 63, 60, 64, 63, 63, 64, 18, 64, 17, 18, 3, 4, 32, 4, 33, 32, 32, 33, 36, 33, 37, 36, 36, 37, 40, 37, 41, 40, 40, 41, 44, 41, 45, 44, 44, 45, 48, 45, 49, 48, 48, 49, 52, 49, 53, 52, 52, 53, 56, 53, 57, 56, 56, 57, 60, 57, 61, 60, 60, 61, 64, 61, 65, 64, 64, 65, 17, 65, 16, 17, 4, 5, 33, 5, 6, 33, 33, 6, 37, 6, 7, 37, 37, 7, 41, 7, 8, 41, 41, 8, 45, 8, 9, 45, 45, 9, 49, 9, 10, 49, 49, 10, 53, 10, 11, 53, 53, 11, 57, 11, 12, 57, 57, 12, 61, 12, 13, 61, 61, 13, 65, 13, 14, 65, 65, 14, 16, 14, 15, 16], "vertices": [108.72, 33.66, 109.06, 21.26, 109.4, 8.86, 109.74, -3.54, 110.08, -15.93, 110.42, -28.33, 98.8, -28.61, 87.18, -28.89, 75.55, -29.16, 63.93, -29.44, 52.3, -29.72, 40.68, -29.99, 29.06, -30.27, 17.43, -30.55, 5.81, -30.82, -5.81, -31.1, -6.15, -18.7, -6.49, -6.3, -6.83, 6.1, -7.17, 18.49, -2.54, 31.67, 5.49, 30.98, 15.73, 31.45, 27.36, 31.72, 38.98, 32, 50.6, 32.28, 62.23, 32.55, 73.85, 32.83, 85.48, 33.11, 97.1, 33.39, 97.44, 20.99, 97.78, 8.59, 98.12, -3.81, 98.46, -16.21, 85.82, 20.71, 86.16, 8.31, 86.5, -4.09, 86.84, -16.49, 74.19, 20.43, 74.53, 8.03, 74.87, -4.37, 75.21, -16.76, 62.57, 20.16, 62.91, 7.76, 63.25, -4.64, 63.59, -17.04, 50.94, 19.88, 51.28, 7.48, 51.62, -4.92, 51.96, -17.32, 39.32, 19.6, 39.66, 7.2, 40, -5.2, 40.34, -17.6, 27.7, 19.33, 28.04, 6.93, 28.38, -5.47, 28.72, -17.87, 16.07, 19.05, 16.41, 6.65, 16.75, -5.75, 17.09, -18.15, 4.45, 18.77, 4.79, 6.37, 5.13, -6.03, 5.47, -18.43], "hull": 30, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 0], "width": 62, "height": 115}}, "bd2": {"bd2": {"type": "mesh", "uvs": [1, 1, 0.75, 1, 0.5, 1, 0.25, 1, 0, 1, 0, 0.90909, 0, 0.81818, 0, 0.72727, 0, 0.63636, 0, 0.54545, 0, 0.45455, 0, 0.36364, 0, 0.27273, 0, 0.18182, 0, 0.09091, 0, 0, 0.25, 0, 0.5, 0, 0.75, 0, 1, 0, 1, 0.09091, 1, 0.18182, 1, 0.27273, 1, 0.36364, 1, 0.45455, 1, 0.54545, 1, 0.63636, 1, 0.72727, 1, 0.81818, 1, 0.90909, 0.75, 0.90909, 0.5, 0.90909, 0.25, 0.90909, 0.75, 0.81818, 0.5, 0.81818, 0.25, 0.81818, 0.75, 0.72727, 0.5, 0.72727, 0.25, 0.72727, 0.75, 0.63636, 0.5, 0.63636, 0.25, 0.63636, 0.75, 0.54545, 0.5, 0.54545, 0.25, 0.54545, 0.75, 0.45455, 0.5, 0.45455, 0.25, 0.45455, 0.75, 0.36364, 0.5, 0.36364, 0.25, 0.36364, 0.75, 0.27273, 0.5, 0.27273, 0.25, 0.27273, 0.75, 0.18182, 0.5, 0.18182, 0.25, 0.18182, 0.75, 0.09091, 0.5, 0.09091, 0.25, 0.09091], "triangles": [0, 1, 29, 1, 30, 29, 29, 30, 28, 30, 33, 28, 28, 33, 27, 33, 36, 27, 27, 36, 26, 36, 39, 26, 26, 39, 25, 39, 42, 25, 25, 42, 24, 42, 45, 24, 24, 45, 23, 45, 48, 23, 23, 48, 22, 48, 51, 22, 22, 51, 21, 51, 54, 21, 21, 54, 20, 54, 57, 20, 20, 57, 19, 57, 18, 19, 1, 2, 30, 2, 31, 30, 30, 31, 33, 31, 34, 33, 33, 34, 36, 34, 37, 36, 36, 37, 39, 37, 40, 39, 39, 40, 42, 40, 43, 42, 42, 43, 45, 43, 46, 45, 45, 46, 48, 46, 49, 48, 48, 49, 51, 49, 52, 51, 51, 52, 54, 52, 55, 54, 54, 55, 57, 55, 58, 57, 57, 58, 18, 58, 17, 18, 2, 3, 31, 3, 32, 31, 31, 32, 34, 32, 35, 34, 34, 35, 37, 35, 38, 37, 37, 38, 40, 38, 41, 40, 40, 41, 43, 41, 44, 43, 43, 44, 46, 44, 47, 46, 46, 47, 49, 47, 50, 49, 49, 50, 52, 50, 53, 52, 52, 53, 55, 53, 56, 55, 55, 56, 58, 56, 59, 58, 58, 59, 17, 59, 16, 17, 3, 4, 32, 4, 5, 32, 32, 5, 35, 5, 6, 35, 35, 6, 38, 6, 7, 38, 38, 7, 41, 7, 8, 41, 41, 8, 44, 8, 9, 44, 44, 9, 47, 9, 10, 47, 47, 10, 50, 10, 11, 50, 50, 11, 53, 11, 12, 53, 53, 12, 56, 12, 13, 56, 56, 13, 59, 13, 14, 59, 59, 14, 16, 14, 15, 16], "vertices": [112.43, 20.95, 112.04, 8.71, 111.64, -3.54, 111.25, -15.78, 110.85, -28.03, 100.58, -27.69, 90.32, -27.36, 80.05, -27.03, 69.78, -26.7, 59.51, -26.37, 49.25, -26.04, 38.98, -25.71, 28.71, -25.37, 18.45, -25.04, 8.18, -24.71, -2.09, -24.38, -1.69, -12.14, -1.3, 0.11, -0.9, 12.35, -0.51, 24.59, 9.76, 24.26, 20.03, 23.93, 30.29, 23.6, 40.56, 23.27, 50.83, 22.94, 61.1, 22.61, 71.36, 22.27, 81.63, 21.94, 91.9, 21.61, 102.17, 21.28, 101.77, 9.04, 101.37, -3.21, 100.98, -15.45, 91.5, 9.37, 91.11, -2.88, 90.71, -15.12, 81.24, 9.7, 80.84, -2.54, 80.44, -14.79, 70.97, 10.03, 70.57, -2.21, 70.18, -14.46, 60.7, 10.36, 60.31, -1.88, 59.91, -14.12, 50.43, 10.69, 50.04, -1.55, 49.64, -13.79, 40.17, 11.03, 39.77, -1.22, 39.38, -13.46, 29.9, 11.36, 29.5, -0.89, 29.11, -13.13, 19.63, 11.69, 19.24, -0.56, 18.84, -12.8, 9.36, 12.02, 8.97, -0.22, 8.57, -12.47], "hull": 30, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 0], "width": 49, "height": 113}}, "bengdai1": {"bengdai1": {"x": -2.17, "y": -1.76, "scaleX": 2, "scaleY": 2, "width": 97, "height": 112}}, "bengdai2": {"bengdai2": {"x": 151.1, "y": -6.2, "rotation": -1.5, "width": 412, "height": 240}}, "bengdai3": {"bengdai2": {"x": 151.1, "y": -6.2, "rotation": -1.5, "width": 412, "height": 240}}, "tx/b0": {"tx/b0": {"x": 144.73, "y": -0.5, "rotation": -90, "width": 307, "height": 434}, "tx/b1": {"x": 144.73, "y": -0.5, "rotation": -90, "width": 307, "height": 434}, "tx/b2": {"x": 144.73, "y": -0.5, "rotation": -90, "width": 307, "height": 434}, "tx/b3": {"x": 144.73, "y": -0.5, "rotation": -90, "width": 307, "height": 434}, "tx/b4": {"x": 144.73, "y": -0.5, "rotation": -90, "width": 307, "height": 434}, "tx/b5": {"x": 144.73, "y": -0.5, "rotation": -90, "width": 307, "height": 434}, "tx/b6": {"x": 144.73, "y": -0.5, "rotation": -90, "width": 307, "height": 434}, "tx/b7": {"x": 144.73, "y": -0.5, "rotation": -90, "width": 307, "height": 434}, "tx/b8": {"x": 144.73, "y": -0.5, "rotation": -90, "width": 307, "height": 434}, "tx/b9": {"x": 144.73, "y": -0.5, "rotation": -90, "width": 307, "height": 434}, "tx/b10": {"x": 144.73, "y": -0.5, "rotation": -90, "width": 307, "height": 434}, "tx/b11": {"x": 144.73, "y": -0.5, "rotation": -90, "width": 307, "height": 434}, "tx/b12": {"x": 144.73, "y": -0.5, "rotation": -90, "width": 307, "height": 434}, "tx/b13": {"x": 144.73, "y": -0.5, "rotation": -90, "width": 307, "height": 434}, "tx/b14": {"x": 144.73, "y": -0.5, "rotation": -90, "width": 307, "height": 434}, "tx/b15": {"x": 144.73, "y": -0.5, "rotation": -90, "width": 307, "height": 434}}}}], "animations": {"rebirth1": {"slots": {"bd1": {"attachment": [{"name": null}, {"time": 0.9, "name": "bd1"}]}, "bd2": {"attachment": [{"name": null}, {"time": 1.5667, "name": "bd2"}]}, "bengdai1": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1, "color": "ffffffff"}]}, "bengdai2": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "color": "ffffffff"}]}, "bengdai3": {"color": [{"color": "00000000", "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "color": "00000028"}]}}, "bones": {"bengdai2": {"rotate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.8333}], "translate": [{"x": -181.19, "curve": 0.167, "c2": 0.4, "c3": 0.621, "c4": 0.8}, {"time": 0.1, "x": -19.98, "curve": 0.389, "c2": 0.63, "c3": 0.745}, {"time": 0.3333, "x": 3.34, "curve": 0.25, "c3": 0.75}, {"time": 0.8333}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.8333}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.8333}]}, "bone": {"translate": [{"curve": "stepped"}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": 26.51, "y": -357.89, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 26.51, "y": -357.89, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "y": 4.51, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 0.906, "y": 0.906, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": 0.906, "y": 0.906, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 0.714, "y": 0.714, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 0.906, "y": 0.906, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 0.906, "y": 0.906, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 0.714, "y": 0.714, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "x": 0.906, "y": 0.906}]}, "bd1": {"scale": [{"time": 0.9, "x": 0.118, "curve": 0.25, "c3": 0.75}, {"time": 1.2333}]}, "bd2": {"scale": [{"time": 1.5667, "x": 0.118, "curve": 0.25, "c3": 0.75}, {"time": 1.9}]}, "bone2": {"rotate": [{"curve": "stepped"}, {"time": 1.3333, "curve": "stepped"}, {"time": 2.6667}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "y": 16.93, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}], "scale": [{"curve": "stepped"}, {"time": 1.3333, "curve": "stepped"}, {"time": 2.6667}], "shear": [{"curve": "stepped"}, {"time": 1.3333, "curve": "stepped"}, {"time": 2.6667}]}, "bengdai3": {"rotate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.8333}], "translate": [{"x": -181.19, "curve": 0.167, "c2": 0.4, "c3": 0.621, "c4": 0.8}, {"time": 0.1, "x": -19.98, "curve": 0.389, "c2": 0.63, "c3": 0.745}, {"time": 0.3333, "x": 3.34, "curve": 0.25, "c3": 0.75}, {"time": 0.8333}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.8333}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.8333}]}}, "drawOrder": [{"time": 1.1667, "offsets": [{"slot": "bengdai1", "offset": -3}]}, {"time": 1.5}, {"time": 1.8333, "offsets": [{"slot": "bengdai1", "offset": -3}]}, {"time": 2.1667}]}, "rebirth2": {"slots": {"bd1": {"attachment": [{"name": null}]}, "bd2": {"attachment": [{"name": null}]}, "bengdai1": {"attachment": [{"name": null}]}, "bengdai2": {"attachment": [{"name": null}]}, "bengdai3": {"attachment": [{"name": null}]}, "tx/b0": {"attachment": [{"name": null}, {"time": 0.0333, "name": "tx/b1"}, {"time": 0.1, "name": "tx/b2"}, {"time": 0.1333, "name": "tx/b3"}, {"time": 0.1667, "name": "tx/b4"}, {"time": 0.2333, "name": "tx/b5"}, {"time": 0.2667, "name": "tx/b6"}, {"time": 0.3, "name": "tx/b7"}, {"time": 0.3667, "name": "tx/b8"}, {"time": 0.4, "name": "tx/b9"}, {"time": 0.4333, "name": "tx/b10"}, {"time": 0.4667, "name": "tx/b11"}, {"time": 0.5333, "name": "tx/b12"}, {"time": 0.5667, "name": "tx/b13"}, {"time": 0.6, "name": "tx/b14"}, {"time": 0.6667, "name": null}]}}}}}