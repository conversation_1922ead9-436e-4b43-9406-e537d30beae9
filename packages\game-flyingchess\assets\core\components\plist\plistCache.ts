import { _decorator, Component, Node, SpriteAtlas } from 'cc'
const { ccclass, property } = _decorator

interface plistCacheData {
    /*每一帧延时 */
    spAtlas: SpriteAtlas
}
export type { plistCacheData }

export class plistCache {
    private static instance: plistCache = null!

    public plistCacheMap: Record<string, plistCacheData> = {}

    static getInstance(): plistCache {
        if (!plistCache.instance) {
            plistCache.instance = new plistCache()
        }
        return plistCache.instance
    }

    add(key: string, value: plistCacheData): void {
        if (!this.has(key)) {
            this.plistCacheMap[key] = value
        }
    }

    getItemFrame(key: string): plistCacheData | undefined {
        return this.plistCacheMap[key]
    }

    has(key: string): boolean {
        return this.plistCacheMap[key] !== undefined
    }
}
