import { _decorator, Camera, Component, log, Node, v3 } from 'cc'
import { BaseComponent } from '@/core/manager/gui/base/BaseComponent'
import { cat } from '@/core/manager'
import { UPDATE_FOV } from './FitWidthCamera'
const { ccclass, property } = _decorator

@ccclass('Scene3dFitWith2d')
export class Scene3dFitWith2d extends BaseComponent {
    @property({ type: Node, tooltip: '2D参考点' })
    base2D: Node

    @property({ type: Node, tooltip: '3D参考点' })
    base3D: Node

    @property({ type: Camera, tooltip: '2D相机' })
    camera2D: Camera

    @property({ type: Camera, tooltip: '3D相机' })
    camera3D: Camera

    protected override onEventListener(): void {
        cat.event.on(UPDATE_FOV, this.updateFit, this)
    }

    override start() {
        this.updateFit()
    }

    private updateFit() {
        window.ccLog('updateFit')
        this.scheduleOnce(() => {
            // this.camera3D.node.setWorldPosition(
            //     v3(
            //         this.camera3D.node.worldPosition.x,
            //         0,
            //         this.camera3D.node.worldPosition.z
            //     )
            // )

            this.camera2D.camera.update(true)
            const worldPosition = this.base2D.worldPosition.clone()

            worldPosition.y = worldPosition.y - 90

            const worldToScreen = v3()
            this.camera2D.camera.worldToScreen(worldToScreen, worldPosition)

            const screenToWorld = v3()
            this.camera3D.camera.update(true)
            this.camera3D.camera.screenToWorld(
                screenToWorld,
                v3(
                    worldToScreen.x,
                    worldToScreen.y,
                    (this.base3D.worldPosition.z - this.camera2D.near) /
                        this.camera2D.far
                )
            )

            const { y } = screenToWorld.clone()

            this.camera3D.node.setWorldPosition(
                v3(
                    this.camera3D.node.worldPosition.x,
                    y,
                    this.camera3D.node.worldPosition.z
                )
            )
        }, 0.016)
    }
}
