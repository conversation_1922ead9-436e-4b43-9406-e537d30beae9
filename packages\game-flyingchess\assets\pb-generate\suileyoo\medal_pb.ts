// @generated by protoc-gen-es v2.2.5 with parameter "target=ts,import_extension=none,json_types=true"
// @generated from file medal.proto (package protos, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv1";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file medal.proto.
 */
export const file_medal: GenFile = /*@__PURE__*/
  fileDesc("CgttZWRhbC5wcm90bxIGcHJvdG9zIj8KFU1lZGFsRG9uZU5vdGlmaWNhdGlvbhIKCgJpZBgBIAEoBBIMCgRuYW1lGAIgASgJEgwKBGtpbmQYAyABKAVCMAokY29tLnN0bnRzLmNsb3VkLnN1aWxleW9vLmVjaG8ucHJvdG9zWgguL3Byb3Rvc2IGcHJvdG8z");

/**
 * 个人勋章获得通知
 *
 * @generated from message protos.MedalDoneNotification
 */
export type MedalDoneNotification = Message<"protos.MedalDoneNotification"> & {
  /**
   * 勋章记录ID
   *
   * @generated from field: uint64 id = 1;
   */
  id: bigint;

  /**
   * 勋章名称
   *
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * 勋章类型
   *
   * @generated from field: int32 kind = 3;
   */
  kind: number;
};

/**
 * 个人勋章获得通知
 *
 * @generated from message protos.MedalDoneNotification
 */
export type MedalDoneNotificationJson = {
  /**
   * 勋章记录ID
   *
   * @generated from field: uint64 id = 1;
   */
  id?: string;

  /**
   * 勋章名称
   *
   * @generated from field: string name = 2;
   */
  name?: string;

  /**
   * 勋章类型
   *
   * @generated from field: int32 kind = 3;
   */
  kind?: number;
};

/**
 * Describes the message protos.MedalDoneNotification.
 * Use `create(MedalDoneNotificationSchema)` to create a new message.
 */
export const MedalDoneNotificationSchema: GenMessage<MedalDoneNotification, MedalDoneNotificationJson> = /*@__PURE__*/
  messageDesc(file_medal, 0);

