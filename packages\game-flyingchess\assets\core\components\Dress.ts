import {
    _decorator,
    Component,
    Node,
    Prefab,
    sp,
    instantiate,
    isValid,
    Vec3,
} from 'cc'
import { BaseComponent } from '@/core/manager/gui/base/BaseComponent'
import { cat } from '@/core/manager'
import {
    EnumJSBridgeWebView,
    JSBridgeWebView,
} from '@/core/business/jsbridge/JSBridge'
import { PlatformConstant } from '@/core/business/constant/PlatformConstant'
import { plistImage } from '@/core/components/plist/plistImage'
import { GameEventConstant, RoomEventConstant } from '@/core/business/constant'
import store from '@/core/business/store'

const { ccclass, property } = _decorator

export type DressProps = {
    playerId: string
}

export type DressData = {
    // 可以根据需要添加数据
}

@ccclass('Dress')
export class Dress extends BaseComponent<DressProps, DressData> {
    @property({ type: Node, tooltip: '声波特效' })
    sound_effect: Node = null!

    @property({ type: Node, tooltip: '声波特效' })
    wave_node: Node = null!

    @property({ type: Node, tooltip: '表情包节点' })
    node_face: Node = null!

    @property({ type: Node, tooltip: '头像框节点' })
    node_frame: Node = null!

    @property(Prefab)
    plistImg: Prefab = null!

    override props: DressProps = {
        playerId: '',
    }

    override data: DressData = {
        // 可以根据需要初始化数据
    }

    protected override onLoad(): void {
        this.sound_effect
            .getComponent(sp.Skeleton)
            ?.setCompleteListener((_) => {
                this.sound_effect.active = false
            })
    }

    protected override onEventListener(): void {
        cat.event.on(
            RoomEventConstant.UPDATE_PLATFORM,
            this.updatePlatform,
            this
        )

        JSBridgeWebView.on(
            EnumJSBridgeWebView.MIKE_SOUND_WAVE,
            this.onSoundWave,
            this
        )
        JSBridgeWebView.on(
            EnumJSBridgeWebView.PLAYMIKEEMOJI,
            this.showFaceImg,
            this
        )
    }

    protected override removeListener(): void {
        JSBridgeWebView.off(
            EnumJSBridgeWebView.MIKE_SOUND_WAVE,
            this.onSoundWave,
            this
        )
        JSBridgeWebView.off(
            EnumJSBridgeWebView.PLAYMIKEEMOJI,
            this.showFaceImg,
            this
        )
    }

    updatePlatform() {
        this.showIconFrame()
    }

    showFaceImg(tempdata: any) {
        let data = tempdata
        if (typeof tempdata == 'string') {
            data = JSON.parse(tempdata)
        }
        console.log(
            '表情显示内容============',
            data.playUserId,
            this.props.playerId,
            data.playUrl
        )
        if (data.playUserId === this.props.playerId) {
            this.createVideoSprite(
                data.playUrl,
                PlatformConstant.face_scale,
                false,
                this.node_face
            )
        }
    }

    onSoundWave(data: any) {
        console.log('声波信息============', typeof data, data)

        if (!this.props?.playerId || data.length <= 0) {
            return
        }
        if (data.indexOf(Number(this.props?.playerId)) <= -1) {
            return
        }

        let wave = store.game.getPlatUserWave(this.props?.playerId)
        if (!wave) {
            if (this.sound_effect.active) return
            this.sound_effect.active = true
            this.sound_effect
                .getComponent(sp.Skeleton)
                ?.setAnimation(0, 'animation', false)
        } else {
            let node = this.wave_node
            if (node.children.length <= 0) {
                this.createVideoSprite(
                    wave,
                    PlatformConstant.wave_scale,
                    false,
                    node
                )
            }
        }
    }

    // 显示头像框
    showIconFrame() {
        if (!this.props.playerId) {
            return
        }
        let avatarFrame = store.game.getPlatUserAvatarFrame(this.props.playerId)
        console.log('showIconFrame===========', avatarFrame)
        if (avatarFrame) {
            let node = this.node_frame
            let childs = node.children
            if (childs.length > 0 && isValid(childs[0])) {
                let comp = childs[0].getComponent(plistImage)
                if (comp?.remoteUrl == avatarFrame) {
                    return
                }
                this.createVideoSprite(
                    avatarFrame,
                    PlatformConstant.iconFrame_scale,
                    true,
                    node
                )
            } else {
                this.createVideoSprite(
                    avatarFrame,
                    PlatformConstant.iconFrame_scale,
                    true,
                    node
                )
            }
        }
    }

    createVideoSprite(url: string, scale: Vec3, loop: boolean, parent: Node) {
        parent.destroyAllChildren()
        let node = instantiate(this.plistImg)
        node.scale = scale
        node.getComponent(plistImage)?.loadUrl(url, loop)
        parent.addChild(node)
    }
}
