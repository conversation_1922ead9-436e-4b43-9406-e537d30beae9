{"skeleton": {"hash": "yWCe/6Eg2Lspxaidybkgmj4OGzU", "spine": "3.8.85", "x": -272.33, "y": -236.65, "width": 470.03, "height": 527.38, "images": "./images/", "audio": "E:/XM/商业策划部/海盗桶/07-强化卡牌动效"}, "bones": [{"name": "root"}, {"name": "a", "parent": "root", "x": -0.66, "y": 70.24, "scaleX": 0.7632, "scaleY": 0.7632}, {"name": "qh1", "parent": "a", "x": -75.15, "y": 156.16}, {"name": "qh2", "parent": "a", "x": -273.54, "y": -102.85, "scaleX": 1.6498, "scaleY": 1.6498}, {"name": "qh3", "parent": "a", "x": 191.21, "y": -225.93, "scaleX": 1.3456, "scaleY": 1.3456}, {"name": "qh4", "parent": "a", "x": -0.7, "y": -72.21}, {"name": "aaa", "parent": "qh4", "rotation": -38.24, "x": 41.29, "y": 42.18}], "slots": [{"name": "qh2", "bone": "qh4", "attachment": "qh2"}, {"name": "qh1", "bone": "qh1", "attachment": "qh1"}, {"name": "qh3", "bone": "qh2", "attachment": "qh1"}, {"name": "qh4", "bone": "qh3", "attachment": "qh1"}, {"name": "gg", "bone": "qh4", "attachment": "gg"}, {"name": "aa", "bone": "aaa", "attachment": "aa"}, {"name": "aa2", "bone": "aaa", "attachment": "aa"}], "skins": [{"name": "default", "attachments": {"aa": {"aa": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [246.66, -239.18, -401.4, 25.45, -302.35, 268, 345.7, 3.38], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 700, "height": 262}}, "aa2": {"aa": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [246.66, -239.18, -401.4, 25.45, -302.35, 268, 345.7, 3.38], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 700, "height": 262}}, "gg": {"gg": {"type": "clipping", "end": "gg", "vertexCount": 18, "vertices": [-106.2, -65.49, -10.86, -117.08, 13.5, -91.49, 56.42, -67.97, 83.72, -48.82, 100.64, -33.13, 125.41, -3.83, 148.11, 32.9, 161.31, 66.33, 169.98, 109.77, 172.46, 153.11, 169.57, 193.97, 161.98, 230.27, 149.13, 269.1, 133.9, 302.26, 117.17, 327.36, 112.98, 330.35, 107.31, 328.55], "color": "ce3a3aff"}}, "qh1": {"qh1": {"x": 0.55, "y": 1.74, "width": 101, "height": 108}}, "qh2": {"qh2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [203.59, -329.89, -241.41, -329.89, -241.41, 361.11, 203.59, 361.11], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 445, "height": 691}}, "qh3": {"qh1": {"x": 0.55, "y": 1.74, "width": 101, "height": 108}}, "qh4": {"qh1": {"x": 0.55, "y": 1.74, "width": 101, "height": 108}}}}], "animations": {"idle": {"slots": {"qh1": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.3, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.6, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.9, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 1.2, "color": "ffffff00"}]}, "qh3": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.3, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.6, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.9, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.2, "color": "ffffffff"}]}, "qh4": {"color": [{"color": "ffffff48", "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.4, "color": "ffffffff", "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.6, "color": "ffffff48", "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.7, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1, "color": "ffffffff", "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.2, "color": "ffffff48"}]}}, "bones": {"qh1": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5667, "y": 196.54, "curve": "stepped"}, {"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "y": 196.54, "curve": "stepped"}, {"time": 1.2}]}, "root": {"rotate": [{}], "translate": [{}], "scale": [{}], "shear": [{}]}, "a": {"rotate": [{}], "translate": [{}], "scale": [{}], "shear": [{}]}, "qh2": {"translate": [{"y": 110.07, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.2667, "y": 196.54, "curve": "stepped"}, {"time": 0.3, "curve": 0.247, "c3": 0.63, "c4": 0.52}, {"time": 0.6, "y": 110.07, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.8667, "y": 196.54, "curve": "stepped"}, {"time": 0.9, "curve": 0.247, "c3": 0.63, "c4": 0.52}, {"time": 1.2, "y": 110.07}]}, "qh3": {"translate": [{"y": 189.62, "curve": 0.358, "c2": 0.65, "c3": 0.693}, {"time": 0.0667, "y": 196.54, "curve": "stepped"}, {"time": 0.1, "curve": 0.245, "c3": 0.714, "c4": 0.85}, {"time": 0.6, "y": 189.62, "curve": 0.358, "c2": 0.65, "c3": 0.693}, {"time": 0.6667, "y": 196.54, "curve": "stepped"}, {"time": 0.7, "curve": 0.245, "c3": 0.714, "c4": 0.85}, {"time": 1.2, "y": 189.62}]}, "qh4": {"rotate": [{"curve": "stepped"}, {"time": 0.6, "curve": "stepped"}, {"time": 1.2}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6, "y": 31.58, "curve": 0.25, "c3": 0.75}, {"time": 1.2}], "scale": [{"curve": "stepped"}, {"time": 0.6, "curve": "stepped"}, {"time": 1.2}], "shear": [{"curve": "stepped"}, {"time": 0.6, "curve": "stepped"}, {"time": 1.2}]}, "aaa": {"rotate": [{"curve": "stepped"}, {"time": 1.2}], "translate": [{"x": -188.64, "y": -239.41, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "x": 167.18, "y": 317.22}], "scale": [{"curve": "stepped"}, {"time": 1.2}], "shear": [{"curve": "stepped"}, {"time": 1.2}]}}}}}