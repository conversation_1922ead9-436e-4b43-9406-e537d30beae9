

declare namespace IBridge {
    /**异常响应 */
    type IErrorResponse = {
        errorCode: number;
        /**错误信息 */
        detailMessage: string;
        message: string
        /**输出数据 */
        data: null;
    }

    /**成功响应 */
    type ISuccessResponse<T> = {
        code: number,
        message: string;
        /**输出数据 */
        data: T;
    }

    /**通过响应格式 */
    type IResponse<T> = ISuccessResponse<T> | IErrorResponse

    //#region 游戏主页

    /**个人数据请求 */
    export type UserGameDataReq = {
        /**游戏ID */
        gameId: number
    }

    /**个人数据响应 */
    type UserGameDataRes = {
        /**当前派对游戏时长(分钟) */
        playTime: number,
        /**当前派对游戏总场次 */
        playCount: number,
        /**当前派对游戏胜利场次 */
        winCount: number,
        /**胜率（百分比） */
        winRatio: string,
        /**用户ID */
        userId: number
        /**当前游戏等级 */
        gameLevel: number
        /**当前游戏经验值 */
        exp: number
        /**当前赛季段位。当前无赛季，返回null */
        seasonTier: SeasonTier | null
        /**当前赛季经验值 */
        seasonExp: number
    }

    /**赛季 */
    type SeasonTier = {
        /**段位logo */
        logo: string
        /**段位等级图片 */
        img: string
        /**段位名称 */
        name: string
        /**等级罗马数值 */
        romanVal: string
    }

    /**1-个人赛(2-5人场) 2-组队赛（2V2）10竞技场单人 20竞技场组队*/
    type PartyMode = 0 | 1 | 2 | 10 | 20

    /**创建房间请求 */
    type CreateRoomReq = {
        /**游戏ID */
        gameId: number
        /**1-个人赛(2-5人场) 2-组队赛（2V2） */
        partyMode: PartyMode
        /**玩家数量 */
        mikeCount: number
        /**0-不匹配用户；1-匹配用户 */
        joinState: number
    }

    /**创建房间响应 */
    type CreateRoomRes = {
        /**房间ID */
        ddRid: string,
        /**房间流水 */
        roomSerial: string,
        /**云信IM账号 */
        imUsername: string,
        /**云信IM密码 */
        impassword: string,
        /**声网token */
        swToken: string,
        /**当前用户ID */
        userId: number,
        /**1-个人赛(2-5人场) 2-组队赛（2V2） */
        partyMode: PartyMode,
    }


    /**搜索房间请求 */
    type SearchRoomReq = {
        /**游戏ID */
        gameId: number,
        /**房间ID */
        ddRid: string
    }

    /**搜索房间响应 */
    type SearchRoomRes = CreateRoomRes

    /**匹配房间请求 */
    type MatchRoomReq = CreateRoomReq

    /**匹配房间响应 */
    type MatchRoomRes = CreateRoomRes



    //#endregion

    //#region 房间主页


    /**用户资产请求 */
    type UserAssetReq = {}

    /**用户资产响应 */
    type UserAssetRes = {
        /**云贝 */
        point: number
        /**小鱼干 */
        pointD10: number
    }


    type Extra = {
        pointD10: number
    }

    /**房间信息请求 */
    type RoomInfoReq = {
        /**游戏ID */
        gameId: number
    }

    /**房间信息响应 */
    type RoomInfoRes = {
        /**房间ID */
        ddRid: string
        /**房间流水号 */
        roomSerial: string,
        /**房间名称 */
        roomName: string,
        /**0未知 1-个人赛(2-5人场) 2-组队赛（2V2） */
        partyMode: PartyMode,
        /**房间公告 */
        announcement: string,
        /**房间游戏状态 0-未开始（匹配中）；1-准备倒计时，2-游戏中*/
        readyStatus: 0 | 1 | 2,
        /**剩余倒计时(s) */
        duration: number,
        /**云信IM账号 */
        imUsername: string,
        /**云信IM密码 */
        impassword: string,
        /**声网token */
        swToken: string,
        /**房间开启时间。格式：yyyy-MM-dd HH:mm:ss */
        openTime: string
        /**0-不匹配用户；1-匹配用户 */
        joinState: number
        /**0-非超管；1-是超管 */
        superManager: number
    }

    type BlockBanner = 0 | 1


    /**全局横幅特效响应 */
    type BannerRes = {
        /**屏蔽房间横幅动效 0、不屏蔽；1、屏蔽 */
        blockBanner: BlockBanner
    }

    /**退出房间请求 */
    type ExitRoomReq = {
        /**房间ID */
        ddRid: string
    }

    //#endregion

    //#region 麦位接口
    type MikeState = 0 | 1 | 2

    type MikeItem = {
        /**麦位ID */
        mikeId: number,
        /**房间ID */
        ddRid: string,
        /**麦位成员用户ID */
        userId: number,
        /**麦位：房客1~5 */
        position: number,
        /**队伍，从1开始 */
        team: number,
        /**麦位状态：0-未锁定，1-已锁定 */
        status: 0 | 1,
        /**是否被禁麦：0-否，1-是 */
        isBanVoice: 0 | 1,
        /**是否开麦：0-否，1-是 */
        isOpenVoice: 0 | 1,
        /**游戏状态(0-未准备,1-已准备) */
        readyStatus: MikeState,
    }

    /**麦位列表请求 */
    type MikeListReq = {
        /**房间ID */
        ddRid: string
    }


    /**麦位列表响应 */
    type MikeListRes = {
        tempHownerId: number //房主UID
        mikeList: MikeItem[] //麦位列表
    }

    /**切换麦位请求 */
    type ChangeMikeReq = {
        /**目标麦位ID */
        mikeId: number
    }


    /**观众列表请求 */
    type AudienceListReq = {
        /**房间ID */
        ddRid: string
        /**页码 */
        page: number
        /**数量 */
        size: number
    }

    type IAudience = { userId: number }

    /**观众列表响应 */
    type AudienceListRes = {
        /**总记录数，必须提供 */
        totalRows: number
        /**总页数，必须提供 */
        totalPage: number
        /**第几页，必须提供 */
        pageNo: number
        /**每页条数，必须提供 */
        pageSize: number
        /**结果集，必须提供，T为具体类型 */
        rows: Array<IAudience>
        /**分页彩虹，必须提供，表示分页的颜色数组或类似用途 */
        rainbow: number[]
    }

    //#endregion

    //#region 游戏接口

    /**准备 */
    type ReadyReq = {
        /**麦位ID */
        mikeId: number
    }

    /**取消准备 */
    type UnReadyReq = ReadyReq


    /**获取进入游戏参数(启动参数) */
    type EnterParam = {
        /**麦位ID */
        battleId: string,
        /**游戏模式 */
        mode: string,
        /**玩家入场token（观战不需要） */
        token?: string
        serverId?: string
    }
    //#endregion

    //#region 游戏结算

    /**0|未填写，1|男，2|女 */
    type Gender = 0 | 1 | 2

    type Profile = {
        /**显示昵称 */
        displayName: string,
        /**头像 */
        avatar: string,
        /**头像框 */
        avatarFrame: string,
        /**性别 */
        gender: Gender,
        /**简介 */
        intro: string,
        /**铭牌地址 */
        nameplate: string
        /**徽章 */
        badge: string
        /**名片卡 */
        bizCard: string
        /**归属地区 */
        region: string
        /**id */
        id: number | undefined
        /**魅力值 */
        charms: number
    }

    /**查询用户信息请求 */
    type QueryUserInfoReq = {
        /**UID(同userId)集合，以逗号分隔。最大支持100 ("A,B...")*/
        followerIds: string
    }

    /**查询用户信息响应 */
    type QueryUserInfoRes = UserInfo[]


    /**查询已关注的用户请求 */
    type QueryFollowedUserReq = {
        /**UID(同userId)集合，需要查的人，多个用逗号分隔 ("A,B...")*/
        selectUids: string
    }

    /**查询已关注的用户响应 */
    type QueryFollowedUserRes = {
        /**已经关注的人集合 */
        followUidList: number[]
    }

    /**关注(添加好友)请求 */
    type FollowReq = {
        /*关注人UID(同userId)*/
        followerId: string
    }

    /**锁麦请求 */
    type MikeLockReq = {
        ddRid: string,
        mikeId: number
    }
    /**解麦请求 */
    type MikeUnLockReq = MikeLockReq

    /**踢人请求 */
    type BanReq = {
        ddRid: string,
        kickUserId: number
    }
    //#endregion

    //#region 任务
    type TaskReq = {
        gameId: number,
    }

    type TaskRes = TaskItem[]

    type TaskItem = {
        /**任务标识 */
        taskCode: string;
        /**任务名称 */
        taskName: string;
        /**已完成进度 */
        finishProgress: number;
        /**总进度 */
        progress: number;
        /**状态, 0-待完成；1-待领取；2-已完成 */
        status: number;
        /**奖励信息 */
        reward: GameTaskReward;
    };

    type GameTaskReward = {
        /**奖励物品图片 */
        icon: string;
        /**奖励数量 */
        value: number;
        /**奖励物品名称 */
        name: string;
        /**奖励物品描述 */
        desc: string;
    };
    //#endregion


    //#region 任务领取


    type TaskReceiveReq = {
        gameId: number,
        taskCode: string
    }

    type TaskReceiveRes = {
        icon: string,
        value: number,
        name: string,
        desc: string
    }

    //#endregion

    //#region 上麦邀请

    type InviteAcceptReq = {
        roomSerial: string
    }

    type InviteRefuseReq = InviteAcceptReq

    //#endregion

}
