/**
 * @describe 游戏初始场景(初始场景需要继承该类)
 * <AUTHOR>
 * @date 2023-09-26 16:26:09
 */

import { BaseComponent } from '../base/BaseComponent'
import { cat } from '../../index'
import { Environment } from '@/core/business/environment'
import { Tracking } from '@/core/business/tracking'

export default class InitialScene extends BaseComponent {
    override async onLoad() {
        // 核心库启动
        await cat.boot()

        this.onLoaded()
    }

    protected onLoaded() {}
}
