/**
 * @describe 全局数据数据存储
 * <AUTHOR>
 * @date 2022-12-28 15:10:13
 */

import { PlatformType } from '../../jsbridge/JSBridge'
import { getURLParameters } from '../../util/StringUtil'
import { BaseStore } from '../BaseStore'

/**自定义平台 (用于引擎无法识别的平台))*/
export enum CustomPlatform {
    /**带带 */
    DaiDaiH5,
    /**随乐游 */
    SuiLeYoo,
}

export enum GameVisiable {
    SHOW,
    HIDE,
}

export enum URLParamsKey {
    params = 'params',
    platform = 'platform',
    safe_top = 'safe_top',
    safe_bottom = 'safe_bottom',
    channel = 'channel',
    env = 'env',
    voice_room_version_num = 'voiceRoomVersionNum',
}

export enum EnvType {
    DEV = 'dev',
    TRIAL = 'trial',
    RELEASE = 'release',
}

export enum ChannelType {
    WEB = 'web',
    APP = 'app',
}

export type URLParams = Partial<Record<URLParamsKey, string>>
export default class GlobalStore extends BaseStore {
    /**自定义渠道 虎扑等等 TODO*/
    customPlatform: CustomPlatform | null = CustomPlatform.SuiLeYoo

    /**渠道 虎扑渠道*/
    channel: string = ''

    /**feature */
    social_game_feature: 'daidai' | 'suileyoo' | 'yiqiyoo' | '1yiqiyoo' =
        'daidai'

    /**社交游戏id */
    social_game_id = 'bombduck'

    /**HTTP服务器时间差 */
    diffServerTimer: number = 0

    /**ws服务器时间差 */
    ws_diff_server_timer: number = 0

    pixelRatio: number = 1

    titleBarHeight: number = 0

    /**appid */
    appId: string = ''

    // EVENT_HIDE 时间
    hideTime: number = 0

    // EVENT_SHOW 时间
    showTime: number = 0

    /**前后台 */
    gameVisiable: GameVisiable = GameVisiable.SHOW

    url_params: URLParams = getURLParameters(
        decodeURIComponent(window.location.href)
    )

    get isFlutterPlatform(): boolean {
        return this.url_params.platform === PlatformType.FLUTTER
    }

    get isChannelWeb(): boolean {
        return this.url_params.channel === ChannelType.WEB
    }

    get isDebugOrTestEnv(): boolean {
        return (
            // location.host.includes('dev') ||
            // location.host.includes('testing') ||
            location.href.includes('debug=1')
        )
    }

    get isGameVisiable(): boolean {
        return this.gameVisiable === GameVisiable.SHOW
    }

    /**是否支持原生播放音频
     *
     * voiceRoomVersionNum: 开始支持原生播放音频的版本号
     */
    //ios，并且voiceRoomVersionNum >= 1
    get isSupportNativePlayAudio(): boolean {
        const version = Number(this.url_params.voiceRoomVersionNum || 0)
        return this.url_params.platform === PlatformType.IOS && version >= 1
    }
}
