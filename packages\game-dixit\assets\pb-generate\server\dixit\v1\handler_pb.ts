// @generated by protoc-gen-es v2.4.0 with parameter "target=ts,json_types=true"
// @generated from file dixit/v1/handler.proto (package dixit.v1, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv1";
import { enumDesc, fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import type { CardInfo, CardInfoJson } from "./card_pb";
import { file_dixit_v1_card } from "./card_pb";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file dixit/v1/handler.proto.
 */
export const file_dixit_v1_handler: GenFile = /*@__PURE__*/
  fileDesc("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", [file_dixit_v1_card]);

/**
 * 说书人选牌的请求
 *
 * @generated from message dixit.v1.PostCardRequest
 */
export type PostCardRequest = Message<"dixit.v1.PostCardRequest"> & {
  /**
   * 手牌索引
   *
   * @generated from field: string hand_card = 1;
   */
  handCard: string;
};

/**
 * 说书人选牌的请求
 *
 * @generated from message dixit.v1.PostCardRequest
 */
export type PostCardRequestJson = {
  /**
   * 手牌索引
   *
   * @generated from field: string hand_card = 1;
   */
  handCard?: string;
};

/**
 * Describes the message dixit.v1.PostCardRequest.
 * Use `create(PostCardRequestSchema)` to create a new message.
 */
export const PostCardRequestSchema: GenMessage<PostCardRequest, PostCardRequestJson> = /*@__PURE__*/
  messageDesc(file_dixit_v1_handler, 0);

/**
 * 说书人选牌响应
 *
 * @generated from message dixit.v1.PostCardResponse
 */
export type PostCardResponse = Message<"dixit.v1.PostCardResponse"> & {
};

/**
 * 说书人选牌响应
 *
 * @generated from message dixit.v1.PostCardResponse
 */
export type PostCardResponseJson = {
};

/**
 * Describes the message dixit.v1.PostCardResponse.
 * Use `create(PostCardResponseSchema)` to create a new message.
 */
export const PostCardResponseSchema: GenMessage<PostCardResponse, PostCardResponseJson> = /*@__PURE__*/
  messageDesc(file_dixit_v1_handler, 1);

/**
 * 说书人讲故事的请求
 *
 * @generated from message dixit.v1.PostStoryRequest
 */
export type PostStoryRequest = Message<"dixit.v1.PostStoryRequest"> & {
  /**
   * 故事内容
   *
   * @generated from field: string story = 1;
   */
  story: string;

  /**
   * 类型，0|最终提交，1 临时同步（间隔0.5s防抖）
   *
   * @generated from field: int32 type = 2;
   */
  type: number;
};

/**
 * 说书人讲故事的请求
 *
 * @generated from message dixit.v1.PostStoryRequest
 */
export type PostStoryRequestJson = {
  /**
   * 故事内容
   *
   * @generated from field: string story = 1;
   */
  story?: string;

  /**
   * 类型，0|最终提交，1 临时同步（间隔0.5s防抖）
   *
   * @generated from field: int32 type = 2;
   */
  type?: number;
};

/**
 * Describes the message dixit.v1.PostStoryRequest.
 * Use `create(PostStoryRequestSchema)` to create a new message.
 */
export const PostStoryRequestSchema: GenMessage<PostStoryRequest, PostStoryRequestJson> = /*@__PURE__*/
  messageDesc(file_dixit_v1_handler, 2);

/**
 * 说书人讲故事的响应
 *
 * @generated from message dixit.v1.PostStoryResponse
 */
export type PostStoryResponse = Message<"dixit.v1.PostStoryResponse"> & {
};

/**
 * 说书人讲故事的响应
 *
 * @generated from message dixit.v1.PostStoryResponse
 */
export type PostStoryResponseJson = {
};

/**
 * Describes the message dixit.v1.PostStoryResponse.
 * Use `create(PostStoryResponseSchema)` to create a new message.
 */
export const PostStoryResponseSchema: GenMessage<PostStoryResponse, PostStoryResponseJson> = /*@__PURE__*/
  messageDesc(file_dixit_v1_handler, 3);

/**
 * 生成故事请求
 *
 * @generated from message dixit.v1.GenStoryRequest
 */
export type GenStoryRequest = Message<"dixit.v1.GenStoryRequest"> & {
};

/**
 * 生成故事请求
 *
 * @generated from message dixit.v1.GenStoryRequest
 */
export type GenStoryRequestJson = {
};

/**
 * Describes the message dixit.v1.GenStoryRequest.
 * Use `create(GenStoryRequestSchema)` to create a new message.
 */
export const GenStoryRequestSchema: GenMessage<GenStoryRequest, GenStoryRequestJson> = /*@__PURE__*/
  messageDesc(file_dixit_v1_handler, 4);

/**
 * 生成故事响应
 *
 * @generated from message dixit.v1.GenStoryResponse
 */
export type GenStoryResponse = Message<"dixit.v1.GenStoryResponse"> & {
  /**
   * 故事列表
   *
   * @generated from field: repeated string stories = 1;
   */
  stories: string[];
};

/**
 * 生成故事响应
 *
 * @generated from message dixit.v1.GenStoryResponse
 */
export type GenStoryResponseJson = {
  /**
   * 故事列表
   *
   * @generated from field: repeated string stories = 1;
   */
  stories?: string[];
};

/**
 * Describes the message dixit.v1.GenStoryResponse.
 * Use `create(GenStoryResponseSchema)` to create a new message.
 */
export const GenStoryResponseSchema: GenMessage<GenStoryResponse, GenStoryResponseJson> = /*@__PURE__*/
  messageDesc(file_dixit_v1_handler, 5);

/**
 * 其他玩家选牌的请求
 *
 * @generated from message dixit.v1.SelectCardRequest
 */
export type SelectCardRequest = Message<"dixit.v1.SelectCardRequest"> & {
  /**
   * @generated from field: string hand_card = 1;
   */
  handCard: string;
};

/**
 * 其他玩家选牌的请求
 *
 * @generated from message dixit.v1.SelectCardRequest
 */
export type SelectCardRequestJson = {
  /**
   * @generated from field: string hand_card = 1;
   */
  handCard?: string;
};

/**
 * Describes the message dixit.v1.SelectCardRequest.
 * Use `create(SelectCardRequestSchema)` to create a new message.
 */
export const SelectCardRequestSchema: GenMessage<SelectCardRequest, SelectCardRequestJson> = /*@__PURE__*/
  messageDesc(file_dixit_v1_handler, 6);

/**
 * 其他玩家选牌的响应
 *
 * @generated from message dixit.v1.SelectCardResponse
 */
export type SelectCardResponse = Message<"dixit.v1.SelectCardResponse"> & {
};

/**
 * 其他玩家选牌的响应
 *
 * @generated from message dixit.v1.SelectCardResponse
 */
export type SelectCardResponseJson = {
};

/**
 * Describes the message dixit.v1.SelectCardResponse.
 * Use `create(SelectCardResponseSchema)` to create a new message.
 */
export const SelectCardResponseSchema: GenMessage<SelectCardResponse, SelectCardResponseJson> = /*@__PURE__*/
  messageDesc(file_dixit_v1_handler, 7);

/**
 * 其他玩家投票的请求
 *
 * @generated from message dixit.v1.VoteRequest
 */
export type VoteRequest = Message<"dixit.v1.VoteRequest"> & {
  /**
   * 目标玩家的索引
   *
   * @generated from field: string hand_card = 1;
   */
  handCard: string;
};

/**
 * 其他玩家投票的请求
 *
 * @generated from message dixit.v1.VoteRequest
 */
export type VoteRequestJson = {
  /**
   * 目标玩家的索引
   *
   * @generated from field: string hand_card = 1;
   */
  handCard?: string;
};

/**
 * Describes the message dixit.v1.VoteRequest.
 * Use `create(VoteRequestSchema)` to create a new message.
 */
export const VoteRequestSchema: GenMessage<VoteRequest, VoteRequestJson> = /*@__PURE__*/
  messageDesc(file_dixit_v1_handler, 8);

/**
 * 其他玩家投票的请求
 *
 * @generated from message dixit.v1.VoteResponse
 */
export type VoteResponse = Message<"dixit.v1.VoteResponse"> & {
};

/**
 * 其他玩家投票的请求
 *
 * @generated from message dixit.v1.VoteResponse
 */
export type VoteResponseJson = {
};

/**
 * Describes the message dixit.v1.VoteResponse.
 * Use `create(VoteResponseSchema)` to create a new message.
 */
export const VoteResponseSchema: GenMessage<VoteResponse, VoteResponseJson> = /*@__PURE__*/
  messageDesc(file_dixit_v1_handler, 9);

/**
 * 打破【托管】时的请求
 *
 * @generated from message dixit.v1.BreakHostingRequest
 */
export type BreakHostingRequest = Message<"dixit.v1.BreakHostingRequest"> & {
};

/**
 * 打破【托管】时的请求
 *
 * @generated from message dixit.v1.BreakHostingRequest
 */
export type BreakHostingRequestJson = {
};

/**
 * Describes the message dixit.v1.BreakHostingRequest.
 * Use `create(BreakHostingRequestSchema)` to create a new message.
 */
export const BreakHostingRequestSchema: GenMessage<BreakHostingRequest, BreakHostingRequestJson> = /*@__PURE__*/
  messageDesc(file_dixit_v1_handler, 10);

/**
 * @generated from message dixit.v1.BreakHostingResponse
 */
export type BreakHostingResponse = Message<"dixit.v1.BreakHostingResponse"> & {
  /**
   * @generated from field: bool success = 1;
   */
  success: boolean;

  /**
   * 玩家索引
   *
   * @generated from field: int32 player_index = 2;
   */
  playerIndex: number;
};

/**
 * @generated from message dixit.v1.BreakHostingResponse
 */
export type BreakHostingResponseJson = {
  /**
   * @generated from field: bool success = 1;
   */
  success?: boolean;

  /**
   * 玩家索引
   *
   * @generated from field: int32 player_index = 2;
   */
  playerIndex?: number;
};

/**
 * Describes the message dixit.v1.BreakHostingResponse.
 * Use `create(BreakHostingResponseSchema)` to create a new message.
 */
export const BreakHostingResponseSchema: GenMessage<BreakHostingResponse, BreakHostingResponseJson> = /*@__PURE__*/
  messageDesc(file_dixit_v1_handler, 11);

/**
 * ScoreboardRequest 计分板请求
 *
 * @generated from message dixit.v1.ScoreboardRequest
 */
export type ScoreboardRequest = Message<"dixit.v1.ScoreboardRequest"> & {
};

/**
 * ScoreboardRequest 计分板请求
 *
 * @generated from message dixit.v1.ScoreboardRequest
 */
export type ScoreboardRequestJson = {
};

/**
 * Describes the message dixit.v1.ScoreboardRequest.
 * Use `create(ScoreboardRequestSchema)` to create a new message.
 */
export const ScoreboardRequestSchema: GenMessage<ScoreboardRequest, ScoreboardRequestJson> = /*@__PURE__*/
  messageDesc(file_dixit_v1_handler, 12);

/**
 * ScoreboardResponse 计分板响应
 *
 * @generated from message dixit.v1.ScoreboardResponse
 */
export type ScoreboardResponse = Message<"dixit.v1.ScoreboardResponse"> & {
  /**
   * 轮次列表
   *
   * @generated from field: repeated dixit.v1.ScoreboardResponse.Round rounds = 1;
   */
  rounds: ScoreboardResponse_Round[];

  /**
   * 总计
   *
   * @generated from field: repeated dixit.v1.ScoreboardResponse.Total totals = 2;
   */
  totals: ScoreboardResponse_Total[];
};

/**
 * ScoreboardResponse 计分板响应
 *
 * @generated from message dixit.v1.ScoreboardResponse
 */
export type ScoreboardResponseJson = {
  /**
   * 轮次列表
   *
   * @generated from field: repeated dixit.v1.ScoreboardResponse.Round rounds = 1;
   */
  rounds?: ScoreboardResponse_RoundJson[];

  /**
   * 总计
   *
   * @generated from field: repeated dixit.v1.ScoreboardResponse.Total totals = 2;
   */
  totals?: ScoreboardResponse_TotalJson[];
};

/**
 * Describes the message dixit.v1.ScoreboardResponse.
 * Use `create(ScoreboardResponseSchema)` to create a new message.
 */
export const ScoreboardResponseSchema: GenMessage<ScoreboardResponse, ScoreboardResponseJson> = /*@__PURE__*/
  messageDesc(file_dixit_v1_handler, 13);

/**
 * @generated from message dixit.v1.ScoreboardResponse.Round
 */
export type ScoreboardResponse_Round = Message<"dixit.v1.ScoreboardResponse.Round"> & {
  /**
   * 每轮玩家列表
   *
   * @generated from field: repeated dixit.v1.ScoreboardResponse.PlayerScore player_scores = 1;
   */
  playerScores: ScoreboardResponse_PlayerScore[];
};

/**
 * @generated from message dixit.v1.ScoreboardResponse.Round
 */
export type ScoreboardResponse_RoundJson = {
  /**
   * 每轮玩家列表
   *
   * @generated from field: repeated dixit.v1.ScoreboardResponse.PlayerScore player_scores = 1;
   */
  playerScores?: ScoreboardResponse_PlayerScoreJson[];
};

/**
 * Describes the message dixit.v1.ScoreboardResponse.Round.
 * Use `create(ScoreboardResponse_RoundSchema)` to create a new message.
 */
export const ScoreboardResponse_RoundSchema: GenMessage<ScoreboardResponse_Round, ScoreboardResponse_RoundJson> = /*@__PURE__*/
  messageDesc(file_dixit_v1_handler, 13, 0);

/**
 * @generated from message dixit.v1.ScoreboardResponse.PlayerScore
 */
export type ScoreboardResponse_PlayerScore = Message<"dixit.v1.ScoreboardResponse.PlayerScore"> & {
  /**
   * 玩家索引
   *
   * @generated from field: int32 player_index = 1;
   */
  playerIndex: number;

  /**
   * 说书人票型的得分数
   *
   * @generated from field: int32 score1 = 2;
   */
  score1: number;

  /**
   * 非说书人票型的得分数
   *
   * @generated from field: int32 score2 = 3;
   */
  score2: number;

  /**
   * 是否弃权
   *
   * @generated from field: bool waiver = 4;
   */
  waiver: boolean;
};

/**
 * @generated from message dixit.v1.ScoreboardResponse.PlayerScore
 */
export type ScoreboardResponse_PlayerScoreJson = {
  /**
   * 玩家索引
   *
   * @generated from field: int32 player_index = 1;
   */
  playerIndex?: number;

  /**
   * 说书人票型的得分数
   *
   * @generated from field: int32 score1 = 2;
   */
  score1?: number;

  /**
   * 非说书人票型的得分数
   *
   * @generated from field: int32 score2 = 3;
   */
  score2?: number;

  /**
   * 是否弃权
   *
   * @generated from field: bool waiver = 4;
   */
  waiver?: boolean;
};

/**
 * Describes the message dixit.v1.ScoreboardResponse.PlayerScore.
 * Use `create(ScoreboardResponse_PlayerScoreSchema)` to create a new message.
 */
export const ScoreboardResponse_PlayerScoreSchema: GenMessage<ScoreboardResponse_PlayerScore, ScoreboardResponse_PlayerScoreJson> = /*@__PURE__*/
  messageDesc(file_dixit_v1_handler, 13, 1);

/**
 * @generated from message dixit.v1.ScoreboardResponse.Total
 */
export type ScoreboardResponse_Total = Message<"dixit.v1.ScoreboardResponse.Total"> & {
  /**
   * 玩家索引
   *
   * @generated from field: int32 player_index = 1;
   */
  playerIndex: number;

  /**
   * 总得分
   *
   * @generated from field: int32 score = 2;
   */
  score: number;
};

/**
 * @generated from message dixit.v1.ScoreboardResponse.Total
 */
export type ScoreboardResponse_TotalJson = {
  /**
   * 玩家索引
   *
   * @generated from field: int32 player_index = 1;
   */
  playerIndex?: number;

  /**
   * 总得分
   *
   * @generated from field: int32 score = 2;
   */
  score?: number;
};

/**
 * Describes the message dixit.v1.ScoreboardResponse.Total.
 * Use `create(ScoreboardResponse_TotalSchema)` to create a new message.
 */
export const ScoreboardResponse_TotalSchema: GenMessage<ScoreboardResponse_Total, ScoreboardResponse_TotalJson> = /*@__PURE__*/
  messageDesc(file_dixit_v1_handler, 13, 2);

/**
 * StorybookRequest 故事书请求
 *
 * @generated from message dixit.v1.StorybookRequest
 */
export type StorybookRequest = Message<"dixit.v1.StorybookRequest"> & {
};

/**
 * StorybookRequest 故事书请求
 *
 * @generated from message dixit.v1.StorybookRequest
 */
export type StorybookRequestJson = {
};

/**
 * Describes the message dixit.v1.StorybookRequest.
 * Use `create(StorybookRequestSchema)` to create a new message.
 */
export const StorybookRequestSchema: GenMessage<StorybookRequest, StorybookRequestJson> = /*@__PURE__*/
  messageDesc(file_dixit_v1_handler, 14);

/**
 * StorybookResponse 故事书响应
 *
 * @generated from message dixit.v1.StorybookResponse
 */
export type StorybookResponse = Message<"dixit.v1.StorybookResponse"> & {
  /**
   * 回合列表
   *
   * @generated from field: repeated dixit.v1.StorybookResponse.Round rounds = 1;
   */
  rounds: StorybookResponse_Round[];
};

/**
 * StorybookResponse 故事书响应
 *
 * @generated from message dixit.v1.StorybookResponse
 */
export type StorybookResponseJson = {
  /**
   * 回合列表
   *
   * @generated from field: repeated dixit.v1.StorybookResponse.Round rounds = 1;
   */
  rounds?: StorybookResponse_RoundJson[];
};

/**
 * Describes the message dixit.v1.StorybookResponse.
 * Use `create(StorybookResponseSchema)` to create a new message.
 */
export const StorybookResponseSchema: GenMessage<StorybookResponse, StorybookResponseJson> = /*@__PURE__*/
  messageDesc(file_dixit_v1_handler, 15);

/**
 * @generated from message dixit.v1.StorybookResponse.Round
 */
export type StorybookResponse_Round = Message<"dixit.v1.StorybookResponse.Round"> & {
  /**
   * 当前回合数
   *
   * @generated from field: int32 round_count = 1;
   */
  roundCount: number;

  /**
   * 说书人索引
   *
   * @generated from field: int32 player_index = 2;
   */
  playerIndex: number;

  /**
   * 本轮故事
   *
   * @generated from field: string story = 3;
   */
  story: string;

  /**
   * 本轮故事来源，1|用户输入，2|自动生成
   *
   * @generated from field: int32 story_from = 5;
   */
  storyFrom: number;

  /**
   * 本轮出牌列表
   *
   * @generated from field: repeated dixit.v1.StorybookResponse.Round.SelectedCard selected_cards = 4;
   */
  selectedCards: StorybookResponse_Round_SelectedCard[];
};

/**
 * @generated from message dixit.v1.StorybookResponse.Round
 */
export type StorybookResponse_RoundJson = {
  /**
   * 当前回合数
   *
   * @generated from field: int32 round_count = 1;
   */
  roundCount?: number;

  /**
   * 说书人索引
   *
   * @generated from field: int32 player_index = 2;
   */
  playerIndex?: number;

  /**
   * 本轮故事
   *
   * @generated from field: string story = 3;
   */
  story?: string;

  /**
   * 本轮故事来源，1|用户输入，2|自动生成
   *
   * @generated from field: int32 story_from = 5;
   */
  storyFrom?: number;

  /**
   * 本轮出牌列表
   *
   * @generated from field: repeated dixit.v1.StorybookResponse.Round.SelectedCard selected_cards = 4;
   */
  selectedCards?: StorybookResponse_Round_SelectedCardJson[];
};

/**
 * Describes the message dixit.v1.StorybookResponse.Round.
 * Use `create(StorybookResponse_RoundSchema)` to create a new message.
 */
export const StorybookResponse_RoundSchema: GenMessage<StorybookResponse_Round, StorybookResponse_RoundJson> = /*@__PURE__*/
  messageDesc(file_dixit_v1_handler, 15, 0);

/**
 * 选择的牌
 *
 * @generated from message dixit.v1.StorybookResponse.Round.SelectedCard
 */
export type StorybookResponse_Round_SelectedCard = Message<"dixit.v1.StorybookResponse.Round.SelectedCard"> & {
  /**
   * 选择的牌
   *
   * @generated from field: string card = 1;
   */
  card: string;

  /**
   * 选择该牌的玩家索引
   *
   * @generated from field: int32 selected_player_index = 2;
   */
  selectedPlayerIndex: number;

  /**
   * 投票的玩家索引
   *
   * @generated from field: repeated int32 voted_player_indices = 3;
   */
  votedPlayerIndices: number[];
};

/**
 * 选择的牌
 *
 * @generated from message dixit.v1.StorybookResponse.Round.SelectedCard
 */
export type StorybookResponse_Round_SelectedCardJson = {
  /**
   * 选择的牌
   *
   * @generated from field: string card = 1;
   */
  card?: string;

  /**
   * 选择该牌的玩家索引
   *
   * @generated from field: int32 selected_player_index = 2;
   */
  selectedPlayerIndex?: number;

  /**
   * 投票的玩家索引
   *
   * @generated from field: repeated int32 voted_player_indices = 3;
   */
  votedPlayerIndices?: number[];
};

/**
 * Describes the message dixit.v1.StorybookResponse.Round.SelectedCard.
 * Use `create(StorybookResponse_Round_SelectedCardSchema)` to create a new message.
 */
export const StorybookResponse_Round_SelectedCardSchema: GenMessage<StorybookResponse_Round_SelectedCard, StorybookResponse_Round_SelectedCardJson> = /*@__PURE__*/
  messageDesc(file_dixit_v1_handler, 15, 0, 0);

/**
 * @generated from message dixit.v1.CardInfoListRequest
 */
export type CardInfoListRequest = Message<"dixit.v1.CardInfoListRequest"> & {
};

/**
 * @generated from message dixit.v1.CardInfoListRequest
 */
export type CardInfoListRequestJson = {
};

/**
 * Describes the message dixit.v1.CardInfoListRequest.
 * Use `create(CardInfoListRequestSchema)` to create a new message.
 */
export const CardInfoListRequestSchema: GenMessage<CardInfoListRequest, CardInfoListRequestJson> = /*@__PURE__*/
  messageDesc(file_dixit_v1_handler, 16);

/**
 * @generated from message dixit.v1.CardInfoListResponse
 */
export type CardInfoListResponse = Message<"dixit.v1.CardInfoListResponse"> & {
  /**
   * 卡牌信息列表
   *
   * @generated from field: repeated dixit.v1.CardInfo card_info_list = 1;
   */
  cardInfoList: CardInfo[];

  /**
   * 主题背景
   *
   * @generated from field: string theme_background = 2;
   */
  themeBackground: string;
};

/**
 * @generated from message dixit.v1.CardInfoListResponse
 */
export type CardInfoListResponseJson = {
  /**
   * 卡牌信息列表
   *
   * @generated from field: repeated dixit.v1.CardInfo card_info_list = 1;
   */
  cardInfoList?: CardInfoJson[];

  /**
   * 主题背景
   *
   * @generated from field: string theme_background = 2;
   */
  themeBackground?: string;
};

/**
 * Describes the message dixit.v1.CardInfoListResponse.
 * Use `create(CardInfoListResponseSchema)` to create a new message.
 */
export const CardInfoListResponseSchema: GenMessage<CardInfoListResponse, CardInfoListResponseJson> = /*@__PURE__*/
  messageDesc(file_dixit_v1_handler, 17);

/**
 * 由任何玩家发起，广播到所有玩家
 *
 * @generated from message dixit.v1.DataBroadcastRequest
 */
export type DataBroadcastRequest = Message<"dixit.v1.DataBroadcastRequest"> & {
  /**
   * 客户端广播数据 json格式
   *
   * @generated from field: string data = 1;
   */
  data: string;
};

/**
 * 由任何玩家发起，广播到所有玩家
 *
 * @generated from message dixit.v1.DataBroadcastRequest
 */
export type DataBroadcastRequestJson = {
  /**
   * 客户端广播数据 json格式
   *
   * @generated from field: string data = 1;
   */
  data?: string;
};

/**
 * Describes the message dixit.v1.DataBroadcastRequest.
 * Use `create(DataBroadcastRequestSchema)` to create a new message.
 */
export const DataBroadcastRequestSchema: GenMessage<DataBroadcastRequest, DataBroadcastRequestJson> = /*@__PURE__*/
  messageDesc(file_dixit_v1_handler, 18);

/**
 * @generated from message dixit.v1.DataBroadcastResponse
 */
export type DataBroadcastResponse = Message<"dixit.v1.DataBroadcastResponse"> & {
  /**
   * @generated from field: bool success = 1;
   */
  success: boolean;
};

/**
 * @generated from message dixit.v1.DataBroadcastResponse
 */
export type DataBroadcastResponseJson = {
  /**
   * @generated from field: bool success = 1;
   */
  success?: boolean;
};

/**
 * Describes the message dixit.v1.DataBroadcastResponse.
 * Use `create(DataBroadcastResponseSchema)` to create a new message.
 */
export const DataBroadcastResponseSchema: GenMessage<DataBroadcastResponse, DataBroadcastResponseJson> = /*@__PURE__*/
  messageDesc(file_dixit_v1_handler, 19);

/**
 * 路由，给客户端调用，也就是下面pirateService的路由
 * buf:lint:ignore ENUM_VALUE_UPPER_SNAKE_CASE
 * buf:lint:ignore ENUM_VALUE_PREFIX
 *
 * @generated from enum dixit.v1.Route
 */
export enum Route {
  /**
   * @generated from enum value: ROUTE_UNSPECIFIED = 0;
   */
  ROUTE_UNSPECIFIED = 0,

  /**
   * 出牌
   *
   * @generated from enum value: PostCard = 1;
   */
  PostCard = 1,

  /**
   * 讲故事
   *
   * @generated from enum value: PostStory = 2;
   */
  PostStory = 2,

  /**
   * 生成故事
   *
   * @generated from enum value: GenStory = 9;
   */
  GenStory = 9,

  /**
   * 其他玩家提交选牌
   *
   * @generated from enum value: SelectCard = 3;
   */
  SelectCard = 3,

  /**
   * 玩家投票
   *
   * @generated from enum value: Vote = 4;
   */
  Vote = 4,

  /**
   * 打破【托管】
   *
   * @generated from enum value: BreakHosting = 5;
   */
  BreakHosting = 5,

  /**
   * 计分板
   *
   * @generated from enum value: Scoreboard = 6;
   */
  Scoreboard = 6,

  /**
   * 当前牌局卡牌列表
   *
   * @generated from enum value: CardInfoList = 7;
   */
  CardInfoList = 7,

  /**
   * 故事书
   *
   * @generated from enum value: Storybook = 8;
   */
  Storybook = 8,

  /**
   * ---- 其它 ----
   * 由任何玩家发起，广播到所有玩家
   *
   * @generated from enum value: DataBroadcast = 11;
   */
  DataBroadcast = 11,

  /**
   * 观看
   *
   * @generated from enum value: Watch = 12;
   */
  Watch = 12,
}

/**
 * 路由，给客户端调用，也就是下面pirateService的路由
 * buf:lint:ignore ENUM_VALUE_UPPER_SNAKE_CASE
 * buf:lint:ignore ENUM_VALUE_PREFIX
 *
 * @generated from enum dixit.v1.Route
 */
export type RouteJson = "ROUTE_UNSPECIFIED" | "PostCard" | "PostStory" | "GenStory" | "SelectCard" | "Vote" | "BreakHosting" | "Scoreboard" | "CardInfoList" | "Storybook" | "DataBroadcast" | "Watch";

/**
 * Describes the enum dixit.v1.Route.
 */
export const RouteSchema: GenEnum<Route, RouteJson> = /*@__PURE__*/
  enumDesc(file_dixit_v1_handler, 0);

/**
 * rpc主路由和方法
 *
 * @generated from service dixit.v1.PirateService
 */
export const PirateService: GenService<{
  /**
   * 说书人提交选牌
   *
   * @generated from rpc dixit.v1.PirateService.PostCard
   */
  postCard: {
    methodKind: "unary";
    input: typeof PostCardRequestSchema;
    output: typeof PostCardResponseSchema;
  },
  /**
   * 说书人提交故事
   *
   * @generated from rpc dixit.v1.PirateService.PostStory
   */
  postStory: {
    methodKind: "unary";
    input: typeof PostStoryRequestSchema;
    output: typeof PostStoryResponseSchema;
  },
  /**
   * 生成故事
   *
   * @generated from rpc dixit.v1.PirateService.GenStory
   */
  genStory: {
    methodKind: "unary";
    input: typeof GenStoryRequestSchema;
    output: typeof GenStoryResponseSchema;
  },
  /**
   * 其他玩家提交选牌
   *
   * @generated from rpc dixit.v1.PirateService.SelectCard
   */
  selectCard: {
    methodKind: "unary";
    input: typeof SelectCardRequestSchema;
    output: typeof SelectCardResponseSchema;
  },
  /**
   * 玩家投票
   *
   * @generated from rpc dixit.v1.PirateService.Vote
   */
  vote: {
    methodKind: "unary";
    input: typeof VoteRequestSchema;
    output: typeof VoteResponseSchema;
  },
  /**
   * 打破【托管】
   *
   * @generated from rpc dixit.v1.PirateService.BreakHosting
   */
  breakHosting: {
    methodKind: "unary";
    input: typeof BreakHostingRequestSchema;
    output: typeof BreakHostingResponseSchema;
  },
  /**
   * 计分板
   *
   * @generated from rpc dixit.v1.PirateService.Scoreboard
   */
  scoreboard: {
    methodKind: "unary";
    input: typeof ScoreboardRequestSchema;
    output: typeof ScoreboardResponseSchema;
  },
  /**
   * 故事书
   *
   * @generated from rpc dixit.v1.PirateService.Storybook
   */
  storybook: {
    methodKind: "unary";
    input: typeof StorybookRequestSchema;
    output: typeof StorybookResponseSchema;
  },
  /**
   * 当前牌局卡牌列表
   *
   * @generated from rpc dixit.v1.PirateService.CardInfoList
   */
  cardInfoList: {
    methodKind: "unary";
    input: typeof CardInfoListRequestSchema;
    output: typeof CardInfoListResponseSchema;
  },
  /**
   * 由任何玩家发起，广播到所有玩家
   *
   * @generated from rpc dixit.v1.PirateService.DataBroadcast
   */
  dataBroadcast: {
    methodKind: "unary";
    input: typeof DataBroadcastRequestSchema;
    output: typeof DataBroadcastResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_dixit_v1_handler, 0);

