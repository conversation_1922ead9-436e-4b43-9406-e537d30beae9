/**
 * @describe Dixit游戏音效常量
 * @date 2025-4-27
 */

export enum AudioEffectConstant {
    /**背景音乐 */
    BGM = 'audio/dixit_bgm2',
    // /**背景音乐2 */
    // BGM2 = 'audio/dixit_bgm2',
    /**游戏开局前准备 - 游戏开局 */
    GAME_START = 'audio/dixit_gamestart',
    /**播放确定主题的动效时 */
    CHOOSE_THEME = 'audio/dixit_choosetheme',
    /**阶段流转音 - 获得初始手牌 */
    GET_INITIAL_CARD = 'audio/dixit_newcard',
    /**回合开始 - 播放翻书动效 */
    OPEN_BOOK = 'audio/dixit_openbook',
    /**阶段流转 - 在播放转场音效之前播放 */
    TRANS_STAGE = 'audio/dixit_transstage',
    /**倒计时提示音 - 任意环节倒计时5s时 */
    COUNTDOWN = 'audio/dixit_countdown',
    /**有效点击 - 包括选牌、确定等 */
    CLICK = 'audio/dixit_click',
    /**点击收起手牌 */
    RETRACT = 'audio/dixit_retract',
    /**点击展开手牌 */
    EXPAND = 'audio/dixit_expand',
    /**功能按键 - 打开计分板 */
    SCOREBOARD = 'audio/dixit_scoreboard',
    /**功能按键 - 打开故事书 */
    STORYBOOK = 'audio/dixit_openbook', // 打开故事书先用 openbook 的音效
    /**唱票环节 - 投票正确/故事人得分类型 */
    SUCCESS = 'audio/dixit_success',
    /**唱票环节 - 投票错误/故事人不得分类型 */
    FAIL = 'audio/dixit_fail',
    /**加分 */
    SCORING = 'audio/dixit_scoring',
    /**补卡 */
    NEW_CARD = 'audio/dixit_newcard',
    /**静音 */
    SILENT = 'audio/silent',
}
