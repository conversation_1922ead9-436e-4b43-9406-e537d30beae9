import { BaseTracking } from "../BaseTracking"


/**游戏加载属性 */
export enum TrackingLoadEvent {
    /**游戏加载进度：0%，100%上报 */
    GAME_LOADING = 'game_loading'
}


export class TrackingLoading extends BaseTracking {
    loadingProgress = (key: number) => {
        this.tracking.upload({
            event_type: TrackingLoadEvent.GAME_LOADING,
            value: { "progress": `${key * 100}%` }
        })
    }
}

