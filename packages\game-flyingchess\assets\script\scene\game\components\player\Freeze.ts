import {
    _decorator,
    Component,
    instantiate,
    isValid,
    Label,
    log,
    Node,
    Prefab,
    Animation,
    Skeleton,
    sp,
} from 'cc'
import { BaseComponent } from '@/core/manager/gui/base/BaseComponent'
import { AudioEffectConstant } from '@/core/business/constant'
import { cat } from '@/core/manager'
import { create } from '@bufbuild/protobuf'
import store from '@/core/business/store'
import { Player, PlayerSchema } from '@/pb-generate/server/pirate/v1/player_pb'

import { PlayerItem } from './PlayerItem'

import { State as DeskState } from '@/pb-generate/server/pirate/v1/state_pb'

const { ccclass, property } = _decorator

@ccclass('Freeze')
export class Freeze extends BaseComponent {
    @property({ type: Node, tooltip: '冻结特效动画节点' })
    spine_slot: Node

    @property({ type: sp.Skeleton })
    spine_freeze: sp.Skeleton = null!

    override props = {
        player: create(PlayerSchema),
    }

    protected override initUI(): void {
        // this.spine_freeze.node.active = false
    }

    protected override onLoad(): void {
        this.initFreezeStatus()
    }

    protected override onAutoObserver(): void {
        this.addReaction(
            () => this.props.player.frozenCount,
            () => {
                this.updateFreezeStatus()
            }
        )
    }

    override onDestroy(): void {}

    initFreezeStatus() {
        if (this.props.player.frozenCount > 0) {
            this.spine_freeze.node.active = true
            this.spine_freeze.setAnimation(0, 'gelivation_idle', false)
        } else {
            this.spine_freeze.node.active = false
        }
    }

    /**更新冻结状态 */
    updateFreezeStatus() {
        if (this.props.player.frozenCount > 0) {
            this.spine_freeze.node.active = true
            this.spine_freeze.setAnimation(0, 'gelivation_admission', false)
        } else {
            cat.audio.playEffect(AudioEffectConstant.ICE_BROKEN)
            this.spine_freeze.setAnimation(0, 'gelivation_idle', false)
            this.scheduleOnce(() => {
                this.spine_freeze.node.active = false
            }, 2)
        }
    }

    hide() {
        this.node.active = false
    }
}
