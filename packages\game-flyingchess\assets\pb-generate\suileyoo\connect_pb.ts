// @generated by protoc-gen-es v2.2.5 with parameter "target=ts,import_extension=none,json_types=true"
// @generated from file connect.proto (package protos, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage } from "@bufbuild/protobuf/codegenv1";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv1";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file connect.proto.
 */
export const file_connect: GenFile = /*@__PURE__*/
  fileDesc("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");

/**
 * 无消息内容
 *
 * @generated from message protos.ConnectAuthAccessTokenRequest
 */
export type ConnectAuthAccessTokenRequest = Message<"protos.ConnectAuthAccessTokenRequest"> & {
};

/**
 * 无消息内容
 *
 * @generated from message protos.ConnectAuthAccessTokenRequest
 */
export type ConnectAuthAccessTokenRequestJson = {
};

/**
 * Describes the message protos.ConnectAuthAccessTokenRequest.
 * Use `create(ConnectAuthAccessTokenRequestSchema)` to create a new message.
 */
export const ConnectAuthAccessTokenRequestSchema: GenMessage<ConnectAuthAccessTokenRequest, ConnectAuthAccessTokenRequestJson> = /*@__PURE__*/
  messageDesc(file_connect, 0);

/**
 * @generated from message protos.ConnectAuthAccessTokenResponse
 */
export type ConnectAuthAccessTokenResponse = Message<"protos.ConnectAuthAccessTokenResponse"> & {
  /**
   * access_token
   *
   * @generated from field: string access_token = 1;
   */
  accessToken: string;

  /**
   * 过期时间
   *
   * @generated from field: uint64 expires_in = 2;
   */
  expiresIn: bigint;
};

/**
 * @generated from message protos.ConnectAuthAccessTokenResponse
 */
export type ConnectAuthAccessTokenResponseJson = {
  /**
   * access_token
   *
   * @generated from field: string access_token = 1;
   */
  accessToken?: string;

  /**
   * 过期时间
   *
   * @generated from field: uint64 expires_in = 2;
   */
  expiresIn?: string;
};

/**
 * Describes the message protos.ConnectAuthAccessTokenResponse.
 * Use `create(ConnectAuthAccessTokenResponseSchema)` to create a new message.
 */
export const ConnectAuthAccessTokenResponseSchema: GenMessage<ConnectAuthAccessTokenResponse, ConnectAuthAccessTokenResponseJson> = /*@__PURE__*/
  messageDesc(file_connect, 1);

/**
 * 获取连接必要参数的请求
 *
 * @generated from message protos.ConnectParamsRequest
 */
export type ConnectParamsRequest = Message<"protos.ConnectParamsRequest"> & {
  /**
   * 业务游戏ID
   *
   * @generated from field: uint64 game_id = 1;
   */
  gameId: bigint;

  /**
   * 是否使用代时券（移除）
   *
   * @generated from field: bool use_coupon = 2;
   */
  useCoupon: boolean;

  /**
   * 代时券ID（移除）
   *
   * @generated from field: uint64 coupon_id = 3;
   */
  couponId: bigint;

  /**
   * 渠道编码
   *
   * @generated from field: string source_code = 4;
   */
  sourceCode: string;

  /**
   * 切换当前游戏如有，如果有游戏正在进行，该值为true时，直接做切换游戏，否则返回另个一连接在进行
   *
   * @generated from field: bool replace = 5;
   */
  replace: boolean;

  /**
   * 是否使用速进（移除）
   *
   * @generated from field: bool fast_enter = 6;
   */
  fastEnter: boolean;

  /**
   * 客户端类型
   *
   * @generated from field: string client_type = 7;
   */
  clientType: string;
};

/**
 * 获取连接必要参数的请求
 *
 * @generated from message protos.ConnectParamsRequest
 */
export type ConnectParamsRequestJson = {
  /**
   * 业务游戏ID
   *
   * @generated from field: uint64 game_id = 1;
   */
  gameId?: string;

  /**
   * 是否使用代时券（移除）
   *
   * @generated from field: bool use_coupon = 2;
   */
  useCoupon?: boolean;

  /**
   * 代时券ID（移除）
   *
   * @generated from field: uint64 coupon_id = 3;
   */
  couponId?: string;

  /**
   * 渠道编码
   *
   * @generated from field: string source_code = 4;
   */
  sourceCode?: string;

  /**
   * 切换当前游戏如有，如果有游戏正在进行，该值为true时，直接做切换游戏，否则返回另个一连接在进行
   *
   * @generated from field: bool replace = 5;
   */
  replace?: boolean;

  /**
   * 是否使用速进（移除）
   *
   * @generated from field: bool fast_enter = 6;
   */
  fastEnter?: boolean;

  /**
   * 客户端类型
   *
   * @generated from field: string client_type = 7;
   */
  clientType?: string;
};

/**
 * Describes the message protos.ConnectParamsRequest.
 * Use `create(ConnectParamsRequestSchema)` to create a new message.
 */
export const ConnectParamsRequestSchema: GenMessage<ConnectParamsRequest, ConnectParamsRequestJson> = /*@__PURE__*/
  messageDesc(file_connect, 2);

/**
 * 获取连接必要参数的响应
 *
 * @generated from message protos.ConnectParamsResponse
 */
export type ConnectParamsResponse = Message<"protos.ConnectParamsResponse"> & {
  /**
   * 响应码
   *
   * @generated from field: protos.ConnectParamsResponse.Code code = 1;
   */
  code: ConnectParamsResponse_Code;

  /**
   * 响应提示消息
   *
   * @generated from field: string message = 2;
   */
  message: string;

  /**
   * 连接 client_id
   *
   * @generated from field: string client_id = 3;
   */
  clientId: string;

  /**
   * 连接优先级
   *
   * @generated from field: uint64 level = 4;
   */
  level: bigint;

  /**
   * 集成服务游戏ID
   *
   * @generated from field: uint64 game_id = 5;
   */
  gameId: bigint;

  /**
   * 连接类型
   *
   * @generated from field: protos.ConnectAction action = 6;
   */
  action: ConnectAction;

  /**
   * 游戏接入类型
   *
   * @generated from field: uint64 type = 7;
   */
  type: bigint;

  /**
   * 挂机超时时间（单位秒）
   *
   * @generated from field: uint32 op_timeout = 8;
   */
  opTimeout: number;

  /**
   * 客户端类型
   *
   * @generated from field: uint32 client_type = 9;
   */
  clientType: number;
};

/**
 * 获取连接必要参数的响应
 *
 * @generated from message protos.ConnectParamsResponse
 */
export type ConnectParamsResponseJson = {
  /**
   * 响应码
   *
   * @generated from field: protos.ConnectParamsResponse.Code code = 1;
   */
  code?: ConnectParamsResponse_CodeJson;

  /**
   * 响应提示消息
   *
   * @generated from field: string message = 2;
   */
  message?: string;

  /**
   * 连接 client_id
   *
   * @generated from field: string client_id = 3;
   */
  clientId?: string;

  /**
   * 连接优先级
   *
   * @generated from field: uint64 level = 4;
   */
  level?: string;

  /**
   * 集成服务游戏ID
   *
   * @generated from field: uint64 game_id = 5;
   */
  gameId?: string;

  /**
   * 连接类型
   *
   * @generated from field: protos.ConnectAction action = 6;
   */
  action?: ConnectActionJson;

  /**
   * 游戏接入类型
   *
   * @generated from field: uint64 type = 7;
   */
  type?: string;

  /**
   * 挂机超时时间（单位秒）
   *
   * @generated from field: uint32 op_timeout = 8;
   */
  opTimeout?: number;

  /**
   * 客户端类型
   *
   * @generated from field: uint32 client_type = 9;
   */
  clientType?: number;
};

/**
 * Describes the message protos.ConnectParamsResponse.
 * Use `create(ConnectParamsResponseSchema)` to create a new message.
 */
export const ConnectParamsResponseSchema: GenMessage<ConnectParamsResponse, ConnectParamsResponseJson> = /*@__PURE__*/
  messageDesc(file_connect, 3);

/**
 * 响应码类型
 *
 * @generated from enum protos.ConnectParamsResponse.Code
 */
export enum ConnectParamsResponse_Code {
  /**
   * @generated from enum value: OK = 0;
   */
  OK = 0,

  /**
   * 一般错误
   *
   * @generated from enum value: ERROR = 1;
   */
  ERROR = 1,

  /**
   * 另一个连接正在进行
   *
   * @generated from enum value: EXIST_ANOTHER_CONNECT = 2;
   */
  EXIST_ANOTHER_CONNECT = 2,

  /**
   * 另一个连接正在等待进入
   *
   * @generated from enum value: EXIST_ANOTHER_WAIT_CONNECT = 3;
   */
  EXIST_ANOTHER_WAIT_CONNECT = 3,

  /**
   * 免费游戏时长已用完
   *
   * @generated from enum value: FREE_TIME_USE_UP = 4;
   */
  FREE_TIME_USE_UP = 4,

  /**
   * 免费游戏个数已用完
   *
   * @generated from enum value: FREE_NUMBER_USE_UP = 5;
   */
  FREE_NUMBER_USE_UP = 5,

  /**
   * 余额不足
   *
   * @generated from enum value: USER_BALANCE_NOT_ENOUGH = 6;
   */
  USER_BALANCE_NOT_ENOUGH = 6,

  /**
   * 时长不足
   *
   * @generated from enum value: USER_PLAY_TIME_NOT_ENOUGH = 7;
   */
  USER_PLAY_TIME_NOT_ENOUGH = 7,

  /**
   * 需要绑定手机号
   *
   * @generated from enum value: USER_NOT_BIND_PHONE = 8;
   */
  USER_NOT_BIND_PHONE = 8,

  /**
   * 不允许跨端操作
   *
   * @generated from enum value: NOT_ALLOWED_CROSS_CLIENT = 9;
   */
  NOT_ALLOWED_CROSS_CLIENT = 9,
}

/**
 * 响应码类型
 *
 * @generated from enum protos.ConnectParamsResponse.Code
 */
export type ConnectParamsResponse_CodeJson = "OK" | "ERROR" | "EXIST_ANOTHER_CONNECT" | "EXIST_ANOTHER_WAIT_CONNECT" | "FREE_TIME_USE_UP" | "FREE_NUMBER_USE_UP" | "USER_BALANCE_NOT_ENOUGH" | "USER_PLAY_TIME_NOT_ENOUGH" | "USER_NOT_BIND_PHONE" | "NOT_ALLOWED_CROSS_CLIENT";

/**
 * Describes the enum protos.ConnectParamsResponse.Code.
 */
export const ConnectParamsResponse_CodeSchema: GenEnum<ConnectParamsResponse_Code, ConnectParamsResponse_CodeJson> = /*@__PURE__*/
  enumDesc(file_connect, 3, 0);

/**
 * @generated from message protos.ConnectStatusRequest
 */
export type ConnectStatusRequest = Message<"protos.ConnectStatusRequest"> & {
};

/**
 * @generated from message protos.ConnectStatusRequest
 */
export type ConnectStatusRequestJson = {
};

/**
 * Describes the message protos.ConnectStatusRequest.
 * Use `create(ConnectStatusRequestSchema)` to create a new message.
 */
export const ConnectStatusRequestSchema: GenMessage<ConnectStatusRequest, ConnectStatusRequestJson> = /*@__PURE__*/
  messageDesc(file_connect, 4);

/**
 * @generated from message protos.ConnectStatusResponse
 */
export type ConnectStatusResponse = Message<"protos.ConnectStatusResponse"> & {
  /**
   * 状态
   *
   * @generated from field: protos.ConnectStatus status = 1;
   */
  status: ConnectStatus;

  /**
   * 类型
   *
   * @generated from field: uint64 type = 2;
   */
  type: bigint;

  /**
   * 在线时长
   *
   * @generated from field: uint64 online_seconds = 3;
   */
  onlineSeconds: bigint;

  /**
   * 游戏ID
   *
   * @generated from field: uint64 game_id = 4;
   */
  gameId: bigint;

  /**
   * 游戏logo
   *
   * @generated from field: string game_logo = 5;
   */
  gameLogo: string;

  /**
   * 游戏名称
   *
   * @generated from field: string game_name = 6;
   */
  gameName: string;

  /**
   * 协议
   *
   * @generated from field: string protocol = 7;
   */
  protocol: string;

  /**
   * 排队人数
   *
   * @generated from field: uint64 index = 8;
   */
  index: bigint;

  /**
   * 锁定释放剩余时间，单位：秒
   *
   * @generated from field: int64 unlock_in = 9;
   */
  unlockIn: bigint;

  /**
   * 节点ID
   *
   * @generated from field: uint64 node_id = 10;
   */
  nodeId: bigint;

  /**
   * 是否使用速进加速
   *
   * @generated from field: bool fast_enter = 11;
   */
  fastEnter: boolean;

  /**
   * 客户端类型
   *
   * @generated from field: string client_type = 12;
   */
  clientType: string;
};

/**
 * @generated from message protos.ConnectStatusResponse
 */
export type ConnectStatusResponseJson = {
  /**
   * 状态
   *
   * @generated from field: protos.ConnectStatus status = 1;
   */
  status?: ConnectStatusJson;

  /**
   * 类型
   *
   * @generated from field: uint64 type = 2;
   */
  type?: string;

  /**
   * 在线时长
   *
   * @generated from field: uint64 online_seconds = 3;
   */
  onlineSeconds?: string;

  /**
   * 游戏ID
   *
   * @generated from field: uint64 game_id = 4;
   */
  gameId?: string;

  /**
   * 游戏logo
   *
   * @generated from field: string game_logo = 5;
   */
  gameLogo?: string;

  /**
   * 游戏名称
   *
   * @generated from field: string game_name = 6;
   */
  gameName?: string;

  /**
   * 协议
   *
   * @generated from field: string protocol = 7;
   */
  protocol?: string;

  /**
   * 排队人数
   *
   * @generated from field: uint64 index = 8;
   */
  index?: string;

  /**
   * 锁定释放剩余时间，单位：秒
   *
   * @generated from field: int64 unlock_in = 9;
   */
  unlockIn?: string;

  /**
   * 节点ID
   *
   * @generated from field: uint64 node_id = 10;
   */
  nodeId?: string;

  /**
   * 是否使用速进加速
   *
   * @generated from field: bool fast_enter = 11;
   */
  fastEnter?: boolean;

  /**
   * 客户端类型
   *
   * @generated from field: string client_type = 12;
   */
  clientType?: string;
};

/**
 * Describes the message protos.ConnectStatusResponse.
 * Use `create(ConnectStatusResponseSchema)` to create a new message.
 */
export const ConnectStatusResponseSchema: GenMessage<ConnectStatusResponse, ConnectStatusResponseJson> = /*@__PURE__*/
  messageDesc(file_connect, 5);

/**
 * @generated from message protos.ConnectOfflineNotification
 */
export type ConnectOfflineNotification = Message<"protos.ConnectOfflineNotification"> & {
  /**
   * @generated from field: protos.ConnectOfflineType offline_type = 1;
   */
  offlineType: ConnectOfflineType;
};

/**
 * @generated from message protos.ConnectOfflineNotification
 */
export type ConnectOfflineNotificationJson = {
  /**
   * @generated from field: protos.ConnectOfflineType offline_type = 1;
   */
  offlineType?: ConnectOfflineTypeJson;
};

/**
 * Describes the message protos.ConnectOfflineNotification.
 * Use `create(ConnectOfflineNotificationSchema)` to create a new message.
 */
export const ConnectOfflineNotificationSchema: GenMessage<ConnectOfflineNotification, ConnectOfflineNotificationJson> = /*@__PURE__*/
  messageDesc(file_connect, 6);

/**
 * @generated from message protos.ConnectOnlineNotification
 */
export type ConnectOnlineNotification = Message<"protos.ConnectOnlineNotification"> & {
};

/**
 * @generated from message protos.ConnectOnlineNotification
 */
export type ConnectOnlineNotificationJson = {
};

/**
 * Describes the message protos.ConnectOnlineNotification.
 * Use `create(ConnectOnlineNotificationSchema)` to create a new message.
 */
export const ConnectOnlineNotificationSchema: GenMessage<ConnectOnlineNotification, ConnectOnlineNotificationJson> = /*@__PURE__*/
  messageDesc(file_connect, 7);

/**
 * @generated from message protos.ConnectPendingNotification
 */
export type ConnectPendingNotification = Message<"protos.ConnectPendingNotification"> & {
};

/**
 * @generated from message protos.ConnectPendingNotification
 */
export type ConnectPendingNotificationJson = {
};

/**
 * Describes the message protos.ConnectPendingNotification.
 * Use `create(ConnectPendingNotificationSchema)` to create a new message.
 */
export const ConnectPendingNotificationSchema: GenMessage<ConnectPendingNotification, ConnectPendingNotificationJson> = /*@__PURE__*/
  messageDesc(file_connect, 8);

/**
 * 连接类型
 *
 * @generated from enum protos.ConnectAction
 */
export enum ConnectAction {
  /**
   * 新连接
   *
   * @generated from enum value: NEW = 0;
   */
  NEW = 0,

  /**
   * 重连
   *
   * @generated from enum value: CONTINUE = 1;
   */
  CONTINUE = 1,
}

/**
 * 连接类型
 *
 * @generated from enum protos.ConnectAction
 */
export type ConnectActionJson = "NEW" | "CONTINUE";

/**
 * Describes the enum protos.ConnectAction.
 */
export const ConnectActionSchema: GenEnum<ConnectAction, ConnectActionJson> = /*@__PURE__*/
  enumDesc(file_connect, 0);

/**
 * @generated from enum protos.ConnectStatus
 */
export enum ConnectStatus {
  /**
   * 未连接
   *
   * @generated from enum value: UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * 排队中
   *
   * @generated from enum value: PENDING = 1;
   */
  PENDING = 1,

  /**
   * 待连接
   *
   * @generated from enum value: WAITING_CONNECT = 2;
   */
  WAITING_CONNECT = 2,

  /**
   * 在线
   *
   * @generated from enum value: ONLINE = 3;
   */
  ONLINE = 3,
}

/**
 * @generated from enum protos.ConnectStatus
 */
export type ConnectStatusJson = "UNKNOWN" | "PENDING" | "WAITING_CONNECT" | "ONLINE";

/**
 * Describes the enum protos.ConnectStatus.
 */
export const ConnectStatusSchema: GenEnum<ConnectStatus, ConnectStatusJson> = /*@__PURE__*/
  enumDesc(file_connect, 1);

/**
 * @generated from enum protos.ConnectOfflineType
 */
export enum ConnectOfflineType {
  /**
   *    1: "用户人为下机",
   *    2: "时长结束",
   *    3: "超时自动下机",
   *    4: "异常下机",
   *    5: "自动结束，超时未下机",
   *    6: "自动结束，开始新连接",
   *    7: "服务异常",
   *
   * @generated from enum value: RESERVE = 0;
   */
  RESERVE = 0,

  /**
   * 用户人为下机
   *
   * @generated from enum value: USER_ACTION = 1;
   */
  USER_ACTION = 1,

  /**
   * 时长结束
   *
   * @generated from enum value: DEDUCTION_FAILED = 2;
   */
  DEDUCTION_FAILED = 2,

  /**
   * 超时自动下机
   *
   * @generated from enum value: TIME_OUT = 3;
   */
  TIME_OUT = 3,

  /**
   * 异常下机
   *
   * @generated from enum value: EXCEPTION = 4;
   */
  EXCEPTION = 4,

  /**
   * 自动结束，开始新连接
   *
   * @generated from enum value: SWITCH_GAME = 6;
   */
  SWITCH_GAME = 6,

  /**
   * 余额不足
   *
   * @generated from enum value: BALANCE_NOT_ENOUGH = 201;
   */
  BALANCE_NOT_ENOUGH = 201,

  /**
   * 免费游戏时长已用完
   *
   * @generated from enum value: FREE_TIME_USED_UP = 202;
   */
  FREE_TIME_USED_UP = 202,

  /**
   * 免费游戏次数已用完
   *
   * @generated from enum value: FREE_NUMBER_USED_UP = 203;
   */
  FREE_NUMBER_USED_UP = 203,

  /**
   * 可用时长不足
   *
   * @generated from enum value: PLAY_TIME_NOT_ENOUGH = 204;
   */
  PLAY_TIME_NOT_ENOUGH = 204,
}

/**
 * @generated from enum protos.ConnectOfflineType
 */
export type ConnectOfflineTypeJson = "RESERVE" | "USER_ACTION" | "DEDUCTION_FAILED" | "TIME_OUT" | "EXCEPTION" | "SWITCH_GAME" | "BALANCE_NOT_ENOUGH" | "FREE_TIME_USED_UP" | "FREE_NUMBER_USED_UP" | "PLAY_TIME_NOT_ENOUGH";

/**
 * Describes the enum protos.ConnectOfflineType.
 */
export const ConnectOfflineTypeSchema: GenEnum<ConnectOfflineType, ConnectOfflineTypeJson> = /*@__PURE__*/
  enumDesc(file_connect, 2);

