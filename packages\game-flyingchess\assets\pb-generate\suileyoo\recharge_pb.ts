// @generated by protoc-gen-es v2.2.5 with parameter "target=ts,import_extension=none,json_types=true"
// @generated from file recharge.proto (package protos, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage } from "@bufbuild/protobuf/codegenv1";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv1";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file recharge.proto.
 */
export const file_recharge: GenFile = /*@__PURE__*/
  fileDesc("Cg5yZWNoYXJnZS5wcm90bxIGcHJvdG9zIoECChRSZWNoYXJnZU5vdGlmaWNhdGlvbhIzCgZzdGF0dXMYASABKA4yIy5wcm90b3MuUmVjaGFyZ2VOb3RpZmljYXRpb24uU3RhdHVzEhAKCG9yZGVyX2lkGAIgASgEEjoKCm9yZGVyX3R5cGUYAyABKA4yJi5wcm90b3MuUmVjaGFyZ2VOb3RpZmljYXRpb24uT3JkZXJUeXBlEg4KBmFtb3VudBgEIAEoCRIMCgRob3VyGAUgASgEIiIKBlN0YXR1cxILCgdSRVNFUlZFEAASCwoHU1VDQ0VTUxABIiQKCU9yZGVyVHlwZRIICgRDT0lOEAASDQoJUExBWV9USU1FEAFCMAokY29tLnN0bnRzLmNsb3VkLnN1aWxleW9vLmVjaG8ucHJvdG9zWgguL3Byb3Rvc2IGcHJvdG8z");

/**
 * 充值状态通知
 *
 * @generated from message protos.RechargeNotification
 */
export type RechargeNotification = Message<"protos.RechargeNotification"> & {
  /**
   * 认证状态
   *
   * @generated from field: protos.RechargeNotification.Status status = 1;
   */
  status: RechargeNotification_Status;

  /**
   * @generated from field: uint64 order_id = 2;
   */
  orderId: bigint;

  /**
   * @generated from field: protos.RechargeNotification.OrderType order_type = 3;
   */
  orderType: RechargeNotification_OrderType;

  /**
   * @generated from field: string amount = 4;
   */
  amount: string;

  /**
   * @generated from field: uint64 hour = 5;
   */
  hour: bigint;
};

/**
 * 充值状态通知
 *
 * @generated from message protos.RechargeNotification
 */
export type RechargeNotificationJson = {
  /**
   * 认证状态
   *
   * @generated from field: protos.RechargeNotification.Status status = 1;
   */
  status?: RechargeNotification_StatusJson;

  /**
   * @generated from field: uint64 order_id = 2;
   */
  orderId?: string;

  /**
   * @generated from field: protos.RechargeNotification.OrderType order_type = 3;
   */
  orderType?: RechargeNotification_OrderTypeJson;

  /**
   * @generated from field: string amount = 4;
   */
  amount?: string;

  /**
   * @generated from field: uint64 hour = 5;
   */
  hour?: string;
};

/**
 * Describes the message protos.RechargeNotification.
 * Use `create(RechargeNotificationSchema)` to create a new message.
 */
export const RechargeNotificationSchema: GenMessage<RechargeNotification, RechargeNotificationJson> = /*@__PURE__*/
  messageDesc(file_recharge, 0);

/**
 * @generated from enum protos.RechargeNotification.Status
 */
export enum RechargeNotification_Status {
  /**
   * 保留
   *
   * @generated from enum value: RESERVE = 0;
   */
  RESERVE = 0,

  /**
   * 成功
   *
   * @generated from enum value: SUCCESS = 1;
   */
  SUCCESS = 1,
}

/**
 * @generated from enum protos.RechargeNotification.Status
 */
export type RechargeNotification_StatusJson = "RESERVE" | "SUCCESS";

/**
 * Describes the enum protos.RechargeNotification.Status.
 */
export const RechargeNotification_StatusSchema: GenEnum<RechargeNotification_Status, RechargeNotification_StatusJson> = /*@__PURE__*/
  enumDesc(file_recharge, 0, 0);

/**
 * @generated from enum protos.RechargeNotification.OrderType
 */
export enum RechargeNotification_OrderType {
  /**
   * 云币
   *
   * @generated from enum value: COIN = 0;
   */
  COIN = 0,

  /**
   * 时长
   *
   * @generated from enum value: PLAY_TIME = 1;
   */
  PLAY_TIME = 1,
}

/**
 * @generated from enum protos.RechargeNotification.OrderType
 */
export type RechargeNotification_OrderTypeJson = "COIN" | "PLAY_TIME";

/**
 * Describes the enum protos.RechargeNotification.OrderType.
 */
export const RechargeNotification_OrderTypeSchema: GenEnum<RechargeNotification_OrderType, RechargeNotification_OrderTypeJson> = /*@__PURE__*/
  enumDesc(file_recharge, 0, 1);

